{
  "extends": "./src/.umi/tsconfig.json",
  "compilerOptions": {
    "target": "es5",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "noEmit": true,
    "module": "esnext",
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "jsxImportSource": "@antv/f2",
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "esModuleInterop": true,
    "strict": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@@/*": ["src/.umi/*"]
    },
    "moduleResolution": "node", // 更好的模块解析
    "allowSyntheticDefaultImports": true, // 允许默认导入非esModule模块
    "resolveJsonModule": true, // 支持导入json模块
    "noImplicitAny": true, // 禁止隐式any类型
    "noUnusedLocals": true, // 检查未使用的局部变量
    "noUnusedParameters": true // 检查未使用的参数
  },
  "exclude": [
    "node_modules",
    "dist",
    "build",
    "**/*.test.ts",
    "**/*.spec.ts"
  ],
  "include": [
    "src"
  ]
}
