

document.body.addEventListener('touchmove', function (event) {
    event.preventDefault()
    // event.stopPropagation()
}, true);
const transformation = () => {
    let width = window.screen.width || document.body.offsetWidth || document.documentElement.offsetWidth;
    if (width > 800) {
        width = 800
    }
    const baseWidth = 375;
    const baseFontSize = 10;
    const fontSize = width / baseWidth * 10;
    document.body.parentNode.style.fontSize = `${fontSize}px`;
}
transformation()
window.onresize = () => {
    transformation()
}
