declare module 'slash2';
declare module '*.css';
declare module '*.less';
declare module '*.scss';
declare module '*.sass';
declare module '*.svg';
declare module '*.png';
declare module '*.jpg';
declare module '*.jpeg';
declare module '*.gif';
declare module '*.bmp';
declare module '*.tiff';
declare module 'omit.js';
declare module 'numeral';
declare module '@antv/data-set';
declare module 'mockjs';
declare module 'react-fittext';
declare module 'bizcharts-plugin-slider';
declare module 'gs-bim-air';
declare module 'ztree';
declare module 'ailabel';
declare module 'bpmn-js-properties-panel';
declare module '@bpmn-io/properties-panel';
declare module '@dvgis/dc-sdk';

declare namespace JSX {
  interface IntrinsicElements {
    'bim-air-plugin': any;
  }
}
interface Response {
  code: '0' | '1' | '6';
  error: string;
  path: string;
  message: string;
  data: any;
  timestamp: string;
  flag: boolean;
}

interface PageList<T extends any> {
  list: T[];
  total: string;
  pageNum: number;
  pageSize: number;
}

interface PageInfo {
  pageNum: number;
  pageSize: number;
}

// 全局响应
interface GlobalResponse<T> {
  error: null | string;
  message: null | string;
  path: null | string;
  status: number;
  timestamp: string;
  code: string;
  data: T;
}
