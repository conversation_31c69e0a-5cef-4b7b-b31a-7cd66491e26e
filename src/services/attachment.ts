import request, { formHeader } from '@/utils/request';
import { parseParamFromData } from '@/utils/from-data';
/**
 *  新增
 * @export
 * @param {array[file]} files
 * @returns {Promise<any>}
 */
 export async function attachmentUpload(data: object) {
  return request.post(`/Attachment/upload` , { data: parseParamFromData(data), headers: formHeader });
}
/**
 *  删除
 * @export
 * @param {array[file]} files
 * @returns {Promise<any>}
 */
 export async function attachmentDelete(data: { id: string }) {
  return request.delete(`/AttachmentContent/${data.id}`);
}


/**
 *  详情
 * @export
 * @param {string} id
 * @returns {Promise<any>}
 */
export async function attachmentDetail(id: string) {
  return request.get(`/AttachmentContent/${id}`);
}
