export interface UserPrivilegeList {
  code: string;
  codeList: null;
  commonModule: string;
  containerInfo: null;
  deleteFlag: boolean;
  description: string | null;
  displayLocation: string;
  displayState: string;
  excludeasset: null;
  iconClass: string;
  id: string;
  idPath: string;
  injectToken: boolean;
  memo: string;
  memo2: string | null;
  memo3: string | null;
  name: string;
  nocheck: boolean;
  parentId: string;
  preLoad: number;
  projectCode: string;
  props: string;
  routingUrl: string;
  sandbox: number;
  sortNo: number;
  treeLevel: string | null;
  activeRule: string | null;
  activeRule2: string | null;
  basicServiceData: string | null;
  checkPermission: boolean;
  checked: boolean;
  children: Array<UserPrivilegeList> | [];
}

// 常用功能模块
export interface CommonModules {
  commonList: Array<any>;
  createTime: string | null;
  createUser: string | null;
  iconClass: string | null;
  id: string | null;
  memo1: string | null;
  memo2: string | null;
  memo3: string | null;
  name: string | null;
  privilegeProductId: string | null;
  routingUrl: string | null;
  sequence: string | null;
  userId: string | null;
  userPrivilegeList: Array<UserPrivilegeList>;
}

// 两票进行中数据统计
export interface TicketStatistical {
  count: string;
  name: string;
}