import request, { j<PERSON><PERSON>eader } from '@/utils/request';
import { parseParamFromData } from '@/utils/from-data';

/**
 * 用户登录
 * @export
 * @param {AccountLogin} data
 * @returns {Promise<any>}
 */
export async function accountLogin(data: any): Promise<any> {
  return request.post(`/dologin`, { data });
}

/**
 * 退出登录
 * @export
 * @returns {Promise<any>}
 */
export async function accountLoginout(): Promise<any> {
  return request.post(`/logout`);
}

/**
 * 获取用户信息
 * @export
 * @param {string} id 用户Id
 * @returns {Promise<any>}
 */
export async function getUserInfo(id: string): Promise<any> {
  return request.get(`/sys/user/findUserById.json`, { params: { id } });
}

/**
 * 判断当前用户是否登录
 * @export
 * @returns {Promise<any>}
 */
export async function loginStatus(): Promise<any> {
  return request.get('/login/states');
}

/**
 * 获取用户权限列表
 * @export
 * @returns {Promise<any>}
 */
export async function getAuthorizeList(): Promise<any> {
  return request.get('/s/myPrivilege.json');
}

/**
 * 更新用户信息
 * @export
 * @returns {Promise<any>}
 */

export async function putUserInformation(id: string, data: any): Promise<any> {
  return request.put(`/sys/User/${id}`, {
    data: JSON.stringify(data),
    headers: jsonHeader,
  });
}

/**
 *  获取用户详情
 * @export
 * @param {string} id 用户id
 * @returns {Promise<any>}
 */
export async function getUserDetail(id: string): Promise<any> {
  return request.get(`/sys/User/${id}`, { headers: jsonHeader });
}

/**
 * 用户密码修改
 * @export
 * @param {string} newPwd 新密码
 * @param {string} oldPwd 旧密码
 * @returns {Promise<any>}
 */
export async function changePasswprd(data: any) {
  return request.post('/sys/User/updatePassword', {
    // data: JSON.stringify(data),
    // headers: jsonHeader,
    data,
  });
}

/**
 *  获取当前登录人权限
 * @export
 * @param {string} privilegeParentId 用户id
 * @returns {Promise<any>}
 */
export async function getMyPrivilege(): Promise<any> {
  return request.get(`/PrivilegeProduct/myPrivilege`);
}
export async function getLoginTitle(): Promise<any> {
  return request.get(`/sys/SysLoginConfig/getLoginConfig`);
}
