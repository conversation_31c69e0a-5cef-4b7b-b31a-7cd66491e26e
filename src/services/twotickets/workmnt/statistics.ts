import { TicketCountAll, TicketCountAllParams } from '@/pages/twotickets/workmnt/types/statistics';
import request, { jsonHeader } from '@/utils/request';

// 列表查询-电气一种标准票列表查询
export const getTicketCountAllAPI = (params: TicketCountAllParams): Promise<TicketCountAll> => {
  return request(!params?.type ? `/WorkBase/statistical/analysis/total` : `/WorkBase/statistical/ticketTotal`, {
    method: 'get',
    params
  })
}


// 列表查询-总览工作票审核统计
export const getTicketAuditAPI = (params: TicketCountAllParams): Promise<TicketCountAll> => {
  return request(!params?.type ? `/WorkBase/statistical/analysis/audit` : `/WorkBase/statistical/ticket`, {
    method: 'get',
    params
  }).then((res) => !params?.type ? res : { data: res?.data?.auditedStatistic })
}


// 列表查询-总览工作票统计
export const getTicketAllAPI = (params: TicketCountAllParams): Promise<TicketCountAll> => {
  return request(!params?.type ? `/WorkBase/statistical/analysis/ticket` : `/WorkBase/statistical/ticket`, {
    method: 'get',
    params
  }).then((res) => !params?.type ? res : { data: { figures: res?.data?.ticketEndStatistic } })
}


// 列表查询-总览工作票统计
export const averageExecutionAPI = (params: TicketCountAllParams): Promise<TicketCountAll> => {
  return request(`/WorkBase/statistical/averageExecution`, {
    method: 'get',
    params
  })
}
