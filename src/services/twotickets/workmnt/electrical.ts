/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-04-26 21:22:35
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-05-23 09:40:54
 * @FilePath: /mobile-yw/src/services/twotickets/workmnt/electrical.ts
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */
import request, { jsonHeader } from '@/utils/request';

// 列表查询-电气一种标准票列表查询
export const getElectricalOnceListAPI = (data: any): Promise<any> => {
  return request(`/WorkBase/listPage`, {
    method: 'get',
    params: data,
  })
}

/** 删除电气工作票 */
export function deleteElectrical(params: any): Promise<boolean> {
  // @ts-ignore
  return request(`/WorkBase/${params.id}`, { method: 'delete', params }).then((response: Response) => response.flag);
}

/** 工作票延期审批 */
export function delayAudit(data: any): Promise<Response> {
  return request(`/WorkBase/delayAudit`, { method: 'post', data: JSON.stringify(data), headers: jsonHeader }).then((response: Response) => response);
}

/** 工作票试运行/恢复申请审批 */
export function tryRunAudit(data: any): Promise<Response> {
  return request(`/WorkBase/tryRunAudit`, { method: 'post', data: JSON.stringify(data), headers: jsonHeader }).then((response: Response) => response);
}

/** 打印 */
export function exportWordUrl(data: any): Promise<Response> {
  return request(`/WorkBase/exportWordUrl`, { method: 'get', params: data, responseType: 'blob' }).then((response: Response) => response);
}

/** 工作票收回记录添加 */
export function ticketReturn(data: any): Promise<Response> {
  return request(`/WorkBase/ticketReturn`, { method: 'post', data: JSON.stringify(data), headers: jsonHeader }).then((response: Response) => response);
}

/** 负责人变更审批 */
export function headerExchangeAudit(data: any): Promise<Response> {
  return request(`/WorkBase/headerExchangeAudit`, { method: 'post', data: JSON.stringify(data), headers: jsonHeader }).then((response: Response) => response);
}
