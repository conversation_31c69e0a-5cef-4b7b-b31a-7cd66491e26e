/* 列表页 */

import request, { jsonHeader } from '@/utils/request';
import { RequestResponse } from 'umi-request';

// 分页列表
export async function getWorkBaseList(params: any): Promise<any> {
  return request.get(`/WorkBase/listPage`, { params, headers: json<PERSON>eader });
}
// 分页列表
export async function getWorkBaseListPage<T extends object, R>(params: T): Promise<RequestResponse<R>> {
  return request.get(`/WorkBase/listPage?ticketType=6`, { params, headers: jsonHeader });
}
// 删除
export async function deleteWorkBaseItem<T extends object, R>(id: T): Promise<RequestResponse<R>> {
  return request.delete(`/WorkBase/${id}?ticketType=6`, {});
}
// 分页列表
export async function getWorkBaseListPage2<T extends object, R>(params: T): Promise<RequestResponse<R>> {
  return request.get(`/WorkBase/listPage?ticketType=7`, { params, headers: j<PERSON><PERSON>eader });
}
// 删除
export async function deleteWorkBaseItem2<T extends object, R>(id: T): Promise<RequestResponse<R>> {
  return request.delete(`/WorkBase/${id}?ticketType=7`, {});
}

/* 关联主票 */
export async function getRelationListPage<T extends object, R>(params: T): Promise<RequestResponse<R>> {
  return request.get(`/WorkBase/relationListPage`, { params, headers: jsonHeader });
}
/* 新增 */
export async function addWorkBase<T extends object, R>(data: T): Promise<RequestResponse<R>> {
  return request.post(`/WorkBase`, { data: JSON.stringify(data), headers: jsonHeader });
}
/* 编辑 */
export async function editWorkBase<T extends object, R>(data: T): Promise<RequestResponse<R>> {
  return request.put(`/WorkBase`, { data: JSON.stringify(data), headers: jsonHeader });
}
/* 详情 */
export async function getWorkBaseDetail(id: string, params: any): Promise<any> {
  return request.get(`/WorkBase/${id}`, { params });
}
/* 流程历史 */
export async function getProcessHistoric(id: string): Promise<any> {
  return request.get(`/flowable/process-instances/${id}/historic`, {});
}

/* 打印 */
export function exportWordUrl(data: any): Promise<Response> {
  return request.get(`/WorkBase/exportWordUrl`, { params: data, responseType: 'blob' }).then((response: Response) => response);
}

/** 标准工作票详情 */
export function getStandardDetail(id: any): Promise<any> {
  return request.get(`/StandardWorkBase/${id}`, {});
}

/** 标准工作票列表查询 */
export function getStandardList(params?: any): Promise<any> {
  return request.get('/StandardWorkBase/listPage', { params: { ...params } });
}
