import request, { jsonHeader } from '@/utils/request';
import { PageInfo, PageList, Response } from "@/global";

/** 获取水工作业工作票分页数据 */
export function getWorkBasePageList(pageInfo: PageInfo, params?: any): Promise<PageList> {
  return request(`/WorkBase/listPage`, { method: 'get', params: { ...pageInfo, ...params } }).then((response: Response) => response);
}

/** 获取水工作业工作票详情数据 */
export function getWorkBaseDetail(params: any): Promise<Response> {
  return request(`/WorkBase/${params.id}`, { method: 'get', params }).then((response: Response) => response);
}

/** 删除作业工作票 */
export function deleteWorkBase(params: any): Promise<boolean> {
  return request(`/WorkBase/${params.id}`, { method: 'delete', params }).then((response: Response) => response.code === '1');
}

/** 新增作业工作票 */
export function addWorkBase(data: any): Promise<Response> {
  return request(`/WorkBase`, { method: 'post', data: JSON.stringify(data), headers: jsonHeader }).then((response: Response) => response);
}

/** 修改作业工作票 */
export function updateWorkBase(data: any): Promise<Response> {
  return request(`/WorkBase`, { method: 'put', data: JSON.stringify(data), headers: jsonHeader }).then((response: Response) => response);
}


/** 工作票延期审批 */
export function delayAudit(data: any): Promise<Response> {
  return request(`/WorkBase/delayAudit`, { method: 'post', data: JSON.stringify(data), headers: jsonHeader }).then((response: Response) => response);
}

/** 负责人变更审批 */
export function headerExchangeAudit(data: any): Promise<Response> {
  return request(`/WorkBase/headerExchangeAudit`, { method: 'post', data: JSON.stringify(data), headers: jsonHeader }).then((response: Response) => response);
}

/** 打印 */
export function exportWordUrl(data: any): Promise<Response> {
  return request(`/WorkBase/exportWordUrl`, { method: 'get', params: data, responseType: 'blob' }).then((response: Response) => response);
}

/** 标准工作票详情 */
export function detailStdWorkBase(id: any) {
  return request(`/StandardWorkBase/${id}`, { method: 'get' }).then((response: Response) => response);
}

/** 标准工作票列表查询 */
export function getStdWorkBaseList(params?: any) {
  return request('/StandardWorkBase/listPage', { method: 'get', params: { ...params } }).then((response: Response) => response);
}

/** 工作票交回 */
export function ticketReturnAudit(data: any): Promise<Response> {
  return request(`/WorkBase/ticketReturn`, { method: 'post', data: JSON.stringify(data), headers: jsonHeader }).then((response: Response) => response);
}

/** 工作票作废 */
export function workTicketNullIfy(data: any): Promise<Response> {
  return request(`/WorkBase/workTicketNullIfy/${data.id}`, { method: 'post', data: JSON.stringify(data), headers: jsonHeader }).then((response: Response) => response);
}
