/******************统计分析************************/
import { OperateBase } from '@/pages/twotickets/operation/typing';
import request, { jsonHeader } from '@/utils/request';

/**
 * 获取操作统计
 *
 * 该函数通过发送GET请求到指定的API端点，来获取操作统计
 * 主要用于统计操作步骤的执行情况，以便于分析和优化操作流程
 *
 * @param params 查询参数，包含需要统计的信息的条件
 * @returns 返回一个Promise对象，解析后包含操作统计
 */
export function getOperationStatistics(params: operationStatisticsParams = {}) {
  return request('/OperateBase/statistics', {
    method: 'get',
    params,
  });
}

/**
 * 获取操作项统计信息
 *
 * 该函数通过发送GET请求到指定的API端点，来获取操作项统计信息
 * 主要用于统计操作步骤的执行情况，以便于分析和优化操作流程
 *
 * @param params 查询参数，包含需要统计的信息的条件
 * @returns 返回一个Promise对象，解析后包含操作统计信息
 */
export function getOperationItemStatistics(params: operationItemStatisticsParams = {}) {
  return request('/OperateBase/executeStatisticsStep', {
    method: 'get',
    params,
  });
}

/**
 * 获取操作票统计信息
 *
 * 该函数通过发送GET请求到指定的API端点，来获取操作票统计信息
 * 主要用于统计操作步骤的执行情况，以便于分析和优化操作流程
 *
 * @param params 查询参数，包含需要统计的信息的条件
 * @returns 返回一个Promise对象，解析后包含操作统计信息
 */
export function getOperationTicketStatistics(params: operationTicketStatisticsParams = {}) {
  return request('/OperateBase/executeStatisticsStep', {
    method: 'get',
    params,
  });
}

/**
 * 获取操作票人员完成任务列表排行
 *
 * 该函数通过发送GET请求到指定的API端点，来获取操作票人员完成任务列表排行
 * 主要用于统计操作步骤的执行情况，以便于分析和优化操作流程
 *
 * @param params 查询参数，包含需要统计的信息的条件
 * @returns 返回一个Promise对象，解析后包含人员完成任务列表排行
 */
export function getPersonnelCompletionRank(params: personnelCompletionRankParams = {}) {
  return request('/OperateBase/personnelCompletionRank', {
    method: 'get',
    params,
  });
}

/**
 * 获取操作票列表
 *
 * 该函数通过发送GET请求到指定的API端点，来获取操作票列表
 * 主要用于统计操作步骤的执行情况，以便于分析和优化操作流程
 *
 * @param params 查询参数，包含需要统计的信息的条件
 * @returns 返回一个Promise对象，解析后包含操作票列表
 */
export function getOperationList(params: Extract<Required<PageInfo>, Partial<OperateBase>>){
  return request('/OperateBase/listPage', {
    method: 'get',
    params,
  });
}

/**
 * 获取组织结构树
 *
 * 该函数通过发送GET请求到指定的API端点，来获取组织结构树
 * 主要用于统计操作步骤的执行情况，以便于分析和优化操作流程
 *
 * @param params 查询参数，包含需要统计的信息的条件
 * @returns 返回一个Promise对象，解析后包含组织结构树
 */
export function getOrganizationTree(params: any = { params: { parentId: 0 } }) {
  return request('/sys/Organization/selectOrgTree', {
    method: 'get',
    params,
  });
}

/**
 * 获取操作票详情信息
 * @param params
 * @returns
 */
export function getOperationDetail(params: operationDetailByIDParams) {
  return request(`/OperateBase/${params.id}`, {
    method: 'get',
  });
}

/** 获取下一步流程按钮 */
export function getNextTasks(taskId: string): Promise<[]> {
  return request(`/flowable/tasks/${taskId}/nextTasks`, { method: 'get' }).then((response) => {
    if (response.code === '1') {
      return response.data;
    }
    return undefined;
  });
}

//类型字典
export const getDeviceType = (): Promise<any> => {
  return request('/ToolDict/getTypeMap');
};

export const saveOperateInfo = (data: any): Promise<any> => {
  return request('/OperateBase', {
    method: 'put',
    headers: jsonHeader,
    data: JSON.stringify(data),
  });
};

/**
 * 打印 - get
 */
export async function exportWordUrl<T extends object, R>(params: T): Promise<RequestResponse<PaginationDataType<R>>> {
  return request(`/OperateBase/exportWordUrl`, {
    params,
    method: 'get',
    responseType: 'blob',
  });
}

/**
 * 打印 - get
 */
export async function exportFileById<T extends object, R>(params: T): Promise<RequestResponse<PaginationDataType<R>>> {
  return request(`/Attachment/downloadAttachment/${params.id}`, {
    params,
    method: 'get',
    responseType: 'blob',
  });
}

/** 添加标准操作票数据 */
export function addStdOperation(data: any) {
  return request('/StandardOperateBase', { method: 'post', data: JSON.stringify(data), headers: jsonHeader }).then(
    (response: Response) => response.flag,
  );
}
