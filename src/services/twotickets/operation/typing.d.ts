
interface operationStatisticsParams {
  /**
   * 结束日期
   */
  endDate?: string;
  /**
   * 操作人名字
   */
  operatorName?: string;
  /**
   * 开始日期
   */
  startDate?: string;
  /**
   * 状态
   */
  state?: number;
  /**
   * 操作任务
   */
  task?: string;
  /**
   * 年月日
   */
  timeUnit?: string;
  [property: string]: any;
}

// 执行情况统计操作项
interface operationItemStatisticsParams {
  /**
   * 结束日期
   */
  endDate?: string;
  /**
   * 查询已终结的票
   */
  isFinished?: number;
  /**
   * 操作人名字
   */
  operatorName?: string;
  selectFinished?: number;
  selectPass?: number;
  selectTotal?: number;
  /**
   * 开始日期
   */
  startDate?: string;
  /**
   * 状态
   */
  state?: number;
  /**
   * 操作任务
   */
  task?: string;
  /**
   * 月日
   */
  timeUnit?: string;
  /**
   * 年
   */
  year?: string;
  [property: string]: any;
}
// 执行情况统计操作票
interface operationTicketStatisticsParams {
  /**
   * 结束日期
   */
  endDate?: string;
  /**
   * 操作人名字
   */
  operatorName?: string;
  /**
   * 开始日期
   */
  startDate?: string;
  /**
   * 月日
   */
  timeUnit?: string;
  /**
   * 年
   */
  year?: string;
  [property: string]: any;
}
// 人员完成任务列表排行
interface personnelCompletionRankParams {
  /**
   * 结束日期
   */
  endDate?: string;
  /**
   * 操作人名字
   */
  operatorName?: string;
  /**
   * 开始日期
   */
  startDate?: string;
  /**
   * 状态
   */
  state?: number;
  /**
   * 月日
   */
  timeUnit?: string;
  [property: string]: any;
}

interface perationListParams {
  /**
   * 操作票编号
   */
  code?: string;
  conditionJson?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 创建人id
   */
  createUserId?: number;
  /**
   * 创建人名称
   */
  createUserName?: string;
  /**
   * kks编码
   */
  deviceKksCode?: string;
  /**
   * 设备名称
   */
  deviceName?: string;
  /**
   * 审核结果
   */
  examineResult?: number;
  /**
   * 审核意见
   */
  examineView?: string;
  /**
   * 第一次审核值长id
   */
  firstShiftSupervisorId?: number;
  /**
   * 第一次审核值长名称
   */
  firstShiftSupervisorName?: string;
  formKey?: string;
  /**
   * 监护人id
   */
  guardianId?: number;
  /**
   * 监护人名称
   */
  guardianName?: string;
  /**
   * id
   */
  id?: number;
  /**
   * 图片id
   */
  imgId?: number;
  isCurTask?: boolean;
  /**
   * 操作地点
   */
  location?: string;
  /**
   * memo
   */
  memo?: string;
  /**
   * 当月序号
   */
  mouthNumber?: number;
  /**
   * 操作开始时间
   */
  operateBeginTime?: string;
  /**
   * 操作结束时间
   */
  operateEndTime?: string;
  /**
   * 操作人id
   */
  operatorId?: string;
  /**
   * 操作人名称
   */
  operatorName?: string;
  /**
   * 操作备注
   */
  operatorRemark?: string;
  orderBy?: string;
  /**
   * 发令值长id
   */
  orderShiftSupervisorId?: number;
  /**
   * 发令值长名称
   */
  orderShiftSupervisorName?: string;
  /**
   * 发令提交时间
   */
  orderSubmitTime?: string;
  /**
   * 发令时间
   */
  orderTime?: string;
  pageNum?: number;
  pageSize?: number;
  /**
   * 计划开始时间
   */
  planBeginTime?: string;
  /**
   * 计划结束时间
   */
  planEndTime?: string;
  /**
   * 流程id
   */
  processInstanceId?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 文件id
   */
  reportId?: number;
  /**
   * 第二次审核值长id
   */
  secondShiftSupervisorId?: number;
  /**
   * 第二次审核值长名称
   */
  secondShiftSupervisorName?: string;
  /**
   * 状态
   */
  state?: number;
  /**
   * 状态名称
   */
  stateName?: string;
  /**
   * 操作任务
   */
  task?: string;
  taskDefKey?: string;
  taskId?: string;
  taskName?: string;
  taskUserName?: string;
  /**
   * 操作票类型
   */
  type?: string;
  /**
   * 操作单位id
   */
  unitId?: number;
  /**
   * 操作单位名称
   */
  unitName?: string;
  /**
   * 值班负责人id
   */
  watchId?: number;
  /**
   * 值班负责人名称
   */
  watchName?: string;
  /**
   * 流程状态
   */
  workflowState?: number;
  workFlowStateName?: string;
  [property: string]: any;
}


export interface operationDetailByIDParams {
  id: string;
  [property: string]: any;
}