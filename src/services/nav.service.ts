import request, { jsonHeader } from '@/utils/request';
import { CommonModules, TicketStatistical } from './nav.service.typing';

export interface pageParams {
  pageNum: number; //页码
  pageSize: number; // 页数
  taskStatus?: string; //任务状态
  checkType?: string; //检查类型
  checkUnit?: string; //检查单位
  checkStartTime?: string; //检查时间-开始
  checkEndTime?: string; // 检查时间-结束
  minQuestionNum?: string; // 问题最小个数
  maxQuestionNum?: string; // 问题最大个数
  [key: string]: any;
}

// 编辑常用功能
export const postCustomizeModules = (params: unknown): Promise<GlobalResponse<any>> => {
  return request.post('/UserCustomPrivilege/customizeModules', { data: JSON.stringify(params), headers: jsonHeader });
};

//获取当前角色的功能模块
export const getModules = (): Promise<GlobalResponse<CommonModules>> => {
  return request.get('/UserCustomPrivilege/getModules');
};

//分页获取当前角色的待办，formKeyType:处理路由为移动端（m）或pc端(b)
export const getMyTodoList = (params: Record<string, any>): Promise<GlobalResponse<any>> => {
  return request.get('/flowable/task/do/mylistPage', { params: { formKeyType: 'm', ...params } });
};
//获取当前角色的所有待办
export const getAllMyTodoList = (): Promise<GlobalResponse<any>> => {
  return request.get('/flowable/task/do/mylist');
};

//获取当前角色的待办的数据统计
export const getMyTodoCount = (params: Record<string, any>): Promise<GlobalResponse<any>> => {
  return request.get('/flowable/task/do/getCountByExample', { params });
};

//获取当前角色的所有已办
export const getAllMyDoneList = (): Promise<GlobalResponse<any>> => {
  return request.get('/flowable/task/done/mylist');
};

//分页获取当前角色的已办
export const getMyDoneList = (params: Record<string, any>): Promise<GlobalResponse<any>> => {
  return request.get('/flowable/task/done/mylistPage', { params: { formKeyType: 'm', ...params } });
};

//获取当前角色的已办的数据统计
export const getMyDoneCount = (params: Record<string, any>): Promise<GlobalResponse<any>> => {
  return request.get('/flowable/task/done/getCountByExample', { params });
};

// 获取用户列表
export const getUserList = (): Promise<GlobalResponse<any>> => {
  return request.get('/sys/User/list');
};

// 获取当前角色的待办数据统计
interface TicketStatisticalParams {
  ticketType: string;
}
export const getTicketStatistical = (params: TicketStatisticalParams): Promise<GlobalResponse<TicketStatistical[]>> => {
  return request.get('/WorkBase/statistical/ongoing', { params });
};
