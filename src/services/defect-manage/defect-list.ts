import { WorkTicketTypeEnum } from '@/constants/twotickets';

// 设备缺陷列表
export interface DefectList {
  attachmentId: null | string;
  businessType: string;
  businessTypeName: string;
  claimUrl: null;
  completedTime: null;
  createTime: string;
  createUser: string;
  curTask: boolean;
  clearanceCardCurTask: boolean; // 缺陷卡是否有权限
  cardState: null | string; // 消缺卡状态
  defectFixDeadline: null;
  deviceName: string;
  doUrl: null;
  fillTime: string;
  fillUser: string;
  fillUserName: string;
  findTime: string;
  findUser: string;
  findUserNmae: string;
  flawCategory: string;
  flawCode: string;
  flawContent: string;
  flawImageId: string;
  flawOtherDescriptions: string;
  flawState: number;
  timeout: boolean;
  flawStateName: string;
  flawType: number;
  formKey: string;
  id: string;
  kksCode: string;
  memo1: null | string;
  memo2: null | string;
  memo3: null | string;
  name: null | string;
  processDefinitionKey: string;
  processInstanceId: null | string;
  projectId: null | string;
  reapplyUrl: null;
  cardWorkflowState: null | string;
  repeatFlaw: number;
  signTime: null;
  speciality: string;
  supplementedFlag: number;
  taskDefKey: null | string;
  taskId: null | string;
  taskName: null | string;
  taskUserName: null | string;
  updateTime: null | string;
  updateUser: null | string;
  viewUrl: null | string;
  workFlowStateName: null | string;
  workUser: null | string;
  workUserName: null | string;
  workflowState: null | string;
}

// 设备缺陷详情
export interface DefectDetail {
  associateWorkBaseVOList: Array<{
    id: number;
    ticketType: string;
    workName: string;
    workTaskName: WorkTicketTypeEnum;
  }>;
  attachmentContentList: null | Array<any>;
  attachmentId: null | string;
  auditoUserName: null | string;
  businessType: string;
  businessTypeName: string;
  claimUrl: null | string;
  completedTime: null | string;
  createTime: string;
  createUser: string;
  curTask: boolean;
  clearanceCardCurTask: boolean; // 缺陷卡是否有权限
  defectFixDeadline: null | string;
  deviceName: string;
  doUrl: null | string;
  fillTime: string;
  fillUser: string;
  fillUserName: null | string;
  findTime: string;
  findUser: string;
  findUserNmae: null | string;
  flawCategory: string;
  flawCode: string;
  flawContent: string;
  flawImageId: string;
  flawOtherDescriptions: null | string;
  flawState: number;
  flawStateName: string;
  flawType: null | string;
  formKey: null | string;
  id: string;
  kksCode: string;
  timeout: boolean;
  memo1: null | string;
  memo2: null | string;
  memo3: null | string;
  name: null | string;
  processDefinitionKey: string;
  processInstanceId: null | string;
  projectId: null | string;
  reapplyUrl: null | string;
  repeatFlaw: number;
  signTime: null | string;
  speciality: string;
  supplementedFlag: number;
  taskDefKey: null | string;
  taskId: null | string;
  taskName: null | string;
  taskUserName: null | string;
  updateTime: null | string;
  updateUser: null | string;
  viewUrl: null | string;
  workFlowStateName: string;
  workUser: null | string;
  workUserName: null | string;
  workflowApprovalRecordList: null | Array<any>;
  workflowState: null | string;
  specialityName: string;
  workTeamId: string;
  pictureContentList: Array<{
    businesstype: null | string;
    endTime: null | string;
    format: string;
    genre: string;
    id: string;
    ids: null | string;
    length: number;
    memo: string;
    name: string;
    startTime: null | string;
    thumbnail: string;
    type: string;
    uploadDate: string;
    uploadUserName: string;
    uploadUserRealName: string;
    url: string;
    userId: string;
  }>;
}

