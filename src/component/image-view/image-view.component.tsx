import { urlPrefix } from '@/utils/request';
import { Space, Image, ImageViewer, ErrorBlock } from 'antd-mobile';
import { useState } from 'react';

interface Props {
  imgList: any[];
  getContainer?: HTMLElement | (() => HTMLElement) | null;
}

const ImageView: React.FC<Props> = ({ imgList = [], getContainer = null }) => {
  const [image, setImage] = useState('');
  const [visible, setVisible] = useState(false);

  const newImgList = imgList?.filter((item) => item && item !== '0');

  return (
    <>
      <Space wrap>
        {newImgList.length > 0 ? (
          newImgList?.map((item) => (
            <Image
              key={item}
              src={`${urlPrefix}/Attachment/downloadAttachment/thumbnail/${item}`}
              width={100}
              height={100}
              fit="fill"
              style={{ borderRadius: '18px' }}
              onClick={() => {
                setVisible(true);
                setImage(`${urlPrefix}/Attachment/downloadAttachment/${item}`);
              }}
            />
          ))
        ) : (
          <ErrorBlock
            status="empty"
            description={'暂无图片'}
            title={null}
            style={{
              '--image-height': '80px',
            }}
          />
        )}
      </Space>
      <ImageViewer
        getContainer={getContainer}
        image={image}
        visible={visible}
        onClose={() => {
          setVisible(false);
        }}
      />
    </>
  );
};
export default ImageView;
