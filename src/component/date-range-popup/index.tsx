import React, { useState, useMemo } from 'react'
import { Popup, CapsuleTabs, PickerView } from 'antd-mobile'

const years = Array.from({ length: 50 }, (_, i) => `${1970 + i}`)
const months = Array.from({ length: 12 }, (_, i) => `${i + 1}`.padStart(2, '0'))

function getDaysInMonth(year, month) {
  const y = parseInt(year, 10)
  const m = parseInt(month, 10)
  const days = new Date(y, m, 0).getDate()
  return Array.from({ length: days }, (_, i) => `${i + 1}`.padStart(2, '0'))
}

const DateRangePopup = ({ visible, onClose, onChange }) => {
  const [tab, setTab] = useState('start')
  const [startDate, setStartDate] = useState(['2020', '01', '01'])
  const [endDate, setEndDate] = useState(['2020', '01', '01'])

  const currentDate = tab === 'start' ? startDate : endDate

  const days = useMemo(() => {
    const [year, month] = currentDate
    return getDaysInMonth(year, month)
  }, [currentDate])

  const handlePickerChange = (val) => {
    if (tab === 'start') {
      setStartDate(val)
      onChange && onChange({ start: val, end: endDate })
    } else {
      setEndDate(val)
      onChange && onChange({ start: startDate, end: val })
    }
  }

  return (
    <div style={{ padding: '16px 0' }}>
      <CapsuleTabs
        activeKey={tab}
        onChange={setTab}
      >
        <CapsuleTabs.Tab title="许可时间" key="start" />
        <CapsuleTabs.Tab title="终结时间" key="end" />
      </CapsuleTabs>
      <div style={{ marginTop: 24 }}>
        <PickerView
          columns={[years, months, days]}
          value={tab === 'start' ? startDate : endDate}
          onChange={handlePickerChange}
        />
      </div>
    </div>
  )
}

export default DateRangePopup