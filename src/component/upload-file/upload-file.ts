export interface propOptions {
  tips?: string; //提示文字
  value?: any;
  uploadType?:string[];//上传方式，图片，文件，拍照
  onChange?: (val: fileOptions[]) => void;
  max?: number | null;
  setCleanupMethods?: (val: any) => void; // 删除文件的方法
}
export interface Actions {
  text: React.ReactNode;
  key: string;
}
export interface fileOptions {
  id: string;
  inquireImager: string;
  format?: string; //后缀名
  url: string;
  type?: string; //类型，others-其他,picture-图片
  pictureUrl?: string;
  name?: string;
  fileId?: string;
}
