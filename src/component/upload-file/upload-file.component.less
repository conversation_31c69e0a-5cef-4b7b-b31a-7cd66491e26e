.uploadText {
  color: var(--adm-color-weak);
  font-size: 36px;
  text-align: right;
}

.img-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.item {
  flex: 0 0 250px;
  height: 250px;
  background: #F5F5F5;
}

.add {
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon {
  color: #D7D7D7;
  font-size: 150px;
}

.img-item {
  position: relative;
  background: #F5F5F5;
}

.del-icon {
  border-radius: 0 0 0 20px;
  top: 0px;
  right: 0px;
  text-align: center;
  display: inline-block;
  padding: 0px 8px;
  color: #fff;
  font-size: 30px;
  position: absolute;
  background: #000;
}

.file-name {
  width: 250px;
  height: 250px;
  text-align: left;
  //overflow-wrap: break-word;
  display: flex;
  align-items: center;
  justify-content: center;
  span {
    max-width: 100%;
    display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3; /* 显示3行 */
  overflow: hidden;
  text-overflow: ellipsis; /* 可选，用于单行文本末尾的省略号 */  
  }
}
:global {
  .upload-file-container {
    .adm-image {
      width: 250px;
      height: 250px;
    }
  }
}