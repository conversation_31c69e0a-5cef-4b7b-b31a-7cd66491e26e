import styles from './file-look.component.less'
import { Popup, NavBar, DotLoading, Toast } from 'antd-mobile'
import { useState, FC, useRef } from 'react'
import type { propOptions } from './file-look'
import FileViewer from 'react-file-viewer';
const FileLook: FC<propOptions> = ({ type="xlsx",url, open, close }) => {
  const [load, setLoad] = useState<boolean>(false)//上传加载控制

  async function afterShow() {
  }
  return (
    <Popup
      visible={open}
      onMaskClick={close}
      onClose={close}
      afterShow={afterShow}
      position='right'
      bodyStyle={{ width: '100vw' }}
    >
      <div className={styles["header-container"]}>
        <NavBar className={styles.nbar}
          backArrow={true}
          onBack={close}
        >
          文件预览
        </NavBar>
      </div>
      <div className={`${styles["file-look-form-container"]} file-look`}>
        {
          url?<FileViewer
          fileType={type}//文件类型
          filePath={url} //文件地址
        />:null
        }
      </div>
    </Popup >
  )


}

export default FileLook