import styles from './upload-file.component.less'
import { propOptions, Actions, fileOptions } from './upload-file'
import { FC, useState, useRef, useEffect, useCallback } from 'react'
import { AddOutline, CloseOutline } from 'antd-mobile-icons'
import { ImageViewer, Image, ActionSheet, ImageUploader, ImageUploadItem, Toast, SpinLoading } from 'antd-mobile'
import { attachmentDetail, attachmentDelete, attachmentUpload } from '@/services/attachment'
import { urlPrefix } from '@/utils/request'
import FileLook from './components/file-look/file-look.component'
import { nativeCallback, usePicFile } from '@/utils/native-fun';

const UploadFile: FC<propOptions> = ({ uploadType = ['img', 'file', 'camera'], onChange, value, tips = ".jpg、.png", max = null, setCleanupMethods }) => {
  const [visible, setVisible] = useState<boolean>(false)//弹框面板控制
  const [load, setLoad] = useState<boolean>(false)//上传加载控制
  const [fileModal, setFileModal] = useState<boolean>(false)//文件预览控制
  const [fileList, setFileList] = useState<fileOptions[]>([])//文件列表
  const [fileUrl, setFileUrl] = useState<string>()//预览文件地址
  const [format, setFormat] = useState<string>()//预览文件类型
  const [deleteIdList, setDeleteIdList] = useState<string[]>([])//需要删除的id列表
  const [isUpload, setIsUpload] = useState<boolean>(false); // 是否调用相机上传图片
  const fileRef = useRef(null)
  const imgRef = useRef(null)
  const { picFile, clearPicFile } = usePicFile();
  const actions: Actions[] = [
    { text: '图片选择', key: 'img' },
    { text: '文件选择', key: 'file' },
    { text: "相机拍照", key: 'camera' },
  ]
  const actionsFilter = actions.filter(item => uploadType.includes(item.key))
  useEffect(() => {
    clearPicFile();

    if (Array.isArray(value)) {
      setFileList(value)
    } else if (typeof value === 'string') {
      fileShowHandle(value)
    }
  }, [value]);

  useEffect(() => {
    if (picFile && isUpload) {
      uploadHandle(picFile);
    }
  }, [picFile, isUpload]);

  function fileShowHandle(value: string) {//文件回显处理
    const list: string[] = value !== "" ? value.split(",") : []
    const fetchs: Promise<any>[] = list.map(item => attachmentDetail(item))
    if (fetchs.length > 0) {
      Promise.allSettled(fetchs).then(res => {//获取文件详情数据
        const fulList = res.filter(item => item.status === "fulfilled")
        const lists: fileOptions[] = fileList.concat(fulList.map((item: any) => {
          return {
            id: item.value.data?.id,
            fileId: item.value.data?.id,
            format: item.value.data?.format,
            inquireImager: `${urlPrefix}/Attachment/inquireImager/${item.value.data?.id}`,//查看图片地址
            url: `${urlPrefix}/Attachment/downloadAttachment/${item.value.data?.id}`,//下载图片地址
            type: item.value.data?.type,
            pictureUrl: item.value.data?.url,
            name: item.value.data?.name,
          }
        }))
        onChange(lists)
      })
    }
  }
  async function onActionHandle(action: Actions) {//ActionSheet选项处理
    if (action.key === "img") {
      const nativeInput = imgRef.current?.nativeElement
      if (nativeInput) {
        nativeInput.click()
      }
    } else if (action.key === "file") {
      const fileInput = fileRef.current
      if (fileInput) {
        fileInput.click()
      }
    } else if (action.key === "camera") {
      const _isCallback = await nativeCallback('camera');
      setIsUpload(_isCallback);
    }
  }
  function uploadHandle(file: File) {
    setLoad(true)
    attachmentUpload({ files: file }).then(res => {
      setLoad(false);
      if (res.code === "1") {
        const list: fileOptions[] = fileList.concat(res.data.attachmentContentList.map(item => {
          return {
            id: item.id,
            fileId: item.id,
            format: item.format,
            inquireImager: `${urlPrefix}/Attachment/inquireImager/${item.id}`,//查看图片地址
            url: `${urlPrefix}/Attachment/downloadAttachment/${item.id}`,//下载图片地址
            type: item.type,
            pictureUrl: item.url,
            name: item.name,
          }
        }))
        onChange(list)
        //setFileList(list)
        Toast.show({
          icon: 'success',
          content: "上传成功",
        });
        setIsUpload(false);
        clearPicFile();
      } else {
        setIsUpload(false);
        clearPicFile();
        Toast.show({
          icon: 'fail',
          content: res.message || '上传失败',
        })
      }
    }).catch(err => {
      setIsUpload(false);
      setLoad(false)
    })
  }
  function fileChange(event: any) {//文件选择
    const file = event.target.files[0]; // 获取文件引用
    if (file) {
      uploadHandle(file)
    }
  }
  function imageChange(file: File): Promise<ImageUploadItem> {//图片选择
    console.log('imageChange file', file, JSON.stringify(file));
    uploadHandle(file)
    return new Promise((resolve, reject) => {
      resolve({
        url: URL.createObjectURL(file)
      })
    })
  }
  function actionOpen(): void {//点击上传处理
    if (load === false) {
      if (max && fileList.length >= max) {
        Toast.show({
          icon: 'fail',
          content: `最多上传${max}个文件`,
        })
      } else {
        setVisible(true)
      }
    }
  }
  function imgShow(item: fileOptions) {//图片预览
    if (item.type === "picture" || item.type == undefined) {
      ImageViewer.show({
        image: item.url
      })
    } else {
      setFileUrl(item.url)
      setFormat(item.format)
      setFileModal(true)
    }
  }
  //缓存需要删除文件id
  function delFile(event: React.MouseEvent<HTMLSpanElement, MouseEvent>, id: string) {
    event.stopPropagation();//阻止点击冒泡
    setDeleteIdList((val) => {
      if (!val.includes(id)) {
        return [...val, id]
      }
      return val;
    })
    const files = fileList.filter((file) => file.id !== id)
    // Toast.show({
    //   icon: 'success',
    //   content: "删除成功",
    // })
    onChange(files);
    setIsUpload(false);
  }

  // 删除附件的方法
  const deleteFileOnSubmit = useCallback(async () => {
    const promises = deleteIdList.map(async (id) => {
      const res = await attachmentDelete({ id });
      if (res.code === '1') {
        clearPicFile();
        //setFileList(files)
      }
      //  else {
      //   Toast.show({
      //     icon: 'fail',
      //     content: res.message || '删除失败',
      //   })
      // }
      return res;
    });

    await Promise.all(promises);
    return 'Cleanup completed';
  }, [deleteIdList])

  function fileListComponent() {//文件展示模板
    return fileList.map((item, index: number) => {
      return <div key={item.id} onClick={() => imgShow(item)} className={styles["img-item"]}>
        {
          item.type === "picture" || item.type == undefined ?
            <Image src={item.url} fit='cover' />
            :
            <div className={styles["file-name"]}>
              <span>{item.name}</span>
            </div>
        }
        <span onClick={(e) => delFile(e, item.id)} className={styles["del-icon"]}>
          <CloseOutline />
        </span>
      </div>
    })
  }

  // 调用原生上传
  const handleUpload = async () => {
    clearPicFile();
    const _isCallback = await nativeCallback('camera');
    setIsUpload(_isCallback)
  }

  // 设置清理方法，以便父级清除时间使用
  useEffect(() => {
    if (setCleanupMethods) {
      setCleanupMethods((list) => [...list, deleteFileOnSubmit]);
      return () => {
        setCleanupMethods(methods => methods.filter(m => m !== deleteFileOnSubmit));
      };
    }
  }, [setCleanupMethods, deleteIdList]);

  return <div className='upload-file-container'>
    <div className={styles["img-container"]}>
      {fileListComponent()}
      <div onClick={actionOpen} className={`${styles.item} ${styles.add}`}>
        {
          load ?
            <SpinLoading color='primary' />
            :
            <AddOutline className={styles.icon} />
        }
      </div>
    </div>
    {tips && <div className={styles.uploadText}>支持：{tips}</div>}
    <ActionSheet
      extra='请选择你要进行的操作'
      cancelText='取消'
      closeOnAction={true}
      onAction={onActionHandle}
      visible={visible}
      actions={actionsFilter}
      onClose={() => setVisible(false)}
    />
    <input onChange={fileChange} ref={fileRef} style={{ display: "none" }} id="fileUpload" type="file" />
    <ImageUploader style={{ display: "none" }} ref={imgRef} upload={imageChange}>
      <span
        className='adm-image-uploader-cell adm-image-uploader-upload-button'
        onClick={handleUpload}
      >
        <span className='adm-image-uploader-upload-button-icon'>
          <AddOutline />
        </span>
      </span>
    </ImageUploader>
    <FileLook type={format} url={fileUrl} open={fileModal} close={() => setFileModal(false)}></FileLook>
  </div>
}

export default UploadFile