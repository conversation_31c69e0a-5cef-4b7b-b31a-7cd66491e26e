.header-filter {
  padding: 30px 35px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .filter-type {
    display: flex;
    // flex-wrap: wrap;
    gap: 24px;
    flex-grow: 0;
    overflow: auto;
    padding-right: 24px;

    // 自定义按钮替代 antd-mobile Button
    .typeButton {
      flex-shrink: 0;
      padding: 8px 16px;
      border-radius: 16px;
      margin-right: 48px;
      font-size: 32px;
      background-color: #f5f5f5;
      text-align: center;

      &.active {
        background-color: rgba(17, 84, 237, 0.1);
        color: #1154ed;
      }
    }

    :global {
      .adm-button {
        min-width: 180px;
        padding: 10px 48px;
        font-size: 42px;
      }
    }
  }


  .icons {
    display: flex;
    font-size: 60px;
    height: 100%;
    align-items: center;
    justify-content: center;

    &>svg+svg {
      margin-left: 48px;
    }
  }
}


.popupHeader {
  --popup-header-height: 144px;
  --popup-header-font-size: 48px;
  height: var(--popup-header-height);
  border-radius: 36px 36px 0px 0px;
  border-bottom: 1px solid rgba(230, 230, 230, 1);
  position: relative;

  .title {
    font-size: var(--popup-header-font-size);
    font-weight: 400;
    line-height: var(--popup-header-height);
    user-select: none;
    color: rgb(51, 51, 51);
    text-align: center;
  }

  .close {
    position: absolute;
    z-index: 2;
    right: 0;
    font-size: var(--popup-header-font-size);
    height: var(--popup-header-height);
    width: var(--popup-header-height);
    top: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.popupContent {
  height: calc(100%); // 减去头部和底部按钮高度
}

.grid-demo-item-block {
  font-size: 48px;
  box-sizing: border-box;
  height: 100%;

}

.contentWrapper {
  :global {
    .adm-form-item-label {
      font-size: 48px;
      font-weight: 500;
      letter-spacing: 0px;
      line-height: 69.999px;
      color: rgba(51, 51, 51, 1);
      margin-bottom: 39px;
    }

    .adm-text-area {
      box-sizing: border-box;
      padding: 21.999px 44.001px;
      border-radius: 20.001px;
      background: rgba(245, 245, 245, 1);

      // 提示文字改小点
      .adm-text-area-element::placeholder {
        font-size: 36px;
      }
    }

    .adm-list-default .adm-list-body {
      border: none;
    }

    .adm-selector .adm-space.adm-space {
      --gap: 21.999px;
    }

    .adm-selector-item {
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      width: 216px;
      height: 102px;
      font-size: 36px;
      font-weight: 400;
      letter-spacing: 0px;
    }
  }
}

// 底部按钮区域
.btns {
  height: 150px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);

  :global {
    .adm-button:nth-child(1) {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }

    .adm-button:nth-last-child(1) {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }
}

.flexContainer {
  display: flex;
  width: 100%;
  height: 100%;

  .sidebarWrapper {
    min-width: 105px;
    flex: 0 0 auto;
  }

  .contentWrapper {
    flex: 1 1 auto;
    overflow: auto;
    padding: 0 8px;
  }
}


.calendarList {
  display: flex;
  gap: 48px;
  margin-bottom: 12px;
  padding: 48px;

  :global {
    .adm-button {
      padding: 12px 48px;
      height: 82px;
      border-width: 4px;


      font-size: 38px;
      line-height: 50px;


      &.adm-button-primary {
        --adm-color-border: rgba(17, 84, 237, 1);
      }
    }
  }
}

.calendarDatePicker {
  :global {
    .header {
      display: none !important;
    }

    .body {
      position: initial !important;
    }
  }
}