import { Button, Form, SideBar } from 'antd-mobile';
import { useState, useMemo } from 'react';
import { useRequest } from 'ahooks';
import styles from './header-filter.component.less';
import { FilterDatePicker } from '@/component/filter-datepicker/filter-datepicker.component';
import { CalendarOutline, FilterOutline } from 'antd-mobile-icons';
import DynamicFormItems, { FormItemType, FormItemConfig } from '@/component/dynamic-form-items/dynamic-form-items.component';
import { FilterPopup } from '../filter-popup/filter-popup.component';
import { getOrganizationTree } from '@/services/twotickets/operation/statistics';
import { WORKFLOW_STATE_MAP_TEXT } from '@/constant';
import dayjs from 'dayjs';

/**
 * 菜单项定义
 */
export interface MenuItem {
  label: string;
  value: string;
  formItems?: FormItemConfig[];
}

/**
 * 头部筛选组件属性
 */
interface HeaderFilterProps {
  /**
   * 顶部筛选类型数组，如 [{label: '全部', value: '全部'}]
   */
  searchTypes?: { label: string; value: string }[];
  /**
   * 当前激活的类型 value（受控）
   */
  activeType?: string;
  /**
   * 默认激活的类型 value（非受控，仅初始化时生效）
   */
  defaultActiveType?: string;
  /**
   * 类型切换回调，参数为 value
   */
  onTypeChange?: (value: string) => void;
  /**
   * 是否显示日历图标
   */
  showCalendarIcon?: boolean;
  /**
   * 筛选弹窗菜单项配置
   */
  menuItems?: MenuItem[];
  /**
   * 筛选提交回调
   */
  onFilterSubmit?: (values: any) => void;
  /**
   * 菜单项切换回调
   */
  onMenuChange?: (menuKey: string) => void;
  /**
   * 表单初始值
   */
  initialValues?: Record<string, any>;
  /**
   * 日期类型按钮配置
   */
  dateTypes?: DateTypeOption[];
  /**
   * 当前选中的日期类型
   */
  activeDateType?: string;
  /**
   * 默认选中的日期类型
   */
  defaultActiveDateType?: string;
  /**
   * 日期类型切换回调
   */
  onDateTypeChange?: (value: string) => void;
}

const initialFormValues = {
  examineResult: [''],
  state: [''],
};

// 默认筛选类型
const defaultFilterTypes = [
  { label: '全部', value: '全部' },
  { label: '待办', value: '待办' },
  { label: '已办', value: '已办' },
];
// 修改日期类型接口
interface DateTypeOption {
  label: string;
  value: string;
  startDate: number | null;
  endDate: number | null;
}

/**
 * 头部筛选组件
 */
const HeaderFilterComponent = (props: HeaderFilterProps) => {
  // 解构 props
  const {
    searchTypes,
    activeType,
    defaultActiveType,
    onTypeChange,
    showCalendarIcon = true,
    menuItems: propMenuItems,
    onFilterSubmit,
    onMenuChange,
    dateTypes,
    activeDateType,
    defaultActiveDateType,
    onDateTypeChange,
  } = props;

  // 获取组织树数据（放在组件内部顶层，符合 Hooks 规则）
  const { data: organizationTreeData } = useRequest(() => getOrganizationTree().then((res) => res.data), { manual: false });

  // 使用 state 存储日期类型选项，以便可以更新其值
  const [dateTypeOptions, setDateTypeOptions] = useState<DateTypeOption[]>(
    dateTypes?.length
      ? dateTypes
      : [
          { label: '终结时间', value: 'finalTime', startDate: null, endDate: null },
          { label: '许可时间', value: 'allowTime', startDate: null, endDate: null },
        ],
  );

  // 根据组织树数据构建默认菜单项
  const defaultMenuItems = useMemo(() => {
    return [
      {
        label: '综合筛选',
        value: 'general',
        formItems: [
          {
            type: FormItemType.SELECTOR,
            name: 'examineResult',
            label: '审核结果',
            options: [
              { label: '全部', value: '' },
              { label: '合格', value: '1' },
              { label: '不合格', value: '0' },
            ],
            defaultValue: [''],
          },
          {
            type: FormItemType.SELECTOR,
            name: 'state',
            label: '状态',
            options: [
              { label: '全部', value: '' },
              ...Object.keys(WORKFLOW_STATE_MAP_TEXT)
                ?.filter((key) => key)
                .map((key) => ({
                  label: WORKFLOW_STATE_MAP_TEXT[key],
                  value: key,
                })),
            ],
            defaultValue: [''],
          },
        ],
      },
      {
        label: '操作单位',
        value: 'unit',
        formItems: [
          {
            type: FormItemType.LINKED_SELECTION,
            name: 'unitId',
            columns: 2,
            data: organizationTreeData, // 这里使用从 useRequest 获取的数据
            style: {
              height: '100%',
            },
          },
        ],
      },
      {
        label: '搜索',
        value: 'search',
        formItems: [
          {
            type: FormItemType.TEXT_AREA,
            name: 'task',
            label: '操作任务',
            placeholder: '请输入',
            rows: 2,
          },
          {
            type: FormItemType.TEXT_AREA,
            name: 'code',
            label: '操作票号',
            placeholder: '请输入',
            rows: 2,
          },
          {
            type: FormItemType.TEXT_AREA,
            name: 'operatorName',
            label: '操作人',
            placeholder: '请输入',
            rows: 2,
          },
          {
            type: FormItemType.TEXT_AREA,
            name: 'guardianName',
            label: '监护人',
            placeholder: '请输入',
            rows: 2,
          },
        ],
      },
    ];
  }, [organizationTreeData]); // 依赖于 organizationTreeData

  // 使用外部传入的类型或默认类型
  const filterTypes = searchTypes?.length ? searchTypes : defaultFilterTypes;

  // 使用外部传入的菜单项或默认菜单项
  const menuItems = propMenuItems?.length ? propMenuItems : defaultMenuItems;

  // 当前选中的筛选菜单项
  const [activeMenu, setActiveMenu] = useState(menuItems[0]?.value || 'general');

  // 内部状态管理，优先使用defaultActiveType，其次使用第一个选项
  const [localActiveType, setLocalActiveType] = useState(defaultActiveType || filterTypes[0]?.value || '全部');

  // 当前激活类型：优先使用受控值，否则使用内部状态
  const currentActiveType = activeType !== undefined ? activeType : localActiveType;

  // 内部状态管理，优先使用defaultActiveDateType，其次使用第一个选项
  const [localActiveDateType, setLocalActiveDateType] = useState(defaultActiveDateType || dateTypeOptions[0]?.value || 'finalTime');

  // 当前激活日期类型：优先使用受控值，否则使用内部状态
  const currentActiveDateType = activeDateType !== undefined ? activeDateType : localActiveDateType;

  // 获取当前活动日期类型的完整选项
  const getCurrentDateTypeOption = () => {
    return dateTypeOptions.find((item) => item.value === currentActiveDateType) || dateTypeOptions[0];
  };

  // 处理日期类型切换
  const handleDateTypeChange = (value: string) => {
    if (activeDateType === undefined) {
      setLocalActiveDateType(value);
    }
    onDateTypeChange?.(value);
  };
  // 处理类型切换
  const handleTypeChange = (value: string) => {
    if (activeType === undefined) {
      setLocalActiveType(value);
    }
    onTypeChange?.(value);
  };

  // 处理筛选菜单切换
  const handleMenuChange = (value: string) => {
    setActiveMenu(value);
    onMenuChange?.(value);
  };

  // 获取当前日期类型的日期范围值
  const currentDateTypeOption = getCurrentDateTypeOption();
  const currentDateValue = {
    startDate: currentDateTypeOption.startDate,
    endDate: currentDateTypeOption.endDate,
    mode: 'day' as const,
  };

  // 获取当前菜单对应的表单项
  const getCurrentFormItems = useMemo(
    () => menuItems.map((item) => item?.formItems?.map?.((ele) => ({ ...ele, hide: item.value !== activeMenu })) || [])?.flat?.() || [],
    [menuItems, activeMenu],
  );

  // 新增：统一管理两个 popup 的表单数据
  const [filterFormValues, setFilterFormValues] = useState<any>({});
  const [dateFormValues, setDateFormValues] = useState<any>({});

  // 新增：处理筛选表单提交（筛选弹窗）
  const handleFilterFormSubmit = (values: any) => {
    setFilterFormValues(values);
    // 只需要合并当前 filterFormValues 和最新的日期数据
    const mergedDateValues = {};
    dateTypeOptions.forEach((option) => {
      mergedDateValues[option.value] = [
        option.startDate ? dayjs(option.startDate).format('YYYY-MM-DD HH:mm:ss') : '',
        option.endDate ? dayjs(option.endDate).format('YYYY-MM-DD HH:mm:ss') : '',
      ];
    });
    // 合并两个 popup 的数据后统一提交
    handleUnifiedSubmit(values, mergedDateValues);
  };
  // 新增：处理日期表单提交（日历弹窗）
  const handleDateFormSubmit = () => {
    // 只需要合并当前 filterFormValues 和最新的日期数据
    const mergedDateValues = {};
    dateTypeOptions.forEach((option) => {
      mergedDateValues[option.value] = [
        option.startDate ? dayjs(option.startDate).format('YYYY-MM-DD HH:mm:ss') : '',
        option.endDate ? dayjs(option.endDate).format('YYYY-MM-DD HH:mm:ss') : '',
      ];
    });
    handleUnifiedSubmit(filterFormValues, mergedDateValues);
  };
  // 新增：日期弹窗内日期变更时同步保存
  const handleChangeDate = (dateValue: { startDate: number | null; endDate: number | null; mode: 'year' | 'month' | 'day' }) => {
    const updatedOptions = dateTypeOptions.map((option) => {
      if (option.value === currentActiveDateType) {
        return {
          ...option,
          startDate: dateValue.startDate,
          endDate: dateValue.endDate,
        };
      }
      return option;
    });
    setDateTypeOptions(updatedOptions);
    setDateFormValues?.({
      dateType: currentActiveDateType,
      startDate: dateValue.startDate,
      endDate: dateValue.endDate,
    });
  };
  // 新增：统一提交方法
  const handleUnifiedSubmit = (filterValues: any, dateValues: any) => {
    const mergedValues = {
      ...filterValues,
      ...dateValues,
    };
    onFilterSubmit?.(mergedValues);
  };

  return (
    <div className={styles['header-filter']}>
      <div className={styles['filter-type']}>
        {/* 顶部类型按钮组 */}
        {filterTypes.map((tab) => (
          <Button
            key={tab.value}
            onClick={() => handleTypeChange(tab.value)}
            shape="rounded"
            style={{ '--border-color': '#0000', '--background-color': '#fff' }}
            fill={currentActiveType === tab.value ? 'outline' : 'none'}
            color={currentActiveType === tab.value ? 'primary' : 'default'}
          >
            {tab.label}
          </Button>
        ))}
      </div>
      <div className={styles.icons}>
        {/* 日期选择 Popup */}
        {showCalendarIcon && (
          <FilterPopup
            popupProps={{
              bodyStyle: {
                borderTopLeftRadius: '3.3333vw',
                borderTopRightRadius: '3.3333vw',
                minHeight: '90vh',
                display: 'flex',
                flexDirection: 'column',
              },
            }}
            iconRender={<CalendarOutline fontSize={24} />}
            onSubmit={handleDateFormSubmit}
          >
            <div>
              <div className={styles.calendarList}>
                {dateTypeOptions.map((dateType) => (
                  <Button
                    key={dateType.value}
                    color={currentActiveDateType === dateType.value ? 'primary' : 'default'}
                    fill="outline"
                    shape="rounded"
                    onClick={() => handleDateTypeChange(dateType.value)}
                  >
                    {dateType.label}
                  </Button>
                ))}
              </div>

              <div className={styles.calendarDatePicker}>
                <Form.Item name="dateType">
                  <FilterDatePicker value={currentDateValue} initialMode="day" onChange={handleChangeDate} />
                </Form.Item>
              </div>
            </div>
          </FilterPopup>
        )}

        {/* 筛选 Popup */}
        <FilterPopup iconRender={<FilterOutline fontSize={24} />} onSubmit={handleFilterFormSubmit}>
          <div className={styles.flexContainer}>
            <div className={styles.sidebarWrapper}>
              <SideBar activeKey={activeMenu} onChange={handleMenuChange}>
                {menuItems.map((item) => (
                  <SideBar.Item key={item.value} title={item.label} />
                ))}
              </SideBar>
            </div>
            <div className={styles.contentWrapper}>
              {/* 根据不同的菜单项显示不同的内容 */}
              <DynamicFormItems formItems={getCurrentFormItems} />
            </div>
          </div>
        </FilterPopup>
      </div>
    </div>
  );
};

export default HeaderFilterComponent;
