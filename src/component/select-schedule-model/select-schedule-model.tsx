import { RightOutline } from "antd-mobile-icons";
import styles from "./select-schedule-model.less";
import { <PERSON><PERSON>, CascaderView, <PERSON>up, Toast } from "antd-mobile";
import { CloseOutline } from "antd-mobile-icons";
import { useEffect, useState } from "react";
import {
  CascaderValue,
  CascaderValueExtend,
} from "antd-mobile/es/components/cascader-view";
import { deepFindParents } from "@/pages/quality-manage/quality-manage.utils";
import useScheduleTree from "@/hooks/useScheduleModelTree";
interface Props {
  value?: string;
  onChange?: (value: string, item) => void;
}
const defaultSelectedData = {
  value: [],
  extend: { isLeaf: false, items: [] },
};

const SelectSchedule: React.FC<Props> = ({ value, onChange }) => {
  const [visible, setVisible] = useState(false);
  const [confirmList, setConfirmList] = useState<
    { label: string; value: string }[]
  >([]);
  const [selectdData, setSelectedData] = useState<{
    value: CascaderValue[];
    extend: CascaderValueExtend;
  }>(defaultSelectedData);

  const { loading, getScheduleTreeData, scheduleTree } = useScheduleTree();

  useEffect(() => {
    if (value && scheduleTree?.length) {
      const items = deepFindParents(scheduleTree, value);
      if(!items){return}
    
      const selectIdPaths =items?.map(item=>item.id)
      const { label } = items[0];
      setSelectedData({
        value: selectIdPaths?.reverse(),
        extend: { isLeaf: true, items },
      });
      setConfirmList([
        {
          label,
          value,
        },
      ]);
    }
  }, [value, scheduleTree]);

  const handleChange = (
    value: CascaderValue[],
    extend: CascaderValueExtend
  ) => {
    setSelectedData({ value, extend });
  };

  const handleConfirm = () => {
    const { isLeaf, items } = selectdData.extend;
    if (isLeaf) {
      const confirmData = items[items.length - 1];
      setVisible(false);
      setConfirmList([confirmData]);
      onChange?.(confirmData.value, confirmData);
    } else {
      Toast.show({
        content: "请选择结构部位",
      });
    }
  };

  const handleCancel = () => {
    setSelectedData(defaultSelectedData);
    setVisible(false);
  };

  useEffect(() => {
    getScheduleTreeData();
  }, []);

  return (
    <div
      className={styles.selectItem}
      onClick={() => {
        setVisible(true);
      }}
    >
      <span className={confirmList?.length ? styles.value : styles.placeholder}>
        {confirmList?.length
          ? confirmList.map((item) => `${item.label}`)
          : "请选择"}
      </span>
      <RightOutline />
      <Popup
        visible={visible}
        getContainer={null}
        onMaskClick={() => {
          setVisible(false);
        }}
        onClose={() => {
          setVisible(false);
        }}
        bodyStyle={{ borderRadius: "8px 8px 0 0" }}
      >
        <div className={styles.popupTitle}>
          <span />
          选择结构部位
          <CloseOutline
            onClick={() => {
              setVisible(false);
            }}
          />
        </div>
        <CascaderView
          loading={loading}
          options={scheduleTree}
          value={selectdData.value}
          onChange={handleChange}
        />
        <div className={styles.btnGroups}>
          <Button color="primary" fill="outline" onClick={handleCancel}>
            取消
          </Button>
          <Button color="primary" onClick={handleConfirm}>
            确定选择
          </Button>
        </div>
      </Popup>
    </div>
  );
};
export default SelectSchedule;
