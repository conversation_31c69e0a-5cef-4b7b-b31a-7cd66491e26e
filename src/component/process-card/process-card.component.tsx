import { Card, Space, Steps, Tag } from 'antd-mobile';
import { DownOutline, RightOutline, UpOutline } from 'antd-mobile-icons';
import dayjs from 'dayjs';
import { useEffect, useMemo, useRef, useState } from 'react';
import { styled, history } from 'umi';

const ProcessCardWrapper = styled(Card)`
  box-sizing: border-box;
  width: 100%;
  .adm-steps-vertical .adm-step .adm-step-indicator::after {
    width: .3703vw;
    background-color: rgba(166, 166, 166, 1);
  }
  .adm-step-icon-dot {
    width: 2.7778vw;
    height: 2.7778vw;
    border-radius: 50%;
  }
  .title {
    font-size: 4.2592vw;
    font-weight: 500;
    letter-spacing: 0vw;
    line-height: 6.1675vw;
    color: rgba(51, 51, 51, 1);
  }
  .content{
    overflow-y: hidden;
  }
  .flowchart {
    font-size: 3.3333vw;
    font-weight: 500;
    letter-spacing: 0vw;
    line-height: 4.8269vw;
    color: rgba(17, 84, 237, 1);
  }
  /* 节点名称 */
  .task_name {
    font-size: 4.4444vw;
    font-weight: 500;
    letter-spacing: 0vw;
    line-height: 6.4353vw;
  }
  .approval_name {
    font-size: 3.3333vw;
    font-weight: 400;
    letter-spacing: 0vw;
    line-height: 4.8269vw;
    color: rgba(17, 84, 237, 1);
    .label {
      color: rgba(51, 51, 51, 1);
    }
  }
  /* 提交时间 */
  .approval_time {
    font-size: 3.3333vw;
    font-weight: 400;
    letter-spacing: 0vw;
    line-height: 4.8269vw;
    color: rgba(102, 102, 102, 1);
  }
  .opinion {
    font-size: 3.8889vw;
    font-weight: 400;
    letter-spacing: 0vw;
    line-height: 5.6314vw;
    color: rgba(51, 51, 51, 1);
    padding: 1.8519vw 2.5925vw;
    border-radius: 1.6667vw;
    background: rgba(245, 245, 245, 1);
  }
  .footer {
    padding-top: 1.0185vw;
    border-top: .0926vw solid #e5e5e5;
    display: flex;
    justify-content: center;
    font-size: 3.8889vw;
    font-weight: 400;
    letter-spacing: 0vw;
    line-height: 5.6314vw;
    color: rgba(17, 84, 237, 1);
  }
`;

type ProcessCardProps = {
  id: number; // 当前任务id
  records: any[]; // 操作记录
  taskUserName: string | null; // 当前用户处理人
  onProcess: (id: number) => void;
};
const { Step } = Steps;

/**
 * ProcessCard组件用于显示处理流程卡片
 * @param {ProcessCardProps} props - 组件的属性
 * @param {number} props.id - 当前任务id
 * @param {any[]} props.records - 操作记录
 * @param {string | null} props.taskUserName - 当前用户处理人
 * @param {() => void} props.onProcess - 处理流程的回调函数
 * @returns {JSX.Element} - 返回渲染的组件
 */
export const ProcessCard = (props: ProcessCardProps) => {
  const { id, records, taskUserName = null, onProcess } = props;
  const [showAllFlowChart, setShowAllFlowChart] = useState(false);
  const [hideStepsHeight, setHideStepsHeight] = useState<string | number>('auto');
  const stepsRef = useRef(null);
  const hideDivRef = useRef(null);
  useEffect(() => {
    if (!showAllFlowChart && records.length > 2 && stepsRef.current && hideDivRef.current) {
      // 获取距离顶部高度
      setHideStepsHeight(hideDivRef.current.getBoundingClientRect().top - stepsRef.current.getBoundingClientRect().top) 
    }else{
      setHideStepsHeight('auto');
    }
  }, [showAllFlowChart, records, stepsRef.current, hideDivRef.current]);
  console.log(records, taskUserName)
  return (
    <ProcessCardWrapper
      title={<span className="title">处理流程</span>}
      extra={
        <Space className="flowchart" onClick={()=>{
          onProcess(id)
        }}>
          查看流程图 <RightOutline fontSize="3.3333vw" />
        </Space>
      }
    >
      <div className="content" ref={stepsRef} style={{
        height: hideStepsHeight
      }}>
        <Steps
          direction="vertical"
          current={0}
          style={{
            // '--title-font-size': '16.899.1667vw',
            '--description-font-size': '1.3889vw',
            '--indicator-margin-right': '1.1111vw',
            '--icon-size': '2.037vw',
            '--adm-color-weak': 'rgba(51, 51, 51, 1)'
          }}
        >
          {records.map((item, index) => {
            return (
              <Step
                key={index}
                title={
                  <span className="task_name">
                    {item.taskName}{item.approverName ? <>（{item.approverName}）</> : null}
                    {item?.approvalResult === '提交' ? (
                      <Tag
                        color="#02aafa"
                        style={{
                          marginLeft: '.9258vw',
                        }}
                      >
                        提交
                      </Tag>
                    ) : null}
                    {item?.approvalResult === '退回' || item?.approvalResult === '驳回' ? (
                      <Tag
                        color="#d9001b"
                        style={{
                          marginLeft: '.9258vw',
                        }}
                      >
                        驳回
                      </Tag>
                    ) : null}
                    {item?.approvalResult === '终结' ? (
                      <Tag
                        color="rgba(17, 84, 237, 1)"
                        style={{
                          marginLeft: '.9258vw',
                        }}
                      >
                        已终结
                      </Tag>
                    ) : null}
                    {item?.approvalResult === '合格' ? (
                      <Tag
                        color="#02e190"
                        style={{
                          marginLeft: '.9258vw',
                        }}
                      >
                        合格
                      </Tag>
                    ) : null}
                    {item?.approvalResult === '通过' ? (
                      <Tag
                        color="#02e190"
                        style={{
                          marginLeft: '.9258vw',
                        }}
                      >
                        通过
                      </Tag>
                    ) : null}
                    {item?.approvalResult === '不合格' ? (
                      <Tag
                        color="#d9001b"
                        style={{
                          marginLeft: '.9258vw',
                        }}
                      >
                        不合格
                      </Tag>
                    ) : null}
                    {item?.approvalResult === '作废' ? (
                      <Tag
                        color="rgba(253, 146, 40, 1)"
                        style={{
                          marginLeft: '.9258vw',
                        }}
                      >
                        作废
                      </Tag>
                    ) : null}
                    {item?.approvalResult === '待提交' ? (
                      <Tag
                        color=""
                        style={{
                          '--background-color': 'rgba(215, 225, 250, 1)',
                          '--text-color': 'rgba(17, 84, 237, 1)',
                          marginLeft: '.9258vw',
                        }}
                      >
                        待提交
                      </Tag>
                    ) : null}
                  </span>
                }
                description={
                  <>
                    <Space block direction="vertical">
                      {taskUserName && index === 0 && item?.approvalResult === '待提交' && (
                        <span className="approval_name">
                          <span className="label">当前处理人：</span>
                          {taskUserName}
                        </span>
                      )}
                      {item?.approvalTime && (
                        <span className="approval_time">提交时间：{dayjs(item?.approvalTime).format('YYYY/MM/DD HH:mm:ss')}</span>
                      )}
                      {item?.opinion ? (
                        <div className="opinion">
                          <span>
                            {['工作票负责人变动', '发起负责人变更申请'].includes(item.taskName)
                              ? '变动情况说明：'
                              : ['工作票延期', '发起工作票延期'].includes(item.taskName)
                              ? '延期理由：'
                              : ['工作票交回'].includes(item.taskName)
                              ? '备注：'
                              : '审核意见：'}
                          </span>
                          <span>{item.opinion}</span>
                        </div>
                      ) : null}
                    </Space>
                    {index === 1 && <div ref={hideDivRef} className="hide-line"></div>}
                  </>
                }
                // onClick={() => onProcess(item.id)}
              />
            );
          })}
        </Steps>
      </div>
      {records?.length > 2 && (
        <div className="footer" onClick={() => setShowAllFlowChart(!showAllFlowChart)}>
          {!showAllFlowChart ? (
            <Space>
              全部流程 <DownOutline />
            </Space>
          ) : (
            <Space>
              收起 <UpOutline />
            </Space>
          )}
        </div>
      )}
    </ProcessCardWrapper>
  );
};
