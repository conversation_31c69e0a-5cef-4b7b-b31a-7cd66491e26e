import React from 'react';
import { Form, TextArea, Selector } from 'antd-mobile';
import { LinkedSelection } from '@/component/linked-selection/linked-selection.component';

/**
 * 表单项类型
 */
export enum FormItemType {
  SELECTOR = 'selector',
  TEXT_AREA = 'textArea',
  LINKED_SELECTION = 'linkedSelection',
}

/**
 * 表单项配置接口
 */
export interface FormItemConfig {
  defaultValue: unknown;
  type: FormItemType;
  name: string;
  label?: string;
  placeholder?: string;
  options?: {
    label: string;
    value: string | number;
  }[];
  rows?: number;
  columns?: number;
  hide?: boolean;
  data?: any[];
  style?: React.CSSProperties;
  fieldProps?: any;
}

interface DynamicFormItemsProps {
  formItems: FormItemConfig[];
  activeTab?: string;
}

/**
 * 动态表单项组件，根据配置渲染不同类型的表单项
 */
const DynamicFormItems: React.FC<DynamicFormItemsProps> = ({ formItems }) => {
  // 默认的选择器样式
  const defaultSelectorStyle = {
    '--border-radius': '.6667rem',
    '--border': 'none',
    '--checked-border': 'none',
    '--padding': '0',
    '--color': 'rgba(245, 245, 245, 1)',
    '--text-color': 'rgba(51, 51, 51, 1)',
    '--checked-color': 'rgba(215, 225, 250, 1)',
    '--checked-text-color': 'rgba(17, 84, 237, 1)',
    '--gap': '8px',
  };

  // 默认的链接选择器样式
  const defaultLinkedSelectionStyle = {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#fff',
    zIndex: 1,
    borderRadius: '.6667rem',
    overflow: 'hidden',
    boxSizing: 'border-box',
    padding: '.8rem 1.2rem',
    border: 'none',
  };

  const renderFormItem = (item: FormItemConfig) => {
    switch (item.type) {
      case FormItemType.SELECTOR:
        return (
          <Form.Item key={item.name} name={item.name} label={item.label}
            style={{ display: item.hide ? 'none' : 'block' }}
            initialValue={item.defaultValue}>
            <Selector
              style={{ ...defaultSelectorStyle, ...item.style }}
              showCheckMark={false}
              options={item.options || []}
              {...item.fieldProps}
            />
          </Form.Item>
        );
      case FormItemType.TEXT_AREA:
        return (
          <Form.Item key={item.name} name={item.name} label={item.label}
            style={{ display: item.hide ? 'none' : 'block' }}
            initialValue={item.defaultValue}>
            <TextArea placeholder={item.placeholder || '请输入'} rows={item.rows || 2} {...item.fieldProps} />
          </Form.Item>
        );
      case FormItemType.LINKED_SELECTION:
        return (
          <Form.Item
            key={item.name}
            name={item.name}
            style={{ ...(item.style || {}), display: item.hide ? 'none' : 'block' }}
            initialValue={item.defaultValue}
          >
            <LinkedSelection
              columns={item.columns || 2}
              data={item.data || []}
              style={{ ...defaultLinkedSelectionStyle, ...item.fieldProps?.style }}
              {...item.fieldProps}
            />
          </Form.Item>
        );
      default:
        return null;
    }
  };

  return (
    <div style={{ overflowY: 'auto', height: '100%' }}>
      {formItems.map(renderFormItem)}
    </div>
  );
};

export default DynamicFormItems; 