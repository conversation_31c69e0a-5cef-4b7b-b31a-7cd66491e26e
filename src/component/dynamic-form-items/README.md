# 动态表单项组件 (DynamicFormItems)

这是一个用于动态渲染表单项的组件，通过传递 JSON 配置数组来实现不同表单项的渲染。

## 特性

- 支持多种表单项类型：选择器 (Selector)、文本域 (TextArea)、链接选择器 (LinkedSelection)
- 简化表单配置，提高代码复用性
- 基于配置驱动，易于扩展

## 用法

### 基本用法

```tsx
import DynamicFormItems, { FormItemType, FormItemConfig } from '@/component/dynamic-form-items/dynamic-form-items.component';

// 定义表单项配置
const formItems: FormItemConfig[] = [
  {
    type: FormItemType.SELECTOR,
    name: 'status',
    label: '状态',
    options: [
      { label: '全部', value: '' },
      { label: '启用', value: '1' },
      { label: '禁用', value: '0' },
    ],
    defaultValue: [''],
  },
  {
    type: FormItemType.TEXT_AREA,
    name: 'remark',
    label: '备注',
    placeholder: '请输入备注',
    rows: 2,
  },
];

// 在组件中使用
const MyComponent = () => {
  return (
    <Form>
      <DynamicFormItems formItems={formItems} />
    </Form>
  );
};
```

### 结合 Tab 页使用

```tsx
import { useState } from 'react';
import { SideBar } from 'antd-mobile';
import DynamicFormItems, { FormItemType, FormItemConfig } from '@/component/dynamic-form-items/dynamic-form-items.component';

const MyFilterComponent = () => {
  const [activeTab, setActiveTab] = useState('1');
  
  // 根据当前活动标签获取表单项
  const getFormItemsByTab = () => {
    const formItems: FormItemConfig[] = [];
    
    if (activeTab === '1') {
      // 标签1的表单项
      formItems.push(
        {
          type: FormItemType.SELECTOR,
          name: 'status',
          label: '状态',
          options: [
            { label: '全部', value: '' },
            { label: '启用', value: '1' },
            { label: '禁用', value: '0' },
          ],
          defaultValue: [''],
        }
      );
    } else if (activeTab === '2') {
      // 标签2的表单项
      formItems.push(
        {
          type: FormItemType.TEXT_AREA,
          name: 'remark',
          label: '备注',
          placeholder: '请输入备注',
          rows: 2,
        }
      );
    }
    
    return formItems;
  };
  
  return (
    <div className="filter-container">
      <div className="sidebar">
        <SideBar activeKey={activeTab} onChange={(key) => setActiveTab(key)}>
          <SideBar.Item key="1" title="标签1" />
          <SideBar.Item key="2" title="标签2" />
        </SideBar>
      </div>
      <div className="content">
        <DynamicFormItems formItems={getFormItemsByTab()} />
      </div>
    </div>
  );
};
```

## 配置项

### FormItemConfig 接口

| 属性 | 类型 | 说明 |
|------|------|------|
| type | FormItemType | 表单项类型 |
| name | string | 表单项名称 |
| label | string (可选) | 表单项标签 |
| placeholder | string (可选) | 占位文本 |
| options | Array (可选) | 选择器选项 |
| defaultValue | any (可选) | 默认值 |
| rows | number (可选) | 文本域行数 |
| columns | number (可选) | 链接选择器列数 |
| data | Array (可选) | 链接选择器数据 |
| style | CSSProperties (可选) | 自定义样式 |
| fieldProps | any (可选) | 字段额外属性 |

### FormItemType 枚举

| 值 | 说明 |
|------|------|
| SELECTOR | 选择器 |
| TEXT_AREA | 文本域 |
| LINKED_SELECTION | 链接选择器 | 