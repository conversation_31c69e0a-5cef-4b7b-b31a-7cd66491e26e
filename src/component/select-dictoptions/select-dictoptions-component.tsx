import { RightOutline } from "antd-mobile-icons";
import styles from "./select-dictoptions-component.less";
import { CheckList, Popup } from "antd-mobile";
import { CloseOutline } from "antd-mobile-icons";
import { useEffect, useState } from "react";
import useDict from "@/hooks/useDict";
import { CheckListValue } from "antd-mobile/es/components/check-list";
import { FormInstance } from "antd-mobile/es/components/form";

interface Props {
  value?: CheckListValue[];
  form?: FormInstance;
  onChange?: (value: string) => void;
  dictTypeId: string;
  title?: string;
}

const SelecDictOptionComponent: React.FC<Props> = ({
  value,
  onChange,
  dictTypeId,
  title,
}) => {
  const [visible, setVisible] = useState(false);
  const [slectedOp, setSlectedOp] = useState("");
  const { dictOptData } = useDict(dictTypeId);

  const handleChange = (value: any) => {
    setVisible(false);
    onChange?.(value)
    setSlectedOp(
      dictOptData?.find((item) => item.value === value?.[0])?.label ?? ""
    );
  };

  useEffect(() => {
    if (value?.length && dictOptData?.length) {
      setSlectedOp(
        dictOptData?.find((item) => item.value === value?.[0])?.label ?? "-"
      );
    }
  }, [value]);

  return (
    <div
      className={styles.selectItem}
      onClick={() => {
        setVisible(true);
      }}
    >
      <span className={slectedOp?.length ? styles.value : styles.placeholder}>
        {slectedOp || "请选择"}
      </span>
      <RightOutline />
      <Popup
        visible={visible}
        getContainer={null}
        onMaskClick={() => {
          setVisible(false);
        }}
        onClose={() => {
          setVisible(false);
        }}
        bodyStyle={{ borderRadius: "8px 8px 0 0" }}
      >
        <div className={styles.popupTitle}>
          <span />
          {title || "请选择"}
          <CloseOutline
            onClick={() => {
              setVisible(false);
            }}
          />
        </div>
        <div className={styles.checkList}>
          <CheckList
            value={value}
            onChange={handleChange}
          >
            {dictOptData?.map((item) => (
              <CheckList.Item value={item?.value} key={item?.value}>{item?.label}</CheckList.Item>
            ))}
          </CheckList>
        </div>
      </Popup>
    </div>
  );
};

export default SelecDictOptionComponent;
