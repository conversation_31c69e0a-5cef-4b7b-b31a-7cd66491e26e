.selectItem {
  width: 100%; //自定义组件修改宽度，5月23日
  display: flex;
  justify-content: right;
  align-items: center;
  gap: 10px;
  margin-right: -55px;

  .value {
    color: var(--adm-color-text);
  }

  .placeholder {
    color: var(--adm-color-weak);
  }

  .popupTitle {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 128px;
    padding: 0 48px;
    font-size: 48px;
    color: var(--adm-color-text);
    border-bottom: 1px solid #e6e6e6;
    background-color: #fff;
  }

  .checkList {
    :global {
      .adm-list-item {
        margin-top: 0;
        border-radius: 0;

        .adm-list-item-content {
          padding: 20px 10px;
        }
      }
    }
  }

  .btnGroups {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    padding: 21px 0;

    :global {
      .adm-button {
        width: 44%;
      }
    }
  }

  :global {
    .adm-tabs-header {
      border-bottom: none;
      background: var(--adm-color-box);
    }

    .adm-cascader-view-content {
      .adm-list {
        padding: 0;

        .adm-list-item {
          margin-top: 0;
          border-radius: 0;
        }

        .adm-list-item-content {
          padding: 0 48px 0 0;
        }
      }
    }
  }

  .selesctedOp {
    border: #1154ED 1px solid;
    border-radius: 12px;
    padding: 8px;
    font-size: 42px;
  }
}