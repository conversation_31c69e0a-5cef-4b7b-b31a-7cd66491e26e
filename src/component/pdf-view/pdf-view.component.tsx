import { urlPrefix } from "@/utils/request";
import { Space, Popup } from "antd-mobile";
import { CloseOutline } from 'antd-mobile-icons';
import { useState } from "react";
import styles from './pdf-view.component.less';

interface Props {
  pdfList: any[];
}

const PdfView: React.FC<Props> = ({ pdfList = [] })=> {
  const [pdf, setPdf] = useState('');
  const [visible, setVisible] = useState(false);

  return (
    <>
      <Space block direction='vertical'>
        {pdfList?.map((item) => (
          <div key={item.id} className={styles.pdfPanel}>
            <span key={item.id} onClick={() => {
              setPdf(`${urlPrefix}/preview/onlinePreview?url=/${item.url}&officePreviewType=allImages`)
              setVisible(true);
            }}>
              {item.name}
            </span>
          </div>
        ))}
      </Space>
      <Popup
        visible={visible}
        onMaskClick={() => {
          setVisible(false)
        }}
        onClose={() => {
          setVisible(false)
        }}
        bodyStyle={{ height: '80%', borderRadius: '8px 8px 0 0' }}
      >
        <div className={styles.popupTitle}><span />文件预览<CloseOutline onClick={() => { setVisible(false) }} /></div>
        <iframe src={pdf} width='100%' height='100%' frameBorder="0" />
      </Popup>
    </>
  )
};
export default PdfView;
