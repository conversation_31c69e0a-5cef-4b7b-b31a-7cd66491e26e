import request, { formHeader, jsonHeader } from '@/utils/request';

/** 获取组织机构树 */
export async function queryOrganizationTree(params: object): Promise<any> {
  return request.get(`/sys/Organization/tree`, { params });
}

/**  通过参建单位id查询组织id */
export async function getBuildIdByOrganizationId(buildId: string): Promise<any> {
  return request.get(`/CheckQuestion/getBuildIdByOrganizationId/${buildId} `);
}

/** 人员列表 */
export async function listAllPage(params?: {}): Promise<any> {
  return request.get('/sys/User/listAllPage', {
    params: { current: 1, pageSize: 99999, ...params },
  });
}
