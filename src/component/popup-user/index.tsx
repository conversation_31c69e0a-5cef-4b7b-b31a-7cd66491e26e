import React, { ReactNode, useEffect, useMemo, useState } from 'react'
import { LeftOutline, RightOutline, CloseOutline } from 'antd-mobile-icons'

import styles from './index.less'
import { <PERSON><PERSON>, CheckList, ErrorBlock, Popover, Popup, SearchBar } from 'antd-mobile';
import { getBuildIdByOrganizationId, listAllPage } from './index.service';
import OrganizationSelect from './organization-select';

interface Iprops {
  value?: any,
  onChange?: any,
  disabled?: boolean, //禁用。默认false
  multiple?: boolean,
  placeholder?: string, //提示文字
  isSearch?: boolean, //是否需要搜索框
  title?: string
  titleRender?: (data: any) => ReactNode;//列表内容自定义
  defaultReformUnit?: string; //参建id
  fieldNames?: { value: string, label: string },
}

export default function PopupUser({
  value,
  onChange,
  disabled = false,
  multiple = false,
  placeholder = '请选择',
  isSearch = false,
  title = "选择人员",
  titleRender,
  defaultReformUnit,
  fieldNames = { value: 'id', label: 'name' },
}: Iprops) {
  const [visible, setvisible] = useState<boolean>(false)

  const [organizationId, setorganizationId] = useState<string>('') //选择的组织

  const [dataSources, setDataSources] = useState<string[]>([]) //所有人员列表
  const [searchText, setSearchText] = useState<string>('')
  const [choseData, setChoseData] = useState<string[]>([]) // 选择的人员
  //已选中列表
  const choseList = useMemo(() => {
    return dataSources.filter(item => {
      return choseData.includes(item[fieldNames.value])
    })
  }, [dataSources, choseData])
  //搜索列表
  const filterList = useMemo(() => {
    return dataSources?.filter(item => {
      if (searchText !== "") {
        return item[fieldNames.label].includes(searchText)
      } else {
        return true
      }
    })
  }, [dataSources, searchText])

  /** 查询所有人员 */
  const getlistAllPage = (id?: string) => {
    listAllPage(id === 'all' ? {} : { organizationId: id }).then((res) => {
      if (res?.code === '1') {
        setDataSources(res?.data?.list || [])
      }
    })
  }

  function showHandle() {//label回显处理
    if (value) {
      const val = Array.isArray(value) ? value : [value]
      setChoseData(val)
    } else {
      setChoseData([])
    }
  }

  function openHandle() {//打开弹框处理
    setvisible(true)
  }

  function checkListChange(val: string[]) {//列表改变
    setChoseData(val)
  }

  /** 点击卡片删除 */
  const delItemHandle = (key: string) => {//选中删除
    const list = choseData.filter(item => item !== key)
    onChange(list)
    setChoseData(list)
  }

  function sureHandle() {//确定处理
    let list: string[] | string = JSON.parse(JSON.stringify(choseData))
    if (!multiple) {
      list = list.length > 0 ? list[0] : ""
    }
    onChange(list)
    setvisible(false)
  }
  function closeHandle() {//关闭处理
    showHandle()
    setvisible(false)
  }

  const labelShow = () => {//显示label
    if (multiple) {
      const val = Array.isArray(value) ? value : [value]
      const list = dataSources.filter(item => val.includes(item[fieldNames.value]))
      return list.map(item => item[fieldNames.label]).join(",")
    } else {
      const findObj = dataSources.find((item) => item[fieldNames.value] === value);
      if (!findObj) return '';
      return findObj?.[fieldNames.label];
    }
  }

  const choseListComponent = () => {//选中列表模板
    return choseList.map(item => {
      return <div key={item[fieldNames.value]} className={styles.item}>
        <Popover
          content={item[fieldNames.label]}
          trigger='click'
          placement='top'
        >
          <span className={`${styles.label} ellipsis`}>{item[fieldNames.label]}</span>
        </Popover>
        {disabled ? null : <CloseOutline onClick={() => delItemHandle(item[fieldNames.value])} />}
      </div>
    })
  }

  /** 根据参见单位id获取组织机构id */
  const queryBuildIdByOrganizationId = (buildId: string) => {
    getBuildIdByOrganizationId(buildId).then((res) => {
      if (res?.code === '1' && res?.data !== '0') {
        setorganizationId(res?.data);
      } else {
        setorganizationId('');
      }
    });
  };


  useEffect(() => {
    // 查所有人员或按组织id查人员
    getlistAllPage(organizationId)
  }, [organizationId])

  // 如果有参建单位，则查组织id
  useEffect(() => {
    if (defaultReformUnit) {
      queryBuildIdByOrganizationId(defaultReformUnit)
    }
  }, [defaultReformUnit])

  useEffect(() => {
    showHandle()
  }, [value])

  return (
    <div>
      {!disabled ? (
        <div className={`${multiple ? styles.text : null}`} onClick={openHandle}>
          {choseData.length > 0 && multiple === false ?
            <>
              {
                labelShow() ?
                  <span className={styles.value}>{labelShow()}</span> :
                  <span className={styles.placeholder}>{placeholder}<RightOutline /></span>
              }
            </> :
            <span className={styles.placeholder}>{placeholder}<RightOutline /></span>
          }
        </div>
      ) : null}
      {multiple ? <div className={styles["label-list-container"]}>
        {choseListComponent()}
      </div> : null}
      <Popup
        visible={visible}
        onMaskClick={() => {
          showHandle()
          setvisible(false)
        }}
        onClose={() => {
          showHandle()
          setvisible(false)
        }}
        bodyStyle={{ height: '70vh' }}
      >
        <div className={`${styles['popup-container']}`}>
          <div className={styles["title-container"]}>
            <LeftOutline onClick={closeHandle} className={styles.close} />
            <span className={styles.title}>{title}</span>
          </div>
          <OrganizationSelect organizationId={organizationId} setorganizationId={setorganizationId} />
          {isSearch && dataSources.length > 0 ? <div className={styles.search}>
            <SearchBar
              placeholder='输入名称搜索'
              value={searchText}
              onChange={v => {
                setSearchText(v)
              }}
            />
          </div> : null}
          <div className={styles["check-list-container"]}>
            {filterList.length > 0 ? <>
              <CheckList multiple={multiple} onChange={checkListChange} value={choseData}>
                {
                  filterList.map(item => {
                    return <CheckList.Item key={item[fieldNames.value]} value={item[fieldNames.value]}>
                      {titleRender ? titleRender(item) : item[fieldNames.label]}
                    </CheckList.Item>
                  })
                }
              </CheckList>
            </> :
              <ErrorBlock
                image='https://gw.alipayobjects.com/zos/antfincdn/ZHrcdLPrvN/empty.svg'
                style={{
                  '--image-height': '150px',
                }}
                status='empty'
              >
              </ErrorBlock>
            }
          </div>
          {filterList.length > 0 ? <div className={styles.btn}>
            <Button onClick={sureHandle} className="solid" block color='primary' fill='solid'>
              确定
            </Button>
          </div> : null}
        </div>
      </Popup>
    </div>
  )
}
