import React, { useEffect, useState } from 'react'

import styles from './index.less'
import PopupList from '../popup-list/popup-list.component'
import { queryOrganizationTree } from './index.service'

export default function OrganizationSelect({ organizationId, setorganizationId }) {

  const [organizationTree, setOrganizationTree] = useState<string[]>([]) //组织机构树

  /** 查询组织机构树 */
  const getOrganizationTree = () => {
    queryOrganizationTree({ id: 0, flag: 1 }).then((res) => {
      if (res?.code === '1') {
        setOrganizationTree(res?.data || [])
      }
    })
  }

  const selectedOrganization = (value) => {
    setorganizationId(value ? value : 'all')
  }
  useEffect(() => {
    getOrganizationTree();
  }, [])

  return (
    <div className={styles.organization}>
      组织：
      <div  className={styles['select-box']}>
        <PopupList
          placeholder="选择组织"
          isSearch
          isTree
          fieldNames={{ value: 'id', label: 'name' }}
          dataSources={organizationTree}
          title="选择组织机构"
          value={organizationId !== 'all' ? organizationId : ''}
          onChange={selectedOrganization}
        ></PopupList>
      </div>
    </div>
  )
}
