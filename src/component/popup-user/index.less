.text {
  position: absolute;
  right: 30px;
  top: 30px;
}

.placeholder {
  font-size: 46px;
  color: #CCD1DE;
}

.label-list-container {
  display: flex;
  width: 100%;
  gap: 35px;
  flex-wrap: wrap;

  .item {
    border-radius: 40px;
    color: #333333;
    font-size: 42px;
    background: #F5F5F5;
    padding: 10px 30px 5px 30px;
    display: flex;
    align-items: center;
  }

  .label {
    display: inline-block;
    max-width: 300px;
    margin-right: 32px;
  }
}

.popup-container {
  display: flex;
  flex-direction: column;
  height: 100%;

  .title-container {
    position: relative;
    padding: 20px;

    .close {
      position: absolute;
      color: #383838;
      font-size: 50px;
      left: 30px;
      top: 30px;
    }


    .title {
      text-align: center;
      color: #383838;
      font-size: 48px;
      width: 100%;
      display: inline-block;
    }
  }

  .organization {
    border-top: 1px solid #ece7e7;
    border-bottom: 1px solid #ece7e7;
    position: relative;
    padding: 20px 48px;
    display: flex;
    align-items: center;
    font-size: 44px;

    .select-box {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      padding: 12px;

      &:hover {
        border-color: #40a9ff;
      }
    }
  }

  .search {
    padding: 12px;
  }

  .check-list-container {
    flex: 1;
    flex-basis: 0;
    overflow-y: auto;
  }

  .btn {
    padding: 20px;
    background: #fff;
  }

}