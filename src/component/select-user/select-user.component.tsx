import { RightOutline } from 'antd-mobile-icons';
import { useEffect, useState } from 'react';
import { CloseOutline } from 'antd-mobile-icons';
import styles from './select-user.component.less';
import { Button, Checkbox, IndexBar, List, Popup, Toast } from 'antd-mobile';
import { pinyin } from 'pinyin-pro';
import { CheckboxValue } from 'antd-mobile/es/components/checkbox';
import useTreeByConfigure from '@/hooks/useTreeByOrg';
import { userList } from '@/services/quality-manage.service';
import BlockTree from '../block-tree/block-tree.component';
interface UserProps {
  name: string;
  id: number;
}
interface Props {
  value?: UserProps[],
  onChange?: (value: UserProps[]) => void;
  radioBox?: boolean
}

const SelectUser: React.FC<Props> = ({ value, onChange, radioBox }) => {
  const [visible, setVisible] = useState(false);
  const [visibleUser, setVisibleUser] = useState(false);
  const [confirmUserList, setConfirmUserList] = useState<any[]>([]);
  const [selectdOrg, setSelectedOrg] = useState<string>('');
  const [selectdUserIds, setSelectedUserIds] = useState<CheckboxValue[]>([]);
  const [userGroups, setUserGroups] = useState({});

  const { loading, treeData, getTreeData } = useTreeByConfigure()

  useEffect(() => {
    if (value?.length) {
      setConfirmUserList(value)
    }
  }, [value])

  const getUserList = (organizationId: string) => {
    userList({ current: 1, pageSize: 999, organizationId }).then((res) => {
      if (res.code === '1') {
        if (res?.data?.list?.length) {
          setVisibleUser(true);
          const groups = {};
          res?.data?.list?.forEach((item) => {
            const code = pinyin(item.username.slice(0, 1), { pattern: 'first', toneType: 'none' }).toLocaleUpperCase();
            if (groups[code]) {
              groups[code].push(item)
            } else {
              groups[code] = [item]
            }
          })
          setUserGroups(groups)
        } else {
          Toast.show({
            content: '暂无人员列表'
          })
        }
      } else {
        Toast.show({
          icon: 'fail',
          content: res?.message ?? '获取人员列表失败'
        })
      }
    })
  }

  // 点击选择组织机构
  const handleSelect = () => {
    setVisible(true)
  }

  // 获取人员列表
  const handleConfirm = () => {
    if (selectdOrg) {
      setSelectedUserIds(value?.map((item) => item.id)); // 如果已选择，默认回填
      getUserList(selectdOrg)
    } else {
      Toast.show({
        content: '请选择部门'
      })
    }
  }

  // 确认选择用户
  const handleConfirmUser = () => {
    if (selectdUserIds?.length) {
      const ids = value ? Array.from(new Set([...value, ...selectdUserIds])) : selectdUserIds
      const confirmData = (Object.values(userGroups)?.reduce((a: any[], b: any[]) => a.concat(b)) as any[])?.filter((item: any) => ids.includes(item.id))
      setVisible(false);
      setVisibleUser(false);
      setConfirmUserList(confirmData)
      onChange?.(confirmData.map((item) => ({ id: item.id, name: item.name })));
    } else {
      Toast.show({
        content: '请选择用户'
      })
    }
  }

  // 取消组织机构选择
  const handleCancel = () => {
    setVisible(false)
  }

  // 取消用户选择
  const handleCancelUser = () => {
    setVisibleUser(false);
    setSelectedUserIds([]);
  }

  // 删除已选用户
  const handleDeleteUser = (id: string) => {
    const filter = confirmUserList?.filter((item) => item.id !== id);
    setConfirmUserList(filter);
    onChange?.(filter.map((item) => ({ id: item.id, name: item.name })))
  }

  useEffect(() => {
    getTreeData()
  }, [])

  return (
    <div className={styles.selectItem}>
      <div className={styles.selectTip} onClick={handleSelect}>
        <div>{confirmUserList.length ? <span>已选 {confirmUserList.length}</span> : '选择处理人员'}</div>
        <RightOutline />
      </div>
      {confirmUserList?.length ? (
        <div className={styles.selectList}>
          {confirmUserList?.map((item) => (
            <div key={item.id}>{item.name}<CloseOutline color='var(--adm-color-text-secondary)' onClick={() => handleDeleteUser(item.id)} /></div>
          ))}
        </div>
      ) : ''}
      <Popup
        visible={visible}
        getContainer={null}
        onMaskClick={handleCancel}
        bodyStyle={{ borderRadius: '8px 8px 0 0' }}
      >
        <div className={styles.popupTitle}><span />人员结构切换<CloseOutline onClick={handleCancel} /></div>
        <BlockTree
          treeData={treeData}
          onSelect={(id) => setSelectedOrg(id)}
        />
        <div className={styles.btnGroups}>
          <Button color='primary' fill='outline' onClick={handleCancel}>取消</Button>
          <Button color='primary' onClick={handleConfirm}>获取人员</Button>
        </div>
      </Popup>
      <Popup
        visible={visibleUser}
        getContainer={null}
        onMaskClick={handleCancelUser}
      >
        <div className={styles.popupTitle}><span>已选 （{selectdUserIds?.length ?? 0}）</span></div>
        <div style={{ height: window.innerHeight - 100 }}>
          <IndexBar>
            {Object.keys(userGroups)?.map(group => {
              return (
                <IndexBar.Panel
                  index={group}
                  title={group}
                  key={group}
                >
                  <Checkbox.Group
                    value={selectdUserIds}
                    onChange={(value: CheckboxValue[]) => {
                      console.log('value', value);

                      radioBox ? setSelectedUserIds([value[value.length - 1]]) : setSelectedUserIds(value)
                      console.log('SelectedUserIds', [value[value.length - 1]]);

                    }}
                  >
                    <List>
                      {userGroups[group].map((item, index) => (
                        <List.Item key={index}>
                          <Checkbox key={item.id} value={item.id}>
                            {item.username}
                            （{item.name}）
                            <span className={styles.roles}>{item?.roleList?.map((item) => item.name)?.join('、')}</span>
                          </Checkbox>
                        </List.Item>
                      ))}
                    </List>
                  </Checkbox.Group>
                </IndexBar.Panel>
              )
            })}
          </IndexBar>
        </div>
        <div className={styles.btnGroups}>
          <Button color='primary' fill='outline' onClick={handleCancelUser}>取消</Button>
          <Button color='primary' onClick={handleConfirmUser}>确定选择</Button>
        </div>
      </Popup>
    </div>
  )
};
export default SelectUser;
