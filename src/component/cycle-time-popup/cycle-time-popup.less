.placeholder {
    font-size: 46px;
    color: #CCD1DE;
}

.text {
    position: absolute;
    right: 30px;
    top: 30px;
}

.label-list-container {
    display: flex;
    width: 100%;
    gap: 35px;
    flex-wrap: wrap;

    .item {
        border-radius: 40px;
        color: #333333;
        font-size: 42px;
        background: #F5F5F5;
        padding: 10px 30px 5px 30px;
        display: flex;
        align-items: center;
    }

    .label {
        display: inline-block;
        max-width: 300px;
        margin-right: 32px;
    }
}

.popup-container {
    display: flex;
    flex-direction: column;
    height: 100%;

    .title-container {
        position: relative;
        border-bottom: 1px solid #E5E5E5;
        padding: 20px;

        .close {
            position: absolute;
            color: #383838;
            font-size: 50px;
            right: 30px;
            top: 30px;
        }

        .title {
            text-align: center;
            color: #383838;
            font-size: 48px;
            width: 100%;
            display: inline-block;
        }
    }

    .check-list-container {
        padding: 60px 50px;

        .week-day-button {
            width: 220px;
            height: 100px;
            background: #F5F5F5;
            border-radius: 20px;
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 40px;
            color: #333333;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .day-button {
            width: 170px;
            height: 100px;
            background: #F5F5F5;
            border-radius: 20px;
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 40px;
            color: #333333;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .selected {
            background: #D7E1FA;

            &::after {
                content: " ";
                display: block;
                position: absolute;
                bottom: -36px;
                right: 36px;
                width: 40px;
                height: 96px;
                background-color: #3184e2;
                transform: rotate(45deg);
                transform-origin: bottom right;
            }
        }

        .checkIcon {
            color: #FFFFFF;
            position: absolute;
            bottom: 6px;
            right: 4px;
            z-index: 1000;
            font-size: 26px;
            font-weight: bold;
        }
    }

    .btns {
        border-top: 1px solid #E5E5E5;
        padding: 20px 40px;
        display: flex;
        gap: 40px;
    }
}