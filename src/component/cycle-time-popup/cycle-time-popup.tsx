import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>iew, Grid, Popup } from 'antd-mobile'
import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { RightOutline, CloseOutline, CheckOutline } from 'antd-mobile-icons'
import dayjs from 'dayjs'

import styles from './cycle-time-popup.less'
import classNames from 'classnames'

const DAYS_WEEK = [2, 3, 4, 5, 6, 7, 1];
const DAYS_FILTER: { [key: string]: string } = {
    '2': '星期一',
    '3': '星期二',
    '4': '星期三',
    '5': '星期四',
    '6': '星期五',
    '7': '星期六',
    '1': '星期日',
};

const SELECT_TYPE: { [key: string]: string } = {
    'week': '周检查',
    'month': '月检查',
    'date': '选择时间',
};

// 生成月份的日期数组
const daysArray = Array.from({ length: 31 }, (_, i) => i + 1);

interface Props {
    value?: any;
    onChange?: (value: any) => void;
    placeholder?: string;
    title?: string;
    checkType: 'week' | 'month' | 'date';
    multiple?: boolean;
}

export default function CycleTimePopup({
    value,
    onChange,
    placeholder = "请选择",
    title,
    checkType,
    multiple = true,
}: Props) {
    const [visible, setVisible] = useState<boolean>(false)
    const [popupValue, setPopupValue] = useState<any>();

    function openHandle() {//打开弹框处理
        setVisible(true)
    }

    function closeHandle() {//关闭处理
        setVisible(false)
    }

    /** 日期选择 */
    function onDateChange(value: any) {
        setPopupValue?.(dayjs(value).format('YYYY-MM-DD'))
    }

    /** 周、月选择 */
    const handleWeekMonthClick = (day: number) => {
        if (popupValue?.includes(day)) {
            setPopupValue?.(popupValue?.filter((item) => item !== day) || []);
        } else {
            if (multiple) {
                setPopupValue?.([...(popupValue || []), day]);
            } else {
                setPopupValue([day]);
            }
        }
    };

    const delItemHandle = (id) => {
        onChange?.(value?.filter((item) => item !== id) || []);
    }

    const onConfirm = () => {
        if (checkType === 'date') {
            onChange?.(popupValue);
        } else {
            onChange?.(popupValue);
        }
        closeHandle();
    }

    useEffect(() => {
        if (visible) {
            setPopupValue(value);
        } else {
            setPopupValue(undefined);
        }
    }, [value, visible])

    const labelRenderer = useCallback((type: string, data: number) => {
        switch (type) {
            case 'year':
                return data + '年'
            case 'month':
                return data + '月'
            case 'day':
                return data + '日'
            case 'hour':
                return data + '时'
            case 'minute':
                return data + '分'
            case 'second':
                return data + '秒'
            default:
                return data
        }
    }, [])

    const showValue = useMemo(() => {
        if (!value) {
            if (checkType === 'date') {
                return ''
            } else {
                return []
            }
        };

        if (checkType === 'week') {
            return value?.map((item) => {
                return {
                    label: DAYS_FILTER[item],
                    value: item
                }
            })
        } else if (checkType === 'month') {
            return value?.map((item) => {
                return {
                    label: `${item}日`,
                    value: item
                }
            })
        } else if (checkType === 'date') {
            return dayjs(value).format('YYYY-MM-DD')
        }
    }, [value, checkType])

    return (
        <>
            <div className={`${styles.text}`} onClick={openHandle}>
                {
                    (checkType === 'date' && value?.length) || (checkType !== 'date' && !multiple && value?.length) ? (
                        <span className={styles.value}>
                            {checkType === 'date' ? showValue : showValue?.[0].label}
                        </span>
                    ) : (
                        <span className={styles.placeholder}>{placeholder}<RightOutline /></span>
                    )
                }
            </div>
            {multiple && checkType !== 'date' && <div className={styles["label-list-container"]}>
                {
                    showValue?.map((item) => {
                        return (
                            <div key={item.value} className={styles.item}>
                                <span className={styles.label}>{item.label}</span>
                                <CloseOutline onClick={() => delItemHandle(item.value)} />
                            </div>
                        )
                    })
                }
            </div>}
            <Popup
                visible={visible}
                onMaskClick={closeHandle}
                onClose={closeHandle}
                bodyStyle={{
                    borderTopLeftRadius: '8px',
                    borderTopRightRadius: '8px',
                }}
            >
                <div className={`${styles['popup-container']}`}>
                    <div className={styles["title-container"]}>
                        <span className={styles.title}>{title} - {SELECT_TYPE[checkType]}</span>
                        <CloseOutline onClick={closeHandle} className={styles.close} />
                    </div>
                    <div className={styles["check-list-container"]}>
                        {
                            checkType === 'week' && (
                                <Grid columns={4} gap={8}>
                                    {
                                        DAYS_WEEK.map((day) => (
                                            <Grid.Item key={day}>
                                                <div
                                                    className={classNames(styles['week-day-button'], { [styles['selected']]: popupValue?.includes(day) })}
                                                    onClick={() => handleWeekMonthClick(day)}
                                                >
                                                    {DAYS_FILTER[day.toString()]}
                                                    {
                                                        popupValue?.includes(day) && <CheckOutline className={styles.checkIcon} />
                                                    }
                                                </div>
                                            </Grid.Item>
                                        ))
                                    }
                                </Grid>
                            )
                        }

                        {
                            checkType === 'month' && (
                                <Grid columns={5} gap={8}>
                                    {
                                        daysArray.map((day) => (
                                            <Grid.Item key={day}>
                                                <div
                                                    className={classNames(styles['day-button'], { [styles['selected']]: popupValue?.includes(day) })}
                                                    onClick={() => handleWeekMonthClick(day)}
                                                >
                                                    {day}
                                                    {
                                                        popupValue?.includes(day) && <CheckOutline className={styles.checkIcon} />
                                                    }
                                                </div>
                                            </Grid.Item>
                                        ))
                                    }
                                </Grid>
                            )
                        }

                        {
                            checkType === 'date' && (
                                <DatePickerView
                                    onChange={onDateChange}
                                    renderLabel={labelRenderer}
                                    value={dayjs(popupValue).toDate()}
                                />
                            )
                        }
                    </div>
                    <div className={styles.btns}>
                        <Button color='primary' fill='outline' block onClick={closeHandle} >
                            取消
                        </Button>
                        <Button color='primary' fill='solid' block onClick={onConfirm}>
                            确认
                        </Button>
                    </div>
                </div>
            </Popup>
        </>
    )
}
