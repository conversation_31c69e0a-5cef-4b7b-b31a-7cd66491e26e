# 组件策略工厂

一个灵活、可扩展的组件策略工厂系统，用于动态渲染各种表单组件和业务组件。

## 主要功能

- 统一组件注册管理
- 支持组件默认属性配置
- 动态渲染组件
- 支持表单项包装
- 支持自定义属性传递
- 安全的组件导入机制

## 使用方法

### 1. 注册组件

可以使用预定义的注册函数注册常用组件：

```typescript
import { strategyFactory } from '@/component/strategy-factory';
import { registerAllStrategies } from '@/component/strategy-factory/predefined-strategies';

// 注册所有预定义组件
registerAllStrategies();

// 或者注册特定分类的组件
import { 
  registerBasicComponents, 
  registerDateComponents 
} from '@/component/strategy-factory/predefined-strategies';

registerBasicComponents();
registerDateComponents();
```

### 2. 注册自定义组件

```typescript
import { strategyFactory } from '@/component/strategy-factory';
import MyCustomComponent from '@/component/my-custom-component';

// 注册单个组件
strategyFactory.registerStrategy('myComponent', {
  component: MyCustomComponent,
  defaultProps: {
    // 默认属性配置
    theme: 'light',
    size: 'medium'
  }
});

// 批量注册组件
strategyFactory.registerStrategies({
  componentA: {
    component: ComponentA,
    defaultProps: { /* ... */ }
  },
  componentB: {
    component: ComponentB,
    defaultProps: { /* ... */ }
  }
});
```

### 3. 渲染单个组件

```tsx
import { ComponentRenderer } from '@/component/strategy-factory';

// 渲染单个组件
const MyComponent = () => {
  const config = {
    type: 'input',  // 策略类型
    props: {
      placeholder: '请输入内容',
      onChange: (val) => console.log(val)
    }
  };

  return <ComponentRenderer config={config} />;
};
```

### 4. 渲染表单字段

```tsx
import { FormFieldRenderer } from '@/component/strategy-factory';

// 渲染表单字段
const MyForm = () => {
  const fields = [
    {
      type: 'input',
      name: 'username',
      label: '用户名',
      rules: [{ required: true, message: '请输入用户名' }],
      props: {
        placeholder: '请输入用户名'
      }
    },
    {
      type: 'radioGroup',
      name: 'gender',
      label: '性别',
      props: {
        options: [
          { label: '男', value: 'male' },
          { label: '女', value: 'female' }
        ]
      }
    }
  ];

  return <FormFieldRenderer fields={fields} />;
};
```

## 支持的表单项配置

每个表单项可以配置以下属性：

- `type`: 组件类型（必须，对应已注册的策略类型）
- `name`: 表单字段名称（可选）
- `label`: 表单项标签（可选）
- `rules`: 校验规则（可选）
- `formItemProps`: Form.Item的属性（可选，false表示不包装Form.Item）
- `props`: 传递给实际组件的属性（可选）
- `children`: 子元素内容（可选）

## 预定义组件类型

系统已预定义以下组件类型：

### 基础输入组件
- `input`: 输入框
- `radioGroup`: 单选组
- `textarea`: 多行文本
- `radio`: 单选按钮

### 日期选择组件
- `datePicker`: 日期选择器
- `datePickerRange`: 日期范围选择器

### 选择器组件
- `signature`: 签名选择器
- `orgPicker`: 部门选择器
- `userPicker`: 用户选择器
- `groupPicker`: 班组选择器

### 业务特定组件
- `finalExplanation`: 终结说明
- `executionStatus`: 执行情况

## 自定义扩展

系统支持随时扩展新的组件类型，只需调用`strategyFactory.registerStrategy`方法注册即可。 