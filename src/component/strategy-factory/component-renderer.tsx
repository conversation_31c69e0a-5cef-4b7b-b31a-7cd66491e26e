/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-21 14:10:07
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-05-21 14:13:08
 * @FilePath: /mobile-yw/src/component/strategy-factory/component-renderer.tsx
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */
import React from 'react';
import { Form } from 'antd-mobile';
import strategyFactory, { ComponentStrategyConfig } from './strategy-factory';

export interface ComponentRendererProps {
  config: ComponentStrategyConfig;
  formInstance?: any;
}

/**
 * 组件渲染器
 * 根据配置渲染对应的组件
 */
const ComponentRenderer: React.FC<ComponentRendererProps> = ({ config, formInstance }) => {
  const { type, props = {}, formItemProps, children, ...restConfig } = config;

  try {
    const strategy = strategyFactory.getStrategy(type);
    const { component: StrategyComponent, defaultProps = {} } = strategy;

    // 合并默认属性和传入的属性
    const mergedProps = {
      ...defaultProps,
      ...props,
    };

    // 如果formItemProps为false，则不包装Form.Item
    if (formItemProps === false) {
      return React.createElement(StrategyComponent, mergedProps, children);
    }

    // 处理Form.Item包装
    if (formItemProps && typeof formItemProps === 'object') {
      return (
        <Form.Item {...formItemProps}>
          {React.createElement(StrategyComponent, mergedProps, children)}
        </Form.Item>
      );
    }

    // 没有Form.Item配置时直接渲染组件
    return React.createElement(StrategyComponent, mergedProps, children);
  } catch (error) {
    console.error(`渲染组件错误: ${error}`);
    return <div style={{ color: 'red' }}>组件类型 "{type}" 渲染错误</div>;
  }
};

export default ComponentRenderer; 