import { Input, Radio, TextArea, DatePicker } from 'antd-mobile';
import { strategyFactory, ComponentStrategy } from './index';

// 尝试导入可能存在的组件
// 注意：这只是一个示例，实际使用时应该根据项目结构调整导入路径
let RadioGroup: any;
let SignatureUserPicker: any;
let OrgPicker: any;
let UserPicker: any;
let TimeRangePicker: any;
let ExecutionStatus: any;
let GroupPicker: any;
let FinalExplanation: any;
let FlowPopup: any;

// 动态尝试导入组件
try {
  // 这些导入将在运行时尝试，如果模块不存在则会跳过
  const optionalImports = {
    // 可以根据实际项目结构调整这些路径
    get RadioGroup() { 
      try { return require('@/component/radio-group').default; } catch (e) { return null; } 
    },
    get SignatureUserPicker() { 
      try { return require('@/component/signature-user-picker').default; } catch (e) { return null; } 
    },
    get OrgPicker() { 
      try { return require('@/component/org-picker').default; } catch (e) { return null; } 
    },
    get UserPicker() { 
      try { return require('@/component/user-picker').default; } catch (e) { return null; } 
    },
    get TimeRangePicker() { 
      try { return require('@/component/time-range-picker').default; } catch (e) { return null; } 
    },
    get ExecutionStatus() { 
      try { return require('@/pages/twotickets/workmnt/electrical/components/execution-status').default; } catch (e) { return null; } 
    },
    get GroupPicker() { 
      try { return require('@/pages/twotickets/workmnt/electrical/components/group-picker').default; } catch (e) { return null; } 
    },
    get FinalExplanation() { 
      try { return require('@/pages/twotickets/workmnt/electrical/components/final-explanation').default; } catch (e) { return null; } 
    },
    get FlowPopup() { 
      try { return require('@/pages/twotickets/workmnt/components/flow-popue/flow-popue.component').default; } catch (e) { return null; } 
    },
  };

  RadioGroup = optionalImports.RadioGroup;
  SignatureUserPicker = optionalImports.SignatureUserPicker;
  OrgPicker = optionalImports.OrgPicker;
  UserPicker = optionalImports.UserPicker;
  TimeRangePicker = optionalImports.TimeRangePicker;
  ExecutionStatus = optionalImports.ExecutionStatus;
  GroupPicker = optionalImports.GroupPicker;
  FinalExplanation = optionalImports.FinalExplanation;
  FlowPopup = optionalImports.FlowPopup;
} catch (error) {
  console.error('部分组件导入失败，这可能会影响某些功能', error);
}

/**
 * 安全注册组件策略
 * 只有当组件存在时才注册
 */
const safeRegisterStrategy = (type: string, strategy: ComponentStrategy) => {
  if (!strategy.component) {
    console.warn(`组件 ${type} 不存在，跳过注册`);
    return;
  }
  strategyFactory.registerStrategy(type, strategy);
};

/**
 * 注册基础输入组件
 */
export const registerBasicComponents = () => {
  // 输入框
  strategyFactory.registerStrategy('input', {
    component: Input,
  });

  // 单选选择器组
  if (RadioGroup) {
    strategyFactory.registerStrategy('radioGroup', {
      component: RadioGroup,
      defaultProps: {
        formItemProps: false,
        direction: 'horizontal',
        options: [
          { label: '合格', value: '1' },
          { label: '不合格', value: '2' },
        ]
      }
    });
  }

  // 多行文本
  strategyFactory.registerStrategy('textarea', {
    component: TextArea,
  });

  // 单选
  strategyFactory.registerStrategy('radio', {
    component: Radio,
  });
  
  // 弹窗组件
  safeRegisterStrategy('flowPopup', {
    component: FlowPopup,
  });
};

/**
 * 注册日期选择相关组件
 */
export const registerDateComponents = () => {
  // 单选选择器时间
  strategyFactory.registerStrategy('datePicker', {
    component: DatePicker,
  });

  // 区间选择器时间
  safeRegisterStrategy('datePickerRange', {
    component: TimeRangePicker,
    defaultProps: {
      initialMode: 'second',
      isHideHeader: true,
    },
  });
};

/**
 * 注册选择器组件
 */
export const registerPickerComponents = () => {
  // 签名选择器
  safeRegisterStrategy('signature', {
    component: SignatureUserPicker,
  });

  // 部门选择器
  safeRegisterStrategy('orgPicker', {
    component: OrgPicker,
    defaultProps: {
      wrapFormItem: false,
      showUsers: false,
      multiple: false,
    }
  });

  // 用户选择器
  safeRegisterStrategy('userPicker', {
    component: UserPicker,
    defaultProps: {
      wrapFormItem: false,
      showUsers: true,
      deduplicateUsers: true,
    }
  });

  // 班组选择器
  safeRegisterStrategy('groupPicker', {
    component: GroupPicker,
    defaultProps: {
      multiple: true,
      wrapFormItem: false,
    }
  });
};

/**
 * 注册业务特定组件
 */
export const registerBusinessComponents = () => {
  // 终结说明
  safeRegisterStrategy('finalExplanation', {
    component: FinalExplanation,
  });

  // 执行情况
  safeRegisterStrategy('executionStatus', {
    component: ExecutionStatus,
  });
};

/**
 * 注册所有预定义组件
 */
export const registerAllStrategies = () => {
  registerBasicComponents();
  registerDateComponents();
  registerPickerComponents();
  registerBusinessComponents();
};

export default registerAllStrategies; 