import React from 'react';
import { Form } from 'antd-mobile';
import ComponentRenderer from './component-renderer';
import { ComponentStrategyConfig } from './strategy-factory';

export interface FormFieldConfig extends ComponentStrategyConfig {
  name?: string;
  label?: string;
  rules?: any[];
}

export interface FormFieldRendererProps {
  fields: FormFieldConfig[];
  form?: any;
}

/**
 * 表单字段渲染器
 * 用于渲染一组表单字段
 */
const FormFieldRenderer: React.FC<FormFieldRendererProps> = ({ fields, form }) => {
  return (
    <>
      {fields.map((field, index) => {
        const { name, label, rules, formItemProps, ...restConfig } = field;
        
        // 准备Form.Item属性
        const itemProps = {
          ...(formItemProps || {}),
          name,
          label,
          rules,
        };

        // 如果formItemProps明确为false，则不包装Form.Item
        const config = {
          ...restConfig,
          formItemProps: formItemProps === false ? false : itemProps,
        };

        return (
          <div key={`field-${index}-${name || index}`}>
            <ComponentRenderer config={config} formInstance={form} />
          </div>
        );
      })}
    </>
  );
};

export default FormFieldRenderer; 