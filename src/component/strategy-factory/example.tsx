import React, { useEffect } from 'react';
import { 
  strategyF<PERSON>y, 
  ComponentRenderer, 
  FormFieldRenderer 
} from './index';
import { registerAllStrategies } from './predefined-strategies';

/**
 * 组件策略工厂使用示例
 */
const StrategyFactoryExample: React.FC = () => {
  // 初始化时注册所有预定义组件
  useEffect(() => {
    registerAllStrategies();
  }, []);

  // 单一组件渲染示例
  const renderSingleComponent = () => {
    const config = {
      type: 'input',
      props: {
        placeholder: '请输入内容',
      }
    };

    return (
      <div style={{ marginBottom: '20px' }}>
        <h3>单一组件渲染示例</h3>
        <ComponentRenderer config={config} />
      </div>
    );
  };

  // 表单字段渲染示例
  const renderFormFields = () => {
    const fields = [
      {
        type: 'input',
        name: 'name',
        label: '姓名',
        rules: [{ required: true, message: '请输入姓名' }],
        props: {
          placeholder: '请输入姓名',
        },
      },
      {
        type: 'radioGroup',
        name: 'gender',
        label: '性别',
        props: {
          options: [
            { label: '男', value: 'male' },
            { label: '女', value: 'female' },
          ],
        },
      },
      {
        type: 'textarea',
        name: 'remark',
        label: '备注',
        props: {
          rows: 3,
          placeholder: '请输入备注',
        },
      },
      {
        type: 'datePicker',
        name: 'date',
        label: '日期',
      }
    ];

    return (
      <div style={{ marginBottom: '20px' }}>
        <h3>表单字段渲染示例</h3>
        <div style={{ border: '1px solid #eee', padding: '15px' }}>
          <FormFieldRenderer fields={fields} />
          <div style={{ margin: '20px 0' }}>
            <button 
              style={{ 
                width: '100%', 
                background: '#1677ff', 
                color: 'white',
                padding: '10px',
                border: 'none',
                borderRadius: '4px'
              }}
              onClick={() => {
                console.log('提交表单');
              }}
            >
              提交
            </button>
          </div>
        </div>
      </div>
    );
  };

  // 业务特定组件渲染示例
  const renderBusinessComponents = () => {
    const fields = [
      {
        type: 'executionStatus',
        name: 'executionStatus',
        label: '执行状态',
      },
      {
        type: 'finalExplanation',
        name: 'explanation',
        label: '终结说明',
      }
    ];

    return (
      <div style={{ marginBottom: '20px' }}>
        <h3>业务组件渲染示例</h3>
        <FormFieldRenderer fields={fields} />
      </div>
    );
  };

  return (
    <div style={{ padding: '16px' }}>
      <h2>组件策略工厂系统示例</h2>
      {renderSingleComponent()}
      {renderFormFields()}
      {renderBusinessComponents()}
    </div>
  );
};

export default StrategyFactoryExample; 