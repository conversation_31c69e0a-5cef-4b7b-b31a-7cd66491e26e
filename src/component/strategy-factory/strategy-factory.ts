import { ReactNode } from 'react';

/**
 * 组件策略接口
 */
export interface ComponentStrategy {
  component: React.ComponentType<any>;
  defaultProps?: Record<string, any>;
}

/**
 * 组件策略配置项接口
 */
export interface ComponentStrategyConfig {
  type: string;
  props?: Record<string, any>;
  formItemProps?: Record<string, any> | boolean;
  children?: ReactNode;
  [key: string]: any;
}

/**
 * 组件策略工厂类
 * 用于注册和获取各种组件策略
 */
class StrategyFactory {
  private strategies: Map<string, ComponentStrategy> = new Map();

  /**
   * 注册组件策略
   * @param type 策略类型
   * @param strategy 组件策略对象
   */
  registerStrategy(type: string, strategy: ComponentStrategy): void {
    this.strategies.set(type, strategy);
  }

  /**
   * 批量注册组件策略
   * @param strategies 策略对象集合
   */
  registerStrategies(strategies: Record<string, ComponentStrategy>): void {
    Object.entries(strategies).forEach(([type, strategy]) => {
      this.registerStrategy(type, strategy);
    });
  }

  /**
   * 获取组件策略
   * @param type 策略类型
   * @returns 对应的组件策略
   * @throws 当找不到对应策略时抛出错误
   */
  getStrategy(type: string): ComponentStrategy {
    const strategy = this.strategies.get(type);
    if (!strategy) {
      throw new Error(`未找到类型为 "${type}" 的组件策略`);
    }
    return strategy;
  }

  /**
   * 检查是否存在某个策略
   * @param type 策略类型
   * @returns 是否存在
   */
  hasStrategy(type: string): boolean {
    return this.strategies.has(type);
  }

  /**
   * 获取所有已注册的策略类型
   * @returns 策略类型数组
   */
  getStrategyTypes(): string[] {
    return Array.from(this.strategies.keys());
  }

  /**
   * 获取所有已注册的策略
   * @returns 策略对象集合
   */
  getAllStrategies(): Record<string, ComponentStrategy> {
    const result: Record<string, ComponentStrategy> = {};
    this.strategies.forEach((strategy, type) => {
      result[type] = strategy;
    });
    return result;
  }

  /**
   * 清除所有策略
   */
  clearStrategies(): void {
    this.strategies.clear();
  }
}

// 导出单例
export const strategyFactory = new StrategyFactory();

export default strategyFactory; 