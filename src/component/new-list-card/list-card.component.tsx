import { styled, history } from 'umi';
import { Card, Space, Button } from 'antd-mobile';
import { CardProps } from 'antd';
import { RightOutline, AddOutline, DownOutline, UpOutline } from 'antd-mobile-icons';
import { useState, useMemo } from 'react';

// 定义 Props 类型
interface ListCardProps<T extends object> extends Omit<CardProps, 'title'> {
  data?: T[]; // 数据列表，默认为可选
  isMore?: boolean; // 是否显示“更多”按钮
  maxLength?: number; // 最大显示数量
  moreLink?: string | (() => void); // “更多”链接
  children?: (data: T[]) => React.ReactNode; // 子组件或函数
  title: React.ReactNode | string; // 标题，必填
  subtitle?: React.ReactNode | string; // 副标题，可选
  rightButtonFn?: () => void; //右侧新增按钮
  isExpandAndMore?: boolean; //底部footer内容区是更多还是展开
  refresh?: any[]; //刷新的依赖项
}
const ListCardWrapper = styled(Card)<{ subtitle?: React.ReactNode | string }>`
  .adm-card-header {
    padding-bottom: ${(props: { subtitle?: React.ReactNode | string }) => (props.subtitle ? '11.1111vw' : '')};
    font-size: 4.2592vw;
    font-weight: 500;
    letter-spacing: 0vw;
    line-height: 6.1675vw;
    color: rgba(51, 51, 51, 1);
  }
  .adm-list-item-content-prefix {
    padding-top: 1.1111vw;
    padding-bottom: 1.1111vw;
  }
  .subTitle {
    position: absolute;
    left: 0;
    font-size: 3.3333vw;
    font-weight: 400;
    letter-spacing: 0vw;
    line-height: 4.8269vw;
    color: rgba(153, 153, 153, 1);
  }

  .footer {
    padding-top: 1.0185vw;
    border-top: 0.0926vw solid #e5e5e5;
    display: flex;
    justify-content: center;
    font-size: 3.8889vw;
    font-weight: 400;
    letter-spacing: 0vw;
    line-height: 5.6314vw;
    color: rgba(17, 84, 237, 1);
  }

  .operatorTitle {
    display: flex;
    align-items: center;
  }

  .rightBtn {
    background-color: rgba(215, 225, 250, 1);
    border-radius: 3.3333vw;
    color: rgba(17, 84, 237, 1);
  }
`;

/**
 * 通用列表卡片
 * @param props
 * @param props.title 标题
 * @param props.data 数据
 * @param props.isMore 是否显示更多
 * @param props.subtitle 副标题
 * @param props.maxLength 卡片列表显示最大长度
 * @param props.moreLink 更多链接(不传则不显示标题右侧更多箭头，如果是函数则执行函数)
 * @param props.children 子组件
 * @param props.rightButtonFn 右侧按钮事件(不传则不显示新增按钮)
 * @param props.isExpandAndMore 底部内容是展开还是更多(true是展开和收起,false更多)
 * @param props.refresh 刷新的依赖项
 * @returns
 */
export const ListCard = <T extends object>(props: ListCardProps<T>) => {
  const {
    rightButtonFn,
    isExpandAndMore = false,
    data = [], // 默认值为空数组，避免 undefined 导致错误
    isMore = false,
    maxLength = 3,
    moreLink = '',
    children,
    title,
    subtitle,
    refresh = [],
    ...rest // 捕获剩余的 CardProps 属性
  } = props;

  const [isExpand, setIsExpand] = useState(false); //是否展开

  const listChildren = useMemo(() => {
    return isExpand ? children(data) : children(data?.length > maxLength ? data?.slice(0, maxLength) : data);
  }, [data, maxLength, isExpand, isExpandAndMore, ...refresh]);

  // 校验 maxLength 是否合法
  if (maxLength <= 0) {
    console.warn('maxLength should be greater than 0, defaulting to 3');
    return null; // 或者根据需求返回其他内容
  }
  // 提取重复使用的 subtitle
  const hasSubtitle = Boolean(subtitle);

  return (
    <ListCardWrapper
      title={
        <>
          {title}
          {hasSubtitle && <div className="subTitle">{subtitle}</div>}
        </>
      }
      subtitle={subtitle}
      extra={
        <>
          <Space className="operatorTitle">
            {rightButtonFn ? (
              <Button
                size="small"
                className="rightBtn"
                onClick={() => {
                  rightButtonFn?.();
                }}
              >
                <AddOutline />
                新增
              </Button>
            ) : null}
            {moreLink && (
              <RightOutline
                fontSize={'3.3333vw'}
                onClick={() => {
                  if (typeof moreLink === 'string') {
                    history.push(moreLink);
                  } else if (typeof moreLink === 'function') {
                    moreLink();
                  } else {
                    console.log('moreLink', moreLink);
                  }
                }}
              />
            )}
          </Space>
        </>
      }
      {...rest}
    >
      {data.length ? (
        <>
          {/* 如果大于 maxLength 个，则截取 maxLength 个作为参数传递给子组件 */}
          {listChildren}
          {/* 如果超出 maxLength 个，并且 isMore 为 true，则显示更多 */}
          {isMore && Array.isArray(data) && data.length > maxLength && !isExpandAndMore && (
            <div
              className="footer"
              onClick={() => {
                if (moreLink && typeof moreLink === 'string') {
                  history.push(moreLink);
                } else if (typeof moreLink === 'function') {
                  moreLink();
                } else {
                  console.error('Invalid or missing moreLink:', moreLink);
                }
              }}
            >
              <Space>更多（{data.length}）</Space>
            </div>
          )}
          {isExpandAndMore && Array.isArray(data) && data.length > maxLength && (
            <div
              className="footer"
              onClick={() => {
                setIsExpand(!isExpand);
              }}
            >
              <Space>
                {!isExpand ? (
                  <>
                    <DownOutline />
                    展开
                  </>
                ) : (
                  <>
                    <UpOutline />
                    收起
                  </>
                )}
                （{data.length}）
              </Space>
            </div>
          )}
        </>
      ) : null}
    </ListCardWrapper>
  );
};
