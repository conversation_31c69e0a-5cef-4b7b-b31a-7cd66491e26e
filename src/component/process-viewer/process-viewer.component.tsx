import { Popover } from 'antd';
import NavigatedViewer from 'bpmn-js/lib/NavigatedViewer';
import type Canvas from 'diagram-js/lib/core/Canvas';
import type ElementRegistry from 'diagram-js/lib/core/ElementRegistry';
import type EventBus from 'diagram-js/lib/core/EventBus';
import type { FC, ReactNode } from 'react';
import { memo, useEffect, useRef, useState } from 'react';

import {
  getProcessDefinitionXml,
  getProcessDefinitionXmlByModelId,
  getProcessInstance,
  getProcessInstanceHighPoint,
} from './process-viewer.service';
import { ProcessViewerWrapper } from './process-viewer.style';
import type { IToolTip } from './process-viewer.types';
import { blobToString } from './utils/file-download';
import { SafeZoom } from './mobile-zoom-scroll.extend';

interface IProps {
  children?: ReactNode;
  modelId?: string;
  processInstanceId?: string;
  toolTips: IToolTip[];
}

const ProcessViewer: FC<IProps> = (props) => {
  const { modelId, processInstanceId, toolTips } = props;
  const bpmnRef = useRef<BpmnViewer | null>(null);

  const bpmnNavigatedViewerRef = useRef<NavigatedViewer | null>(null);
  const bpmnEventBusRef = useRef<EventBus | null>(null);
  const bpmnCanvasRef = useRef<Canvas | null>(null);
  const bpmnElementRegistryRef = useRef<ElementRegistry | null>(null);

  const isHovering = useRef<boolean>(false);
  const [hoveringEvent, setHoveringEvent] = useState<any | null>(null);
  
  useEffect(() => {
    if (bpmnRef.current) {
      // 实例化建模器
      initializeModeler();
      // 初始化内部模块
      initializeModules();
    }
  }, [bpmnRef.current]);

  useEffect(() => {
    // 加载流程图
    loadDiagram();
  }, [modelId, processInstanceId]);

  function initializeModeler() {
    bpmnNavigatedViewerRef.current = new NavigatedViewer({
      container: bpmnRef.current as HTMLDivElement,
      additionalModules: [
        {
          __init__: ['zoomScroll'],
          zoomScroll: ['type', SafeZoom],
        },
      ],
    });
  }

  function initializeModules() {
    // 注册事件总线
    bpmnEventBusRef.current = bpmnNavigatedViewerRef.current!.get<EventBus>('eventBus');
    // 注册Canvas
    bpmnCanvasRef.current = bpmnNavigatedViewerRef.current!.get<Canvas>('canvas');
    // 注册图形注册表
    bpmnElementRegistryRef.current = bpmnNavigatedViewerRef.current!.get<ElementRegistry>('elementRegistry');
    const canvasContainer = bpmnNavigatedViewerRef.current!.get<Canvas>('canvas').getContainer();
    // 在画布容器上添加touch事件处理
    let startX,
      startY,
      isDragging = false;

    canvasContainer.addEventListener('touchstart', (e) => {
      if (e.touches.length === 1) {
        const touch = e.touches[0];
        startX = touch.clientX;
        startY = touch.clientY;
        isDragging = true;
        e.preventDefault();
      }
    });

    canvasContainer.addEventListener('touchmove', (e) => {
      if (isDragging && e.touches.length === 1) {
        const touch = e.touches[0];
        const deltaX = touch.clientX - startX;
        const deltaY = touch.clientY - startY;

        // 调用diagram-js的scroll方法
        bpmnCanvasRef.current.scroll({
          dx: deltaX,
          dy: deltaY,
        });

        startX = touch.clientX;
        startY = touch.clientY;
        e.preventDefault();
      }
    });

    canvasContainer.addEventListener('touchend', () => {
      isDragging = false;
    });
  }

  async function loadDiagram() {
    let fileBlob: Blob | null = null,
      highPointRes: any = null;
    // 流程已启动
    if (processInstanceId) {
      const res = await getProcessInstance(processInstanceId);
      fileBlob = await getProcessDefinitionXml(res.processDefinitionId);
      highPointRes = await getProcessInstanceHighPoint(processInstanceId);
    }
    // 流程未启动
    else if (modelId) {
      // 更改为外层传入modelId;
      fileBlob = await getProcessDefinitionXmlByModelId(modelId);
    } else {
      throw new Error('未传入必要的props：getProcessInstance 或 processDefinitionId，二者至少传其一！');
    }

    blobToString(fileBlob!).then((xmlStr) => {
      bpmnNavigatedViewerRef.current!.importXML(xmlStr).then(() => {
        // 交互事件总线处理
        registerEventBus();
        // 已完成路径着色
        if (highPointRes) fillInColorForExecutedPath(highPointRes.data);
      });
    });
  }

  function registerEventBus() {
    let hoverTimeout: any = null,
      lastElementId: string | null = null;
    bpmnEventBusRef.current!.on<any>('element.click', (event) => {
      if (event?.element?.type === 'bpmn:UserTask') {
        if (hoverTimeout) clearTimeout(hoverTimeout);

        if (isHovering.current) {
          if (event.element.id !== lastElementId) {
            setHoveringEvent(event);
            lastElementId = event.element.id;
          }
        } else {
          isHovering.current = true;
          setHoveringEvent(event);
          lastElementId = event.element.id;
        }
      } else {
        hoverTimeout = setTimeout(() => {
          isHovering.current = false;
          setHoveringEvent(null);
        }, 200);
      }
    });
  }

  function fillInColorForExecutedPath(highPointData: any) {
    // 流程已完成
    if (highPointData.code === '0') return;

    const { waitingToDo, highPoint, highLine } = highPointData;
    // 已完成路径元素ID列表
    const completedPathIds = [...highPoint, ...highLine];
    // 添加HighLight
    completedPathIds.forEach((id: string) => {
      const element = bpmnElementRegistryRef.current!.get(id);
      if (element) {
        const gfx = bpmnElementRegistryRef.current!.getGraphics(element);
        const shape = gfx.querySelector('g.djs-visual > rect, g.djs-visual > path');
        // 未完成时，着色当前节点
        const colorStr = id === waitingToDo[0] ? '#02aafa' : 'red';
        if (shape) (shape as HTMLElement).style.setProperty('stroke', colorStr);
      }
    });
  }

  const popOverContent = (
    <div>
      <p>id: {hoveringEvent?.element.businessObject.id || '-'}</p>
      <p>处理人： {toolTips.find((item) => item.userTaskId === hoveringEvent?.element.businessObject.id)?.approver || '-'}</p>
      <p>处理意见： {toolTips.find((item) => item.userTaskId === hoveringEvent?.element.businessObject.id)?.comment || '-'}</p>

      {/* <p>处理人： {hoveringEvent?.element.businessObject.$attrs['flowable:assignee'] || '-'}</p>
      <ul>
        描述：
        {hoveringEvent?.element.businessObject.documentation?.map((item: { text: string }, index: number) => (
          // eslint-disable-next-line react/no-array-index-key
          <li key={index}>{item.text}</li>
        )) || '-'}
      </ul> */}
    </div>
  );

  return (
    <ProcessViewerWrapper
      $postionInfo={{
        x: 15,
        y: 15,
      }}
    >
      <Popover
        content={popOverContent}
        title={hoveringEvent?.element.businessObject.name}
        open={isHovering.current}
        getPopupContainer={(triggerNode) => triggerNode.parentNode as HTMLElement}
      />
      <div id="bpmn-navigated-viewer" ref={bpmnRef} />
    </ProcessViewerWrapper>
  );
};

export default memo(ProcessViewer);
