import { styled } from 'umi';

interface IProps {
  $postionInfo: {
    x: number;
    y: number;
  };
}

export const ProcessViewerWrapper = styled.div<IProps>`
  touch-action: manipulation;
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  #bpmn-navigated-viewer {
    flex: 1;
    height: 100%;
    overflow: hidden !important;
    background-color: #fff;

    /** 隐藏Logo */
    a.bjs-powered-by {
      display: none;
    }

    /** 网格背景 */
    .djs-container {
      background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHBhdHRlcm4gaWQ9ImEiIHdpZHRoPSI0MCIgaGVpZ2h0PSI0MCIgcGF0dGVyblVuaXRzPSJ1c2VyU3BhY2VPblVzZSI+PHBhdGggZD0iTTAgMTBoNDBNMTAgMHY0ME0wIDIwaDQwTTIwIDB2NDBNMCAzMGg0ME0zMCAwdjQwIiBmaWxsPSJub25lIiBzdHJva2U9IiNlMGUwZTAiIG9wYWNpdHk9Ii4yIi8+PHBhdGggZD0iTTQwIDBIMHY0MCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjZTBlMGUwIi8+PC9wYXR0ZXJuPjwvZGVmcz48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSJ1cmwoI2EpIi8+PC9zdmc+');
    }
  }

  .ant-popover {
    top: 0 !important;

    & > .ant-popover-content {
      position: absolute !important;
      /* transform: translate(-50%, -110%) !important; */

      ${(props) => {
        if (!props.$postionInfo.x) return `display: none; !important`;

        return `
          left: ${props.$postionInfo.x}px !important;
          top: ${props.$postionInfo.y}px !important;
          `;
      }}

      .ant-popover-arrow {
        span {
          background-color: #ccc !important;
        }
      }

      .ant-popover-inner {
        word-break: break-all;
        background-color: #fff !important;

        .ant-popover-title {
          font-weight: bold !important;
          font-size: 16px !important;
        }

        .ant-popover-inner-content {
          ul {
            padding: 0 !important;
          }

          li {
            margin-left: 1.5em !important;
            list-style: disc !important;
          }
        }
      }
    }
  }
`;
