import request from '@/utils/request';

/**
 *  获取流程实例
 * @export
 * @param {number} processInstanceId
 * @returns {Promise<any>}
 */
export async function getProcessInstance(processInstanceId: string) {
  return request.get(`/flowable/process-instances/${processInstanceId}`);
}

/**
 *  获取流程定义xml
 * @export
 * @param {number} processDefinitionId
 * @returns {Promise<any>}
 */
export async function getProcessDefinitionXml(processDefinitionId: string) {
  return request.get(`/flowable/processdefinitions/${processDefinitionId}/xml`);
}
/**
 *  根据modelID获取流程定义xml
 * @export
 * @param {number} processDefinitionId
 * @returns {Promise<any>}
 */
export async function getProcessDefinitionXmlByModelId(modelId: string) {
  return request(`/flowable/models/${modelId}/xml`, {
    responseType: 'blob'
  });
}
/**
 *  流程实例高亮节点
 * @export
 * @param {number} processInstanceId
 * @returns {Promise<any>}
 */
export async function getProcessInstanceHighPoint(processInstanceId: string) {
  return request.get(`/flowable/process-instances/${processInstanceId}/highPoint`);
}

/**
 *  获取流程模型xml
 * @export
 * @param {string} processDefinitionId
 * @returns {Promise<any>}
 */
export async function getProcessXml(processInstanceId: string) {
  return request.get(`/flowable/models/${processInstanceId}/xml`);
}
