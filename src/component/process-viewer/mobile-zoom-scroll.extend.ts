import ZoomScroll from 'diagram-js/lib/navigation/zoomscroll/ZoomScroll';

export class MobileZoomScroll extends ZoomScroll {
  constructor(config, eventBus, canvas) {
    super(config, eventBus, canvas);

    // 触摸起始距离
    this.lastTouchDistance = null;
  }

  _init() {
    super._init();
    console.log(this);
    // 绑定触摸事件
    this._canvas._container.addEventListener('touchstart', this._handleTouchStart.bind(this));
    this._canvas._container.addEventListener('touchmove', this._handleTouchMove.bind(this), { passive: false });
    this._canvas._container.addEventListener('touchend', this._handleTouchEnd.bind(this));
  }

  _handleTouchStart(event) {
    if (event.touches.length === 2) {
      // 记录初始双指距离
      this.lastTouchDistance = this._getTouchDistance(event);
      event.preventDefault();
    }
  }

  _handleTouchMove(event) {
    if (event.touches.length === 2) {
      const newDistance = this._getTouchDistance(event);
      const delta = newDistance - this.lastTouchDistance;

      // 计算缩放方向
      const direction = delta > 0 ? 1 : -1;

      // 调用父类缩放方法
      this._zoom(direction * 0.1, {
        x: (event.touches[0].clientX + event.touches[1].clientX) / 2,
        y: (event.touches[0].clientY + event.touches[1].clientY) / 2,
      });

      this.lastTouchDistance = newDistance;
      event.preventDefault();
    }
  }

  _handleTouchEnd() {
    this.lastTouchDistance = null;
  }

  _getTouchDistance(event) {
    const t1 = event.touches[0];
    const t2 = event.touches[1];
    return Math.hypot(t2.clientX - t1.clientX, t2.clientY - t1.clientY);
  }
}

export class EnhancedMobileZoomScroll extends ZoomScroll {
  constructor(config, eventBus, canvas) {
    super(config, eventBus, canvas);
    this._initTouchHandlers();
  }

  _initTouchHandlers() {
    console.log('EnhancedMobileZoomScroll', this);
    const container = this._canvas._container;
    const _that = this;
    console.log('_that', _that);
    // 双指缩放逻辑
    let lastScale = 1;
    let startPos = null;
    let rafId;
    container.addEventListener(
      'touchstart',
      (e) => {
        if (e.touches.length === 1) {
          startPos = {
            x: e.touches[0].clientX,
            y: e.touches[0].clientY,
            scroll: this._canvas.viewbox(),
          };
        }
        if (e.touches.length === 2) {
          lastScale = this._getPinchDistance(e);
        }
      },
      { passive: false },
    );

    container.addEventListener(
      'touchmove',
      (e) => {
        if (rafId) cancelAnimationFrame(rafId);
        rafId = requestAnimationFrame(() => {
          // 实际滚动逻辑

          if (startPos && e.touches.length === 1) {
            const dx = e.touches[0].clientX - startPos.x;
            const dy = e.touches[0].clientY - startPos.y;

            this._canvas.scroll({
              dx: dx,
              dy: dy,
            });

            startPos.x = e.touches[0].clientX;
            startPos.y = e.touches[0].clientY;
            e.preventDefault();
          }
          if (e.touches.length === 2) {
            const newScale = this._getPinchDistance(e);
            const delta = (newScale - lastScale) / 100; // 灵敏度调节

            // 计算中心点
            const center = this._getTouchCenter(e);

            // 执行缩放
            this.zoom(delta, center);

            lastScale = newScale;
            e.preventDefault();
          }
        });
      },
      { passive: false },
    );
    let lastTapTime = 0;
    let tapTimeout: any = null;
    container.addEventListener('touchend', (event) => {
      // 如果element为空，就查询当前节点得父级，一直循环，直到找到的第一个
      let target = event.target?.closest(".djs-element.djs-shape")
      const element = _that._canvas._elementRegistry!.get(target?.dataset?.elementId);
      if (element) {
        _that._canvas._eventBus!.fire('element.click', { element });
      }else{
        _that._canvas._eventBus!.fire('element.click', { element });
      }
    });
    // // 单指拖动
    // let startPos = null;
    // container.addEventListener('touchstart', e => {

    //   if (e.touches.length === 1) {
    //     startPos = {
    //       x: e.touches[0].clientX,
    //       y: e.touches[0].clientY,
    //       scroll: this._canvas.viewbox()
    //     };
    //   }
    // });

    // container.addEventListener('touchmove', e => {
    //   if (startPos && e.touches.length === 1) {
    //     const dx = e.touches[0].clientX - startPos.x;
    //     const dy = e.touches[0].clientY - startPos.y;

    //     this._canvas.scroll({
    //       dx: -dx,
    //       dy: -dy
    //     });

    //     startPos.x = e.touches[0].clientX;
    //     startPos.y = e.touches[0].clientY;
    //     e.preventDefault();
    //   }
    // }, { passive: false });
  }

  _getPinchDistance(e) {
    const [t1, t2] = e.touches;
    return Math.sqrt(Math.pow(t2.clientX - t1.clientX, 2) + Math.pow(t2.clientY - t1.clientY, 2));
  }

  _getTouchCenter(e) {
    const [t1, t2] = e.touches;
    return {
      x: (t1.clientX + t2.clientX) / 2,
      y: (t1.clientY + t2.clientY) / 2,
    };
  }

  zoom(delta, position) {
    const newScale = this._canvas._cachedViewbox.scale * (1 + delta);
    this._canvas._eventBus.fire('canvas.viewbox.changing', { scale: newScale });
    this._canvas.zoom(newScale, position);
  }
}

// 添加缩放限制（防止过大/过小）
const MIN_ZOOM = 0.2;
const MAX_ZOOM = 5.0;

export class SafeZoom extends EnhancedMobileZoomScroll {
  zoom(delta, position) {
    const newScale = Math.max(MIN_ZOOM, Math.min(MAX_ZOOM, this._canvas._cachedViewbox.scale * (1 + delta)));
    super.zoom(newScale / this._canvas._cachedViewbox.scale - 1, position);
  }
}
