import { RightOutline } from 'antd-mobile-icons';
import styles from './select-model.component.less';
import { <PERSON><PERSON>, CascaderView, Popup, Toast } from 'antd-mobile';
import { CloseOutline, DeleteOutline } from 'antd-mobile-icons';
import { useState } from 'react';
import { CascaderValue, CascaderValueExtend } from 'antd-mobile/es/components/cascader-view';

interface ItemProps {
  label: string;
  value: string;
  modelNodeId?: string;
}
interface Props {
  value?: ItemProps[],
  onChange?: (value: ItemProps[]) => void;
}
const defaultSelectedData = {
  value: [],
  extend: { isLeaf: false, items: [] }
}

const SelectModel: React.FC<Props> = ({ value, onChange }) => {
  const [visible, setVisible] = useState(false);
  const [confirmList, setConfirmList] = useState<ItemProps[]>(value?.length ? value : []);
  const [selectdData, setSelectedData] = useState<{
    value: CascaderValue[],
    extend: CascaderValueExtend
  }>(defaultSelectedData);

  const data = [{
    label: '防汛仓库',
    value: '1',
    modelNodeId: '1',
    children: [{
      label: '董铺防汛仓库',
      value: '1-1',
      modelNodeId: '1',
      children: [{
        label: '董铺防汛仓库-1楼',
        value: '1-1-1',
        modelNodeId: '1',
        children: [{
          label: '外墙',
          value: '1-1-1-1',
          modelNodeId: '1',
          children: [{
            label: '外墙1',
            value: '1-1-1-1-1',
            modelNodeId: '1',
          }]
        }]
      }]
    },
    {
      label: '大房郢防汛仓库',
      value: '1-2',
      modelNodeId: '2',
    }]
  }];

  const handleSelect = () => {
    setSelectedData(defaultSelectedData)
    setVisible(true)
  }

  const handleModelChange = (value: CascaderValue[], extend: CascaderValueExtend) => {
    setSelectedData({ value, extend })
  }

  const formatData = (list) => {
    return list?.map((item: any) => (
      {
        description: item.label,
        extendIds: item.value,
        modelNodeId: item.modelNodeId
      }
    ))
  }

  const handleConfirm = () => {
    const { isLeaf, items  } = selectdData.extend;
    if (isLeaf) {
      const confirmData = items[items.length - 1];
      const sameData = confirmList?.filter((item) => item.value === confirmData.value);
      if (sameData.length) {
        Toast.show({
          content: '已选择该模型部位'
        });
        return;
      }
      setVisible(false);
      const newConfirmList = [...confirmList, confirmData];
      setConfirmList(newConfirmList)
      onChange?.(formatData(newConfirmList));
    } else {
      Toast.show({
        content: '请选择模型部位'
      })
    }
  }

  const handleCancel = () => {
    setSelectedData(defaultSelectedData)
    setVisible(false)
  }

  const handleDelete = (value: string) => {
    const filter = confirmList?.filter((item) => item.value !== value);
    setConfirmList(filter);
    onChange?.(formatData(filter))
  }

  return (
    <div className={styles.selectItem}>
      <div className={styles.selectTip} onClick={handleSelect}>
        <div>{ confirmList.length ? <span>已选 {confirmList.length}</span> : '选择模型部位' }</div>
        <RightOutline />
      </div>
      {confirmList?.length ? (
        <div className={styles.selectList}>
          { confirmList?.map((item)=> (
            <div key={item.value}>{item.label}<DeleteOutline color='var(--adm-color-text)' onClick={() => handleDelete(item.value)} /></div>
          )) }
        </div>
      ) : ''}
      <Popup
        visible={visible}
        getContainer={null}
        onMaskClick={() => {
          setVisible(false)
        }}
        onClose={() => {
          setVisible(false)
        }}
        bodyStyle={{ borderRadius: '8px 8px 0 0' }}
      >
        <div className={styles.popupTitle}><span />选择模型部位<CloseOutline onClick={() => { setVisible(false) }} /></div>
        <CascaderView options={data} value={selectdData.value} onChange={handleModelChange} />
        <div className={styles.btnGroups}>
          <Button color='primary' fill='outline' onClick={handleCancel}>取消</Button>
          <Button color='primary' onClick={handleConfirm}>确定选择</Button>
        </div>
      </Popup>
    </div>
  )
};
export default SelectModel;
