import styles from './select-model.component.less';
import { DeleteOutline } from 'antd-mobile-icons';

interface ItemProps {
  description: string;
  extendIds: string;
  modelId?: string;
}
interface Props {
  value?: ItemProps[],
  onChange?: (value: ItemProps[]) => void;
}

const SelectModel: React.FC<Props> = ({ value, onChange }) => {

  const handleDelete = (extendIds: string) => {
    const filter = value?.filter((item) => item.extendIds !== extendIds);
    onChange?.(filter)
  }

  return (
    <div className={styles.selectItem}>
      <div className={styles.selectTip}>
        <div>{ value?.length ? <span>已关联 {value.length}</span> : '关联模型部位' }</div>
      </div>
      {value?.length ? (
        <div className={styles.selectList}>
          { value?.map((item)=> (
            <div key={item.extendIds}>{item.description}<DeleteOutline color='var(--adm-color-text)' onClick={() => handleDelete(item.extendIds)} /></div>
          )) }
        </div>
      ) : ''}
    </div>
  )
};
export default SelectModel;
