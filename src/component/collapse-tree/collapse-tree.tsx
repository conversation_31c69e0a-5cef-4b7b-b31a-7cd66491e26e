import { Collapse, ProgressBar } from "antd-mobile";
import { useEffect, useState } from "react";
import styles from "./index.less";
import { RightOutline, DownFill } from "antd-mobile-icons";
import useDict from "@/hooks/useDict";

interface treeData {
  id: string;
  name: string;
  children?: treeData[];
}

interface Iprops {
  // 树形结构数据
  treeData: treeData[];
  // 点击右侧图标事件
  onClickIcon: (record: treeData) => void;
}

const CollapseTree = (props: Iprops) => {
  const { treeData, onClickIcon } = props;
  const [treeList, setTreeList] = useState([]);
  const { dictOptData } = useDict("task_type");

  const renderCollapse = (list) => {
    return list?.map((item) => {
      if (item?.children?.length > 0) {
        return (
          <Collapse accordion key={item?.id}>
            <Collapse.Panel
              key={item?.id}
              title={
                <div className={styles.progress}>
                  <div className={styles.title}>
                    {item?.itemType && (
                      <span className={styles.typeItem}>
                        {dictOptData
                          ?.find((c) => c?.value === item?.itemType)
                          ?.label?.slice(1, 2) ?? "无"}
                      </span>
                    )}
                    {item?.name}
                  </div>
                  <ProgressBar
                    style={{
                      width: 50,
                    }}
                    percent={item?.progress ?? 0}
                  />
                  <div className={styles.proNum}>{item?.progress}%</div>
                  <RightOutline
                    className={styles?.outLine}
                    onClick={() => onClickIcon?.(item)}
                  />
                </div>
              }
              arrow={(active) =>
                active ? (
                  <DownFill style={{ color: "#999", fontSize: "16px" }} />
                ) : (
                  <DownFill
                    style={{
                      rotate: "270deg",
                      color: "#999",
                      fontSize: "16px",
                    }}
                  />
                )
              }
            >
              {renderCollapse(item?.children)}
            </Collapse.Panel>
          </Collapse>
        );
      } else {
        return (
          <Collapse accordion className={styles.lastPanel} key={item?.id}>
            <Collapse.Panel
              key={item?.id}
              title={
                <div className={styles.progress}>
                  <div className={styles.title}>{item?.name}</div>
                  <ProgressBar
                    style={{
                      width: 50,
                      "--fill-color":
                        "linear-gradient(to right, #BED9F2 0%, #1154ED 100%)",
                    }}
                    percent={item?.progress ?? 0}
                  />
                  <span className={styles.proNum}>{item?.progress}%</span>
                  <RightOutline
                    className={styles?.outLine}
                    onClick={() => onClickIcon?.(item)}
                  />
                </div>
              }
              arrow={<div style={{ width: "19px", height: "19px" }}></div>}
            ></Collapse.Panel>
          </Collapse>
        );
      }
    });
  };

  useEffect(() => {
    if (treeData?.length > 0) {
      setTreeList(treeData);
    }
  }, [treeData]);
  return <div className={styles?.pageWrap}>{renderCollapse(treeList)}</div>;
};

export default CollapseTree;
