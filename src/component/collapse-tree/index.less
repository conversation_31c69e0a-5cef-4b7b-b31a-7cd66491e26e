.lastPanel {
  // border: 1px solid red;
  :global {
    .adm-collapse-panel-content-inner {
      display: none;
    }

    .adm-collapse-panel-content {
      color: #333;
    }

    .adm-list-item-content-main {
      border-bottom: solid 1px var(--adm-color-border);
      padding-left: 22px;
      padding-bottom: 16px;
    }
  }
}

.progress {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  .title {
    width: 70%;
    margin-right: 16px;

    .typeItem {
      background: rgba(255, 255, 255, 0.2);
      border: 2px solid #1154ed;
      border-radius: 8px;
      padding: 2px 5px;
      font-size: 32px;
      font-family: PingFang SC;
      margin-right: 8px;
      font-weight: 500;
      color: #1154ed;
      line-height: 89px;
    }
  }

  .proNum {
    width: 67px;
    // height: 25px;
    font-size: 32px;
    font-family: PingFang SC;
    font-weight: 500;
    color: #666666;
    margin-left: 15px;
  }
  .outLine {
    width: 60px;
    font-size: 80px;
    color: #d9d9d9;
  }
}

.pageWrap {
  color: #333;
  :global {
    .adm-list-body {
      border-top: none;
    }

    .adm-collapse-panel-content {
      color: #333;
    }
    .adm-list-item-content {
      border-top: none;
      padding-right: 0;
      flex-direction: row-reverse;
    }

    .adm-list-item-content-main {
      border-bottom: solid 1px var(--adm-color-border);
      padding-left: 22px;
      padding-bottom: 16px;
    }
    .adm-collapse-panel-header {
      padding-left: 0;
    }
    .adm-list-body {
      border-bottom: none;
    }

    .adm-list-item-content-main:last-child {
      border-bottom: none;
      // border: 1px solid red;
      padding: 0;
    }
  }
}
