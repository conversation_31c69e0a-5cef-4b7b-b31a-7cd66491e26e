import { NavBar, NavBarProps } from 'antd-mobile';
import { rest } from 'lodash';
import { styled } from 'umi';

export interface TwoTicketsNavBarProps extends Omit<NavBarProps, 'onBack'> {
  title: string | React.ReactNode; // 标题
  subTitle?: string; // 副标题
  right?: React.ReactNode; // 右侧操作按钮
  bgcolor?: string; // 背景颜色
  color?: string; // 字体颜色
  onBack?: Function;
};

const NavBarWrapper = styled(NavBar) <{
  bgcolor: string;
}>`
  background-color: ${(props: any) => props.bgcolor};
  color: ${(props: any) => props.color};
  font-weight: 700;
  font-size: 5.3571vw;
  position: sticky;
  top: 0;
  z-index: 1;
  .adm-nav-bar-left {
    flex: none;
    .adm-nav-bar-back {
      margin-right: 0;
    }
  }
  .adm-nav-bar-title {
    text-align: left;
    font-size: 5vw;
    font-weight: 400;
    letter-spacing: .025vw;
    line-height: 6.5742vw;
    color: ${(props: any) => props.color};
    text-align: left;
    vertical-align: top;
    &-subtitle {
      font-size: 3.3333vw;
      font-weight: 400;
    }
  }
`;

/**
 * 两票导航栏
 * @param porps
 * @param porps.title 标题
 * @param porps.subTitle 副标题
 * @param porps.right 右侧操作按钮
 * @param porps.bgcolor 背景颜色
 * @param porps.color 字体颜色
 * @param porps.onBack 返回按钮点击事件
 * @returns
 */
const TwoTicketsNavBar = ({ onBack, title, subTitle = null, right = null, bgcolor = 'transparent', color = '#fff', ...rest }: TwoTicketsNavBarProps) => {
  return (
    <NavBarWrapper
      onBack={() => {
        if (onBack) {
          onBack?.()
          return
        }
        history.back();
      }}
      bgcolor={bgcolor}
      right={right}
      style={{
        color: color,
      }}
      data-testid="twotickets-navbar"
      {...rest}
    >
      {title}
      {subTitle && <div className="adm-nav-bar-title-subtitle">{subTitle}</div>}
    </NavBarWrapper>
  );
};

export default TwoTicketsNavBar;
