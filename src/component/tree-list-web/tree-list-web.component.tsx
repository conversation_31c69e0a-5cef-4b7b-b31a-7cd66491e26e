import React, { memo, useCallback, useState } from "react"
import type { ReactNode, FC } from "react"

import TreeWeb from "../tree-web/tree-web.component";
import styles from "./tree-list-web.style.less";
import type { ITreeNode } from "../tree-web/tree-web.types";

interface IProps {
  children?: ReactNode
  treeList: ITreeNode[]
  selectedTreeNode: ITreeNode
  onTreeNodeClick: (treeNode: ITreeNode) => void
}

const TreeListWeb: FC<IProps> = (props) => {
  const { treeList, selectedTreeNode, onTreeNodeClick } = props

  return (
    <div className={styles['container']}>
      {treeList.map((treeNode) => <TreeWeb key={treeNode.id} root={treeNode} selectedTreeNode={selectedTreeNode} onTreeNodeClick={onTreeNodeClick} />)}
    </div>
  )
}

export default memo(TreeListWeb)
