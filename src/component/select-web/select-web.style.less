.container {
  position: relative;
  width: 100%;

  .selector {
    box-sizing: border-box;
    padding: 1rem;
    height: 4rem;
    background: #fff;
    border-radius: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 1.3rem;
    font-weight: bold;
  }

  .options {
    padding: 1rem;
    box-sizing: border-box;
    position: absolute;
    border-radius: 1rem;
    top: 4rem;
    max-height: 30rem;
    width: 100%;
    background: #fff;
    box-shadow: 0 1rem 5rem 0 rgba(0, 0, 30, 0.15);
    overflow-y: auto;

    .option {
      box-sizing: border-box;
      height: 3rem;
      line-height: 1rem;
      font-weight: bold;
      font-size: 1.2rem;
      padding: 1rem;
      border-radius: 1rem;
    }

    .selected {
      background: #e7eefc;
    }
  }

  .ellipsis {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}
