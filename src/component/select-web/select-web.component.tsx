import React, { memo, useState } from "react"
import type { <PERSON>actNode, FC } from "react"
import classNames from "classnames";
import { DownOutline, UpOutline } from 'antd-mobile-icons'

import styles from "./select-web.style.less";
import type { IOption } from "./select-web.types";

interface IProps {
  children?: ReactNode
  options: IOption[]
  selectedOption: IOption | null
  onSelectChange: (value: string) => void;
}

const SelectWeb: FC<IProps> = (props) => {
  const { selectedOption, options, onSelectChange } = props

  const [isExpanding, setIsExpanding] = useState(false);

  const handleSelectorClick = () => {
    setIsExpanding(!isExpanding)
  }

  const handleOptionClick = (index: number) => {
    setIsExpanding(false)
    onSelectChange(options[index].value)
  }

  return (
    <div className={styles['container']}>
      <div className={styles['selector']} onClick={handleSelectorClick}>
        <span className={styles['ellipsis']}>{selectedOption?.label}</span>
        { isExpanding ? <UpOutline color="#3c69cd" /> : <DownOutline color="#989898" /> }
      </div>
      {isExpanding && (
        <div className={styles['options']}>
          {options.map((item, index) => (
            <div key={item.id} className={classNames([{[styles['selected']]: item.value === selectedOption.value}, styles['ellipsis'], styles['option']])} onClick={() => handleOptionClick(index)}>
              {item.label}
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

export default memo(SelectWeb)
