import styles from './body-box.component.less'
import {
    InfiniteScroll,
    List,
    DotLoading,
    ErrorBlock
} from 'antd-mobile'
import { FC } from 'react'
import { propOptions } from './body-box'
const BodyBoxComponent: FC<propOptions> = ({ err=false,loading, children }) => {
    return <div className={styles["body-box-component"]}>
        {/* <PullToRefresh
                onRefresh={reLoad}
                threshold={30}

            >

</PullToRefresh> */}
        {loading ? <div className={styles["loading-container"]}>
            <div className={styles.loadingWrapper}>
                <DotLoading />
            </div>
            正在加载数据
        </div> : err?<ErrorBlock status='default' />:children}
    </div>
}

export default BodyBoxComponent