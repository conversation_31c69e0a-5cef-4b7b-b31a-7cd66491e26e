import { useRef, useEffect, FC, useState } from 'react';
import styles from './columns-component.less';
import { Column, Line } from '@antv/g2plot';
import type { ColumnOptions } from '@antv/g2plot';
import _ from 'lodash';

interface Props {
  data: { name: string; xData: any; yData: any; unit: string }[]; //name是图例描述，中文字符串
  color: string[];
  scrollSize?: number | null;
  columnOptions?: ColumnOptions;
}

export const ColumnsComponent: FC<Props> = ({ data, color, scrollSize = null, columnOptions }) => {
  const column = useRef(null);
  const columnPlot = useRef(null);
  // const names = [...new Set(data?.map((it) => it.name))];
  const names = Array.from(new Set(data?.map((it) => it.name)));

  const [plot, setPlot] = useState<any>();
  const config: ColumnOptions = _.merge(
    {
      data,
      isGroup: true,
      seriesField: 'name',
      xField: 'xData',
      yField: 'yData',
      tooltip: {
        formatter: (item: any) => {
          const unit = data?.find((it) => it.name === item.name)?.unit || '';
          return { name: item.name, value: `${item.yData}${unit || ''}` };
        },
      },
      columnStyle: (item: any) => {
        for (let i = 0; i < names?.length; i++) {
          if (item.name === names?.[i]) {
            return {
              fill: color?.[i],
            };
          }
        }
      },
      columnWidthRatio: 0.4,
      marginRatio: 0.5,
      yAxis: {
        grid: {
          line: { style: { lineDash: [4, 2], stroke: 'rgba(123, 129, 134, 0.3)' } },
        },
        label: {
          style: {
            fill: '#666666', //颜色
          },
        },
      },
      xAxis: {
        // 坐标线
        line: null,
        // 主刻度
        tickLine: null,
        // 次刻度
        subTickLine: null,
        // 网格
        grid: null,

        // 字体样式
        label: {
          autoHide: true,
          autoRotate: false,
          style: {
            fill: '#666666', //颜色
          },
        },
      },
      // 滚动
      scrollbar: scrollSize
        ? {
            type: 'horizontal',
            categorySize: scrollSize,
          }
        : null,
      // 图例
      legend: {
        layout: 'horizontal',
        position: 'top-right',
        // 文字描述
        itemName: {
          style: { fill: '#666666' },
        },
        marker: (item: any) => {
          for (let i = 0; i < names?.length; i++) {
            if (item === names?.[i]) {
              return {
                style: {
                  fill: color?.[i],
                  stroke: color?.[i],
                  r: 4,
                },
              };
            }
          }
        },
      },
    },
    columnOptions,
  );

  useEffect(() => {
    if (column.current) {
      // 为了防止多次创建，在新建的时候进行清除
      if (plot) {
        plot.destroy();
      }
      const stackedColumnPlot = new Column(column.current, config);
      stackedColumnPlot.render();
      setPlot(stackedColumnPlot);
      columnPlot.current = stackedColumnPlot;
      // 添加点击事件
      stackedColumnPlot?.on('element:click', ColumnPlotClick);
      return () => {
        // 清除点击事件
        stackedColumnPlot?.off('element:click', ColumnPlotClick);
        stackedColumnPlot?.destroy();
      };
    }
  }, []);
  useEffect(() => {
    if (column.current && plot) {
      plot.changeData(data);
    }
  }, [data, plot]);

  return <div ref={column} className={styles.box}></div>;

  /**
   * 点击柱状图柱子
   * @param ev 点击事件
   * @returns
   */
  function ColumnPlotClick(ev) {
    const chart = columnPlot.current.chart;
    // 清除之间的所有注解，避免重复显示
    chart?.annotation?.().clear(true);

    // 通过对事件对象获取点击的元素数据
    const datum = ev.data.data;
    const value = datum.yData;
    // 尝试使用 ev.gEvent.target 获取图形实例
    const shape = ev.gEvent && ev.gEvent.target;
    if (!shape || typeof shape.getBBox !== 'function') {
      console.log('未能获取到图形实例或其 getBBox 方法');
      return;
    }

    // 获取图形在画布上的边界信息
    const bbox = shape.getBBox();
    // 计算柱子顶部中心点，在画布坐标下，并向上偏移 10 像素
    const canvasX = bbox.x + bbox.width / 2;
    const canvasY = bbox.y - 10;

    // 将画布坐标转换为数据坐标
    const dataPoint = chart.getCoordinate().invert({ x: canvasX, y: canvasY });

    // 添加文本注解作为弹窗，在顶部显示数量
    chart?.annotation().text({
      position: [dataPoint.x, dataPoint.y],
      content: value.toString(),
      style: {
        // 设置文本样式
        fill: '#000', // 文本颜色
        fontSize: 12,
        textAlign: 'center',
        // 设置背景为白色，并加上内边距
        background: {
          padding: [4, 6],
          fill: '#fff', // 背景颜色白色
          stroke: '#ccc', // 可选边框颜色
          lineWidth: 1,
        },
      },
    });
  }
};
