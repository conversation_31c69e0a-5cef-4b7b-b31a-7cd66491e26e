import { useRef, useEffect, FC } from 'react';
import styles from './line-component.less';
import { Line } from '@antv/g2plot';

interface Props {
  data: { xData: string; yData: number; label: string }[];
}
export const LineComponent: FC<Props> = ({ data }) => {
  const line = useRef(null);
  const config: any = {
    data,
    xField: 'xData',
    yField: 'yData',
    seriesField: 'label',
    xAxis: {
      tickLine: null, //轴刻度不显示
      line: null, //轴线不显示
      label: {
        style: {
          fill: '#D4EBFD', //颜色
        },
      },
    },
    yAxis: {
      grid: {
        line: { style: { lineDash: [4, 2], stroke: 'rgba(108, 161, 221,0.3)' } },
      },
      label: {
        style: {
          fill: '#D4EBFD', //颜色
        },
        // 数值格式化为千分位
        formatter: (v: any) => `${v}`.replace(/\d{1,3}(?=(\d{3})+$)/g, (s) => `${s},`),
      },
    },
    colorField: 'label', // 部分图表使用 seriesField
    color: ['#30D8C7', '#FFCF0D'],
    legend: {
      position: 'top-right',
      itemName: {
        style: (item: any, index: number) => {
          const shapeAttrs: any = {};
          if (index === 0) {
            shapeAttrs.fill = '#30D8C7';
          } else if (index === 1) {
            shapeAttrs.fill = '#FFCF0D';
          }
          return shapeAttrs;
        },
      },
    },
  };
  useEffect(() => {
    if (line.current) {
      const G2_line = new Line(line.current, config);
      G2_line.render();
    }
  }, []);

  return <div ref={line} className={styles.box}></div>;
};
