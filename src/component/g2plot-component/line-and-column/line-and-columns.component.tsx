import { useEffect, useRef, useState } from 'react';
import styles from './line-and-columns.component.less';
import { Datum, DualAxes } from '@antv/g2plot';

interface Props {
  columnData: { xData: string | number; yColumnData: number; name?: string; label: string }[]; //柱状数据，name和label对应，label用于图例描述，name用于代码区分
  lineData: { xData: string | number; yLineData: number; label: string }[]; //折线数据
  color: string[]; //数据图形颜色,最后一个是折现颜色
  setLineMax?: number; //设置折线最大值，不设置默认为最大值
}

// Example:
// const columnData = [
//   { xData: '2019-03', label: '质量验评个数', yColumnData: 350 },
//   { xData: '2019-03', label: '完成率', yColumnData: 123 },
//   { xData: '2019-04', label: '质量验评个数', yColumnData: 900 },
//   { xData: '2019-04', label: '完成率', yColumnData: 342 },
// ];
// const lineData = [
//   { xData: '2019-03', label: '质量验评优良率', yLineData: 800 },
//   { xData: '2019-04', label: '质量验评优良率', yLineData: 600 },
// ];

const LineAndColumns: React.FC<Props> = ({ columnData = [], lineData = [], color, setLineMax }) => {
  const dualAxesRef = useRef(null);
  const [plot, setPlot] = useState<any>();

  const uniqueNames = [
    ...columnData?.map((it) => {
      return { name: it?.label, label: it?.label };
    }),
    { name: 'yLineData', label: lineData?.[0]?.label },
  ].filter((obj, index, self) => {
    return self.findIndex((t) => t?.name === obj?.name) === index;
  });
  const maxValue = Math.ceil(Math.max(...columnData?.map(item => Number(item.yColumnData)))); // 向上取整
  // 获取折线最大值
  const lineMax = lineData?.reduce((acc, cur) => {
    return acc > cur?.yLineData ? acc : cur?.yLineData;
  }, 0);

  // 处理刻度的方法(说要整数刻度，只能处理一下咯)
  const customTickMethod = (info) => {
    let min = info?.min;
    let max = info?.max;
    let count = 5;

    min = Math.floor(min);
    max = Math.ceil(max);

    // 计算刻度之间的间隔
    const interval = Math.ceil((max - min) / count);

    // 生成刻度数组
    let ticks = [];
    for (let i = min; i <= max; i += interval) {
      ticks.push(i);
    }

    // 防止最后的刻度超出max_val
    if (ticks.length && ticks[ticks.length - 1] > max) {
      ticks[ticks.length - 1] = max;
    }
    return ticks;
  };

  const config: any = {
    data: [columnData, lineData],
    xField: 'xData',
    yField: ['yColumnData', 'yLineData'],
    geometryOptions: [
      // 柱状图配置
      {
        geometry: 'column',
        // 是否为分组柱状图
        isGroup: !!uniqueNames.length,
        seriesField: 'label',
        columnWidthRatio: 0.4,
        // 图形样式
        columnStyle: (item: any) => {
          for (let i = 0; i < uniqueNames.length; i++) {
            if (item.label === uniqueNames[i]?.label) {
              return {
                fill: color?.[i],
                stroke: color?.[i],
              };
            }
          }
        },
      },
      // 折线图样式
      {
        geometry: 'line',
        color: color[color.length - 1],
        // label: {
        //   formatter: (text, item) => {
        //     return text.yLineData;
        //   },
        // }
      },
    ],
    // 图例
    legend: {
      layout: 'horizontal',
      position: 'top-right',
      // 文字描述
      itemName: {
        style: { fill: '#666666' },
        formatter: (text: string) => {
          for (let i = 0; i < uniqueNames.length; i++) {
            if (text === uniqueNames[i]?.name) {
              return uniqueNames[i]?.label;
            }
          }
        },
      },
      // 图形标记
      marker: (text: any) => {
        for (let i = 0; i < uniqueNames.length; i++) {
          if (text === uniqueNames[i]?.name) {
            return {
              symbol: uniqueNames[i]?.name === 'yLineData' ? 'line' : 'square',
              style: {
                fill: color?.[i],
                stroke: color?.[i],
                r: 4,
              },
            };
          }
        }
      },
    },
    // 轴线
    xAxis: {
      // tickLine: null, //轴刻度不显示
      tickCount: 5, //期望的坐标轴刻度数量，非最终结果
      // line: null, //轴线不显示
      label: {
        style: {
          fill: '#666666', //颜色
        },
      },
    },
    yAxis: {
      yColumnData: {
        grid: {
          line: { style: { lineDash: [4, 2], stroke: 'rgba(123, 129, 134, 0.3)' } },
        },
        label: {
          style: {
            fill: '#666666', //颜色
          },
        },
        nice: false,
        min: 0, // 最小值肯定大于等于0
        max: maxValue,
        minTickInterval: 1,
        tickMethod: customTickMethod,
      },
      yLineData: {
        // grid: null,
        max: setLineMax || lineMax,
        label: {
          style: {
            fill: '#666666', //颜色
          },
        },
      },
    },
    tooltip: {
      formatter: (datum: Datum) => {
        for (let i = 0; i < uniqueNames.length; i++) {
          if (datum.label === uniqueNames[i]?.name) {
            return { name: uniqueNames?.[i]?.label, value: datum.yColumnData };
          } else if (!datum?.label) {
            return { name: uniqueNames?.[uniqueNames.length - 1]?.label, value: datum.yLineData };
          }
        }
      },
    },
  };

  useEffect(() => {
    if (dualAxesRef.current && columnData.length && lineData.length) {
      const dualAxes = new DualAxes(dualAxesRef.current, config);
      dualAxes.render();
      setPlot(dualAxes);
    }
  }, []);
  useEffect(() => {
    if (dualAxesRef.current && plot) {
      plot.changeData([columnData, lineData]);
    }
  }, [columnData, lineData, plot]);
  return <div ref={dualAxesRef} className={styles.lineAndColums}></div>;
};
export default LineAndColumns;
