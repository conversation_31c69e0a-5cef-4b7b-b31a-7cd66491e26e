import { Calendar, Popup, Toast } from 'antd-mobile'
import styles from './date-range.less'
import { useEffect, useState } from 'react'
import moment from 'moment'
interface Props {
  onRangeChange: (time: { startTime: string, endTime: string }) => void
  onReset: (resetFn: () => any) => void
  data: { startTime: string, endTime: string }
}

const DateRange: React.FC<Props> = ({ onRangeChange, data, onReset }) => {
  const [visible, setVisible] = useState(false)
  const [startTime, setStartTime] = useState('')
  const [endTime, setEndTime] = useState('')
  const [timeType, setTimeType] = useState(null)//时间类型，1、2
  const [curDate, setCurDate] = useState<Date>()//当前组件日期

  // 打开日期弹窗
  const openDateModal = (type) => {
    setVisible(true)
    setTimeType(type)
  }

  // 日期改变
  const onDateChange = (time) => {
    if (time) {
      const date = moment(time).format('YYYY-MM-DD')
      // 选择开始时间，且没有结束时间
      if (timeType === 1 && !endTime) {
        setStartTime(date)
        setVisible(false)
      }
      // 选择开始时间，且有结束时间
      else if (timeType === 1 && endTime) {
        const days = moment(date, 'YYYY-MM-DD').diff(moment(endTime, 'YYYY-MM-DD'), 'days')
        if (days > 0) {
          Toast.show({
            content: '开始时间不能大于结束时间',
          })
        } else {
          setStartTime(date)
          setVisible(false)
        }
      }
      // 选择结束时间，且没有开始时间
      if (timeType === 2 && !startTime) {
        setVisible(false)
        setEndTime(date)
      }
      // 选择结束时间，且有开始时间
      else if (timeType === 2 && startTime) {
        const days = moment(date, 'YYYY-MM-DD').diff(moment(startTime, 'YYYY-MM-DD'), 'days')
        if (days < 0) {
          Toast.show({
            content: '结束时间不能小于开始时间',
          })
        } else {
          setVisible(false)
          setEndTime(date)
        }
      }
    }
  }

  // 重置时间
  const resetTime = () => {
    setStartTime('')
    setEndTime('')
  }
  // 将重置时间的方法传递给父组件，供父组件需要时调用
  useEffect(() => {
    onReset(() => resetTime)
  }, [])

  // 根据时间类型设置组件当前日期
  useEffect(() => {
    if (timeType) {
      if (timeType === 1 && startTime) {
        const time = new Date(startTime)
        setCurDate(time)
      } else if (timeType === 1 && !startTime) {
        setCurDate(null)
      }
      if (timeType === 2 && endTime) {
        const time = new Date(endTime)
        setCurDate(time)
      } else if (timeType === 2 && !endTime) {
        setCurDate(null)
      }
    }
  }, [timeType, startTime, endTime])

  // 将时间传递给父组件
  useEffect(() => {
    onRangeChange({ startTime, endTime })
  }, [startTime, endTime])

  // 回显
  useEffect(() => {
    if (data) {
      setStartTime(data.startTime)
      setEndTime(data.endTime)
    }
  }, [])

  return <div className={`${styles["dateRange"]} containers`}>
    <div className={startTime ? [styles.dateBox, styles.activeDate].join(' ') : styles.dateBox} onClick={() => openDateModal(1)}>
      {startTime ?
        <span className="adm-input-element">{startTime}</span> :
        <span className={styles.placeholder}>开始时间</span>
      }
    </div>
    <span className={styles.tips}>至</span>
    <div className={endTime ? [styles.dateBox, styles.activeDate].join(' ') : styles.dateBox} onClick={() => openDateModal(2)}>
      {endTime ?
        <span className="adm-input-element">{endTime}</span> :
        <span className={styles.placeholder}>结束时间</span>
      }
    </div>
    <Popup
      visible={visible}
      onMaskClick={() => {
        setVisible(false)
      }}
      onClose={() => {
        setVisible(false)
      }}
      bodyStyle={{
        height: '60vh', borderTopLeftRadius: '8px',
        borderTopRightRadius: '8px', borderBottomLeftRadius: '0px',
        borderBottomRightRadius: '0px',
      }}
    >
      <Calendar
        value={curDate}
        selectionMode='single'
        onChange={onDateChange}
      />
    </Popup>
  </div>
}
export default DateRange;