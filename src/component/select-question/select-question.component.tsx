import { RightOutline } from "antd-mobile-icons";
import styles from "./select-question.component.less";
import { CheckList, Popup } from "antd-mobile";
import { CloseOutline } from "antd-mobile-icons";
import { useEffect, useState } from "react";
import useDict from "@/hooks/useDict";
import { CheckListValue } from "antd-mobile/es/components/check-list";

interface Props {
  value?: CheckListValue[];
  onChange?: (value: string) => void;
}

const SelecQuestion: React.FC<Props> = ({ value, onChange }) => {
  const [visible, setVisible] = useState(false);
  const [slectedOp, setSlectedOp] = useState("");
  const { dictOptData } = useDict("issue_type");

  const handleChange = (value: any) => {
    setVisible(false);
    setSlectedOp(
      dictOptData?.find((item) => item.value === value?.[0])?.label
    );
    onChange?.(value?.length ? value : '')
  };

  useEffect(() => {
    if (value && dictOptData?.length) {
      setSlectedOp(
        dictOptData?.find((item) => item.value === value?.[0])?.label ?? "-"
      );
    }
  }, [value]);

  return (
    <div
      className={styles.selectItem}
      onClick={() => {
        setVisible(true);
      }}
    >
      <span className={slectedOp?.length ? styles.value : styles.placeholder}>
        {slectedOp ?? '请选择'}
      </span>
      <RightOutline />
      <Popup
        visible={visible}
        getContainer={null}
        onMaskClick={() => {
          setVisible(false);
        }}
        onClose={() => {
          setVisible(false);
        }}
        bodyStyle={{ borderRadius: "8px 8px 0 0" }}
      >
        <div className={styles.popupTitle}>
          <span />
          选择问题类型
          <CloseOutline
            onClick={() => {
              setVisible(false);
            }}
          />
        </div>
        <div className={styles.checkList}>
          <CheckList
            value={value}
            onChange={handleChange}
          >
            {dictOptData?.map((item) => (
              <CheckList.Item key={item.value} value={item?.value}>{item?.label}</CheckList.Item>
            ))}
          </CheckList>
        </div>
      </Popup>
    </div>
  );
};

export default SelecQuestion;
