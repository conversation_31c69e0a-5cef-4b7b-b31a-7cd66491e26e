.selectItem {
  display: flex;
  justify-content: right;
  align-items: center;
  gap: 10px;
  margin-right: -55px;
  .value {
    color: var(--adm-color-text);
  }
  .placeholder {
    color: var(--adm-color-weak);
  }
  .popupTitle {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 128px;
    padding: 0 48px;
    font-size: 48px;
    color: var(--adm-color-text);
    border-bottom: 1px solid #e6e6e6;
    background-color: #fff;
  }
  .checkList {
    :global {
      .adm-list {
        padding: 0;
      }
      .adm-list-item {
        margin-top: 0;
        border-radius: 0;
        font-size: 42px;
        .adm-list-item-content{
          padding: 0 var(--padding-right) 0 0;
        }
      }
    }
  }
}
