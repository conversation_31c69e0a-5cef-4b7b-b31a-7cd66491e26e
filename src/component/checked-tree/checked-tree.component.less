.blockTree {
  background-color: #fff;
  overflow: hidden;

  .item {
    font-size: 42px;
    padding: 33px 36px 38px;
    border-bottom: 1px solid #E6E6E6;
    display: flex;
    align-items: flex-start;
    flex-wrap: nowrap;
    user-select: none;
    position: relative;
    z-index: 2;

    .indent {
      align-self: stretch;
      white-space: nowrap;
      user-select: none;
      font-size: 0;
      line-height: 0;

      .indentUnit {
        display: inline-block;
        width: 40px;
      }
    }

    .contentWrapper {
      position: relative;
      z-index: 1;
      flex: auto;
      display: flex;
      text-rendering: optimizeLegibility;
      -webkit-font-smoothing: antialiased;
      user-select: none;
      align-items: center;
      justify-content: space-between;

      .collapseArrow {
        margin-right: 1.85185vw;
        left: 1.85185vw;
      }

      .expend {
        transform: translateY(-50%) rotate(0deg);
      }

      .selected {
        position: absolute;
        right: 0;
      }

      .rightSelector {
        margin-left: auto;
      }

      :global {
        .adm-checkbox-icon {
          width: 40px !important;
          height: 40px !important;
          border-radius: 6px !important;
          border: 1px solid #1154ED;
        }
      }
    }

    &::after {
      position: absolute;
      top: 0;
      inset-inline-end: 0;
      bottom: 0;
      inset-inline-start: 0;
      left: 0;
      right: 0;
      transition: background-color 0.2s;
      content: "";
      pointer-events: none;
    }

    &:active:not(.disabled)::after {
      background: #eee;
    }

  }

  .warp {
    overflow: hidden;
    padding-left: 64px;

    &.collapse {
      height: 0px;
    }

    &.excollapse {
      height: 100%;
    }

    .warp {
      border-radius: 0;
    }
  }
}

.blockTree+.blockTree {
  margin-top: 20px;
}

:global {
  .checked-tree-container {
    .adm-spin-loading {
      --size: 52px !important;
    }

    .adm-checkbox {
      margin-left: 90px;
    }

    .adm-checkbox-icon {
      width: 50px !important;
      height: 50px !important;
      border-radius: unset !important;
    }
  }

}