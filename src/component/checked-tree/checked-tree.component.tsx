import React, { useState, useEffect, ReactNode } from "react";
import classnames from "classnames";
import styles from "./checked-tree.component.less";
import { RightOutline } from "antd-mobile-icons";
import { Checkbox, Radio } from 'antd-mobile';
import { deepForEachFn } from "@/utils/data-processing-tools";
export interface TreeNodeType {
  id: string;
  parentId?: string;
  name?: string;
  children?: TreeNodeType[] | null;
}
export interface fieldsOptions {
  label: string;
  value: string
}
interface BlockTreeProps {
  multiple?: boolean;//是否多选
  value: string[];//已选中ids
  checkStrictly?: boolean;//父子节点是否关联
  fields?: fieldsOptions;//展示字段配置
  treeData: any[];//展示数据
  loading?: boolean;//数据重置加载动画
  defaultExpandedKeys?: string[];//默认展开ids
  isExpendAll?: boolean;//是否展开全部
  height?: string | number | undefined;
  onChange?: (id: string[], list: any[], node: any) => void;//选中改变方法
  renderRight?: (node: any) => ReactNode;
  titleRender?: (node: any, index: number) => ReactNode;//标题模板
  loadData?: (node: any) => Promise<any>;//动态加载方法
  disabledFilter?: (node: any) => boolean;//是否禁用选择的方法
}
const fieldsDef: fieldsOptions = {
  label: "name",
  value: "id"
}

interface loadOptions {
  id: string;
  load: boolean;
}
/**
 * 树组件 树形结构的第一层的每一块占一个白色块儿
 * @param param0
 * @returns
 */
const CheckedTree: React.FC<BlockTreeProps> = ({
  multiple = true,
  value,
  loadData,
  isExpendAll = false,
  fields = fieldsDef,
  checkStrictly = true,
  treeData = [],
  defaultExpandedKeys = [],
  onChange = () => { },
  height = "",
  renderRight,
  titleRender,
  disabledFilter,
}) => {
  const [loadIds, setLoadIds] = useState<loadOptions[]>([]);//加载id列表
  const [expendIds, setExpendIds] = useState<any[]>([]);//已展开项id列表
  const [collapseKeys, setCollapseKeys] = useState<string[]>([]);
  const [selectedNodes, setSelectedNodes] = useState<any[]>([]);
  const loadIdList = loadIds.map(item => item.id)
  function exPendHandle(data: any) {
    return expendIds.includes(data[fields.value])
  }
  function iconShowHandle(data: any) {//图标展示控制
    // 只用RightOutline
    if (data.children && data.children.length > 0) {
      return true;
    }
    return false;
  }
  const renderTreeNode = (list: any[] = [], deepCount = "") => {
    let d_arr = deepCount.split("");

    return list.map((item: any, index: number) => {
      return (
        <div key={item[fields.value]}>
          <div
            className={classnames(styles.item, {
              [styles.select]: collapseKeys.includes(item[fields.value]),
            })}
            onClick={() => handleSelect(item)}
          >
            <div className={styles.indent}>
              {d_arr.map((_, index) => (
                <div
                  key={`${item[fields.value]}_indent_${index}`}
                  className={styles.indentUnit}
                />
              ))}
            </div>
            <div className={styles.contentWrapper}>
              {/* 展开/收起图标 */}
              {iconShowHandle(item) && (
                <span
                  id={`arrow_${item[fields.value]}`}
                  className={styles.collapseArrow}
                  onClick={() => handleCollapse(item)}
                >
                  <RightOutline style={{ transform: exPendHandle(item) ? 'rotate(90deg)' : 'none', transition: 'transform 0.2s' }} />
                </span>
              )}
              {/* 标题内容 */}
              <span className={styles.titleText}>
                {titleRender ? titleRender(item, index) : <span>{item[fields.label]}</span>}
              </span>
              {/* 右侧Radio/Checkbox */}
              <span className={styles.rightSelector}>
                {multiple ? (
                  // <Checkbox
                  //   onChange={val => selectCheckedHandle(val, item[fields.value], item)}
                  //   value={item[fields.value]}
                  //   checked={value.includes(item[fields.value])}
                  //   disabled={disabledFilter ? disabledFilter(item) : false}
                  // />

                  <Radio
                    onChange={val => selectCheckedHandle(val, item[fields.value], item)}
                    value={item[fields.value]}
                    checked={value.includes(item[fields.value])}
                    disabled={disabledFilter ? disabledFilter(item) : false}
                  />
                ) : (
                  <Radio
                    onChange={val => selectCheckedHandle(val, item[fields.value], item)}
                    value={item[fields.value]}
                    checked={value.includes(item[fields.value])}
                    disabled={disabledFilter ? disabledFilter(item) : false}
                  />
                )}
              </span>
            </div>
          </div>
          <div
            id={`warp_${item[fields.value]}`}
            className={`${styles.warp} ${exPendHandle(item) ? styles.excollapse : styles.collapse}`}>
            {item?.children?.length > 0
              ? renderTreeNode(item.children, deepCount + "1")
              : null}
          </div>
        </div>
      )
    });
  };
  // 选择行
  const handleSelect = (record: any) => {
    // setSelectedKey(record.id);
    // onSelect?.(record.id);
  };
  function getTreeSingleData(list: any[], id: string): any {//获取树节点某个数据
    let data = {} as any
    list.find(item => {
      if (item[fields.value] === id) {
        data = item
        return true
      } else if (item.children) {
        const cur = getTreeSingleData(item.children, id)
        if (cur[fields.value]) {
          data = cur
        }
      }
    })
    return data
  }
  function getAllids(list: any[], data: any = undefined): string[] {//获取数据所有id
    let ids = []
    if (data) {
      ids.push(data)
    }
    if (list) {
      list.forEach(item => {
        ids.push(item)
        if (item.children) {
          ids = ids.concat(getAllids(item.children))
        }
      })
    }
    return ids
  }
  function loadHandle(id: string) {//加载控制处理
    const tf = loadIds.find(item => item.id === id)
    let list: loadOptions[] = []
    if (tf) {
      list = loadIds.map(item => {
        return {
          ...item,
          load: false
        }
      })
    } else {
      list = loadIds.concat({ id, load: false })
    }
    setLoadIds(list)
  }
  // 折叠收起
  const handleCollapse = (record: any) => {
    if (expendIds.includes(record[fields.value])) {//收起
      setExpendIds(expendIds.filter(item => item !== record[fields.value]))
    } else {//展开
      const tf: boolean = loadIds.some(item => item.load)
      const list = expendIds.concat([record[fields.value]])
      const ids = loadIds.concat({
        id: record[fields.value],
        load: true
      })
      if (!tf && loadData && (!record.children || record.children.length === 0)) {
        setLoadIds(ids)
        loadData(record).then(res => {
          loadHandle(record[fields.value])
          setExpendIds(list)
        }).catch(err => {
          loadHandle(record[fields.value])
        })
      } else if (record.children && record.children.length > 0) {
        setExpendIds(list)
      }
    }
  };
  function selectCheckedHandle(checked: boolean, id: string, node: any) {//复选框点击处理
    let list = null
    let listData = []
    const data: any = getTreeSingleData(treeData, id)
    const lists = data.children && data.children.length > 0 && checkStrictly ? getAllids(data.children, data) : [data]
    const ids = lists.map(item => item[fields.value])
    let selNodes = lists.concat(value.map(item => getTreeSingleData(treeData, item)))//
    if (!checked) {//取消
      list = value.filter(item => !ids.includes(item))
      listData = selNodes.filter(item => !ids.includes(item[fields.value]))
    } else if (checked) {//选择
      if (multiple) {
        list = Array.from(new Set([...value, ...ids]))//Set过滤重复id
      } else {
        list = [id]
      }
      listData = selNodes
    }
    onChange(list, listData, node)
    setSelectedNodes(listData)
  }

  useEffect(() => {
    if (isExpendAll && treeData.length > 0) {
      let allIds = []
      deepForEachFn(treeData, (node) => {
        allIds.push(node.id)
      })
      setExpendIds(allIds)
    } else {
      setExpendIds([])
    }
    return () => setExpendIds([])
  }, [isExpendAll, treeData])

  return (
    <div className="checked-tree-container" style={{ minHeight: height }}>
      {multiple ? (
        <Checkbox.Group value={value}>
          {treeData.map((item) => (
            <div className={styles.blockTree} key={item[fields.value]}>
              {renderTreeNode([item])}
            </div>
          ))}
        </Checkbox.Group>
      ) : (
        <Radio.Group value={value[0] || ''}>
          {treeData.map((item) => (
            <div className={styles.blockTree} key={item[fields.value]}>
              {renderTreeNode([item])}
            </div>
          ))}
        </Radio.Group>
      )}
    </div>
  );
};

export default CheckedTree;
