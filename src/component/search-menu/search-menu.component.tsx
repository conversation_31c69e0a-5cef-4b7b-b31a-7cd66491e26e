
import { CheckList, Dropdown, ErrorBlock } from 'antd-mobile';
import styles from './search-menu.component.less'
import { DownOutline } from 'antd-mobile-icons'
import { FC, useState, useEffect, useRef, useImperativeHandle, forwardRef } from 'react'
import { propsOptions, fieldNames, listOptions } from './search-menu'

const fieldName: fieldNames = {
    label: "label",
    value: "value"
}
const SearchMenu: FC<propsOptions> = forwardRef(({ onChange, options, customFilterComponent }, ref) => {
    const menuRef = useRef<any>()
    const [moreShowModal, setMoreShowModal] = useState<boolean>(false)
    const [formData, setFormData] = useState<any>(() => {
        const data = {}
        options.forEach(item => {
            data[item.field] = item.defValue ? [item.defValue] : []
        })
        return data
    })//搜索参数对象
    useImperativeHandle(ref, () => ({//外面暴露方法
        searchReset,
        close: menuRef.current.close
    }));
    function searchReset() {//重置
        let data = { ...formData }
        for (let key in data) {
            data[key] = []
        }
        const params = searchParamsHandle(data)
        setFormData(data)
        return params
    }
    function searchParamsHandle(data:any):any{//搜索参数处理
        const datas = {}
        for(const key in data){
            if(data[key] instanceof Array &&data[key].length>0){
                datas[key] = data[key][0]
            } else {
                datas[key] = ""
            }
        }
        return datas
    }
    function checkListChange(value: string[], field: string) {//搜索参数改变
        let data = { ...formData }
        if (value.length) {
            data[field] = value
        } else {
            data[field] = []
        }
        const params = searchParamsHandle(data)
        onChange(params)
        setFormData(data)
        if (menuRef) {
            menuRef.current.close()
        }
    }
    function menuChange(key: string | null) {//菜单key改变
        if (key === "more") {
            setMoreShowModal(true)
        } else {
            setMoreShowModal(false)
        }
    }
    function titleShowHandle(data: listOptions) {
        if (formData[data.field] instanceof Array && formData[data.field].length>0) {
            const value = data.fieldNames ? data.fieldNames.value : fieldName.value
            const label = data.fieldNames ? data.fieldNames.label : fieldName.label
            const item = data.dataSource.find(item => item[value] === formData[data.field][0]) || {}
            return item[label]
        } else {
            return data.title
        }
    }
    function dropdownItemComponent() {//下拉菜单模板
        return options.map(item => {
            const value = item.fieldNames ? item.fieldNames.value : fieldName.value
            const label = item.fieldNames ? item.fieldNames.label : fieldName.label
            return <Dropdown.Item key={item.field} title={<span className={`${styles.title} titles`}>{titleShowHandle(item)}</span>}>
                {
                    item.dataSource && item.dataSource.length > 0 ?
                        <CheckList className='drop-popup-container' value={formData[item.field]} onChange={(val: string[]) => checkListChange(val, item.field)}>
                            {
                                item.dataSource.map(citem => {
                                    return <CheckList.Item key={citem[value]} value={citem[value]}>{citem[label]}</CheckList.Item>
                                })
                            }
                        </CheckList> : <ErrorBlock
                            image='https://gw.alipayobjects.com/zos/antfincdn/ZHrcdLPrvN/empty.svg'
                            style={{
                                '--image-height': '150px',
                            }}
                            title="暂无数据"
                            description=""
                            status='empty'
                        >
                        </ErrorBlock>
                }
            </Dropdown.Item>
        })
    }
    function moreIcon() {//更多搜索条件图标
        return <div className={`${styles["more-filter"]} ${moreShowModal ? styles["more-filter-active"] : null}`}></div>
    }
    return (
        <div className={`${styles["search-menu-container"]} search-menu`}>
            <Dropdown ref={menuRef} onChange={menuChange} arrow={<DownOutline className={styles.icon} />}>
                {dropdownItemComponent()}
                {/* 更多搜索条件 */}
                {customFilterComponent ? <Dropdown.Item className={styles["more-custom"]} arrow={null} key="more" title={moreIcon()}>
                    {customFilterComponent}
                </Dropdown.Item> : null}
            </Dropdown>
        </div>
    )
})
export default SearchMenu;