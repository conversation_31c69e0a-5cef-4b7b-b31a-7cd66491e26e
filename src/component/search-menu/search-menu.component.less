.search-menu-container {
    background: #fff;
    padding: 10px 20px;
    position: relative;
    position: sticky;
    top: 0px;
    z-index: 1;
}

.more-filter {
    width: 58px;
    height: 58px;
    background: url(~@/assets/screen_icon.png) no-repeat;
    background-size: cover;
}

.more-custom {
    position: absolute;
    right: 20px;
    top: 10px;
}

.more-filter-active {
    background: url(~@/assets/screen_icon1.png) no-repeat;
    background-size: cover;
}

.title {
    color: #666;
    font-size: 40px;
    max-width: 200px;
    display: inline-block;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}

.icon {
    font-size: 40px;
}

:global {
    .search-menu {
        .adm-dropdown-item {
            flex: unset !important;
        }


    }

    .drop-popup-container {
        .adm-list-body {
            max-height: 800px;
            overflow-y: auto;
        }
    }

    .adm-dropdown-item-active {

        .adm-dropdown-item-title-arrow,
        .titles {
            color: #1154ED !important;
        }
    }

    .adm-check-list-item-active {
        .adm-list-item-content-main {
            color: #1154ED !important;
        }
    }
}