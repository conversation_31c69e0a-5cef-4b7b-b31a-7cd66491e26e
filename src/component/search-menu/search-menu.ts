export interface listOptions {
    title: string,//标题
    field: string,//字段
    fieldNames?: fieldNames,//字段配置
    defValue?: any;//默认值
    dataSource: any[]//配置项
}

export interface fieldNames {
    label: string,
    value: string
}
export interface propsOptions {
    options: listOptions[];//基础搜索配置
    customFilterComponent?: JSX.Element//自定义更多搜索区域
    onChange: (val: any) => void//搜索方法
    ref?: any;
}

