import React, { useState, useEffect } from 'react';
import { history, useLocation } from 'umi';
import { TabBar } from 'antd-mobile'
import { getTarBars } from '@/utils/get-route-object.utils';
import type { Routes } from '../../../config/routes';
import styles from './tab-bar.component.less'
import { pathOptions } from '@/pages/nav/nav.page';
type Props = {
  options?: pathOptions[],
  tabBar: string;
  changeBadge?: (data: Routes[], callback: (newData: Routes[]) => void) => void;
}

const TabBarComponent: React.FC<Props> = ({
  options = [],
  tabBar,
  changeBadge = () => { },
}) => {

  const [tarBars, setTarBar] = useState<Routes[]>(() => getTarBars(tabBar))
  const location = useLocation();
  const [activeKey, setActiveKey] = useState<string>(() => location.pathname);
  const handleChange = (key: string) => {
    history.replace(key);
  }

  useEffect(() => {
    setActiveKey(location.pathname);
    if (changeBadge) {
      changeBadge(tarBars, (data: Routes[]) => {
        setTarBar([...data])
      })
    }
  }, [location.pathname])
  function iconShowHandle(data: Routes, active: boolean) {//图标展示处理
    if (data.img) {//在路由配置中添加img为true即可在此处使用自定义的图片
      const item = options.find(item => item.listPath === data.path)
      if (active && item) {
        return <img className={styles.icon} src={item.activeImg}></img>
      } else if (item) {
        return <img className={styles.icon} src={item.defImg}></img>
      }
    } else {
      return <data.icon></data.icon>
    }
  }
  return (
    <div className={styles.tabbar}>
      <TabBar onChange={handleChange} activeKey={activeKey} >
        {tarBars.map(item => (
          <TabBar.Item key={item.path} icon={(active) => iconShowHandle(item, active)} title={item.name} badge={item.badge} />
        ))}
      </TabBar>
    </div>
  )
}

export default TabBarComponent;
