import { CheckList, Grid, List } from 'antd-mobile';
import { CheckListItem } from 'antd-mobile/es/components/check-list/check-list-item';
import { useEffect, useMemo, useState } from 'react';
import { styled } from 'umi';

type LinkedSelectionProps = {
  value?: string[];
  columns?: number;
  data: any[]; // 顶层数据，数据格式：{ id, name, children? }
  onChange?: (value: string[] | null[]) => void;
  placeholder?: string;
  style?: React.CSSProperties;
};

const GridWrapper = styled(Grid)`
  height: 100%;
  .column {
    height: 100%;
    overflow: auto;
  }
  .adm-list-item-content {
    padding: 0;
    border: none;
  }
  .adm-check-list-item-active {
    color: rgba(17, 84, 237, 1);
  }
  .column-list {
    overflow: hidden;
    .adm-list-body-inner {
      overflow-y: auto;
      // 隐藏滚动条
      &::-webkit-scrollbar {
        width: 0;
        height: 0;
        -webkit-user-select: none;
        -webkit-touch-callout: none;
        overflow-y: hidden; /* 隐藏水平滚动条 */
      }
    }
  }
  .item-name {
    font-size: 3.8889vw;
    font-weight: 400;
    letter-spacing: 0vw;
    line-height: 5.6314vw;
    /* color: rgba(51, 51, 51, 1); */
    // 超出两行时，只显示两行，使用省略号
    display: -webkit-box; /* 设置为WebKit内核的弹性盒子模型 */
    -webkit-box-orient: vertical; /* 垂直排列 */
    -webkit-line-clamp: 2; /* 限制显示两行 */
    overflow: hidden; /* 隐藏超出范围的内容 */
    text-overflow: ellipsis; /* 使用省略号 */
  }
  .placeholder {
    font-size: 3.8889vw;
    font-weight: 400;
    letter-spacing: 0vw;
    line-height: 13.9647vw;
    color: rgba(153, 153, 153, 1);
    text-align: center;
  }
`;

/**
 * LinkedSelection 组件用于创建联动选择框，允许用户在多列选项中进行级联选择。
 * @param {LinkedSelectionProps} props 组件的属性对象。
 * @param {number} props.columns 列数，默认为2。
 * @param {Array} props.data 第一列的数据源。
 * @param {Function} props.onChange 当选项改变时的回调函数。
 * @param {string} props.placeholder 未选择时的占位符，默认为'请选择'。
 *
 * 组件内部维护每列的选中项和每列的选项，支持根据用户选择动态更新后续列的选项。
 */
export const LinkedSelection = (props: LinkedSelectionProps) => {
  const { value, columns = 2, data, onChange, placeholder = '请选择' } = props;
  // 计算级联选项（使用 useMemo 优化性能）
  const cascadingOptions = useMemo(() => {
    const options = Array(Math.max(columns, 1)).fill([]);
    options[0] = data || [];
    const newValues = Array.from({ length: columns }, (_, i) => value?.[i] || null);
    // 递归查找子选项
    for (let i = 0; i < columns - 1; i++) {
      const currentValue = newValues[i];
      const currentLevelOptions = options[i];

      if (currentValue && currentLevelOptions) {
        const foundItem = currentLevelOptions.find((item) => item.id === currentValue);
        options[i + 1] = foundItem?.children || [];
      }
    }

    return options;
  }, [value, data, columns]); // 依赖项确保数据变化时重新计算

  /// 处理选择逻辑（包含类型标注）
  const handleSelection = (columnIndex: number, selectedId: string) => {
    if (!onChange) return;

    // 创建新值数组并截断到有效列数
    const newValue = [...Array.from({ length: columns }, (_, i) => value?.[i] || null)].slice(0, columns);

    // 更新当前列并重置后续列
    newValue[columnIndex] = selectedId;
    for (let i = columnIndex + 1; i < columns; i++) {
      newValue[i] = null;
    }

    // 保证数组长度与列数一致
    while (newValue.length < columns) {
      newValue.push(null);
    }

    onChange(newValue);
  };
  return (
    <GridWrapper columns={columns} style={props?.style}>
      {Array.from({ length: columns }).map((_, colIndex) => {
        const currentOptions = cascadingOptions[colIndex] || [];
        const currentValue = value?.[colIndex] || null;
        return (
          <Grid.Item key={colIndex} className="column">
            {currentOptions?.length > 0 ? (
              // 使用自定义虚拟滚动组件优化长列表性能
              <CheckList
                activeIcon={null}
                value={currentValue ? [currentValue] : ([] as any)}
                className="column-list"
                onChange={(value) => {
                  handleSelection(colIndex, value[0] as string);
                }}
              >
                {/* 当没有选择时，显示占位符 */}
                {currentOptions.map((item) => (
                  <CheckList.Item key={item?.id} value={item?.id}>
                    <div className="item-name">{item?.name}</div>
                  </CheckList.Item>
                ))}
              </CheckList>
            ) : (
              <div className="placeholder">{placeholder}</div>
            )}
          </Grid.Item>
        );
      })}
    </GridWrapper>
  );
};
