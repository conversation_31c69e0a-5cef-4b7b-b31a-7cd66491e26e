.numberRange {
    display: flex;
    width: 100%;

    .numBox {
        display: flex;
        align-items: center;
        flex: 1;
        justify-content: center;
        border-bottom: 1px solid #E6E6E6;
        padding-bottom: 20px;
        margin: 0 40px;

        span {
            color: #333333;
            font-size: 40px;
        }

        .placeholder {
            color: #999999;
            font-size: 40px;
        }
    }

    .numBox.activeNum {
        border-bottom: 1px solid #1154ED;
    }

    .tips {
        color: #999999;
        font-size: 44px;
        padding-bottom: 20px;
    }


    :global {
        .unit {

            .adm-list-body,
            .adm-list-item-content {
                border: unset !important;
            }
        }

        .adm-input-element {
            text-align: center;
            color: #1154ED !important;
            font-size: 40px;
        }

        .solid {
            border-top-left-radius: 0px;
            border-bottom-left-radius: 0px;
        }

        .adm-button-fill-outline {
            border-top-right-radius: 0px;
            border-bottom-right-radius: 0px;
        }

    }

}