import { Input } from 'antd-mobile'

import styles from './number-range.less'

interface Props {
  value?: { startNum: string, endNum: string };
  onChange: (numRange: { startNum: string, endNum: string }) => void
}

const NumberRange: React.FC<Props> = ({ value, onChange }) => {
  const { startNum, endNum } = value;

  return <div className={`${styles["numberRange"]}`}>
    <div className={startNum ? [styles.numBox, styles.activeNum].join(' ') : styles.numBox}>
      <Input placeholder='请输入' type='number' value={startNum} onChange={(val) => { onChange({ startNum: val, endNum }) }} />个
    </div>
    <span className={styles.tips}>至</span>
    <div className={endNum ? [styles.numBox, styles.activeNum].join(' ') : styles.numBox}>
      <Input placeholder='请输入' type='number' value={endNum} onChange={(val) => { onChange({ startNum, endNum: val }) }} />个
    </div>
  </div>
}
export default NumberRange;