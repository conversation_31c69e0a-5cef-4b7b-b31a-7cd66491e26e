import { Ellipsis, Grid } from "antd-mobile";
import styles from './grid-list.component.less';

export interface ColumnsProps {
  title: string;
  dataIndex: string;
  align?: 'center' | 'left' | 'right';
  span?: number;
  ellipsis?: boolean;
  render?: (text, record, index) => void;
}

interface Props {
  columns: ColumnsProps[];
  dataSource: any[];
  rowKey: string | number;
  gridConfig: {
    columns?: number;
    gap?: number;
  }
};

const GridList: React.FC<Props> = ({ columns, dataSource = [], rowKey = 'id', gridConfig }) => {
  return (
    <>
      <div className={styles.listHeader}>
        <Grid columns={gridConfig?.columns} gap={gridConfig?.gap}>
          {columns?.map((item) => (
            <Grid.Item span={item.span} key={item.dataIndex} style={{ textAlign: item.align }}>{item.title}</Grid.Item>
          ))}
        </Grid>
      </div>
      <div className={styles.listBody}>
        {dataSource?.map((item, index) => (
          <Grid columns={gridConfig?.columns} gap={gridConfig?.gap} key={item?.[rowKey]}>
            {columns?.map((column: ColumnsProps) => (
              <Grid.Item span={column.span} key={column.dataIndex} style={{ textAlign: column.align }}>
                {
                  column?.render ? column.render(column.dataIndex, item, index) :
                    column?.ellipsis ? (
                      <Ellipsis direction="end" content={String(item[column.dataIndex])} />
                    ) : item[column.dataIndex]
                }
              </Grid.Item>
            ))}
          </Grid>
        ))}
      </div>
    </>
  )
};
export default GridList;
