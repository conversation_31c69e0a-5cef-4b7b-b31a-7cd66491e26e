import { propOptions } from './scroll-list';
import { FC, useEffect, useState } from 'react';
import { InfiniteScroll, List, DotLoading, ErrorBlock, PullToRefresh } from 'antd-mobile';
import styles from './scroll-list.component.less';
const ScrollList: FC<propOptions> = ({
  threshold = 20,
  maxScroll = 10,
  isErr,
  reLoad = undefined,
  loading = false,
  hasMore,
  loadMore,
  dataSource,
  itemComponent,
}) => {
  return (
    <>
      {loading === false && dataSource.length > 0 ? (
        <>
          <PullToRefresh
            onRefresh={reLoad}
            disabled={reLoad === undefined ? true : false}
            threshold={30}
            pullingText={<span className={styles.tips}>下拉刷新</span>}
            canReleaseText={<span className={styles.tips}>释放立即刷新</span>}
          >
            <List className="sroll-list-com">{dataSource.length > 0 && dataSource.map((item, index) => itemComponent(item, index))}</List>
          </PullToRefresh>
          {dataSource.length >= maxScroll ? <InfiniteScroll threshold={threshold} loadMore={loadMore} hasMore={hasMore} /> : null}
        </>
      ) : null}
      {loading === false && dataSource.length === 0 ? (
        <PullToRefresh
          onRefresh={reLoad}
          threshold={30}
          pullingText={<span className={styles.tips}>下拉刷新</span>}
          canReleaseText={<span className={styles.tips}>释放立即刷新</span>}
        >
          <ErrorBlock
            image="https://gw.alipayobjects.com/zos/antfincdn/ZHrcdLPrvN/empty.svg"
            style={{
              '--image-height': '150px',
            }}
            status="empty"
            title="暂无数据"
            description=""
          ></ErrorBlock>
        </PullToRefresh>
      ) : null}
      {loading === true && dataSource.length === 0 ? (
        <div className={styles.placeholder}>
          <div className={styles.loadingWrapper}>
            <DotLoading />
          </div>
          正在加载数据
        </div>
      ) : null}
    </>
  );
};
export default ScrollList;
