import { useEffect, useState } from 'react';
import { getOrganizationTree } from './user-approver-modal.service';

export default function useTreeByConfigure() {
  const [treeData, setTreeData] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    getTreeData();
  }, []);

  // 获取标段树
  const getTreeData = async () => {
    setLoading(true);
    const { code, data } = await getOrganizationTree({ id: 0, flag: 1 });
    if (code === '1') setTreeData(data);
    setLoading(false);
  };
  return {
    treeData,
    loading,
  };
}
