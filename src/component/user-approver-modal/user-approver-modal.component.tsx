import React, { memo, useMemo, useState } from 'react';
import type { ReactNode, FC } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ist, <PERSON>rror<PERSON>lock, Popup, Toast } from 'antd-mobile';
import type { CascaderValue, CascaderValueExtend } from 'antd-mobile/es/components/cascader-view';

import useTreeByOrg from './useTreeByOrg';
import styles from './user-approver-modal.style.less';
import type { IOption } from './user-approver-modal.types';

function traverseTree(root: any) {
  return {
    label: root.name,
    value: root.id,
    children: root.children?.map((child: any) => traverseTree(child)) ?? null,
    users: root.users,
  } as IOption;
}

interface IProps {
  children?: ReactNode;
  title?: string;
  visible: boolean;
  onOk: (id: string, name: string, userName: string) => void;
  onCancel: () => void;
}

const UserApproverModal: FC<IProps> = (props) => {
  const { title = '用户选择', visible, onOk, onCancel } = props;

  const { treeData, loading } = useTreeByOrg(); // 获取标段树

  const [selectedNode, setSelectedNode] = useState<IOption | null>(null);
  const [selectedUserId, setSelectedUserId] = useState<string>(null);
  const [selectedName, setSelectedName] = useState<string>(null);
  const [selectedUserName, setSelectedUserName] = useState<string>(null);

  const options = useMemo(() => treeData.map((item) => traverseTree(item)), [treeData]);

  const handleCascaderChange = (_: CascaderValue[], extend: CascaderValueExtend) => {
    const { items, isLeaf: __ } = extend;
    setSelectedNode((items[items.length - 1] as IOption) ?? null);
  };

  const handleCheckListChange = (id: string, name: string, userName: string) => {
    // 取消选择
    if (selectedUserId === id) {
      setSelectedUserId(null);
      setSelectedName(null);
      setSelectedUserName(null);
      return;
    }
    setSelectedUserId(id);
    setSelectedName(name);
    setSelectedUserName(userName);
  };

  const handleOnOk = () => {
    if (!selectedUserId) {
      Toast.show({
        icon: 'fail',
        content: '未选择用户！',
      });
      return;
    }
    onOk(selectedUserId, selectedName, selectedUserName);
  };

  return (
    <div className={styles['user-approver-modal']}>
      <Popup visible={visible} style={{ maxHeight: '320px' }}>
        <div className={styles['top-bar']}>
          <Button color="primary" fill="none" onClick={onCancel}>
            取消
          </Button>
          <span className={styles['title']}>{selectedUserId ? `已选择：${selectedName} (${selectedName})` : title}</span>
          <Button color="primary" fill="none" onClick={handleOnOk}>
            确认
          </Button>
        </div>
        <div className={styles['content']}>
          <CascaderView loading={loading} options={options} style={{ '--height': '40vh' }} onChange={handleCascaderChange} />
          {selectedNode && selectedNode.users.length ? (
            <CheckList defaultValue={[selectedUserId]} style={{ height: '420px', overflow: 'auto' }}>
              {selectedNode.users.map((user) => (
                <CheckList.Item
                  key={user.id}
                  value={user.id}
                  onClick={() => handleCheckListChange(user.id, user.name, user.username)}
                >{`${user.name} (${user.username})`}</CheckList.Item>
              ))}
            </CheckList>
          ) : selectedNode ? (
            <ErrorBlock status="empty" title="该组织机构下未查询到用户" description="请选择其他组织机构查看" />
          ) : (
            <ErrorBlock status="empty" title="未选择组织机构" description="请选择一个组织机构查看" />
          )}
        </div>
      </Popup>
    </div>
  );
};

export default memo(UserApproverModal);
