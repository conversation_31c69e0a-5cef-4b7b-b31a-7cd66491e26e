.top-bar {
  height: 3rem;
  width: 100%;
  display: flex;
  justify-content: space-between;

  :global {
    .adm-button {
      color: #1677ff;
      font-size: 1.5rem;
    }
  }

  .title {
    max-width: 70vw;
    font-size: 1.3rem;
    font-weight: bold;
    line-height: 3rem;
    overflow: hidden; 
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.content {
  flex: 1;
  display: flex;

  :global {
    .adm-cascader-view {
      border-top: 0.1rem solid #eee;
      width: 50%;

      .adm-tabs-header {
        .adm-tabs-tab-wrapper {
          max-width: 50%;

          & > .adm-tabs-tab {
            max-width: 100%;

            & > .adm-cascader-view-header-title {
              max-width: 100%;
              font-size: 1rem;
              font-weight: bold;
            }
          }
        }
      }

      .adm-cascader-view-content {
        .adm-list {
          width: 100%;

          .adm-list-item-content-main {
            font-size: 1rem;
          }
        }
      }
    }

    .adm-cascader-view + .adm-list {
      width: 50%;

      .adm-list-item-content-main {
        font-size: 1rem;
      }
    }

    .adm-error-block {
      width: 50%;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }
  }
}
