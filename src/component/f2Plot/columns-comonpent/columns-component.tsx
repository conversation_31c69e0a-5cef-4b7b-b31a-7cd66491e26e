import Canvas from '@antv/f-react';
import { Chart, Axis, Interval, ScrollBar, Tooltip, Legend, withLegend } from '@antv/f2';

type F2ColumnsComponentProps = {
  data: {
    name: string;
    xData: any;
    yData: any;
  }[];
  xField?: string;
  yField?: string;
  color?: string;
};

// 自定义 Legend
const CustomLegend = withLegend((props) => {
  const { items, itemWidth } = props;
  return (
    <group
      style={{
        display: 'flex',
        ...props.style,
      }}
    >
      {items.map((item) => {
        const { color, name } = item;
        return (
          <group
            className="legend-item"
            style={{
              width: itemWidth,
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center',
            }}
            data-item={item}
          >
            <rect
              style={{
                fill: color,
                width: 16,
                height: 8,
              }}
            />
            <text
              style={{
                marginLeft: 4,
                marginTop: 3,
                fill: color,
                fontSize: window.innerWidth < 375 ? 10 : 12,
                text: name,
              }}
            />
          </group>
        );
      })}
    </group>
  );
});

/**
 * F2的柱状图实现
 * @returns
 */
export const F2ColumnsComponent = (props: F2ColumnsComponentProps) => {
  const { data, xField = 'xData', yField = 'yData', color = 'rgba(73, 97, 243, 1)' } = props;

  return (
    <Canvas pixelRatio={3}>
      <Chart data={data}>
        <CustomLegend
          value={'name'}
          position="top"
          style={{
            justifyContent: 'flex-end',
          }}
        />
        <Axis
          field={xField}
          style={{
            label: {
              fontSize: window.innerWidth < 375 ? 10 : 12,
            },
          }}
          formatter={(text) => {
            // 显示前 2 个字符，超出部分用省略号替代
            return text.length > 3 ? text.slice(0, 3) + '...' : text;
          }}
        />
        <Axis
          field={yField}
          style={{
            label: {
              fontSize: window.innerWidth < 375 ? 10 : 12,
            },
          }}
          visible={false}
        />
        <Interval
          x={xField}
          y={yField}
          size={12}
          style={{
            radius: [10, 10, 0, 0],
            fill: 'rgba(73, 97, 243, 1)',
          }}
          color={['name', [color]]}
          // color={{
          //   field: 'name',
          //   mapper: (name) => {
          //     return color;
          //   },
          // }}
        />
        <ScrollBar
          mode="x"
          pan
          // margin={'-120px'}
          range={[0, 6 / data.length]}
          background={{
            stroke: 'rgba(242, 242, 242, 1)',
            lineWidth: 2,
          }}
          barStyle={{
            stroke: 'rgba(187, 215, 242, 1)',
            radius: 3,
            lineWidth: 3,
          }}
        />
        <Tooltip
          showCrosshairs
          showArrow
          position="top"
          nameStyle={{ fontSize: 12, fill: 'rgba(51, 51, 51, 1)' }}
          valueStyle={{
            fontSize: 12,
            fill: 'rgba(51, 51, 51, 1)',
          }}
          background={{
            fill: 'rgba(255, 255, 255, 0.9)',
          }}
        />
      </Chart>
    </Canvas>
  );
};