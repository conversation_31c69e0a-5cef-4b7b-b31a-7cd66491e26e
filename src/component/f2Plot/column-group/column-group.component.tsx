import { Axis, Chart, Interval, Line, ScrollBar, Legend, withLegend, Tooltip } from '@antv/f2';
import Canvas from '@antv/f-react';
import { useRef, useState } from 'react';

const CustomLegend = withLegend((props) => {
  const { items, itemWidth } = props;
  console.log(props);
  return (
    <group
      style={{
        display: 'flex',
        ...props.style,
        justifyContent: 'flex-end',
      }}
    >
      {items.map((item) => {
        const { color, name } = item;
        return (
          <group
            key={name}
            className="legend-item"
            style={{
              width: itemWidth,
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'flex-end',
            }}
            data-item={item}
          >
            {item?.type === 'line' ? (
              <rect
                style={{
                  fill: color,
                  width: 10,
                  height: 2,
                  marginTop: 3,
                }}
              />
            ) : (
              <rect
                style={{
                  fill: color,
                  width: 12,
                  height: 5,
                }}
              />
            )}
            <text
              style={{
                marginLeft: 6,
                marginTop: 3,
                marginRight: 4,
                fill: '#000',
                fontSize: window.innerWidth < 375 ? 10 : 12,
                text: name,
              }}
            />
          </group>
        );
      })}
    </group>
  );
});

const Shape = (props: { position?: 'left' | 'right'; name: string }) => {
  const { position = 'left', name } = props;
  console.log(position === 'left' ? props.coord.left - (window.innerWidth < 375 ? 10 : 12) : props.coord.width - props.coord.left);
  return (
    <group>
      <text
        style={{
          x: position === 'left' ? props.coord.left - (window.innerWidth < 375 ? 10 : 12) : props.coord.right,
          y: props.coord.top - window.innerWidth < 375 ? 33 : 35,
          fontSize: window.innerWidth < 375 ? 10 : 12,
          fill: '#666',
          text: name,
        }}
      />
    </group>
  );
};

const CustomTooltip = ({ x, y, list }) => {
  return (
    <div
      style={{
        position: 'absolute',
        left: x,
        top: y,
        background: 'rgba(255,255,255,0.95)',
        border: '1px solid #eee',
        borderRadius: 6,
        boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
        padding: 8,
        zIndex: 9999,
        pointerEvents: 'none',
      }}
    >
      {list.map((item) => (
        <div key={item.name} style={{ color: item.color, fontSize: 12, marginBottom: 2 }}>
          {item.name}：
          <span style={{ fontWeight: 600 }}>
            {item.value} {item?.unit}
          </span>
        </div>
      ))}
    </div>
  );
};
const F2PlotColumnGroup = (props) => {
  const { type = 'dodge', items, data = [], ...rest } = props;
  const [visible, setVisible] = useState(false);
  const [tooltips, setTooltips] = useState(null);
  console.log(data)
  // 合并数据，便于同时渲染
  return (
    <div
      style={{
        position: 'relative',
        height: '100%',
      }}
    >
      <Canvas pixelRatio={window.devicePixelRatio}>
        <Chart data={data}>
          <CustomLegend
            marker="square"
            // itemWidth={window.innerWidth < 375 ? 10 : 12}
            // height={window.innerWidth < 375 ? 10 : 12}
            nameStyle={{
              fontSize: window.innerWidth < 375 ? 10 : 12,
            }}
            height={40}
            style={{ justifyContent: 'flex-end', alignItems: 'flex-start' }}
            items={items}
          />
          <Axis
            field="xdata"
            labelTransform={'rotate(45)'}
            style={{
              label: {
                fontSize: window.innerWidth < 375 ? 10 : 12,
                rotate: -Math.PI / 2,
                textAlign: 'center',
              },
            }}
          />
          <Shape name={'个'} />
          <Axis
            field="ydata"
            tickCount={5}
            label={{
              autoRotate: true,
            }}
            style={{
              label: {
                fontSize: window.innerWidth < 375 ? 10 : 12,
                // 倾斜45度
                rotate: 45,
              },
              title: {
                fontSize: window.innerWidth < 375 ? 10 : 12,
                fill: '#666',
                textAlign: 'center',
                textBaseline: 'bottom',
              },
            }}
          />

          <Shape position="right" name={'%'} />
          <Axis
            field="value"
            tickCount={5}
            position="right"
            label="%"
            labelStyle={{
              fontSize: window.innerWidth < 375 ? 10 : 12,
              fill: '#666',
              textAlign: 'center',
              textBaseline: 'bottom',
            }}
            style={{
              label: {
                fontSize: window.innerWidth < 375 ? 10 : 12,
              },
              title: {
                fontSize: window.innerWidth < 375 ? 10 : 12,
                fill: '#666',
                textAlign: 'center',
                textBaseline: 'bottom',
              },
            }}
            title={{
              text: '数量 (个)',
              position: 'top',
              offset: 10,
              style: {
                fontSize: window.innerWidth < 375 ? 10 : 12,
                fill: '#666',
              },
            }}
          />
          <Interval
            x="xdata"
            y="ydata"
            viewClip
            color={['name', ['#4A61F3', '#F8BC05']]}
            adjust={type}
            sizeRatio={0.5}
            size={12}
            style={{
              radius: type === 'stack' ? 0 : [6, 6, 0, 0],
              marginRatio: type === 'stack' ? 0 :0.05, // 设置分组间柱子的间距
            }}
          />
          <Line
            x="xdata"
            y="value"
            color={'rgba(1, 208, 102, 1)'}
            style={{
              width: 4,
              shadowType: 'outer',
              // box-shadow
              shadowColor: 'rgba(109, 112, 109, 0.75)',
              shadowBlur: 10,
              shadowOffsetX: 4,
              shadowOffsetY: 7,
            }}
            shape="smooth"
            connectNulls
            size={2}
          />

          {data.length >= 8 && (
            <ScrollBar
              mode="x"
              pan
              range={[0, 8 / data.length]}
              background={{
                stroke: 'rgba(242, 242, 242, 1)',
                lineWidth: 2,
              }}
              barStyle={{
                stroke: 'rgba(187, 215, 242, 1)',
                radius: 3,
                lineWidth: 3,
              }}
            />
          )}
          <Tooltip
            custom
            position="top"
            nameStyle={{ fontSize: 12, fill: 'rgba(51, 51, 51, 1)' }}
            valueStyle={{
              fontSize: 12,
              fill: 'rgba(51, 51, 51, 1)',
            }}
            background={{
              fill: 'rgba(255, 255, 255, 0.9)',
            }}
            onShow={() => {
              setVisible(true);
            }}
            onHide={() => {
              setVisible(false);
              setTooltips(null);
            }}
            onChange={(obj) => {
              setTooltips(obj);
            }}
          />
        </Chart>
      </Canvas>
      {visible && tooltips?.length && (
        <CustomTooltip
          x={tooltips[0].x}
          y={0}
          list={tooltips?.map((o) => ({
            name: o.name,
            value: o?.value || o?.origin?.value,
            unit: o?.unit,
          }))}
        />
      )}
    </div>
  );
};

export default F2PlotColumnGroup;
