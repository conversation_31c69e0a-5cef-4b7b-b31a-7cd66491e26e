import { PickerView } from 'antd-mobile';
import dayjs from 'dayjs';
import { useEffect, useMemo, useState } from 'react';

// 生成数据的辅助函数（未变，依然可以使用原有实现）
const generateYears = (start = 2000, end = 2030) => {
  const years = [];
  for (let i = start; i <= end; i++) {
    years.push({ label: i, value: i });
  }
  return years;
};

const generateMonths = () => {
  const months = [];
  for (let i = 1; i <= 12; i++) {
    const label = (i < 10 ? '0' + i : i) + '月';
    months.push({ label, value: i });
  }
  return months;
};

const getDays = (year, month) => {
  // 构造指定年月的第一天，dayjs 会自动处理各个月天数
  const daysInMonth = dayjs(`${year}-${month}-01`).daysInMonth();
  const days = [];
  for (let i = 1; i <= daysInMonth; i++) {
    // 自动补零，比如 '01日'
    const label = (i < 10 ? '0' + i : i) + '日';
    days.push({ label, value: i });
  }
  return days;
};

// 生成小时数据
const generateHours = () => {
  const hours = [];
  for (let i = 0; i < 24; i++) {
    const label = (i < 10 ? '0' + i : i) + '时';
    hours.push({ label, value: i });
  }
  return hours;
};

// 生成分钟数据
const generateMinutes = () => {
  const minutes = [];
  for (let i = 0; i < 60; i++) {
    const label = (i < 10 ? '0' + i : i) + '分';
    minutes.push({ label, value: i });
  }
  return minutes;
};

// 生成秒数据
const generateSeconds = () => {
  const seconds = [];
  for (let i = 0; i < 60; i++) {
    const label = (i < 10 ? '0' + i : i) + '秒';
    seconds.push({ label, value: i });
  }
  return seconds;
};

type CustomDatePickerProps = {
  mode: 'year' | 'month' | 'day' | 'hour' | 'minute' | 'second';
  value: number | null;
  onChange: (value: number) => void;
};

/**
 * 自定义日期选择器组件
 *
 * @param props 组件属性
 * @param mode 选择器模式，可以是 'year'、'month' 或 'day' 或 'hour'、'minute' 或 'second'
 * @param value 选择的日期值
 * @param onChange 日期值变化时的回调函数
 * @returns 返回日期选择器组件
 */
export const CustomDatePicker = (props: CustomDatePickerProps) => {
  const { mode, value, onChange } = props;
  console.log('CustomDatePicker', props);

  // 初始化选定的日期，默认为当前日期
  const [selected, setSelected] = useState<any>([dayjs().year(), dayjs().month() + 1, dayjs().date()]);

  // 根据选定的年份和月份动态计算天数
  const [days, setDays] = useState(mode === 'day' ? getDays(selected[0], selected[1]) : []);

  // 固定数据列
  const years = generateYears();
  const months = generateMonths();
  const hours = generateHours();
  const minutes = generateMinutes();
  const seconds = generateSeconds();

  // 当年份或月份变化时，使用 dayjs 重新计算“日”列
  useEffect(() => {
    if (mode === 'day' || mode === 'hour' || mode === 'minute' || mode === 'second') {
      const year = dayjs(value || new Date()).year();
      const month = dayjs(value || new Date()).month() + 1;
      const day = dayjs(value || new Date()).day();
      const hour = dayjs(value || new Date()).hour();
      const minute = dayjs(value || new Date()).minute();
      const second = dayjs(value || new Date()).second();
      const newDays = getDays(year, month);
      setDays(newDays);
      if (day > newDays.length) {
        setSelected([year, month, newDays.length, hour, minute, second]);
      }
    }
  }, [value, mode]);

  // 根据选择器的值和模式设置选定的日期
  useEffect(() => {
    if (value) {
      if (mode === 'day') {
        setSelected([dayjs(value).year(), dayjs(value).month() + 1, dayjs(value).date(), 1, 1, 1]);
      } else if (mode === 'month') {
        setSelected([dayjs(value).year(), dayjs(value).month() + 1, 1, 1, 1, 1]);
      } else if (mode === 'year') {
        setSelected([dayjs(value).year(), 1, 1, 1, 1, 1]);
      } else if (mode === 'hour') {
        setSelected([dayjs(value).year(), dayjs(value).month() + 1, dayjs(value).date(), dayjs(value).hour(), 1, 1]);
      } else if (mode === 'minute') {
        setSelected([dayjs(value).year(), dayjs(value).month() + 1, dayjs(value).date(), dayjs(value).hour(), dayjs(value).minute(), 1]);
      } else {
        setSelected([
          dayjs(value).year(),
          dayjs(value).month() + 1,
          dayjs(value).date(),
          dayjs(value).hour(),
          dayjs(value).minute(),
          dayjs(value).second(),
        ]);
      }
    } else {
      setSelected([dayjs().year(), dayjs().month() + 1, dayjs().date(), dayjs().hour(), dayjs().minute(), dayjs().second()]);
    }
  }, [mode, value]);

  // 根据模式动态生成列
  const columns: any = useMemo(() => {
    if (mode === 'year') {
      return [years];
    } else if (mode === 'month') {
      return [years, months];
    } else if (mode === 'day') {
      return [years, months, days];
    } else if (mode === 'hour') {
      return [years, months, days, hours];
    } else if (mode === 'minute') {
      return [years, months, days, hours, minutes];
    } else {
      return [years, months, days, hours, minutes, seconds];
    }
  }, [mode, years, months, days]);

  // 返回日期选择器组件
  return (
    <PickerView
      columns={columns}
      value={selected}
      style={{
        '--item-height': '16.3492vw',
      }}
      onChange={(val: number[]) => {
        // setSelected(val)
        onChange(dayjs(`${val[0]}-${val[1]}-${val[2]} ${val[3]}:${val[4]}:${val[5]}`).valueOf());
      }}
    />
  );
};
