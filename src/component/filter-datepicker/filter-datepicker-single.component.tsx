import React, { useState, useEffect, useMemo, useCallback, ReactNode } from 'react';
import { PickerView } from 'antd-mobile';
import dayjs from 'dayjs';
import { CustomDatePicker } from './custom-date-picker.component';
import _, { set } from 'lodash';
import { useDeepCompareEffect } from 'ahooks';
import { styled } from 'umi';

const FilterDatePickerWrapper = styled.div.attrs({
  className: (props: { className: any }) => `${props.className} filter-date-picker-wrapper` as any,
})`
  .header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin: 0 auto 8.2408vw;
    .types {
      .type-item {
        display: inline-block;
        /* min-width: 12.5925vw; */
        height: 6.6667vw;
        opacity: 1;
        /** 文本1 */
        font-size: 3.3333vw;
        font-weight: 400;
        letter-spacing: 0vw;
        line-height: 6.6667vw;
        color: rgba(102, 102, 102, 1);
        text-align: center;
        padding: 0 2.6042vw;
        &.active {
          color: rgba(17, 84, 237, 1);
          border-radius: 3.3333vw;
          border: 0.3703vw solid rgba(17, 84, 237, 1);
        }
      }
    }
    .reset {
      font-size: 3.8889vw;
      font-weight: 400;
      letter-spacing: 0vw;
      line-height: 5.3703vw;
      color: rgba(228, 20, 18, 1);
    }
  }
  .body {
    .date-picker {
      display: flex;
      flex-direction: row;
      justify-content: space-around;
      align-items: flex-start;
      font-size: 3.8889vw;
      font-weight: 400;
      letter-spacing: -0.1631vw;
      line-height: 5.3703vw;
      color: rgba(153, 153, 153, 1);
      .date-picker-item {
        display: inline-block;
        width: 33.3333vw;
        padding-bottom: 3.8889vw;
        font-size: 3.8889vw;
        font-weight: 400;
        letter-spacing: -0.1481vw;
        line-height: 4.9075vw;
        color: rgba(153, 153, 153, 1);
        border-bottom: 0.3703vw solid rgba(229, 229, 229, 1);
        text-align: center;
        &.has {
          color: rgba(51, 51, 51, 1);
          border-color: rgba(51, 51, 51, 1);
        }
        &.hover {
          color: rgba(17, 84, 237, 1);
          border-color: rgba(17, 84, 237, 1);
        }
      }
    }
  }
`;

type FilterDatePickerProps = {
  initialMode: 'year' | 'month' | 'day' | 'hour' | 'minute' | 'second';
  initValues?: Record<string, any>;
  headerRender?: (props: {
    values: {
      startDate: number;
      endDate: number;
      mode: 'year' | 'month' | 'day' | 'hour' | 'minute' | 'second';
      [key: string]: any;
    };
    dateTypeOptions: { label: string; value: string }[];
    onReset: () => void;
    onModeChange: (mode: 'year' | 'month' | 'day' | 'hour' | 'minute' | 'second') => void;
    onMoreChange: (data: Record<string, any>) => void;
  }) => React.ReactNode;
  isHideHeader?: boolean;
  value?: {
    date: number | null;
    mode: 'year' | 'month' | 'day' | 'hour' | 'minute' | 'second';
  };
  onChange?: (value: { date: number | null; mode: 'year' | 'month' | 'day' }) => void;
};

export const FilterDatePickerSingle = (props: FilterDatePickerProps) => {
  const { isHideHeader = false, initialMode = 'year', initValues, value, onChange } = props;
  const [currentDateType, setCurrentDateType] = useState<'start' | 'end'>('start');
  const DATE_TYPE = [
    {
      label: '年',
      value: 'year',
    },
    {
      label: '月',
      value: 'month',
    },
    {
      label: '日',
      value: 'day',
    },
  ];

  useEffect(() => {
    onDateChange({
      date: value?.date,
      mode: initialMode,
      ...(initValues || {}),
    });
  }, []);

  const formatShowDate = (date: number) => {
    if (value.mode === 'year') {
      return dayjs(date).format('YYYY');
    } else if (value.mode === 'month') {
      return dayjs(date).format('YYYY-MM');
    } else if (value.mode === 'day') {
      return dayjs(date).format('YYYY-MM-DD');
    } else if(value.mode === 'hour'){
      return dayjs(date).format('YYYY-MM-DD HH');
    } else if(value.mode === 'minute'){
      return dayjs(date).format('YYYY-MM-DD HH:mm');
    } else {
      return dayjs(date).format('YYYY-MM-DD HH:mm:ss');
    }
  };

  const onDateChange = useCallback(
    (data: { date?: number | null; mode?: 'year' | 'month' | 'day' | 'hour' | 'minute' | 'second'; [key: string]: any }) => {
      onChange(_.clone(_.merge(value, data as any)));
    },
    [value],
  );

  const handleModeChange = (newMode: 'year' | 'month' | 'day' | 'hour' | 'minute' | 'second') => {
    setCurrentDateType('start');
    onDateChange({
      date: null,
      mode: newMode,
    });
  };

  const handleMoreChange = (data: Record<string, any>) => {
    setCurrentDateType('start');
    onDateChange({
      ...value,
      date: null,
      ...(data || {}),
    });
  };

  const handleReset = () => {
    setCurrentDateType('start');
    onDateChange({
      date: null,
      mode: initialMode,
    });
  };

  const renderDefaultHeader = useCallback(
    () => (
      <div className="header">
        <div className="types">
          {DATE_TYPE.map((item) => (
            <span
              key={item.value}
              className={`type-item ${item.value === value?.mode && 'active'}`}
              onClick={() => handleModeChange(item.value as any)}
            >
              {item.label}
            </span>
          ))}
        </div>
        <div className="reset" onClick={handleReset}>
          重置
        </div>
      </div>
    ),
    [value],
  );

  const { date } = useMemo(() => {
    return (
      value || {
        date: null,
        mode: initialMode,
      }
    );
  }, [initialMode, value]);

  return (
    <FilterDatePickerWrapper>
      {/* 显示选中时间区间类型和重置按钮 */}
      {!isHideHeader && (
        <>
          {props.headerRender ? (
            props.headerRender({
              value,
              dateTypeOptions: DATE_TYPE,
              onReset: handleReset,
              onModeChange: handleModeChange,
              onMoreChange: handleMoreChange,
            })
          ) : (
            <>{renderDefaultHeader()}</>
          )}
        </>
      )}
      <div className="body">
        <CustomDatePicker
          mode={value?.mode || initialMode}
          value={value?.date}
          onChange={(val: any) => {
            onDateChange?.({
              date: val,
            });
          }}
        />
      </div>
    </FilterDatePickerWrapper>
  );
};
