import React, { useState, useEffect, useMemo, useCallback, ReactNode } from 'react';
import { PickerView } from 'antd-mobile';
import dayjs from 'dayjs';
import { CustomDatePicker } from './custom-date-picker.component';
import _, { set } from 'lodash';
import { useDeepCompareEffect } from 'ahooks';
import { styled } from 'umi';

const FilterDatePickerWrapper = styled.div.attrs({
  className: (props: { className: any }) => `${props.className} filter-date-picker-wrapper` as any,
})`
  .header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin: 0 auto 8.2408vw;
    .types {
      .type-item {
        display: inline-block;
        /* min-width: 12.5925vw; */
        height: 6.6667vw;
        opacity: 1;
        /** 文本1 */
        font-size: 3.3333vw;
        font-weight: 400;
        letter-spacing: 0vw;
        line-height: 6.6667vw;
        color: rgba(102, 102, 102, 1);
        text-align: center;
        padding: 0 2.6042vw;
        &.active {
          color: rgba(17, 84, 237, 1);
          border-radius: 3.3333vw;
          border: 0.3703vw solid rgba(17, 84, 237, 1);
        }
      }
    }
    .reset {
      font-size: 3.8889vw;
      font-weight: 400;
      letter-spacing: 0vw;
      line-height: 5.3703vw;
      color: rgba(228, 20, 18, 1);
    }
  }
  .body {
    .date-picker {
      display: flex;
      flex-direction: row;
      justify-content: space-around;
      align-items: flex-start;
      font-size: 3.8889vw;
      font-weight: 400;
      letter-spacing: -0.1631vw;
      line-height: 5.3703vw;
      color: rgba(153, 153, 153, 1);
      .date-picker-item {
        display: inline-block;
        width: 33.3333vw;
        padding-bottom: 3.8889vw;
        font-size: 3.8889vw;
        font-weight: 400;
        letter-spacing: -0.1481vw;
        line-height: 4.9075vw;
        color: rgba(153, 153, 153, 1);
        border-bottom: 0.3703vw solid rgba(229, 229, 229, 1);
        text-align: center;
        &.has {
          color: rgba(51, 51, 51, 1);
          border-color: rgba(51, 51, 51, 1);
        }
        &.hover {
          color: rgba(17, 84, 237, 1);
          border-color: rgba(17, 84, 237, 1);
        }
      }
    }
  }
`;

export type FilterDatePickerProps = {
  initialMode: 'year' | 'month' | 'day' | 'hour' | 'minute' | 'second';
  initValues?: Record<string, any>;
  headerRender?: (props: {
    values: {
      startDate: number;
      endDate: number;
      mode: 'year' | 'month' | 'day' | 'hour' | 'minute' | 'second';
      [key: string]: any;
    };
    dateTypeOptions: { label: string; value: string }[];
    onReset: () => void;
    onModeChange: (mode: 'year' | 'month' | 'day' | 'hour' | 'minute' | 'second') => void;
    onMoreChange: (data: Record<string, any>) => void;
  }) => React.ReactNode;
  isHideHeader?: boolean;
  value?: {
    startDate: number | null;
    endDate: number | null;
    mode: 'year' | 'month' | 'day' | 'hour' | 'minute' | 'second';
  };
  onChange?: (value: { startDate: number | null; endDate: number | null; mode: 'year' | 'month' | 'day' }) => void;
};
const getStartOfByMode = (date, mode) => {
  if (!date) return date;
  let d = dayjs(date);
  switch (mode) {
    case 'minute':
      return d.second(0).valueOf();
    case 'hour':
      return d.minute(0).second(0).valueOf();
    case 'day':
      return d.hour(0).minute(0).second(0).valueOf();
    case 'month':
      return d.date(1).hour(0).minute(0).second(0).valueOf();
    case 'year':
      return d.month(0).date(1).hour(0).minute(0).second(0).valueOf();
    default:
      return date;
  }
};
const getEndOfByMode = (date, mode) => {
  if (!date) return date;
  let d = dayjs(date);
  switch (mode) {
    case 'minute':
      return d.second(59).valueOf();
    case 'hour':
      return d.minute(59).second(59).valueOf();
    case 'day':
      return d.hour(23).minute(59).second(59).valueOf();
    case 'month':
      return d.date(d.daysInMonth()).hour(23).minute(59).second(59).valueOf();
    case 'year':
      return d.month(11).date(31).hour(23).minute(59).second(59).valueOf();
    default:
      return date;
  }
};

export const FilterDatePicker = (props: FilterDatePickerProps) => {
  const { isHideHeader = false, initialMode = 'year', initValues, value, onChange } = props;
  const [currentDateType, setCurrentDateType] = useState<'start' | 'end'>('start');
  const DATE_TYPE = [
    {
      label: '年',
      value: 'year',
    },
    {
      label: '月',
      value: 'month',
    },
    {
      label: '日',
      value: 'day',
    },
  ];
  useEffect(() => {
    onDateChange({
      startDate: value?.startDate,
      endDate: value?.endDate,
      mode: initialMode,
      ...(initValues || {}),
    });
  }, []);

  const formatShowDate = (date: number) => {
    if (value.mode === 'year') {
      return dayjs(date).format('YYYY');
    } else if (value.mode === 'month') {
      return dayjs(date).format('YYYY-MM');
    } else if (value.mode === 'day') {
      return dayjs(date).format('YYYY-MM-DD');
    } else if (value.mode === 'hour') {
      return dayjs(date).format('YYYY-MM-DD HH');
    } else if (value.mode === 'minute') {
      return dayjs(date).format('YYYY-MM-DD HH:mm');
    } else {
      return dayjs(date).format('YYYY-MM-DD HH:mm:ss');
    }
  };

  const onDateChange = useCallback(
    (data: {
      startDate?: number | null;
      endDate?: number | null;
      mode?: 'year' | 'month' | 'day' | 'hour' | 'minute' | 'second';
      [key: string]: any;
    }) => {
      let merged = _.clone(_.merge(value || {}, data as any));
      // 优化结束时间
      if (merged.endDate && merged.mode) {
        merged.endDate = getEndOfByMode(merged.endDate, merged.mode);
        merged.startDate = getStartOfByMode(merged.startDate, merged.mode);
      }
      onChange && onChange(merged);
      // console.log(value);
      // onChange(_.clone(_.merge(value || {}, data as any)));
    },
    [value],
  );

  const handleModeChange = (newMode: 'year' | 'month' | 'day' | 'hour' | 'minute' | 'second') => {
    setCurrentDateType('start');
    onDateChange({
      startDate: null,
      endDate: null,
      mode: newMode,
    });
  };

  const handleMoreChange = (data: Record<string, any>) => {
    setCurrentDateType('start');
    onDateChange({
      ...value,
      startDate: null,
      endDate: null,
      ...(data || {}),
    });
  };

  const handleReset = () => {
    setCurrentDateType('start');
    onDateChange({
      startDate: null,
      endDate: null,
      mode: initialMode,
    });
  };

  const renderDefaultHeader = useCallback(
    () => (
      <div className="header">
        <div className="types">
          {DATE_TYPE.map((item) => (
            <span
              key={item.value}
              className={`type-item ${item.value === value?.mode && 'active'}`}
              onClick={() => handleModeChange(item.value as any)}
            >
              {item.label}
            </span>
          ))}
        </div>
        <div className="reset" onClick={handleReset}>
          重置
        </div>
      </div>
    ),
    [value],
  );

  const { startDate, endDate } = useMemo(() => {
    return (
      value || {
        startDate: null,
        endDate: null,
        mode: initialMode,
      }
    );
  }, [initialMode, value]);

  return (
    <FilterDatePickerWrapper>
      {!isHideHeader && (
        <>
          {props.headerRender ? (
            props.headerRender({
              value,
              dateTypeOptions: DATE_TYPE,
              onReset: handleReset,
              onModeChange: handleModeChange,
              onMoreChange: handleMoreChange,
            })
          ) : (
            <>{renderDefaultHeader()}</>
          )}
        </>
      )}
      {/* 显示选中时间区间类型和重置按钮 */}

      <div className="body">
        <div className="date-picker">
          <span
            className={`date-picker-item ${value?.startDate && 'has'} ${currentDateType === 'start' && 'hover'}`}
            onClick={() => {
              setCurrentDateType('start');
            }}
          >
            {value?.startDate ? formatShowDate(value?.startDate) : '开始时间'}
          </span>
          <span className="date-picker-split">到</span>
          <span
            className={`date-picker-item ${value?.endDate && 'has'}  ${currentDateType === 'end' && 'hover'}`}
            onClick={() => {
              setCurrentDateType('end');
            }}
          >
            {value?.endDate ? formatShowDate(value?.endDate) : '结束时间'}
          </span>
        </div>
        <CustomDatePicker
          mode={value?.mode || initialMode}
          value={currentDateType === 'start' ? startDate : endDate}
          onChange={(val: any) => {
            if (currentDateType === 'start') {
              if (value?.endDate && val > value?.endDate) {
                onDateChange?.({
                  startDate: value?.endDate,
                  endDate: val,
                });
                return;
              }
              onDateChange?.({
                startDate: val,
              });
            } else {
              if (value?.startDate && val < value?.startDate) {
                onDateChange?.({
                  startDate: val,
                  endDate: value?.startDate,
                });
                return;
              }
              onDateChange?.({
                endDate: val,
              });
            }
          }}
        />
      </div>
    </FilterDatePickerWrapper>
  );
};
