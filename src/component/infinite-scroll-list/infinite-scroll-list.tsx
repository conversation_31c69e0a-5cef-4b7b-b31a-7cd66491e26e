import { DotLoading, ErrorBlock, PullToRefresh, Toast } from 'antd-mobile'
import { forwardRef, useEffect, useImperativeHandle, useRef } from 'react'
import { useInfiniteScroll, useRequest, useUpdateEffect } from 'ahooks'
import classNames from 'classnames';
import { clone } from 'lodash';

import styles from './infinite-scroll-list.less'

interface Props {
  requestParams: any; //请求参数(参数变化清空列表并重新请求数据)
  pageSize: number; //默认每页请求数量
  getListRequest: (val: any) => Promise<any>; //加载数据的接口
  deleteItemRequest?: (val: any) => Promise<any>; //删除单条数据的接口
  onListChange: (val: any) => void; //数据改变的回调
  children?: React.ReactNode;
  defaultList?: {
    list: any[]; //列表数据
    total: number | string; //列表总数
    nextPage: number; //下一页页码
    endRow: number | string; //当前数据结束行
    pageNum: number; //当前页码
  } | any; //默认列表数据(存在默认数据，则不会首次自动加载，适用于数据回显)
  replaceItemId?: string; // 需要替换的item的id(旧数据回显时，需要更新的项)
  oldScrollTop?: number; //设置滚动条位置
  clearOldList?: () => void; //清除缓存的旧数据
}

const InfiniteScrollList: React.FC<Props> = forwardRef(({ requestParams, pageSize, getListRequest, deleteItemRequest, onListChange, children, defaultList, replaceItemId, oldScrollTop = 0, clearOldList }, ref) => {
  const listWrapRef = useRef<HTMLDivElement>(null); //列表容器

  // 获取列表
  const { data: resData, loading, loadMore, loadingMore, noMore, reload, mutate } = useInfiniteScroll((d) => {
    return getListRequest({ ...requestParams, pageNum: d?.nextPage || 1, pageSize })
  },
    {
      manual: true,
      target: listWrapRef,
      isNoMore: (d) => {
        return !d?.hasNextPage || d?.nextPage == 0 || Number(d?.endRow) >= Number(d?.total);
      },
    },
  );

  /** 回显数据 */
  const echoData = async (oldData) => {
    if (replaceItemId) {
      // 当前项可能存在修改，从新请求数据进行替换
      const index = oldData.list?.findIndex((i) => i.id === replaceItemId); //在list中的位置
      const currentPageNum = Math.ceil((index + 1) / 5);
      const res = await getListRequest({ ...requestParams, pageNum: currentPageNum, pageSize })
      const list = res?.list || [];
      const newObj = list?.find((item) => item.id === replaceItemId);
      if (newObj) {
        const newListInfo = clone(oldData);
        newListInfo.list[index] = newObj;
        mutate({ ...newListInfo, total: res.total });
        setTimeout(() => {
          listWrapRef.current?.scrollTo({ top: oldScrollTop, behavior: 'auto' });
        });
        clearOldList();
        return;
      }
    }
    //直接回显
    mutate(oldData);
    setTimeout(() => {
      listWrapRef.current?.scrollTo({ top: oldScrollTop, behavior: 'auto' });
    });
    clearOldList();
  }

  //删除单条数据接口，无删除需求可不传
  const { run: remove } = useRequest((id) => {
    if (deleteItemRequest) {
      return deleteItemRequest(id)
    } else {
      return Promise.resolve()
    }
  }, {
    manual: true,
    onSuccess: (res, [id]) => {
      if (res.code === '1' && resData) {
        Toast.show({ icon: 'success', content: '删除成功' });
        const newListInfo = clone(resData);
        const index = newListInfo.list?.findIndex((i) => i.id === id); //在list中的位置
        if (noMore) {
          //如果加载完毕了就直接删除
          newListInfo?.list.splice(index, 1);
          mutate({ ...newListInfo });
        } else {
          // 未加载完毕的情况
          // 适用于后台接口只能pageNUm请求的情况，如果能支持nextRow或startRow通过起始位置查询列表。则不用这么麻烦
          // 由于删除后数据前移1条，需加载填充一条，避免后续的请求滚动请求覆盖不到前移的这条数据
          getListRequest({ ...requestParams, pageNum: resData.pageNum, pageSize }).then((res) => {

            newListInfo?.list.splice(index, 1);
            newListInfo?.list.push(res.list?.lastItem);
            // 将最新的total值赋值给resData.total以判断是否加载完毕
            mutate({ ...newListInfo, total: res.total });
          })
        }
      }
    },
    onError: (err) => {
      console.log("========err", err)
      Toast.show({ icon: 'fail', content: '出错了，请重试' });
    },
  });

  // 将删除接口、修改行数据方法暴露给父组件
  useImperativeHandle(ref, () => ({
    removeItem(id) {
      remove(id);
    },
    changeItem(id, newData) {
      let newListInfo = clone(resData);
      newListInfo.list = newListInfo.list?.map((item) => {
        if (item.id === id) {
          return { ...item, ...newData };
        }
        return item;
      })

      // 外部修改的，在加载新数据后会被覆盖，故须使用mutate修改数据
      mutate({ ...newListInfo });
    }
  }));

  //筛选参数变化，数据重置，重新获取数据
  useUpdateEffect(() => {
    reload();
  }, [requestParams])

  useEffect(() => {
    onListChange(resData)
  }, [resData])

  //判断是回显数据还是首次加载数据
  useEffect(() => {
    if (defaultList?.list?.length) {
      echoData(defaultList)
    } else {
      reload();
    }
  }, [])

  return (
    <div ref={listWrapRef} className={classNames(styles.scrollContent, 'list-wrap')} id='infinite-scroll-list-content'>
      <PullToRefresh
        onRefresh={async () => {
          // 下拉刷新
          reload();
        }}
      >
        {
          loading ?
            <div className={styles.loading}> <span>数据加载中</span> <DotLoading color='#333333' /></div> : (
              resData?.list?.length > 0 ? (
                <>
                  {children}
                  <div className={styles.footer}>
                    {!noMore && loadingMore && (
                      <span>
                        正在加载更多...
                      </span>
                    )}
                    {noMore && <span>无更多数据~</span>}
                  </div>
                </>
              ) : <div className={styles.loading}><ErrorBlock status='empty' /></div>
            )
        }
      </PullToRefresh>
    </div>
  )
})

export default InfiniteScrollList;
