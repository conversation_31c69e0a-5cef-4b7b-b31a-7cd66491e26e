import {
  Popup,
  <PERSON><PERSON>,
  CheckList,
  SearchBar,
  Button,
  ErrorBlock
} from 'antd-mobile'
import { useState, FC, useEffect } from 'react'
import styles from './popup-list.component.less'
import { propsOptions } from './popup-list'
import { LeftOutline, RightOutline, CloseOutline } from 'antd-mobile-icons'
import CheckedTree from "@/component/checked-tree/checked-tree.component"
const PopupList: FC<propsOptions> = ({
  titleRender,
  isTree = false,
  onChange,
  multiple = false,
  value,
  isSearch = false,
  fieldNames = { value: "value", label: "label" },
  dataSources,
  title,
  defValue,
  placeholder = "请选择",
  customComponent,
  disabled = false
}) => {
  const [visible, setvisible] = useState<boolean>(false)
  const [choseData, setChoseData] = useState<string[]>([])
  const [searchText, setSearchText] = useState<string>('')
  const filterList = dataSources?.filter(item => {//搜索列表

    if (searchText !== "") {
      if (isTree) {
        return false
      } else {
        return item[fieldNames.label].includes(searchText)
      }
    } else {
      return true
    }
  })
  const choseList = dataSources.filter(item => {//已选中列表
    return choseData.includes(item[fieldNames.value])
  })
  useEffect(() => {
    if (defValue) {
      defValueHandle()
    }
  }, [defValue])
  useEffect(() => {
    showHandle()
  }, [value])
  useEffect(() => {
    !visible && setSearchText("")
  }, [visible])
  function defValueHandle() {//默认值处理
    if (typeof defValue === "string") {
      setChoseData([defValue])
    } else if (Array.isArray(defValue)) {
      setChoseData(defValue)
    }
  }
  function delItemHandle(key: string) {//选中删除
    const list = choseData.filter(item => item !== key)
    onChange(list)
    setChoseData(list)
  }
  function choseListComponent() {//选中列表模板
    return choseList.map(item => {
      return <div key={item[fieldNames.value]} className={styles.item}>
        <Popover
          content={item[fieldNames.label]}
          trigger='click'
          placement='top'
        >
          <span className={`${styles.label} ellipsis`}>{item[fieldNames.label]}</span>
        </Popover>

        {disabled ? null : <CloseOutline onClick={() => delItemHandle(item[fieldNames.value])} />}
      </div>
    })
  }
  function showHandle() {//label回显处理
    if (value) {
      const val = Array.isArray(value) ? value : [value]
      setChoseData(val)
    } else {
      setChoseData([])
    }
  }
  function openHandle() {//打开弹框处理
    setvisible(true)
  }
  function treeChange(ids: string[], list: any[], node: any) {//树型改变
    if (ids.length === 0) {
      setChoseData([])
    } else {
      setChoseData([node[fieldNames.value]])
    }
  }
  function checkListChange(val: string[]) {//列表改变
    setChoseData(val)
  }
  function getTreeSingleData(list: any[], id: string): any {//获取树节点某个数据
    let data = {} as any
    list.find(item => {
      if (item[fieldNames.value] === id) {
        data = item
        return true
      } else if (item.children) {
        const cur = getTreeSingleData(item.children, id)
        if (cur[fieldNames.value]) {
          data = cur
        }
      }
    })
    return data
  }
  function labelShow() {//选中label展示
    if (isTree) {
      const id = choseData.find((item, index) => index === 0) || ""
      const data = getTreeSingleData(dataSources, id)
      return data?.[fieldNames.label];
    } else {
      if (multiple) {
        const val = Array.isArray(value) ? value : [value]
        const list = dataSources.filter(item => val.includes(item[fieldNames.value]))
        return list.map(item => item[fieldNames.label]).join(",")
      } else {
        const findObj = dataSources.find((item) => item[fieldNames.value] === value);
        if (!findObj) return '';
        return findObj?.[fieldNames.label];
      }
    }

  }
  function sureHandle() {//确定处理
    let list: string[] | string = JSON.parse(JSON.stringify(choseData))
    if (!multiple) {
      list = list.length > 0 ? list[0] : ""
    }
    onChange(list)
    setvisible(false)
  }
  function closeHandle() {//关闭处理
    showHandle()
    setvisible(false)
  }
  return <div>
    {!disabled ? <div className={`${multiple ? styles.text : null}`} onClick={openHandle}>
      {choseData.length > 0 && multiple === false ?
        <>
          {
            labelShow() ?
              <span className={styles.value}>{labelShow()}</span> :
              <span className={styles.placeholder}>{placeholder}<RightOutline /></span>
          }
        </> :
        <span className={styles.placeholder}>{placeholder}<RightOutline /></span>
      }
    </div> : null}
    {multiple ? <div className={styles["label-list-container"]}>
      {choseListComponent()}
    </div> : null}
    <Popup
      visible={visible}
      onMaskClick={() => {
        showHandle()
        setvisible(false)
      }}
      onClose={() => {
        showHandle()
        setvisible(false)
      }}
      bodyStyle={{ height: '60vh' }}
    >
      <div className={`${styles['popup-container']}`}>
        <div className={styles["title-container"]}>
          <LeftOutline onClick={closeHandle} className={styles.close} />
          <span className={styles.title}>{title}</span>
        </div>
        {isSearch && dataSources.length > 0 && isTree === false ? <div className={styles.search}>
          <SearchBar
            placeholder='输入名称搜索'
            value={searchText}
            onChange={v => {
              setSearchText(v)
            }}
          />
        </div> : null}
        <div className={styles["check-list-container"]}>
          {filterList.length > 0 ? customComponent ? customComponent : <>
            {isTree ?
              <CheckedTree titleRender={titleRender} fields={fieldNames} checkStrictly={false} onChange={treeChange} value={choseData} treeData={filterList}>
              </CheckedTree>
              :
              <CheckList multiple={multiple} onChange={checkListChange} value={choseData}>
                {
                  filterList.map(item => {
                    return <CheckList.Item key={item[fieldNames.value]} value={item[fieldNames.value]}>
                      {titleRender ? titleRender(item) : item[fieldNames.label]}
                    </CheckList.Item>
                  })
                }
              </CheckList>}
          </> :
            <ErrorBlock
              image='https://gw.alipayobjects.com/zos/antfincdn/ZHrcdLPrvN/empty.svg'
              style={{
                '--image-height': '150px',
              }}
              status='empty'
            >
            </ErrorBlock>

          }

        </div>
        {filterList.length > 0 ? <div className={styles.btn}>
          <Button onClick={sureHandle} className="solid" block color='primary' fill='solid'>
            确定
          </Button>
        </div> : null}
      </div>
    </Popup>
  </div>
}

export default PopupList