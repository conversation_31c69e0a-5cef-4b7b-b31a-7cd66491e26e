.popup-container {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.placeholder {
    font-size: 46px;
    color: #CCD1DE;
}

.title-container {
    position: relative;
    padding: 20px;
}

.close {
    position: absolute;
    color: #383838;
    font-size: 50px;
    left: 30px;
    top: 30px;
}

.title {
    text-align: center;
    color: #383838;
    font-size: 48px;
    width: 100%;
    display: inline-block;
}

.search {
    padding: 12px;
}

.check-list-container {
    flex: 1;
    flex-basis: 0;
    overflow-y: auto;
}

.btn {
    padding: 20px;
    background: #fff;
}

.text {
    position: absolute;
    right: 30px;
    top: 30px;
}

.label-list-container {
    display: flex;
    width: 100%;
    gap: 35px;
    flex-wrap: wrap;

    .item {
        border-radius: 40px;
        color: #333333;
        font-size: 42px;
        background: #F5F5F5;
        padding: 10px 30px 5px 30px;
        display: flex;
        align-items: center;
    }

    .label {
        display: inline-block;
        max-width: 300px;
        margin-right: 32px;
    }
}