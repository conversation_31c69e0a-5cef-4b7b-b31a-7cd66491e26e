import { fieldNames } from '../search-menu/search-menu'
import { ReactNode } from "react";
export interface propsOptions {
    title: string;
    dataSources: any[];
    fieldNames?: fieldNames;
    isTree?:boolean;//是否树形结构
    placeholder?: string;//提示文字
    isSearch?: boolean;//是否需要搜索框
    value?: string | string[];//表单值
    defValue?:string | string[];//默认选中值
    onChange?: (val: string[]|string) => void;//表单赋值方法
    multiple?: boolean;//是否多选
    customComponent?: JSX.Element;//自定义内容
    titleRender?:(data:any)=>ReactNode;//列表内容自定义
    disabled?: boolean;//禁用。默认false
}