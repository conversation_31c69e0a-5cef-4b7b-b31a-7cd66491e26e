import { useState, useCallback, useMemo, useEffect } from 'react';
import { Button, DatePickerProps, Form, Popup } from 'antd-mobile';
import { styled } from 'umi';
import dayjs from 'dayjs';
import { FilterDatePicker as DatePicker, FilterDatePickerSingle as DatePickerSingle } from '@/component/filter-datepicker';

// 类型定义优化
type DateMode = 'year' | 'month' | 'day' | 'hour' | 'minute' | 'second';
type DateValue = {
  date?: number;
  startDate?: number;
  endDate?: number;
  mode: DateMode;
};

type DatePickerPopupProps = DatePickerProps & {
  dateType?: 'single' | 'range';
  title?: string;
  initialMode: 'year' | 'month' | 'day' | 'hour' | 'minute' | 'second';
  value: DateValue;
  children: (value: DateValue) => React.ReactNode;
  onChange: (value: DateValue) => void;
  format?: string;
};

// 样式组件优化
const PopupTitle = styled.div`
  text-align: center;
  font-size: 4.4444vw;
  color: #000;
  margin: 2.9631vw 0;
`;

const PopupContainer = styled.div`
  flex: 1;
  border-top: 1px solid #e6e6e6;
  border-bottom: 1px solid #e6e6e6;
  margin-bottom: 3.3333vw;

  .adm-picker-view {
    --height: 50vh;
  }
`;

const PopupFooter = styled.div`
  margin: 0 3.3333vw 2.2222vw;
  border-radius: 3.3333vw;
  overflow: hidden;
  border: 0.3703vw solid #1154ed;

  .adm-button {
    --border-radius: 3.3333vw;
    height: 11.1111vw;
    font-size: 3.8881vw;
  }
`;

const DateText = styled.div<{ $hasValue: boolean }>`
  font-size: 3.8889vw;
  line-height: 5.6314vw;
  margin-bottom: 2.4075vw;
  color: ${(props) => (props.$hasValue ? '#000' : '#999')};
  cursor: pointer;
`;

// 日期格式化工具函数
const formatDateValue = (value: DateValue, format: string, dateType: 'single' | 'range') => {
  if (dateType === 'single') {
    return value?.date ? dayjs(value?.date).format(format) : '请选择时间';
  }
  return value?.startDate && value?.endDate
    ? `${dayjs(value?.startDate).format(format)} ~ ${dayjs(value?.endDate).format(format)}`
    : '请选择时间范围';
};

const DatePickerPopup = ({
  title = '请选择',
  dateType = 'single',
  children,
  value,
  onChange,
  format = 'YYYY-MM-DD HH:mm',
  initialMode = 'day',
  ...pickerProps
}: DatePickerPopupProps) => {
  const [visible, setVisible] = useState(false);
  const [localValue, setLocalValue] = useState<any>(value);

  const handleConfirm = useCallback(() => {
    onChange(localValue);
    setVisible(false);
  }, [localValue, onChange]);

  useEffect(() => {
    onChange(value);
  }, []);

  const PickerComponent = useMemo(() => (dateType === 'single' ? DatePickerSingle : DatePicker), [dateType]);

  return (
    <>
      <DateText $hasValue={!!(dateType === 'single' ? value?.date : value?.startDate)} onClick={() => setVisible(true)}>
        {children?.(value) || formatDateValue(value, format, dateType)}
      </DateText>

      <Popup
        visible={visible}
        onMaskClick={() => setVisible(false)}
        showCloseButton
        destroyOnClose
        onClose={() => setVisible(false)}
        bodyStyle={{
          borderTopLeftRadius: '0.7408vw',
          borderTopRightRadius: '0.7408vw',
          minHeight: '60vh',
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        <PopupTitle>{title}</PopupTitle>

        <PopupContainer>
          <PickerComponent
            {...pickerProps}
            initialMode={initialMode}
            value={localValue}
            onChange={(v: any) => {
              setLocalValue(v);
            }}
          />
        </PopupContainer>

        <PopupFooter>
          <Button
            color="primary"
            block
            onClick={handleConfirm}
            style={{
              '--background-color': '#1154ed',
              '--border-color': '#1154ed',
            }}
          >
            确定
          </Button>
        </PopupFooter>
      </Popup>
    </>
  );
};

// 表单项组件优化
export const DatePickerItem = (props: DatePickerItemProps) => {
  const { datePickerConfig = {}, format = 'YYYY-MM-DD HH:mm', ...formItemProps } = props;

  return (
    <Form.Item {...formItemProps} layout={datePickerConfig.dateType === 'single' ? 'horizontal' : 'vertical'}>
      <DatePickerPopup {...datePickerConfig}>{(value) => formatDateValue(value, format, datePickerConfig.dateType!)}</DatePickerPopup>
    </Form.Item>
  );
};
