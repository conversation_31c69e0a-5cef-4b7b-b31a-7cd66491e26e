## **DatePickerPopup 和 DatePickerItem 使用文档**

### **1. 组件概述**
- **[DatePickerPopup](file://\src\component\dynamic-form\items\date-picker-popup\date-picker-popup.component.tsx#L77-L153)**：一个自定义的日期选择器弹框组件，支持单日期和日期范围选择。
- **[DatePickerItem](file://\src\component\dynamic-form\items\date-picker-popup\date-picker-popup.component.tsx#L156-L169)**：基于 [DatePickerPopup](file://\src\component\dynamic-form\items\date-picker-popup\date-picker-popup.component.tsx#L77-L153) 的表单项组件，用于在表单中集成日期选择功能。

---

### **2. 组件依赖**
- **依赖库**：
  - `antd-mobile`：用于基础组件（如 `Popup`、`Button` 等）。
  - `dayjs`：用于日期格式化。
  - `umi`：用于样式组件（`styled`）。

---

### **3. 组件 Props**

#### **[DatePickerPopup](file://\src\component\dynamic-form\items\date-picker-popup\date-picker-popup.component.tsx#L77-L153) Props**
| 属性名         | 类型                          | 默认值      | 描述                                                                 |
|----------------|-------------------------------|-------------|----------------------------------------------------------------------|
| [title](file://\src\component\popup-user\index.tsx#L15-L15)        | `string`                      | `'请选择'`  | 弹框标题。                                                           |
| [dateType](file://\src\component\dynamic-form\items\date-picker\date-picker.component.tsx#L9-L9)     | `'single' \| 'range'`         | `'single'`  | 日期选择类型，支持单日期（`single`）和日期范围（`range`）。          |
| [value](file://\src\component\popup-user\index.tsx#L9-L9)        | [DateValue](file://\src\component\dynamic-form\items\date-picker-popup\date-picker-popup.component.tsx#L11-L16)                   | -           | 当前选中的日期值，具体结构见 [DateValue](file://\src\component\dynamic-form\items\date-picker-popup\date-picker-popup.component.tsx#L11-L16) 类型定义。                  |
| [onChange](file://\src\component\popup-user\index.tsx#L10-L10)     | `(value: DateValue) => void`  | -           | 日期选择确认后的回调函数。                                           |
| [format](file://\src\component\upload-file\upload-file.ts#L15-L15)       | `string`                      | `'YYYY-MM-DD HH:mm'` | 日期格式化字符串。                                               |
| [children](file://\src\utils\tree.ts#L10-L10)     | `(value: DateValue) => React.ReactNode` | - | 自定义渲染日期显示内容。                                     |
| 其他 [DatePickerProps](file://\src\component\dynamic-form\items\date-picker\date-picker.component.tsx#L8-L10) | - | - | 支持 `antd-mobile` 的 `DatePicker` 组件的其他属性。                  |

#### **[DateValue](file://\src\component\dynamic-form\items\date-picker-popup\date-picker-popup.component.tsx#L11-L16) 类型定义**
```typescript
type DateValue = {
  date?: number;        // 单日期的时间戳
  startDate?: number;   // 日期范围的开始时间戳
  endDate?: number;     // 日期范围的结束时间戳
  mode: DateMode;       // 日期选择模式（如 'year', 'month', 'day' 等）
};
```

#### **[DatePickerItem](file://\src\component\dynamic-form\items\date-picker-popup\date-picker-popup.component.tsx#L156-L169) Props**
| 属性名            | 类型                          | 默认值      | 描述                                                                 |
|-------------------|-------------------------------|-------------|----------------------------------------------------------------------|
| `datePickerConfig`| [DatePickerPopupProps](file://\src\component\dynamic-form\items\date-picker-popup\date-picker-popup.component.tsx#L18-L25)        | -           | [DatePickerPopup](file://\src\component\dynamic-form\items\date-picker-popup\date-picker-popup.component.tsx#L77-L153) 的配置项。                                         |
| 其他 `Form.Item` Props | - | - | 支持 `antd-mobile` 的 `Form.Item` 组件的其他属性。                  |

---

### **4. 使用示例**

#### **示例 1：使用 [DatePickerPopup](file://\src\component\dynamic-form\items\date-picker-popup\date-picker-popup.component.tsx#L77-L153)**
```typescript
import React, { useState } from 'react';
import { DatePickerPopup } from '@/component/dynamic-form/items/date-picker-popup';

const MyComponent = () => {
  const [value, setValue] = useState({
    date: Date.now(),
    mode: 'day',
  });

  return (
    <DatePickerPopup
      title="选择日期"
      dateType="single"
      value={value}
      onChange={setValue}
    >
      {(value) => (value.date ? dayjs(value.date).format('YYYY-MM-DD') : '请选择日期')}
    </DatePickerPopup>
  );
};
```

#### **示例 2：使用 `DatePickerItem`**
```typescript
import React from 'react';
import { Form } from 'antd-mobile';
import { DatePickerItem } from '@/component/dynamic-form/items/date-picker-popup';

const MyForm = () => {
  return (
    <Form>
      <DatePickerItem
        name="date"
        label="选择日期"
        datePickerConfig={{
          dateType: 'single',
          value: { date: Date.now(), mode: 'day' },
          onChange: (value) => console.log(value),
        }}
      />
    </Form>
  );
};
```

---

### **5. 样式定制**
- **`PopupTitle`**：弹框标题样式。
- **`PopupContainer`**：弹框内容区域样式。
- **`PopupFooter`**：弹框底部按钮区域样式。
- **`DateText`**：日期显示文本样式。

可以通过 `styled` 组件修改默认样式，例如：
```typescript
const CustomPopupTitle = styled(PopupTitle)`
  font-size: 5vw;
  color: #1154ed;
`;
```

---

### **6. 注意事项**
- **日期格式**：默认使用 `YYYY-MM-DD HH:mm` 格式，可以通过 `format` 属性自定义。
- **日期范围**：当 `dateType` 为 `range` 时，`value` 需要包含 `startDate` 和 `endDate`。
- **表单集成**：`DatePickerItem` 可以直接在 `Form` 中使用，支持 `Form.Item` 的所有属性。

---