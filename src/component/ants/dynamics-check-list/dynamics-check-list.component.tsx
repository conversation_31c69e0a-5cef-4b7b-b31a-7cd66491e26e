import { List, Space } from 'antd-mobile';
import CheckInput from '../check-input/check-input.component';
import { AddCircleOutline, MinusCircleOutline } from 'antd-mobile-icons';
import { styled } from 'umi';

const DynamicsCheckListWrapper = styled(List)`
  .adm-list-item-content-main{
    padding-top: 0;
  }
`

interface DynamicsItemProps {
  checked: boolean;
  value: string;
}

interface DynamicsCheckListProps {
  value: DynamicsItemProps[];
  onChange: (value: DynamicsItemProps[]) => void;
}

const DynamicsCheckList = (props) => {
  const {
    value = [
      {
        checked: false,
        value: '',
      },
    ],
    onChange,
  } = props;
  console.log(props);
  return (
    <DynamicsCheckListWrapper
      style={{
        '--padding-left': '0',
      }}
    >
      {value?.map((val, index) => (
        <List.Item
          extra={
            <Space>
              <AddCircleOutline
                fontSize={22}
                onClick={() => {
                  onChange?.([...value, { checked: false, value: '' }]);
                }}
              />
              {value?.length > 1 && (
                <MinusCircleOutline
                  fontSize={22}
                  onClick={() => {
                    onChange?.(value.filter((v, i) => i !== index));
                  }}
                />
              )}
            </Space>
          }
          key={val.value}
          style={{
            padding: '0',
          }}
        >
          <CheckInput
            data={val}
            onChange={(val) => {
              onChange?.(value?.map((v,i)=>i === index?val:v));
            }}
          />
        </List.Item>
      ))}
    </DynamicsCheckListWrapper>
  );
};

export default DynamicsCheckList;
