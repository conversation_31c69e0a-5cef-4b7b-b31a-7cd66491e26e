import { Checkbox, Input, Space } from 'antd-mobile';
import { AddCircleOutline, MinusCircleOutline } from 'antd-mobile-icons';
import { useEffect, useState } from 'react';
import { styled } from 'umi';

const CheckInputWrapper = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 100%;
  .adm-input {
    margin-left: 2.963vw;
    flex: 1;
  }
`;
/**
 * 可输入的复选框
 * @param props
 */
const CheckInput = (props) => {
  const { data, onChange } = props;
  const [inputValue, setInputValue] = useState(data);

  useEffect(() => {
    setInputValue(data.value);
  }, [data.value]);

  return (
    <CheckInputWrapper block align="center">
      <Checkbox checked={!!data?.checked} onChange={(checked) => onChange?.({ ...data, checked })} />
      <Input
        value={inputValue}
        onChange={(val)=>{
          setInputValue(val)
        }}
        onBlur={(e) => {
          onChange?.({ ...data, value: e.target.value })
        }}
        placeholder="请输入"
        style={{
          '--font-size': '3.8889vw',
          borderBottom: '1px solid #ccc',
          width: '100%',
        }}
      />
    </CheckInputWrapper>
  );
};

export default CheckInput;
