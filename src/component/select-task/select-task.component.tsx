import { RightOutline } from 'antd-mobile-icons';
import styles from './select-task.component.less';
import { <PERSON><PERSON>, CascaderView, Popup, Toast } from 'antd-mobile';
import { CloseOutline } from 'antd-mobile-icons';
import { useEffect, useState } from 'react';
import { CascaderValue, CascaderValueExtend } from 'antd-mobile/es/components/cascader-view';
import useBuildPlanTree from '@/hooks/useBuildPlanTree';
import { deepFindParents } from '@/pages/quality-manage/quality-manage.utils';
interface Props {
  value?: string,
  onChange?: (value: string, item) => void;
}
const defaultSelectedData = {
  value: [],
  extend: { isLeaf: false, items: [] }
}

const SelectTask: React.FC<Props> = ({ value, onChange }) => {
  const [visible, setVisible] = useState(false);
  const [confirmList, setConfirmList] = useState<{ label: string, value: string }[]>([]);
  const [selectdData, setSelectedData] = useState<{
    value: CascaderValue[],
    extend: CascaderValueExtend
  }>(defaultSelectedData);

  const { loading, buildPlanTree, getBuildPlanTreeData } = useBuildPlanTree();

  useEffect(() => {
    if (value && buildPlanTree?.length) {
      const items = deepFindParents(buildPlanTree, value);
      if (!items?.length) return;
      const { idPath, label } = items[0];
      setSelectedData({
        value: idPath.slice(5, idPath.length).split('/'),//将路径从root后切割并组成数组
        extend: { isLeaf: true, items }
      })
      setConfirmList([{
        label,
        value
      }])
    }
  }, [value, buildPlanTree])

  const handleChange = (value: CascaderValue[], extend: CascaderValueExtend) => {
    setSelectedData({ value, extend })
  }

  const handleConfirm = () => {
    const { isLeaf, items } = selectdData.extend;
    if (isLeaf) {
      const confirmData = items[items.length - 1];
      setVisible(false);
      setConfirmList([confirmData])
      onChange?.(confirmData.value, confirmData);
    } else {
      Toast.show({
        content: '请选择结构部位'
      })
    }
  }

  const handleCancel = () => {
    setSelectedData(defaultSelectedData)
    setVisible(false)
  }

  useEffect(() => {
    getBuildPlanTreeData()
  }, [])

  return (
    <div className={styles.selectItem} onClick={() => { setVisible(true) }}>
      <span className={confirmList?.length ? styles.value : styles.placeholder}>
        {confirmList?.length ? confirmList.map((item) => (
          `${item.label}`
        )) : '请选择'}
      </span>
      <RightOutline />
      <Popup
        visible={visible}
        getContainer={null}
        onMaskClick={() => {
          setVisible(false)
        }}
        onClose={() => {
          setVisible(false)
        }}
        bodyStyle={{ borderRadius: '8px 8px 0 0' }}
      >
        <div className={styles.popupTitle}><span />选择结构部位<CloseOutline onClick={() => { setVisible(false) }} /></div>
        <CascaderView loading={loading} options={buildPlanTree} value={selectdData.value} onChange={handleChange} />
        <div className={styles.btnGroups}>
          <Button color='primary' fill='outline' onClick={handleCancel}>取消</Button>
          <Button color='primary' onClick={handleConfirm}>确定选择</Button>
        </div>
      </Popup>
    </div>
  )
};
export default SelectTask;
