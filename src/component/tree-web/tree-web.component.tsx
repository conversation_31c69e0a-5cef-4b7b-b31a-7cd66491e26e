import React, { memo, useState } from 'react';
import type { ReactNode, FC } from 'react';
import classNames from 'classnames';
import { DownOutline, UpOutline } from 'antd-mobile-icons';

import styles from './tree-web.style.less';
import type { ITreeNode } from './tree-web.types';

interface IProps {
  children?: ReactNode;
  root: ITreeNode;
  selectedTreeNode: ITreeNode;
  onTreeNodeClick: (treeNode: ITreeNode) => void;
}

const TreeWeb: FC<IProps> = (props) => {
  const { root, selectedTreeNode, onTreeNodeClick } = props;

  const [isExpanding, setIsExpanding] = useState(root.engineeringType === '1'); // 默认展开一层

  return (
    <>
      <div className={classNames([{ [styles['selected']]: selectedTreeNode?.value === root.value }, styles['tree-node']])}>
        <div className={classNames(styles['label'], styles['ellipsis'])} onClick={() => onTreeNodeClick({ ...root })}>
          <span style={{ marginLeft: Number(root.engineeringType) * 2 + 'rem' }}>{root.label}</span>
        </div>
        {!!root.children?.length && (
          <div className={styles['icon']} onClick={() => setIsExpanding(!isExpanding)}>
            {isExpanding ? <UpOutline color="#3c69cd" /> : <DownOutline color="#989898" />}
          </div>
        )}
      </div>
      {isExpanding &&
        root.children?.map((treeNode) => {
          return <TreeWeb key={treeNode.id} root={treeNode} selectedTreeNode={selectedTreeNode} onTreeNodeClick={onTreeNodeClick} />;
        })}
    </>
  );
};

export default memo(TreeWeb);
