import { ReactNode } from 'react';
import { TreeNodeType, fieldsOptions } from '../checked-tree/checked-tree.component';

// 自定义树节点数据结构
export interface CustomTreeNode {
  id: string;
  key: string;
  title?: ReactNode;
  name: string;
  parentId?: string;
  children?: CustomTreeNode[];
  [key: string]: any;
}

// 自定义输入转换成对象的接口定义
export interface CustomInputNode extends CustomTreeNode {
  custom: boolean;
  checked: boolean;
  username: string;
}

export interface TreePickerProps {
  value?: string[] | string | any[];
  onChange?: (value: any[]) => void;
  onConfirm?: (value: any[]) => void;
  placeholder?: string;
  title?: string;
  treeData: TreeNodeType[];
  multiple?: boolean;
  fields?: fieldsOptions;
  checkStrictly?: boolean;
  disabledFilter?: (node: any) => boolean;
  // 新增支持传入的属性
  titleRender?: (nodeData: CustomTreeNode) => ReactNode;
  switcherIcon?: ReactNode | ((props: { expanded: boolean }) => ReactNode);
  defaultExpandAll?: boolean;
  showIcon?: boolean;
  onExpand?: (expandedKeys: React.Key[], info: any) => void;
  bodyStyle?: React.CSSProperties;
  // 添加新的控制节点标题渲染的props
  nodeTitleRender?: (nodeData: CustomTreeNode, selectNodes: CustomTreeNode[]) => ReactNode;
  // 显示字段配置
  displayField?: keyof CustomTreeNode;
  // 最大显示数量，超过则显示"已选择x项"
  displayMaxCount?: number;
  // 自定义显示内容
  displayRender?: (selectedNodes: CustomTreeNode[]) => ReactNode;
  // 节点类型过滤器，用于限制只能选择特定类型的节点
  nodeTypeFilter?: string | string[];
  // 控制是否显示用户节点，如果为false则忽略person类型的节点
  showUsers?: boolean;
  // 控制是否只能选择叶子节点（没有子节点的节点）
  leafNodesOnly?: boolean;
  // 添加自定义模式选项
  customMode?: boolean;
  // 自定义输入的回调函数
  onCustomInputChange?: (values: string[]) => void;
  // 分隔符，默认为空格、逗号、句号、分号、顿号、连字符等
  // 支持转义：使用反斜杠(\)前缀的分隔符将被视为普通字符，例如: \, \. \、 \- 等
  // 完整的分隔符列表包括：空格、逗号（,）、中文逗号（，）、句号（.）、中文句号（。）、分号（;）、中文分号（；）、顿号（、）和连字符（-）
  separators?: string | RegExp;
  // 非自定义模式下的布局方式
  layout?: 'vertical' | 'horizontal';
  // 是否对用户去重，为true时会移除重复的用户，只保留一个实例
  // 为false时会保留完整的组织层级信息，返回格式为 "组织ID_用户ID"
  deduplicateUsers?: boolean;
  // 返回完整的节点数据而不是仅ID
  returnNodeObjects?: boolean;
}

export interface TreePickerRef {
  open: () => void;
  close: () => void;
  getAllSelectedNodeData: () => (CustomTreeNode | CustomInputNode)[];
  getAllSelectedNodeIds: () => string[];
}

export interface TreeNodeRendererProps {
  nodeData: CustomTreeNode;
  selectedNodes: CustomTreeNode[];
  multiple: boolean;
  isNodeSelectable: (node: CustomTreeNode) => boolean;
  nodeTitleRender?: (nodeData: CustomTreeNode, selectNodes: CustomTreeNode[]) => ReactNode;
  onSelect: (e: React.MouseEvent, nodeData: CustomTreeNode) => void;
}

export interface CustomTreeProps {
  treeData: CustomTreeNode[];
  treeHeight: number;
  defaultExpandAll: boolean;
  showIcon: boolean;
  selectedNodes: CustomTreeNode[];
  multiple: boolean;
  switcherIcon?: ReactNode | ((props: { expanded: boolean }) => ReactNode);
  titleRender?: (nodeData: CustomTreeNode) => ReactNode;
  nodeTitleRender?: (nodeData: CustomTreeNode, selectNodes: CustomTreeNode[]) => ReactNode;
  onExpand?: (expandedKeys: React.Key[], info: any) => void;
  isNodeSelectable: (node: CustomTreeNode) => boolean;
  onSelect: (e: React.MouseEvent, nodeData: CustomTreeNode) => void;
}

export interface CustomModeProps {
  customInput: string;
  customNodes: CustomInputNode[];
  selectedNodes: CustomTreeNode[];
  displayField: keyof CustomTreeNode;
  onCustomInputChange: (value: string) => void;
  onSelectButtonClick: () => void;
  setSelectedNodes: React.Dispatch<React.SetStateAction<CustomTreeNode[]>>;
  setCustomNodes: React.Dispatch<React.SetStateAction<CustomInputNode[]>>;
  setCustomValues: React.Dispatch<React.SetStateAction<string[]>>;
  customValues: string[];
  onCustomNodesChange?: (values: string[]) => void;
  multiple?: boolean;
} 