import React, { forwardRef, useImperativeHandle } from 'react';
import { Popup, Button, Space, Form } from 'antd-mobile';
import styles from './tree-picker.component.less';
import { SearchOutline } from 'antd-mobile-icons';
import TwoTicketsNavBar from '../twotickets-navbar/twotickets-navbar.component';
import { useTreePicker } from './hooks/useTreePicker';
import { useTreeHeight } from './hooks/useTreeHeight';
import CustomTree from './components/CustomTree';
import CustomMode from './components/CustomMode';
import { TreePickerProps, TreePickerRef, CustomTreeNode } from './types';
import { convertTreeData } from './utils';

const TreePicker = forwardRef<TreePickerRef, TreePickerProps>((props, ref) => {
  const {
    title = '请选择',
    bodyStyle = {},
    customMode = false,
    multiple = true,
    titleRender,
    switcherIcon,
    defaultExpandAll = true,
    showIcon = false,
    onExpand,
    nodeTitleRender,
    displayField = 'name',
    onCustomInputChange,
  } = props;

  // 使用自定义Hook管理树选择器状态和逻辑
  const {
    visible,
    selectedNodes,
    treeHeight,
    isHeightCalculated,
    displayText,
    customInput,
    customValues,
    customNodes,
    containerRef,
    footerRef,
    navbarHeight,
    setVisible,
    setSelectedNodes,
    setTreeHeight,
    setIsHeightCalculated,
    setCustomNodes,
    setCustomValues,
    handleConfirm,
    handleSelect,
    handleSelectButtonClick,
    handleCustomInputChange,
    isNodeSelectable,
    openPicker,
    closePicker,
    getAllSelectedNodeData,
    getAllSelectedNodeIds
  } = useTreePicker(props);

  // 使用自定义Hook管理树高度计算
  useTreeHeight(
    footerRef,
    navbarHeight,
    visible,
    isHeightCalculated,
    setTreeHeight,
    setIsHeightCalculated
  );

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    open: openPicker,
    close: closePicker,
    getAllSelectedNodeData,
    getAllSelectedNodeIds,
  }));

  // 转换树数据格式
  const customTreeData = convertTreeData(props.treeData);

  return (
    <>
      {customMode ? (
        <CustomMode
          customInput={customInput}
          customNodes={customNodes}
          selectedNodes={selectedNodes}
          displayField={displayField}
          onCustomInputChange={handleCustomInputChange}
          onSelectButtonClick={handleSelectButtonClick}
          setSelectedNodes={setSelectedNodes}
          setCustomNodes={setCustomNodes}
          setCustomValues={setCustomValues}
          customValues={customValues}
          onCustomNodesChange={onCustomInputChange}
          multiple={multiple}
        />
      ) : (
        <div className={styles.pickerValue}>{displayText}</div>
      )}

      <Popup
        visible={visible}
        onMaskClick={() => setVisible(false)}
        position="bottom"
        bodyStyle={{
          "--header-height": "45px",
          height: '100vh',
          background: 'rgba(245, 245, 245, 33)',
          ...bodyStyle,
        } as any}
      >
        {/* 标题 */}
        <TwoTicketsNavBar
          onBack={() => setVisible(false)}
          title={title}
          bgcolor={'#fff'}
          color={'#000'}
          right={<SearchOutline className={styles.searchIcon} />}
        />

        <CustomTree
          treeData={customTreeData}
          treeHeight={treeHeight}
          defaultExpandAll={defaultExpandAll}
          showIcon={showIcon}
          selectedNodes={selectedNodes}
          multiple={multiple}
          switcherIcon={switcherIcon}
          titleRender={titleRender}
          nodeTitleRender={nodeTitleRender}
          onExpand={onExpand}
          isNodeSelectable={isNodeSelectable}
          onSelect={handleSelect}
        />

        <div className={styles.footer} ref={footerRef}>
          <div className={styles.selectedCount}>
            已选择: <span className={styles.count}>{selectedNodes.length}</span> 项
          </div>
          <Space>
            {/* @ts-ignore - 忽略类型错误，确保组件可以正常渲染 */}
            <Button color="primary" onClick={handleConfirm}>选择</Button>
          </Space>
        </div>
      </Popup>
    </>
  );
});

// Form组件适配器
export const TreePickerFormItem = forwardRef((props: TreePickerProps & {
  label?: string;
  name?: string;
  /**
   * 是否包裹Form.Item
   * @default true
   */
  wrapFormItem?: boolean;
  /**
   * Form.Item的其他属性
   */
  formItemProps?: Record<string, any>;
}, ref) => {
  const { label, name, wrapFormItem = true, formItemProps = {}, ...restProps } = props;

  // 如果不需要包裹Form.Item，直接返回TreePicker
  if (!wrapFormItem) {
    // @ts-ignore - 忽略类型错误，确保组件可以正常渲染
    return <TreePicker ref={ref} {...restProps} />;
  }

  // 确保使用Form.Item时必须提供name属性
  if (!name) {
    console.warn('TreePickerFormItem: name is required when wrapFormItem is true');
  }

  return (
    <Form.Item
      label={label}
      name={name}
      trigger="onConfirm"
      arrow={restProps.customMode ? false : undefined}
      layout={restProps.customMode ? 'vertical' : restProps.layout || 'horizontal'}
      onClick={(e, treePickerRef: React.RefObject<TreePickerRef>) => {
        treePickerRef.current?.open();
      }}
      {...formItemProps}
    >
      {/* @ts-ignore - 忽略类型错误，确保组件可以正常渲染 */}
      <TreePicker {...restProps} />
    </Form.Item>
  );
});

export default TreePicker;