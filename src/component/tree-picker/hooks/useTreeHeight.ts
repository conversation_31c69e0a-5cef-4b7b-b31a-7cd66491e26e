/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-13 14:56:13
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-05-28 18:26:11
 * @FilePath: /mobile-yw/src/component/tree-picker/hooks/useTreeHeight.ts
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */
import { useEffect, useCallback, RefObject } from 'react';
import { useSize } from 'ahooks';

export const useTreeHeight = (
  footerRef: RefObject<HTMLDivElement>,
  navbarHeight: number,
  visible: boolean,
  isHeightCalculated: boolean,
  setTreeHeight: (height: number) => void,
  setIsHeightCalculated: (calculated: boolean) => void
) => {
  // 测量footer高度
  const footerSize = useSize(footerRef);

  // 计算树的高度函数
  const calculateTreeHeight = useCallback(() => {
    if (footerRef.current) {
      // 获取容器高度
      const containerHeight = window.innerHeight;
      // 计算树的高度 = 容器高度 - 导航栏高度 - 底部高度
      const footerHeight = footerSize?.height || footerRef.current.clientHeight;
      const calculatedHeight = containerHeight - navbarHeight - footerHeight;
      setTreeHeight(calculatedHeight > 200 ? calculatedHeight : 200); // 确保最小高度
      setIsHeightCalculated(true); // 标记高度已计算
    }
  }, [footerRef, footerSize?.height, navbarHeight, setIsHeightCalculated, setTreeHeight]);

  // 在弹窗可见后计算树的高度，但只计算一次
  useEffect(() => {
    if (visible && !isHeightCalculated) {
      // 使用setTimeout确保DOM已经渲染
      const timer = setTimeout(() => {
        calculateTreeHeight();
      }, 50);
      return () => clearTimeout(timer);
    }
  }, [visible, isHeightCalculated, calculateTreeHeight]);

  // 当窗口大小变化时重新计算
  useEffect(() => {
    const handleResize = () => {
      if (visible) {
        calculateTreeHeight();
      }
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [visible, calculateTreeHeight]);

  return { calculateTreeHeight };
}; 