import { useState, useCallback, useRef, useEffect } from 'react';
import { CustomInputNode, CustomTreeNode, TreePickerProps } from '../types';
import { processUserSelection, flattenTreeData, convertTreeData, processValueToArray, extractCustomNodes, generateUniqueId, isNodeSelectable as checkNodeSelectable, deduplicateUserNodes } from '../utils';
import { message } from 'antd';

export const useTreePicker = (props: TreePickerProps) => {
  const {
    value = [],
    onChange,
    onConfirm,
    placeholder = '请选择',
    treeData = [],
    multiple = true,
    displayField = 'name',
    displayMaxCount = 2,
    displayRender,
    customMode = false,
    onCustomInputChange,
    separators = /[\s,，.。;；、\-\n\r]+/,
    deduplicateUsers = true,
    returnNodeObjects = true,
    nodeTypeFilter,
    leafNodesOnly = false,
  } = props;

  // 状态
  const [visible, setVisible] = useState(false);
  const [selectedNodes, setSelectedNodes] = useState<CustomTreeNode[]>([]);
  const [treeHeight, setTreeHeight] = useState<number>(500); // 默认高度
  const [isHeightCalculated, setIsHeightCalculated] = useState(false); // 追踪高度是否已计算
  const [displayText, setDisplayText] = useState<React.ReactNode>(placeholder); // 表单项显示文本
  const [customInput, setCustomInput] = useState<string>('');
  const [customValues, setCustomValues] = useState<string[]>([]); // 存储自定义输入的ID
  const [customNodes, setCustomNodes] = useState<CustomInputNode[]>([]); // 存储自定义输入的完整节点对象

  // Refs
  const containerRef = useRef<HTMLDivElement>(null);
  const footerRef = useRef<HTMLDivElement>(null);
  const navbarHeight = 45; // TwoTicketsNavBar固定高度

  // 检查节点是否可选择的包装函数
  const isNodeSelectable = useCallback(
    (node: CustomTreeNode) => {
      return checkNodeSelectable(node, leafNodesOnly, nodeTypeFilter);
    },
    [leafNodesOnly, nodeTypeFilter]
  );

  // 更新表单项显示文本
  const updateDisplayText = useCallback((nodes: (CustomTreeNode | CustomInputNode)[]) => {
    // 应用用户去重逻辑
    const deduplicatedNodes = deduplicateUserNodes(nodes);
    
    if (deduplicatedNodes.length === 0) {
      setDisplayText(placeholder);
      return;
    }

    // 如果有自定义显示内容，使用自定义显示
    if (displayRender) {
      setDisplayText(displayRender(deduplicatedNodes as CustomTreeNode[]));
      return;
    }

    // 根据数量显示不同内容
    if (deduplicatedNodes.length <= displayMaxCount) {
      // 显示节点名称而非ID
      const names = deduplicatedNodes.map(node => node[displayField] || node.name).join(', ');
      setDisplayText(names);
    } else {
      setDisplayText(`已选择 ${deduplicatedNodes.length} 项`);
    }
  }, [displayField, displayMaxCount, displayRender, placeholder]);

  // 获取所有选中节点的ID
  const getAllSelectedNodeIds = useCallback((): string[] => {
    const treeNodeIds = processUserSelection(selectedNodes, deduplicateUsers);
    return [...treeNodeIds, ...customValues];
  }, [selectedNodes, customValues, deduplicateUsers]);

  // 获取所有选中节点的完整对象
  const getAllSelectedNodeObjects = useCallback((): (CustomTreeNode | CustomInputNode)[] => {
    const allNodes = [...selectedNodes, ...customNodes];
    // 应用用户去重逻辑
    return deduplicateUserNodes(allNodes);
  }, [selectedNodes, customNodes]);

  // 获取所有节点的数据，根据returnNodeObjects决定返回对象还是ID
  const getAllSelectedData = useCallback((): any[] => {
    if (returnNodeObjects) {
      return getAllSelectedNodeObjects();
    } else {
      // 先进行用户去重，然后提取ID
      const deduplicatedNodes = deduplicateUserNodes([...selectedNodes, ...customNodes]);
      return deduplicatedNodes.map(node => {
        // 判断是否为用户节点
        const isUserNode = node.type === 'user' || node.nodeType === 'user' || (node as any).username;

        // 如果是自定义节点，保留完整ID
        if ('custom' in node && node.custom === true) {
          return node.id;
        }

        // 如果是用户节点且需要去重，优先使用userId字段
        if (isUserNode && deduplicateUsers) {
          if ((node as any).userId) {
            return (node as any).userId.toString();
          }
          // 检查ID是否包含组织ID前缀（格式为"组织ID_用户ID"）
          const idParts = node.id.toString().split('_');
          if (idParts.length > 1) {
            return idParts[idParts.length - 1];
          }
        }

        // 否则返回原始ID或originalId
        return ((node as any).originalId || node.id).toString();
      });
    }
  }, [getAllSelectedNodeObjects, selectedNodes, customNodes, returnNodeObjects, deduplicateUsers]);

  // 确认选择
  const handleConfirm = useCallback(() => {
    // 根据配置获取节点数据（对象或ID）
    const selectedData = getAllSelectedData();

    if (onChange) {
      onChange(selectedData);
    }

    if (onConfirm) {
      onConfirm(selectedData);
    }

    // 更新显示
    if (!customMode) {
      // 普通模式下，更新显示文本时使用合并的节点数据
      updateDisplayText([...selectedNodes, ...customNodes]);
    }

    setVisible(false);
  }, [getAllSelectedData, onChange, onConfirm, customMode, updateDisplayText, selectedNodes, customNodes]);

  // 默认的树节点点击处理
  const handleSelect = useCallback((e: React.MouseEvent, nodeData: CustomTreeNode) => {
    e.stopPropagation();

    // 检查节点是否可选择
    if (!isNodeSelectable(nodeData)) {
      return;
    }

    const isSelected = selectedNodes.some(item => item.id === nodeData.id);
    let newSelectedNodes: CustomTreeNode[] = [];

    if (multiple) {
      // 多选模式
      if (isSelected) {
        // 如果已选中，则取消选中
        newSelectedNodes = selectedNodes.filter(item => item.id !== nodeData.id);
      } else {
        // 如果未选中，则添加到选中列表
        const candidateNodes = [...selectedNodes, nodeData];
        // 应用用户去重逻辑
        newSelectedNodes = deduplicateUserNodes(candidateNodes) as CustomTreeNode[];
      }
    } else {
      // 单选模式
      if (isSelected) {
        // 单选模式下，如果点击已选中项，保持选中状态
        newSelectedNodes = [nodeData];
      } else {
        // 单选模式下，选择新项
        newSelectedNodes = [nodeData];
      }
      
      // 当 customMode 为 true 且 multiple 为 false 时，选择新的树节点时清空自定义节点
      if (customMode) {
        setCustomNodes([]);
        setCustomValues([]);
        setCustomInput('');
      }
    }

    setSelectedNodes(newSelectedNodes);
  }, [multiple, selectedNodes, isNodeSelectable, customMode, setCustomNodes, setCustomValues, setCustomInput]);

  // 选择按钮点击事件处理
  const handleSelectButtonClick = useCallback(() => {
    setVisible(true);
    setIsHeightCalculated(false);
  }, []);

  // 处理自定义输入变化
  const handleCustomInputChange = useCallback((value: string) => {
    setCustomInput(value);

    // 判断是否输入了新的分隔符
    const separatorRegex = /[\s,，.。;；、\-\n\r]+/;
    let finalValue = value;

    // 如果输入了分隔符，则处理分隔操作
    if (value) {
      // 处理转义字符，临时替换带有\前缀的分隔符为一个特殊标记
      let tempText = value;
      // 处理所有可能的转义字符
      const escapeChars = ['\\,', '\\，', '\\.', '\\。', '\\;', '\\；', '\\、', '\\-', '\\ '];
      const placeholder = '##ESCAPED_SEPARATOR##';
      const escapedMap: { [key: string]: string } = {};

      // 为每个转义字符创建唯一的占位符并记录映射关系
      escapeChars.forEach((escChar, index) => {
        const uniquePlaceholder = `${placeholder}${index}`;
        // 记录映射关系以便后续还原
        escapedMap[uniquePlaceholder] = escChar.substring(1); // 去掉反斜杠
        // 替换所有转义字符为占位符
        tempText = tempText.split(escChar).join(uniquePlaceholder);
      });

      // 使用分隔符进行分割
      const splitValues = tempText.split(separatorRegex).filter(item => item.trim() !== '');
      // 如果检测到有效的输入项，则处理节点创建
      if (splitValues.length > 0) {

        // 获取所有项
        const itemsToProcess = splitValues;

        if (itemsToProcess.length > 0) {
          // 还原被转义的分隔符，并转换为自定义节点对象
          const processedNodes: CustomInputNode[] = itemsToProcess.map(item => {
            let processedName = item;
            Object.entries(escapedMap).forEach(([placeholder, original]) => {
              processedName = processedName.split(placeholder).join(original);
            });

            // 创建自定义节点对象
            const uniqueId = generateUniqueId(processedName);
            const customNode: CustomInputNode = {
              name: processedName,
              id: uniqueId,
              key: uniqueId,
              custom: true,
              username: '',
              checked: true,
              type: 'custom',
              nodeType: 'custom'
            };

            return customNode;
          });

          // 提取自定义节点的ID作为值
          const processedValues = processedNodes.map(node => node.id);

          // 当 customMode 为 true 且 multiple 为 false 时，输入新的自定义内容时清空已选择的树节点
          let currentSelectedNodes = selectedNodes;
          if (customMode && !multiple) {
            currentSelectedNodes = [];
            setSelectedNodes([]);
          }

          // 保存当前的自定义节点和值，以便在后续操作中使用
          let updatedCustomNodes: CustomInputNode[];
          let updatedCustomValues: string[];
          
          if (multiple) {
            // 多选模式：累加节点并去重
            const candidateCustomNodes = [...customNodes, ...processedNodes];
            updatedCustomNodes = deduplicateUserNodes(candidateCustomNodes) as CustomInputNode[];
            updatedCustomValues = updatedCustomNodes.map(node => node.id);
          } else {
            // 单选模式：只保留最后一个节点
            updatedCustomNodes = processedNodes.slice(-1);
            updatedCustomValues = processedValues.slice(-1);
          }

          setCustomNodes(updatedCustomNodes);
          setCustomValues(updatedCustomValues);

          // 如果有自定义输入回调，则调用
          if (onCustomInputChange) {
            onCustomInputChange(updatedCustomValues);
          }

          // 获取所有选中的数据，使用更新后的值
          const allSelectedData = returnNodeObjects
            ? deduplicateUserNodes([...currentSelectedNodes, ...updatedCustomNodes])
            : deduplicateUserNodes([...currentSelectedNodes, ...updatedCustomNodes]).map(node => {
                // 判断是否为用户节点
                const isUserNode = node.type === 'user' || node.nodeType === 'user' || (node as any).username;

                // 如果是自定义节点，保留完整ID
                if ('custom' in node && node.custom === true) {
                  return node.id;
                }

                // 如果是用户节点且需要去重，优先使用userId字段
                if (isUserNode && deduplicateUsers) {
                  if ((node as any).userId) {
                    return (node as any).userId.toString();
                  }
                  // 检查ID是否包含组织ID前缀（格式为"组织ID_用户ID"）
                  const idParts = node.id.toString().split('_');
                  if (idParts.length > 1) {
                    return idParts[idParts.length - 1];
                  }
                }

                // 否则返回原始ID或originalId
                return ((node as any).originalId || node.id).toString();
              });

          // 触发onChange和onConfirm
          if (onChange) {
            onChange(allSelectedData);
          }

          if (onConfirm) {
            onConfirm(allSelectedData);
          }
        }

        // 延迟设置，以确保UI更新
        setTimeout(() => {
          setCustomInput(finalValue);
        }, 0);
      }
    } else {
      // 清空自定义输入
      setCustomNodes([]);
      setCustomValues([]);

      if (onCustomInputChange) {
        onCustomInputChange([]);
      }

      // 获取当前选中的数据
      const selectedData = returnNodeObjects 
        ? deduplicateUserNodes(selectedNodes) 
        : deduplicateUserNodes(selectedNodes).map(node => {
            // 判断是否为用户节点
            const isUserNode = node.type === 'user' || node.nodeType === 'user' || (node as any).username;

            // 如果是用户节点且需要去重，优先使用userId字段
            if (isUserNode && deduplicateUsers) {
              if ((node as any).userId) {
                return (node as any).userId.toString();
              }
              // 检查ID是否包含组织ID前缀（格式为"组织ID_用户ID"）
              const idParts = node.id.toString().split('_');
              if (idParts.length > 1) {
                return idParts[idParts.length - 1];
              }
            }

            // 否则返回原始ID或originalId
            return ((node as any).originalId || node.id).toString();
          });

      // 触发onChange和onConfirm
      if (onChange) {
        onChange(selectedData);
      }

      if (onConfirm) {
        onConfirm(selectedData);
      }
    }
  }, [
    getAllSelectedData,
    onChange,
    onConfirm,
    onCustomInputChange,
    returnNodeObjects,
    selectedNodes,
    deduplicateUsers,
    customValues, // 添加这个依赖项，确保能正确累加节点
    customMode,
    multiple,
    setSelectedNodes
  ]);

  // 打开选择器
  const openPicker = useCallback(() => {
    setVisible(true);
    setIsHeightCalculated(false);
  }, []);

  // 关闭选择器
  const closePicker = useCallback(() => {
    setVisible(false);
  }, []);

  // 处理键盘事件，支持Enter键作为分隔符
  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault(); // 防止表单提交

      // 获取当前输入值
      const currentValue = (e.target as HTMLInputElement).value;

      if (currentValue.trim()) {
        // 手动添加换行符作为分隔符处理，但不会清空输入框的内容
        const valueWithNewline = currentValue + '\n';
        handleCustomInputChange(valueWithNewline);
      }
    }
  }, [handleCustomInputChange]);

  // 初始化时，如果有默认选中的value，找到对应的节点
  useEffect(() => {
    if (value && treeData.length > 0) {
      const flattenedData = flattenTreeData(convertTreeData(treeData));

      // 处理不同类型的value
      const valueArray = processValueToArray(value);

      if (valueArray.length > 0) {
        // 找到对应的树节点
        const selected = flattenedData.filter(node =>
          valueArray.includes(node.id.toString()) || valueArray.includes(node.userId)
        );

        // 处理自定义节点
        const { customValueIds, customValueNodes , customInputLabel} = extractCustomNodes(valueArray);

        // 设置自定义节点
        if (customValueNodes.length > 0) {
          setCustomNodes(customValueNodes);
          setCustomValues(customValueIds);
        }

        setCustomInput(customInputLabel)
        setSelectedNodes(selected);
        updateDisplayText([...selected, ...customValueNodes]);
      }
    }
  }, [value, treeData, updateDisplayText]);

  return {
    visible,
    selectedNodes,
    treeHeight,
    isHeightCalculated,
    displayText,
    customInput,
    customValues,
    customNodes,
    containerRef,
    footerRef,
    navbarHeight,
    setVisible,
    setSelectedNodes,
    setTreeHeight,
    setIsHeightCalculated,
    setDisplayText,
    setCustomInput,
    setCustomValues,
    setCustomNodes,
    updateDisplayText,
    getAllSelectedNodeIds,
    getAllSelectedNodeObjects,
    getAllSelectedData,
    getAllSelectedNodeData: getAllSelectedNodeObjects,
    handleConfirm,
    handleSelect,
    handleSelectButtonClick,
    handleCustomInputChange,
    isNodeSelectable,
    openPicker,
    closePicker,
    convertTreeData: (data: any) => convertTreeData(data),
    handleKeyDown,
    // 输入框属性，用于配置移动端键盘
    inputProps: {
      enterKeyHint: 'done', // 'send', // 或者使用 
      onKeyDown: handleKeyDown
    }
  };
}; 