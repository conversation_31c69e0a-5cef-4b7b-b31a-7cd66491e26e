import { TreeNodeType } from '../checked-tree/checked-tree.component';
import { CustomInputNode, CustomTreeNode } from './types';

// 将TreeNodeType转换为自定义树节点格式
export const convertTreeData = (treeData: TreeNodeType[]): CustomTreeNode[] => {
  return treeData.map(node => {
    // 检查是否已有key属性（通过as any访问）
    const existingKey = (node as any).key;
    return {
      ...node,
      key: existingKey || node.id, // 优先使用已有的key，如果没有则使用id
      name: node.name || '',
      children: node.children ? convertTreeData(node.children) : undefined
    };
  });
};

// 扁平化树数据以便查找
export const flattenTreeData = (data: CustomTreeNode[]): CustomTreeNode[] => {
  let result: CustomTreeNode[] = [];
  data.forEach(node => {
    result.push(node);
    if (node.children && node.children.length > 0) {
      result = result.concat(flattenTreeData(node.children));
    }
  });
  return result;
};

// 根据用户ID对用户节点进行去重
export const deduplicateUserNodes = (nodes: (CustomTreeNode | CustomInputNode)[]): (CustomTreeNode | CustomInputNode)[] => {
  const userIdMap = new Map<string, CustomTreeNode | CustomInputNode>();
  const nonUserNodes: (CustomTreeNode | CustomInputNode)[] = [];

  nodes.forEach(node => {
    // 判断是否为用户节点
    const isUserNode = node.type === 'user' || node.nodeType === 'user' || node.username;
    
    if (isUserNode) {
      // 获取用户ID用于去重
      let userId: string;
      
      // 如果是自定义节点，使用完整ID
      if ('custom' in node && node.custom === true) {
        userId = node.id;
      } else {
        // 优先使用userId字段
        if ((node as any).userId) {
          userId = (node as any).userId.toString();
        } else {
          // 检查ID是否包含组织ID前缀（格式为"组织ID_用户ID"）
          const idParts = node.id.toString().split('_');
          if (idParts.length > 1) {
            userId = idParts[idParts.length - 1];
          } else {
            userId = node.id;
          }
        }
      }

      // 如果该用户ID还没有被记录，或者当前节点是更新的版本，则保存
      if (!userIdMap.has(userId)) {
        userIdMap.set(userId, node);
      }
    } else {
      // 非用户节点直接保留
      nonUserNodes.push(node);
    }
  });

  // 返回去重后的用户节点和所有非用户节点
  return [...Array.from(userIdMap.values()), ...nonUserNodes];
};

// 处理用户去重或保留组织层级信息
export const processUserSelection = (nodes: CustomTreeNode[], deduplicateUsers: boolean): string[] => {
  if (deduplicateUsers) {
    // 去重模式：提取纯用户ID
    return nodes.map(node => {
      // 判断是否为用户节点
      const isUserNode = node.type === 'user' || node.nodeType === 'user' || node.username;

      // 如果是自定义节点，保留完整ID
      if ('custom' in node && node.custom === true) {
        return node.id;
      }

      // 如果是用户节点，优先使用userId字段
      if (isUserNode) {
        if (node.userId) {
          return node.userId.toString();
        }
        // 检查ID是否包含组织ID前缀（格式为"组织ID_用户ID"）
        const idParts = node.id.toString().split('_');
        if (idParts.length > 1) {
          return idParts[idParts.length - 1];
        }
      }

      // 否则返回原始ID
      return node.id;
    });
  } else {
    // 非去重模式：优先使用originalId（如果有），否则使用id
    return nodes.map(node => {
      return (node.originalId || node.id).toString();
    });
  }
};

// 从ID字符串中提取自定义节点
export const extractCustomNodes = (valueArray: string[]): { customValueIds: string[], customValueNodes: CustomInputNode[], customInputLabel: string } => {
  // 处理自定义节点，这些节点不在树数据中
  const customValueIds = valueArray.filter(id => id.includes('custom'));
  const customValueNodes: CustomInputNode[] = [];

  // 从ID中提取自定义节点的名称并创建节点对象
  if (customValueIds.length > 0) {
    customValueIds.forEach(id => {
      const parts = id.split('-');
      // 确保ID格式正确且包含至少3部分（custom-timestamp-name）
      if (parts.length >= 3) {
        // 第三部分及之后的所有部分都作为名称（防止名称中含有横杠）
        const nodeName = parts.slice(2).join('-');

        const customNode: CustomInputNode = {
          id: id,
          key: id,
          name: nodeName,
          custom: true,
          checked: true,
          username: '',
          children: [],
          type: 'custom',
          nodeType: 'custom'
        };

        customValueNodes.push(customNode);
      }
    });
  }
  const customInputLabel = customValueNodes.map(node => node.name).join(', ');

  return { customValueIds, customValueNodes, customInputLabel };
};

// 处理不同类型的value，转换为字符串数组
export const processValueToArray = (value: any): string[] => {
  let valueArray: string[] = [];

  // 如果value是字符串（可能是逗号分隔的ID）
  if (typeof value === 'string') {
    valueArray = (value as string).split(/[,，]/).map(item => item.trim()).filter(Boolean);
  }
  // 如果value是数组
  else if (Array.isArray(value)) {
    if (value.length > 0) {
      // 判断数组中的项是否为对象（包含项数据的数组）
      if (typeof value[0] === 'object' && value[0] !== null) {
        valueArray = value.map(item => {
          // 确保item是对象类型
          if (typeof item === 'object' && item !== null) {
            return (item as any).id?.toString() || '';
          }
          return '';
        }).filter(Boolean);
      } else {
        // 数组中的项是ID字符串
        valueArray = value.map(item => item?.toString() || '').filter(Boolean);
      }
    }
  }

  return valueArray;
};

// 生成唯一ID
export const generateUniqueId = (name: string): string => {
  const timestamp = new Date().getTime() + Math.floor(Math.random() * 1000);
  return `custom-${timestamp}-${name}`;
};

// 检查节点是否可选择（基于节点类型过滤）
export const isNodeSelectable = (
  node: CustomTreeNode,
  leafNodesOnly: boolean,
  nodeTypeFilter?: string | string[]
): boolean => {
  // 叶子节点判断逻辑
  if (leafNodesOnly) {
    if (node.children && node.children.length > 0) {
      return false;
    }
  }
  // 如果没有设置过滤器，则所有节点都可选
  if (!nodeTypeFilter) return true;
  // 获取节点类型（假设节点类型存储在type字段中，如果不是，请修改为实际字段）
  const nodeType = node.type || node.nodeType;
  // 如果节点没有类型信息，默认可选
  if (!nodeType) return true;
  // 如果过滤器是字符串，直接比较
  if (typeof nodeTypeFilter === 'string') {
    return nodeType === nodeTypeFilter;
  }
  // 如果过滤器是数组，检查节点类型是否在数组中
  if (Array.isArray(nodeTypeFilter)) {
    return nodeTypeFilter.includes(nodeType);
  }
  return true;
}; 