# 树形选择器组件 (TreePicker)

这是一个自定义的表单组件，点击后弹起带有树形结构数据的选择器。树节点可以多选，Picker下方展示已选总数，以及确定选择按钮。

## 功能特点

- 支持树形结构数据展示
- 支持节点多选功能
- 显示已选项总数
- 集成表单组件适配器，方便在Form中使用
- 支持自定义字段映射
- 支持父子节点关联选择

## 使用方法

### 基本用法

```tsx
import { TreePicker, TreePickerFormItem } from '@/component/tree-picker/tree-picker.component';

// 树形数据
const treeData = [
  {
    id: '1',
    name: '父节点1',
    children: [
      { id: '1-1', name: '子节点1-1' },
      { id: '1-2', name: '子节点1-2' },
    ],
  },
  {
    id: '2',
    name: '父节点2',
    children: [
      { id: '2-1', name: '子节点2-1' },
    ],
  },
];

// 在表单中使用
<Form layout='horizontal' mode='card'>
  <TreePickerFormItem
    label='设备选择'
    name='equipments'
    placeholder='请选择设备'
    title='设备选择'
    treeData={treeData}
    multiple={true}
    checkStrictly={false}
  />
</Form>

// 单独使用
const [value, setValue] = useState<string[]>([]);

<TreePicker
  value={value}
  onChange={setValue}
  placeholder='请选择'
  title='请选择'
  treeData={treeData}
  multiple={true}
  checkStrictly={true}
/>
```

## 参数说明

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| value | 选中的值 | string[] | [] |
| onChange | 选择变化时的回调函数 | (value: string[]) => void | - |
| placeholder | 未选择时的占位文字 | string | '请选择' |
| title | 弹出层的标题 | string | '请选择' |
| treeData | 树形结构数据 | TreeNodeType[] | [] |
| multiple | 是否支持多选 | boolean | true |
| fields | 自定义字段映射 | fieldsOptions | { label: 'name', value: 'id' } |
| checkStrictly | 父子节点是否关联 | boolean | true |
| disabledFilter | 是否禁用选择的方法 | (node: any) => boolean | - |
| nodeTypeFilter | 节点类型过滤函数 | (node: TreeNodeType) => boolean | - |
| showUsers | 是否显示用户节点 | boolean | false |
| leafNodesOnly | 仅允许选择叶子节点 | boolean | false |

### 参数组合与互相影响说明

- **showUsers 与 nodeTypeFilter**：当 showUsers=false 时，person 类型节点会被忽略，即使 nodeTypeFilter 设置为 'person' 也不会生效。
- **leafNodesOnly 与 nodeTypeFilter**：当 leafNodesOnly=true 时，即使 nodeTypeFilter 包含非叶子节点类型，也只能选择叶子节点。
- **showUsers 与 leafNodesOnly**：当 showUsers=false 且 leafNodesOnly=true 时，只有非 person 类型的叶子节点可选。

#### 组合示例

| showUsers | leafNodesOnly | nodeTypeFilter         | 实际可选节点类型说明                |
|-----------|---------------|------------------------|-------------------------------------|
| true      | false         | ['device','person']    | device 和 person 类型节点均可选      |
| false     | false         | ['device','person']    | 仅 device 类型节点可选，person 被忽略|
| true      | true          | ['device','org']       | 仅 device 类型的叶子节点可选         |
| false     | true          | ['device','person']    | 仅 device 类型的叶子节点可选         |

## TreeNodeType 类型

```ts
export interface TreeNodeType {
  id: string;
  parentId?: string;
  name?: string;
  children?: TreeNodeType[] | null;
}
```

## fieldsOptions 类型

```ts
export interface fieldsOptions {
  label: string; // 显示的字段名
  value: string; // 值的字段名
}
```

### 进阶用法示例

```tsx
// 仅允许选择叶子节点，且只显示特定类型节点
<TreePicker
  value={value}
  onChange={setValue}
  placeholder="请选择"
  title="请选择"
  treeData={treeData}
  multiple={true}
  checkStrictly={true}
  leafNodesOnly={true}
  nodeTypeFilter={(node) => node.type === 'device'}
/>

// 显示用户节点
<TreePicker
  value={value}
  onChange={setValue}
  placeholder="请选择"
  title="请选择"
  treeData={treeData}
  showUsers={true}
/>
```