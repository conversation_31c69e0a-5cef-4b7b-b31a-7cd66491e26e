.selector {
  width: 100%;
  height: calc(168px - ~"24px");
  line-height: 168px;
  color: #666;
  font-size: 48px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.popupHeader {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 2.5rem;
  border-bottom: 1px solid #f0f0f0;
  position: relative;

  .title {
    font-size: 1rem;
    font-weight: 500;
    color: #333;
  }
}

.treeContainer {
  height: calc(100vh - var(--header-height));
  overflow-y: auto;
  margin: 24px 0;

  .nodeContent {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    cursor: pointer;

    .title {
      flex: 1;
    }

    .radio {
      margin-left: auto;
      margin-right: 24px;
    }
  }

  .disabledNode {
    color: #999;
    cursor: not-allowed;
  }

  .disabledNodeContent {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .expandIcon {
    font-size: 36px !important;
  }

  :global {
    .ant-tree-list-scrollbar.ant-tree-list-scrollbar-vertical {
      display: none;
    }

    .ant-tree .ant-tree-treenode {
      line-height: 61px;
      padding: 33px 0 38px;
      background-color: #fff;
      // margin-bottom: 0;
    }

    .ant-tree.ant-tree-directory .ant-tree-treenode .ant-tree-node-content-wrapper:before {
      border-radius: 0;
    }

    .ant-tree {
      font-size: 42px;
      border-radius: 0;
      background-color: #0000;
    }

    .ant-tree.ant-tree-directory .ant-tree-treenode .ant-tree-node-content-wrapper:hover:before {
      background: rgb(255, 255, 255);
    }

    .ant-tree.ant-tree-directory .ant-tree-treenode-selected .ant-tree-node-content-wrapper:before,
    .ant-tree.ant-tree-directory .ant-tree-treenode-selected .ant-tree-node-content-wrapper:hover:before {
      background: #fff;
    }

    .ant-tree.ant-tree-directory .ant-tree-treenode-selected .ant-tree-node-content-wrapper {
      color: rgba(0, 0, 0, 0.88);
    }

    .ant-tree.ant-tree-directory .ant-tree-treenode-selected .ant-tree-switcher,
    .ant-tree.ant-tree-directory .ant-tree-treenode-selected .ant-tree-draggable-icon {
      color: rgba(0, 0, 0, 0.88);
    }

    // .ant-tree-list-holder-inner>div[class*=ant-tree-treenode-switcher] {
    //   margin-bottom: 1px;
    // }

    // .ant-tree.ant-tree-directory .ant-tree-treenode .ant-tree-node-content-wrapper {
    //   position: relative;

    //   &::before {
    //     content: '';
    //     display: block;
    //     position: absolute;
    //     border-bottom: 1px solid #e5e5e5e5;
    //     left: -15px;
    //     right: 25px;
    //     bottom: -38px;
    //   }
    // }
  }
}

.footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 36px 24px 41px;
  border-top: 1px solid #f0f0f0;
  background-color: #fff;
  height: 120px;

  .selectedCount {
    font-size: 48px;
    color: #666;

    .count {
      color: var(--adm-color-primary);
      font-weight: 500;
    }
  }

  :global {
    .adm-button {
      width: 600px;
      font-size: 48px;
      line-height: 48px;
      padding: 25px 0;
      border-radius: 36px;
    }
  }
}

.searchIcon {
  width: 54px;
  font-size: 54px;
  height: 54px;
}

.curssTag {
  width: 102px;
  height: 54px;
  opacity: 1;
  border-radius: 12px;
  background: rgba(215, 225, 250, 1);
  font-size: 32px;
  font-weight: 400;
  line-height: 54px;
  color: rgba(17, 84, 237, 1);
  margin-left: auto;
  margin-right: 24px;
  text-align: center;
  display: block;
  float: right;
  margin-top: 2px;
}

.pickerValue {
  color: #333;
  font-size: 42px;
  line-height: 60px;
  padding: 12px 0;
}

// 自定义模式包装容器
.customModeWrapper {
  margin-bottom: 16px;
}

// 自定义模式样式
.customModeContainer {
  display: flex;
  align-items: center;
  width: 100%;
  margin: 12px 0;
  border-radius: 8px;
}

.customInputWrapper {
  flex: 1;
  margin-right: 12px;
}

.customInput {
  width: 100%;
  font-size: 38px;
  box-shadow: none;
  outline: none;
  border: none;
}

:global(a.adm-list-item:active:not(.adm-list-item-disabled)) .customInput {
  background-color: var(--active-background-color);
}

.selectButton {
  width: 140px;
  height: 64px;
  border-radius: 24px;
  background: rgba(215, 225, 250, 1);

  font-size: 32px;
  font-weight: 400;
  line-height: 84px;
  color: rgba(17, 84, 237, 1);

  position: absolute;
  right: var(--padding-right);

  display: flex;
  align-items: center;
  justify-content: center;
}

// 已选择项样式
.customSelectedItems {
  margin-top: 16px;
  padding: 12px 24px;
  background-color: #f5f5f573;
  border-radius: 8px;
}

.selectedNodesSection,
.customValuesSection {
  margin-bottom: 12px;
}

.sectionTitle {
  font-size: 32px;
  line-height: 52px;
  height: 52px;
  color: #666;
  margin-bottom: 8px;
}

.itemsContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.selectedItem {
  display: inline-flex;
  align-items: center;
  padding: 4px 12px;
  background-color: #e1f2ff;
  border-radius: 16px;
  font-size: 36px;
  color: #1154ed;
}

.removeItem {
  margin-left: 8px;
  font-size: 38px;
  width: 24px;
  height: 24px;
  line-height: 20px;
  text-align: center;
  cursor: pointer;
  color: #999;

  &:hover {
    color: #ff4d4f;
  }
}