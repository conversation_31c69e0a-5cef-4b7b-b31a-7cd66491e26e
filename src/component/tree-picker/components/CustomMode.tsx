import React, { useRef, useEffect } from 'react';
import styles from '../tree-picker.component.less';
import { CustomInputNode, CustomModeProps, CustomTreeNode } from '../types';

const CustomMode: React.FC<CustomModeProps> = ({
  customInput,
  customNodes,
  selectedNodes,
  displayField,
  onCustomInputChange,
  onSelectButtonClick,
  setSelectedNodes,
  setCustomNodes,
  setCustomValues,
  customValues,
  onCustomNodesChange,
  multiple = true
}) => {
  // 在第15行左右，其他props后添加
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // 自动调整文本区域高度的函数
  const adjustTextareaHeight = () => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = `${textarea.scrollHeight}px`;
    }
  };

  // 当输入内容变化时调整高度
  useEffect(() => {
    adjustTextareaHeight();
  }, [customInput]);

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onCustomInputChange(e.target.value);
  };

  // 处理按键事件，检测 Enter 键
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      // 触发确认操作 - 使用逗号作为分隔符来模拟当前的行为
      onCustomInputChange(customInput + ',');
    }
  };

  // 渲染已选择的节点和自定义输入
  const renderSelectedItems = () => {
    if (selectedNodes.length === 0 && customNodes.length === 0) {
      return null;
    }

    // 当 customMode 为 true 且 multiple 为 false 时，合并显示
    if (!multiple) {
      // 合并所有选中的节点（包括树节点和自定义节点）
      const allSelectedItems = [...selectedNodes, ...customNodes];
      
      if (allSelectedItems.length === 0) {
        return null;
      }

      return (
        <div className={styles.customSelectedItems} onClick={(e: React.MouseEvent) => {
          e.stopPropagation();
        }}>
          <div className={styles.selectedNodesSection}>
            <div className={styles.sectionTitle}>已选择人员：</div>
            <div className={styles.itemsContainer}>
              {allSelectedItems.map((node, index) => (
                <span key={node.id} className={styles.selectedItem}>
                  {node[displayField as keyof (CustomTreeNode | CustomInputNode)] || node.name}
                  <span
                    className={styles.removeItem}
                    onClick={(e: React.MouseEvent) => {
                      e.stopPropagation();
                      // 判断是树节点还是自定义节点
                      if ('custom' in node && node.custom) {
                        // 自定义节点
                        const customNodeIndex = customNodes.findIndex(item => item.id === node.id);
                        if (customNodeIndex !== -1) {
                          const newNodes = [...customNodes];
                          const newValues = [...customValues];
                          newNodes.splice(customNodeIndex, 1);
                          newValues.splice(customNodeIndex, 1);
                          setCustomNodes(newNodes);
                          setCustomValues(newValues);

                          if (onCustomNodesChange) {
                            onCustomNodesChange(newValues);
                          }
                        }
                      } else {
                        // 树节点
                        setSelectedNodes(selectedNodes.filter(item => item.id !== node.id));
                      }
                    }}
                  >
                    ×
                  </span>
                </span>
              ))}
            </div>
          </div>
        </div>
      );
    }

    // 多选模式下，分别显示已选择人员和自定义输入
    return (
      <div className={styles.customSelectedItems} onClick={(e: React.MouseEvent) => {
        e.stopPropagation();
      }}>
        {selectedNodes.length > 0 && (
          <div className={styles.selectedNodesSection}>
            <div className={styles.sectionTitle}>已选择人员：</div>
            <div className={styles.itemsContainer}>
              {selectedNodes.map(node => (
                <span key={node.id} className={styles.selectedItem}>
                  {node[displayField as keyof CustomTreeNode] || node.name}
                  <span
                    className={styles.removeItem}
                    onClick={(e: React.MouseEvent) => {
                      e.stopPropagation();
                      setSelectedNodes(selectedNodes.filter(item => item.id !== node.id));
                    }}
                  >
                    ×
                  </span>
                </span>
              ))}
            </div>
          </div>
        )}

        {customNodes.length > 0 && (
          <div className={styles.customValuesSection}>
            <div className={styles.sectionTitle}>自定义输入：</div>
            <div className={styles.itemsContainer}>
              {customNodes.map((node, index) => (
                <span key={node.id} className={styles.selectedItem}>
                  {node.name}
                  <span
                    className={styles.removeItem}
                    onClick={(e: React.MouseEvent) => {
                      e.stopPropagation();
                      const newNodes = [...customNodes];
                      const newValues = [...customValues];
                      newNodes.splice(index, 1);
                      newValues.splice(index, 1);
                      setCustomNodes(newNodes);
                      setCustomValues(newValues);

                      if (onCustomNodesChange) {
                        onCustomNodesChange(newValues);
                      }
                    }}
                  >
                    ×
                  </span>
                </span>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={styles.customModeWrapper}>
      <div className={styles.customModeContainer}>
        <div className={styles.customInputWrapper}>
          <textarea
            ref={textareaRef}
            placeholder="请输入"
            value={customInput}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            onClick={(e: React.MouseEvent) => e.stopPropagation()}
            className={styles.customInput}
            rows={1}
            style={{
              resize: 'none',
              overflow: 'hidden',
            }}
          />
        </div>
        <div
          className={styles.selectButton}
          style={{ top: '6px' }}
          onClick={onSelectButtonClick}
        >
          +选择
        </div>
      </div>
      {renderSelectedItems()}
    </div>
  );
};

export default CustomMode; 