import React from 'react';
import { Tree } from 'antd';
import { DownOutline, RightOutline } from 'antd-mobile-icons';
import styles from '../tree-picker.component.less';
import { CustomTreeNode, CustomTreeProps } from '../types';
import TreeNodeRenderer from './TreeNodeRenderer';

const { DirectoryTree } = Tree;

const CustomTree: React.FC<CustomTreeProps> = ({
  treeData,
  treeHeight,
  defaultExpandAll,
  showIcon,
  selectedNodes,
  multiple,
  switcherIcon,
  titleRender,
  nodeTitleRender,
  onExpand,
  isNodeSelectable,
  onSelect
}) => {
  // 默认的展开/收起图标
  const defaultSwitcherIcon = ({ expanded }: { expanded: boolean }) => {
    return expanded
      ? <DownOutline className={styles.expandIcon} />
      : <RightOutline className={styles.expandIcon} />;
  };

  // 默认的展开处理
  const defaultOnExpand = (keys: React.Key[], info: any) => {
    console.log('Tree expanded:', keys, info);
  };

  // 默认的节点渲染
  const defaultTitleRender = (nodeData: CustomTreeNode) => {
    return (
      <TreeNodeRenderer
        nodeData={nodeData}
        selectedNodes={selectedNodes}
        multiple={multiple}
        isNodeSelectable={isNodeSelectable}
        nodeTitleRender={nodeTitleRender}
        onSelect={onSelect}
      />
    );
  };

  return (
    <div className={styles.treeContainer} title={treeHeight}>
      {/* @ts-ignore - 忽略类型错误，确保组件可以正常渲染 */}
      <DirectoryTree
        defaultExpandAll={defaultExpandAll}
        onExpand={onExpand || defaultOnExpand}
        switcherIcon={switcherIcon || defaultSwitcherIcon}
        treeData={treeData}
        showIcon={showIcon}
        height={treeHeight}
        titleRender={(nodeData) => (titleRender || defaultTitleRender)(nodeData as CustomTreeNode)}
      />
    </div>
  );
};

export default CustomTree; 