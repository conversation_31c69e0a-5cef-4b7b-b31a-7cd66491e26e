import React from 'react';
import { Radio } from 'antd-mobile';
import styles from '../tree-picker.component.less';
import { TreeNodeRendererProps } from '../types';

// 树节点渲染组件
const TreeNodeRenderer: React.FC<TreeNodeRendererProps> = ({
  nodeData,
  selectedNodes,
  multiple,
  isNodeSelectable,
  nodeTitleRender,
  onSelect
}) => {
  const isDisabled = !isNodeSelectable(nodeData);
  const isSelected = selectedNodes.some(item => item.id === nodeData.id);

  // 默认的节点标题渲染函数
  const defaultNodeTitleRender = () => {
    return (
      <div 
        className={`${styles.title} ${isDisabled ? styles.disabledNode : ''}`} 
        style={isDisabled ? { color: '#999', cursor: 'not-allowed' } : undefined}
      >
        {nodeData.name}
        {!multiple && isSelected && <span className={styles.curssTag}>当前</span>}
        {isDisabled && <span style={{ marginLeft: '8px', color: '#999', fontSize: '0.9em' }}>[不可选]</span>}
      </div>
    );
  };

  return (
    <div
      key={nodeData.id}
      className={`${styles.nodeContent} ${isDisabled ? styles.disabledNodeContent : ''}`}
      onClick={(e) => !isDisabled && onSelect(e, nodeData)}
      style={isDisabled ? { opacity: 0.6, cursor: 'not-allowed' } : undefined}
    >
      {/* 使用nodeTitleRender来渲染节点标题部分，如果未提供则使用默认渲染 */}
      {nodeTitleRender ? nodeTitleRender(nodeData, selectedNodes) : defaultNodeTitleRender()}
      {multiple && (
        <Radio
          onClick={(e) => !isDisabled && onSelect(e, nodeData)}
          className={styles.radio}
          checked={isSelected}
          disabled={isDisabled}
        />
      )}
    </div>
  );
};

export default TreeNodeRenderer; 