import { RightOutline } from 'antd-mobile-icons';
import styles from './select-part.component.less';
import { Popup, TreeSelect } from 'antd-mobile';
import { CloseOutline } from 'antd-mobile-icons';
import { useState } from 'react';

interface Props {
  value?: string,
  onChange?: (value: string) => void;
}
const SelectPart: React.FC<Props> = ({ value, onChange }) => {
  const [visible, setVisible] = useState(false);
  const [confirmData, setConfirmData] = useState(null);

  const data = [{
    label: '19标段',
    value: '1',
    children: [{
      label: '左线',
      value: '1-1'
    },
    {
      label: '右线',
      value: '1-2'
    }]
  },
  {
    label: '20标段',
    value: '2',
    children: [{
      label: '左线',
      value: '2-1'
    },
    {
      label: '右线',
      value: '2-2'
    }]
  }];

  return (
    <div className={styles.selectItem} onClick={() => { setVisible(true) }}>
      <span className={confirmData?.length ? styles.value : ''}>
        {confirmData?.length ? confirmData.map((item, index) => (
          `${item.label}${index < confirmData.length - 1 ? ' / ' : ''}`
        )) : '请选择'}
      </span>
      <RightOutline />
      <Popup
        visible={visible}
        getContainer={null}
        onMaskClick={() => {
          setVisible(false)
        }}
        onClose={() => {
          setVisible(false)
        }}
        bodyStyle={{ borderRadius: '8px 8px 0 0' }}
      >
        <div className={styles.popupTitle}><span />选择标段名称<CloseOutline onClick={() => { setVisible(false) }} /></div>
        <TreeSelect
          options={data}
          onChange={(value, nodes) => {
            console.log(value, nodes)
            if (!nodes.options[nodes.options.length - 1].children) {
              setVisible(false)
              setConfirmData(nodes.options)
              onChange?.(value[value.length - 1]);
            }
          }}
        />
      </Popup>
    </div>
  )
};
export default SelectPart;
