.selectItem {
  margin-right: -55px;
  // text-align: right;
  .placeholder{
    text-align: right;
    color: #999;
  }
  .choosed{
    text-align: right;
  }
  .selectTip {
    display: flex;
    justify-content: right;
    align-items: center;
    gap: 10px;
    color: var(--adm-color-weak);
  }
  .selectList {
    position: relative;
    z-index: 1;
    display: flex;
    gap: 35px;
    margin-top: 52px;
    margin-left: -8em;
    & > div {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 30px;
      padding: 15px 40px;
      border-radius: 40px;
      background-color: var(--adm-color-box);
      font-size: 40px;
      color: var(--adm-color-text);
    }
  }
  .popupTitle {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 128px;
    padding: 0 48px;
    font-size: 48px;
    color: var(--adm-color-text);
    border-bottom: 1px solid #E6E6E6;
    background-color: #fff;
  }
  .btnGroups {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    padding: 21px 0;
    :global {
      .adm-button {
        width: 44%;
      }
    }
  }
  .tree{
   height: 1200px;
   overflow: auto;
  }

  :global {
    
    .adm-tabs-header {
      border-bottom: none;
      background: var(--adm-color-box);
    }
    .adm-list {
      padding: 0;
      .adm-list-item {
        margin-top: 0;
        border-radius: 0;
      }
      .adm-list-item-content {
        padding: 0 48px 0 0 !important;
      }
    }
    .adm-popup-body {
      border-radius: 0;
    }
  }
 
}

.tree{
  border: 1px soild red;
}