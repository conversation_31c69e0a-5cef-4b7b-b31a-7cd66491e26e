import { RightOutline } from "antd-mobile-icons";
import styles from "./select-modeltree.component.less";
import { <PERSON><PERSON>, Popup } from "antd-mobile";
import { CloseOutline } from "antd-mobile-icons";
import { useEffect, useState } from "react";
import Tree from "rc-tree";
import { findObject } from "@/pages/progress-manage/progress-manage-utils";
interface Props {
  value?: string[];
  onChange?: (value: string[]) => void;
  treeData?: any[];
}

const SelectModelTree: React.FC<Props> = ({ value, onChange, treeData }) => {
  const [visible, setVisible] = useState(false);
  const [selectdData, setSelectedData] = useState([]);
  const [selectCompId,setSelectCompId]=useState([])

  useEffect(() => {}, [value]);

  const handleConfirm = () => {
    setVisible(false);
    onChange?.(selectCompId);
  };

  const handleCancel = () => {
    setSelectedData([]);
    setVisible(false);
  };

  useEffect(()=>{
    console.log('value',value)
    if(value?.length){
      const arr =value?.map(item=>findObject(treeData,'componentId',item)?.id)
      setSelectedData(arr)
    }
  },[value])


  return (
    <div
      className={styles.selectItem}
      onClick={() => {
        setVisible(true);
      }}
    >
      <div className={selectdData?.length?styles.choosed:styles.placeholder}>
      <span >
        {selectdData?.length?`已选择${selectdData?.length}条`:'请选择'}
      </span>
      <RightOutline />
      </div>
   
      <Popup
        visible={visible}
        getContainer={null}
        onMaskClick={() => {
          setVisible(false);
        }}
        onClose={() => {
          setVisible(false);
        }}
        bodyStyle={{ borderRadius: "8px 8px 0 0" }}
      >
        <div className={styles.popupTitle}>
          <span />
          选择结构部位
          <CloseOutline
            onClick={() => {
              setVisible(false);
            }}
          />
        </div>
        <div className={styles.tree}>
          <Tree
            treeData={treeData || []}
            defaultExpandAll={false} // 展开所有节点
            showLine={true}
            checkable
            showIcon={true}
            autoExpandParent={true}
            multiple={true}
            checkedKeys={selectdData}
            onSelect={(keys, event) => {
            }}
            onCheck={(keys:any, event:any) => {
              const nodes = event?.checkedNodes?.filter(item=>item?.isComponent)
              setSelectedData(nodes.map(item=>item?.key))
              setSelectCompId(nodes.map(item=>item?.componentId))
            }}
          />
        </div>

        <div className={styles.btnGroups}>
          <Button color="primary" fill="outline" onClick={handleCancel}>
            取消
          </Button>
          <Button color="primary" onClick={handleConfirm}>
            确定选择
          </Button>
        </div>
      </Popup>
    </div>
  );
};
export default SelectModelTree;
