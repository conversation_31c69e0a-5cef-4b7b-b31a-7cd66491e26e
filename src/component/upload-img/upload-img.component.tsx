import { ImageUploadI<PERSON>, ImageUploader, ImageViewer } from 'antd-mobile'
import styles from './upload-img.conponent.less'
import { useEffect, useState } from 'react'
import { attachmentDelete, attachmentUpload } from '@/services/attachment'
import { urlPrefix } from '@/utils/request'
import { AddOutline } from 'antd-mobile-icons';
import { nativeCallback, usePicFile } from '@/utils/native-fun';

export type FileProps = { id: number; fileId: string; url: string }

interface Props {
  isCatalogue?: boolean
  value?: FileProps[] | any
  onChange?: (files: FileProps[]) => void
}

const UploadImg: React.FC<Props> = ({ value = [], onChange, isCatalogue = true }) => {
  const [fileList, setFileList] = useState<FileProps[]>([])
  const [attachImgId, setAttachImgId] = useState<number>()
  const [isUpload, setIsUpload] = useState<boolean>(false); // 是否调用相机上传图片
  const { picFile, clearPicFile } = usePicFile();

  useEffect(() => {
    if (Array.isArray(value) && value.length) {
      setFileList(JSON.parse(JSON.stringify(value)))
      const id = value?.length ? value[0].id || value[0].attachmentId : 0
      setAttachImgId(id)
    } else if (typeof value === "string" && isCatalogue === false && value) {
      const list: any[] = value.split(",").map(item => {
        return {
          fileId: item,
          inquireImager: `${urlPrefix}/Attachment/inquireImager/${item}`,
          url: `${urlPrefix}/Attachment/downloadAttachment/${item}`,
        }
      })
      setFileList(list)
    }
  }, [value]);

  useEffect(() => {
    if (picFile && isUpload) {
      onUploadImg(picFile);
      // onUploadChange([{ file: picFile, id: 1, url: URL.createObjectURL(picFile) }]);
      attachmentUpload({ files: [picFile], id: attachImgId && isCatalogue ? attachImgId : undefined }).then((res) => {
        if (res.code === '1') {
          const newFileList = res.data.attachmentContentList?.map((item) => ({
            id: res?.data?.id,
            fileId: item.id,
            inquireImager: `${urlPrefix}/Attachment/inquireImager/${item.id}`,
            url: `${urlPrefix}/Attachment/downloadAttachment/${item.id}`,
            pictureUrl: item.url,
            name: item.name,
          }))
          onChange(isCatalogue ? newFileList : fileList.concat(newFileList));
          clearPicFile();
          setIsUpload(false)
        }
      });
    }
  }, [picFile, isUpload])

  const onUploadImg = async (file: File) => {
    return {
      url: URL.createObjectURL(file),
      file,
    }
  }

  const onDeleteImg = (item) => {
    attachmentDelete({ id: item.fileId }).then((res) => {
      if (res.code === '1') {
        const files = fileList.filter((file) => file.fileId !== item.fileId)
        onChange(files);
        clearPicFile();
      }
    })
  }

  const onUploadChange = (items: (ImageUploadItem & FileProps)[]) => {
    setFileList(items)
    if (fileList.length > items.length) {
      return // 代表删除操作触发的onChange
    }
    const files = items?.filter((item) => !item.id)?.map((item: any) => item.file)
    attachmentUpload({ files, id: attachImgId && isCatalogue ? attachImgId : undefined }).then((res) => {
      if (res.code === '1') {
        const newFileList = res.data.attachmentContentList?.map((item) => ({
          id: res?.data?.id,
          fileId: item.id,
          inquireImager: `${urlPrefix}/Attachment/inquireImager/${item.id}`,
          url: `${urlPrefix}/Attachment/downloadAttachment/${item.id}`,
          pictureUrl: item.url,
          name: item.name,
        }))
        onChange(isCatalogue ? newFileList : fileList.concat(newFileList))
      }
    })
  }

  // 调用原生上传
  const handleUpload = async () => {
    clearPicFile();
    const _isCallback = await nativeCallback('camera');
    setIsUpload(_isCallback)
  }

  return (
    <>
      <ImageUploader
        multiple
        onPreview={(index: number, item: ImageUploadItem) => {
          ImageViewer.show({
            image: item.url
          })
        }}
        accept=".jpg,.png"
        value={fileList}
        preview={false}
        onChange={onUploadChange}
        onDelete={onDeleteImg}
        upload={onUploadImg}
        disableUpload
      >
        <span
          className='adm-image-uploader-cell adm-image-uploader-upload-button'
          onClick={handleUpload}
        >
          <span className='adm-image-uploader-upload-button-icon'>
            <AddOutline />
          </span>
        </span>
      </ImageUploader>
      <div className={styles.uploadText}>支持扩展名：.jpg、.png</div>
    </>
  )
}
export default UploadImg
