import React, { useState, useEffect, ReactNode } from "react";
import classnames from "classnames";
import styles from "./block-tree.component.less";
import { CheckOutline, RightOutline } from "antd-mobile-icons";

export interface TreeNodeType {
  id: string;
  parentId: string;
  name?: string;
  children?: TreeNodeType[] | null;
}

interface BlockTreeProps {
  treeData: TreeNodeType[];
  loading?: boolean;
  defaultExpandedKeys?: string[];
  height?: string | number | undefined;
  onSelect?: (id: string, record: TreeNodeType) => void;
  value?: string;
  renderRight?: (node: TreeNodeType) => ReactNode;
}

/**
 * 树组件 树形结构的第一层的每一块占一个白色块儿
 * @param param0
 * @returns
 */
const BlockTree: React.FC<BlockTreeProps> = ({
  treeData = [],
  defaultExpandedKeys = [],
  value,
  onSelect = () => { },
  height = "",
  renderRight,
}) => {
  const [tree, setTree] = useState<TreeNodeType[]>([]);
  const [collapseKeys, setCollapseKeys] = useState<string[]>([]);
  const [selectedKey, setSelectedKey] = useState("");

  const renderTreeNode = (list: TreeNodeType[] = [], deepCount = "") => {
    let d_arr = deepCount.split("");
    return list.map((item: TreeNodeType) => (
      <div key={item.id}>
        <div
          className={classnames(styles.item, {
            [styles.select]: collapseKeys.includes(item.id),
          })}
          onClick={() => handleSelect(item)}
        >
          <div className={styles.indent}>
            {d_arr.map((_, index) => (
              <div
                key={`${item.id}_indent_${index}`}
                className={styles.indentUnit}
              />
            ))}
          </div>
          <div className={styles.contentWrapper}>
            {item?.children?.length > 0 ? (
              <span
                id={`arrow_${item.id}`}
                className={styles.collapseArrow}
                style={{ top: 6 }}
                onClick={() => handleCollapse(item)}
              />
            ) : null}
            <span>{item.name}</span>
            {renderRight ? (
              <div className={styles.selected}>{renderRight(item)}</div>
            ) : item.id === selectedKey ? (
              <span className={styles.selected}>
                <CheckOutline color="var(--adm-color-primary)" fontSize={18} />
              </span>
            ) : null}
          </div>
        </div>
        <div id={`warp_${item.id}`} className={styles.warp}>
          {item?.children?.length > 0
            ? renderTreeNode(item.children, deepCount + "1")
            : null}
        </div>
      </div>
    ));
  };

  // 选择行
  const handleSelect = (record: TreeNodeType) => {
    setSelectedKey(record.id);
    onSelect?.(record.id, record);
  };

  // 选中项受控
  useEffect(() => {
    if (value) {
      setSelectedKey(value);
    }
  }, [value])


  // 折叠收起
  const handleCollapse = (record: TreeNodeType) => {
    const $warp_dom = document.getElementById(`warp_${record.id}`);
    const $arrow_dom = document.getElementById(`arrow_${record.id}`);
    if ($warp_dom) {
      $warp_dom.style.transition = "0.5s max-height";
      $arrow_dom.style.transition = "0.5s all";
      if ($warp_dom.style.maxHeight == "")
        $warp_dom.style.maxHeight = ($warp_dom.offsetHeight || 0) + "px";
    }
    const isCollapse = $warp_dom?.className.includes(styles.collapse);
    setTimeout(() => {
      if ($warp_dom) {
        // 收起状态
        if (isCollapse) {
          $warp_dom.className = $warp_dom?.className
            .split(" ")
            .filter((item) => item !== styles.collapse)
            .join(" ");
          setTimeout(() => {
            $warp_dom.style.maxHeight = "";
          }, 501);
          $arrow_dom.style.transform = "rotate(0deg)";
          $arrow_dom.style.top = "6px";
        } else {
          $warp_dom.className = $warp_dom.className + ` ${styles.collapse}`;
          $arrow_dom.style.transform = "rotate(-90deg)";
          $arrow_dom.style.top = "2px";
        }
      }
    }, 1);
    setCollapseKeys(
      collapseKeys.includes(record.id)
        ? collapseKeys.filter((item) => item !== record.id)
        : [...collapseKeys, record.id]
    );
  };

  useEffect(() => {
    setTree(treeData);
    setCollapseKeys(defaultExpandedKeys);
  }, [treeData]);

  return (
    <div style={{ minHeight: height }}>
      {tree.map((item) => (
        <div className={styles.blockTree} key={item.id}>
          {renderTreeNode([item])}
        </div>
      ))}
    </div>
  );
};

export default BlockTree;
