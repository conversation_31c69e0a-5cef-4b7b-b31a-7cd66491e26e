.blockTree {
  background-color: #fff;
  overflow: hidden;
  .item {
    font-size: 44px;
    min-height: 120px;
    line-height: 120px;
    padding: 0 50px;
    display: flex;
    align-items: flex-start;
    flex-wrap: nowrap;
    user-select: none;
    position: relative;
    z-index: 2;

    .indent {
      align-self: stretch;
      white-space: nowrap;
      user-select: none;
      font-size: 0;
      line-height: 0;

      .indentUnit {
        display: inline-block;
        width: 100px;
      }
    }
    .contentWrapper {
      position: relative;
      z-index: 1;
      flex: auto;
      text-rendering: optimizeLegibility;
      -webkit-font-smoothing: antialiased;
      user-select: none;
      .collapseArrow {
        position: relative;
        display: inline-block;
        margin-right: 20px;
        border: 20px solid transparent;
        border-top: 24px solid var(--adm-color-weak);
      }
      .selected {
        position: absolute;
        right: 0;
      }
    }
    &::after {
      position: absolute;
      top: 0;
      inset-inline-end: 0;
      bottom: 0;
      inset-inline-start: 0;
      left: 0;
      right: 0;
      transition: background-color 0.2s;
      content: "";
      pointer-events: none;
    }

    &:active:not(.disabled)::after {
      background: #eee;
    }

  }
  .warp {
    overflow: hidden;
    &.collapse {
      max-height: 0 !important;
    }
    .warp {
      border-radius: 0;
    }
  }
}
