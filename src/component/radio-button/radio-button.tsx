import { useEffect, useState } from 'react'
import styles from './radio-button.less'
interface Props {
  data: [string, string] // 内容
  value?: string // 当前选中
  onChange?: (value: string) => void// 选中回调
  style?: React.CSSProperties
}

const RadioButton: React.FC<Props> = ({ data, value, onChange, style = {} }) => {
  const [leftClass, setLeftClass] = useState<string>('normal')
  const [rightClass, setRightClass] = useState<string>('normal')
  const onLeftClick = () => {
    if (onChange && data.length === 2) {
      onChange(data?.[0])
    } else {
      setLeftClass('Choosen')
      setRightClass('normal')
    }
  }
  const onRightClick = () => {
    if (onChange && data.length === 2) {
      onChange(data?.[1])
    } else {
      setLeftClass('normal')
      setRightClass('Choosen')
    }
  }

  useEffect(() => {
    setLeftClass('Choosen')
  }, [])
  useEffect(() => {
    if (value === data?.[0]) {
      setLeftClass('Choosen')
      setRightClass('normal')
    } else if (value === data?.[1]) {
      setLeftClass('normal')
      setRightClass('Choosen')
    }
  }, [value])

  return <div className={styles.radioButton} style={style}>
    <div className={styles[leftClass]} onClick={onLeftClick}><div>{data?.[0]}</div></div>
    <div className={styles[rightClass]} onClick={onRightClick}><div>{data?.[1]}</div></div>
  </div>
}
export default RadioButton;
