import { Form,  FormProps,  Popup, PopupProps } from 'antd-mobile';
import { FilterOutline } from 'antd-mobile-icons';
import React, { useState, useEffect } from 'react';
import { styled } from 'umi';

const PopupTitle = styled.div.attrs({ className: (props: { className: any; }) => `${props.className} popup-title` as any })`
  box-sizing: border-box;
  font-family: PingFang SC;
  font-size: 4.4444vw;
  font-weight: 400;
  letter-spacing: 0vw;
  line-height: 6.4353vw;
  color: rgba(0, 0, 0, 1);
  text-align: center;
  vertical-align: top;
  margin-top: 2.9631vw;
  margin-bottom: 2.9631vw;
`;

const PopupContainer = styled.div.attrs({ className: (props: { className: any; }) => `${props.className} popup-container` as any  })`
  flex: 1;
  box-sizing: border-box;
  width: 100%;
  position: relative;
  margin-bottom: 3.3333vw;
  border-top: .0925vw solid rgba(230, 230, 230, 1);
  border-bottom: .0925vw solid rgba(230, 230, 230, 1);
  .body {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background-color: #fff;
    .adm-list,
    .adm-list-body,
    .adm-list-body-inner {
      height: 100%;
    }
    .adm-form-item-label {
      font-size: 4.4444vw;
      font-weight: 500;
      letter-spacing: 0vw;
      line-height: 6.4814vw;
      color: rgba(51, 51, 51, 1);
      margin-bottom: 3.6111vw;
    }
    .adm-text-area {
      box-sizing: border-box;
      padding: 2.0369vw 4.0742vw;
      border-radius: 1.8519vw;
      background: rgba(245, 245, 245, 1);
      // 提示文字改小点
      .adm-text-area-element::placeholder {
        font-size: 3.3333vw;
      }
    }
  }
`;

export const PopupFooter = styled.div.attrs({ className: (props: { className: any; }) => `${props.className} popup-footer` as any  })`
  box-sizing: border-box;
  overflow: hidden;
  border-radius: 3.3333vw;
  margin: 0 3.3333vw 2.2222vw;
  border: .3703vw solid rgba(17, 84, 237, 1);
  .btn {
    display: inline-block;
    width: 50%;
    text-align: center;
    height: 11.1111vw;
    line-height: 11.1111vw;
    font-size: 3.8881vw;
    font-weight: 400;
    letter-spacing: 0vw;
    color: #2b488c;
    &.btn-submit {
      color: #fff;
      background: rgba(17, 84, 237, 1);
    }
  }
`;

type FilterPopueComponentProps = {
  iconRender?: React.ReactNode; // 自定义图标
  title?: string; // 弹出框标题
  popupProps?: PopupProps; // 弹出框自定义
  formProps?: FormProps; // 表单自定义
  initialValues?: any;
  children: React.ReactNode; // 弹出框内容
  // 自定义底部按钮
  footer?: (handleSubmit, handleRest, handleCustom) => React.ReactNode;
  onVisible?: (visible: boolean) => void; // 弹出框显示状态回调
  onSubmit: (value: any) => void;
};

/**
 * FilterPopup是一个用于显示筛选弹出框的组件
 * 它允许用户通过点击图标来显示或隐藏一个包含筛选条件的弹出框
 *
 * @param {FilterPopueComponentProps} props - 组件的属性对象
 * @param {string} props.title - 弹出框的标题，默认为"筛选"
 * @param {React.ReactNode} props.iconRender - 自定义图标渲染函数，如果提供，则使用此图标来打开弹出框
 * @param {object} props.popupProps - 传递给Popup组件的属性对象
 * @param {React.ReactNode} props.children - 弹出框内容区域的子组件
 * @param {Function} props.onSubmit - 点击确定按钮时的回调函数
 * @returns {JSX.Element} 返回一个包含弹出框的React组件
 */
export const FilterPopup = React.forwardRef((props: FilterPopueComponentProps, ref) => {
  // 解构组件属性，设置默认值
  const { title = '筛选', iconRender = null, popupProps = {}, formProps = {},  children, footer = null, onSubmit } = props;
  // 定义visible状态，用于控制弹出框的显示与隐藏
  const [visible, setVisible] = useState<boolean>(false);
  const [form] = Form.useForm();

  // 暴露form方法
  React.useImperativeHandle(ref, () => ({
    form,
  }), [form]);

  const handleReset = () => {
    form.resetFields();
  };

  const handleSubmit = () => {
    form.setFieldValue('isCustom', 0);
    form.submit();
  };

  const handleCustom = () => {
    form.setFieldValue('isCustom', 1);
    form.submit();
  };
  
  useEffect(() => {
    if (props.onVisible) {
      props.onVisible(visible);
    }
  }, [visible]);

  return (
    <>
      {React.cloneElement((iconRender || <FilterOutline fontSize={24} color="#fff" data-testid="filter-trigger" />) as any, {
        "data-testid": "filter-trigger",
        onClick: (e) => {
          e.preventDefault()
          setVisible(true);
        },
      })}
      <Popup
        visible={visible}
        onMaskClick={() => {
          setVisible(false);
        }}
        showCloseButton
        destroyOnClose
        onClose={() => {
          setVisible(false);
        }}
        bodyStyle={{
          borderTopLeftRadius: '3.3333vw',
          borderTopRightRadius: '3.3333vw',
          minHeight: '90vh',
          display: 'flex',
          flexDirection: 'column',
        }}
        data-testid="filter-panel"
        {...popupProps}
      >
        <PopupTitle>{title}</PopupTitle>
        <PopupContainer>
          <Form
            form={form}
            className="body"
            {...formProps}
            onFinish={(values) => {
              onSubmit(values);
              setVisible(false);
            }}
          >
            <Form.Item name="isCustom" hidden initialValue={0}>
            </Form.Item>
            {children}
          </Form>
        </PopupContainer>

        {footer ? (
          footer(handleSubmit, handleReset, handleCustom)
        ) : (
          <PopupFooter>
            <span className="btn btn-reset" onClick={handleReset} data-testid="filter-popup-reset-btn">
              重置
            </span>
            <span className="btn btn-submit" onClick={handleSubmit} data-testid="filter-popup-submit-btn">
              确定
            </span>
          </PopupFooter>
        )}
      </Popup>
    </>
  );
});
