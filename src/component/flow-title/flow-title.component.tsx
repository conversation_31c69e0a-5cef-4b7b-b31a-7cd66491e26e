import { useMemo } from 'react';

export const Statuses: (StatusesType | undefined)[] = [
  { color: '#6c757d', name: '操作人填写' },
  { color: '#6c757d', name: '操作人填写' },
  { color: '#d9534f', name: '监护人审批' },
  { color: '#5bc0de', name: '值班负责人审批' },
  { color: '#417690', name: '值长审批' },
  { color: '#f0ad4e', name: '值长发令' },
  { color: '#d6a214', name: '监护人汇报' },
  { color: '#0275d8', name: '值长审批' },
  { color: '#6f42c1', name: '监护人终结' },
  { color: '#a94442', name: '监察审核' },
  { color: '#5cb85c', name: '已完成' },
  { color: '#FF5722', name: '未执行完成' },
  { color: '#6c757d', name: '已作废' },
];

interface FlowTitleProps {
  state?: string;
  guardianId?: string;
}

const FlowTitleComponent = ({ state = '0', guardianId = null }: FlowTitleProps) => {
  const { title, color } = useMemo(() => {
    const title = Statuses[Number(state)]?.name;
    // 如果是监护人审批，且监护人id不为空，则显示红色
    const color = Boolean(Number(state) === 1 ? guardianId : '') ? '#f00' : Statuses[Number(state)]?.color;
    return {
      title,
      color,
    };
  }, [state]);
  return (
    <span
      style={{
        fontWeight: 'bold',
        color,
      }}
    >
      {title}
    </span>
  );
};


export default FlowTitleComponent;