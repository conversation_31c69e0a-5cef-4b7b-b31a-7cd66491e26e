import { Component } from 'react';
import { Toast, Switch } from 'antd-mobile';
import { vmsroot } from '@/utils/constant';
import styles from './flv.less';
import playerAddImg from '@/assets/video/playerAdd.png';
import playerSubImg from '@/assets/video/playerSub.png';
import playerChangeImg from '@/assets/video/playerChange.png';

// 全局设置，减少控制台输出
(window as any).flvjs.LoggingControl.enableAll = false;
(window as any).flvjs.LoggingControl.enableDebug = false;
(window as any).flvjs.LoggingControl.enableVerbose = false;
(window as any).flvjs.LoggingControl.enableInfo = false;
(window as any).flvjs.LoggingControl.enableWarn = false;
(window as any).flvjs.LoggingControl.enableError = false;

type Props = {
    option: any;
};
let timer = null
export default class Flv extends Component<Props> {
    state = {
        isFull: false,
        controller: false,
        doeToa: null,
        scale: 1,
    };

    componentDidMount() {
        console.log('333', this.video);
        const {
            option: { url },
        } = this.props;
        if (this.video) {
            const scale = this.video.offsetWidth / 600;
            this.setState({ scale });
            this.createPlayer(url);
        }
        document.addEventListener('fullscreenchange', this.addEsc);
    }
    componentWillReceiveProps(nextProps) {
        console.log('nextProps', nextProps)
        this.createPlayer(nextProps.option.url);
    }

    componentWillUnmount() {
        this.stopPlayer();
        document.removeEventListener('fullscreenchange', this.addEsc);
    }

    addEsc = () => {
        const scale = this.video.offsetWidth / 600;
        this.setState({ scale });
    };

    createPlayer = (url) => {
        if (!url) return;
        const player = window.flvjs.createPlayer(
            {
                type: 'flv',
                url,
                isLive: true,
                cors: true,
                // hasAudio: false,
                hasAudio: url.includes('/vmlive/'), //因为海康摄像头声音解码上有问题，所以要判断是否是海康摄像头，如果是，就不开声音，否则会出问题。
            },
            {
                // enableWorker: true,
                // stashInitialSize: 128,
                // enableStashBuffer: false,
                // lazyLoad: false,

                enableWorker: false,
                stashInitialSize: 256,
                enableStashBuffer: false,
                autoCleanupSourceBuffer: true,
                lazyLoad: false,
            },
        );
        this.player = player;
        player.attachMediaElement(this.video);
        player.load();
        player.play();
        this.setState({ controller: true }, this.closeController)
        // // vidwo标签播放过程事件，用于监测延迟
        // this.inter = setInterval(() => {
        //   // Chrome最小化或标签后置，会自动暂停，不用渲染，直接退出
        //   if (this.video.paused) return;

        //   // 发现延迟超过一定时间，就直接设置到最新
        //   const bf = this.video.buffered;
        //   let endtime = -1;
        //   for (let i = 0; i < bf.length; i++)
        //     if (bf.end(i) > endtime) endtime = bf.end(i);
        //   if (endtime > 0 && endtime - this.video.currentTime > 0.5) {
        //     this.video.currentTime = endtime - 0.01;
        //   }

        //   // 清除“网络异常”样式，恢复正常播放样式
        //   // if(this.video.className!="playerContainer")
        //   //     this.video.className="playerContainer";
        // }, 1000);

        //设置一个定时器，检查由于网络延迟或断流，造成的视频播放延迟，如果超过允许范围，则调整当前播放时间
        this.inter = setInterval(() => {
            if (this.video && this.video.buffered && this.video.buffered.length) {
                //console.log('Player'+id+': '+(this.video.currentTime - this.video.buffered.end(0)));
                if (Math.abs(this.video.buffered.end(0) - this.video.currentTime) >= 1) {
                    this.video.currentTime = this.video.buffered.end(0) - 0.1;
                    player.unload();
                    player.load();
                    player.play();
                    //console.log('Player'+id+': Because time delay, SetCurrentTime!');
                }
            } else {
                //player.play();
                //console.log('Player'+id+': Because Buffer 0, Replay!');
            }
        }, 2000);

        // 播放期间由于任何原因发生错误
        player.on(window.flvjs.Events.ERROR, (err) => {
            // // 显示“网络异常”样式，需前端开发，注意：网络错误会连续触发
            // if(playerContainer.className!="playerContainerERR")
            //     playerContainer.className="playerContainerERR";

            // 发生错误就停止，并重连，再发生错误会又执行这个过程
            player.pause();
            player.unload();
            setTimeout(function () {
                player.load();
                player.play();
            }, 30000);
        });

        // 窗口关闭或者刷新的时候，一定要执行以下代码，否则会触发网络错误
        window.addEventListener('beforeunload', (event) => {
            this.stopPlayer();
        });
    };

    stopPlayer = () => {
        if (this.player) {
            this.player.pause();
            this.player.unload();
            this.player.destroy();
            this.player = null;
            console.log('3333')
        }

        clearInterval(this.inter);

    };

    handleIcon = (key) => {
        return (
            // <div onMouseDown={()=>this.handleMouseDown(key)} >{key === 9 ? <Icon   type="plus-circle" /> : key === 10 ? <Icon  type="minus-circle" /> : <Icon  type="backward" />}</div>
            <div onMouseDown={() => this.handleMouseDown(key)}>
                {key === 9 ? (
                    <img style={{ width: '1em' }} src={playerAddImg} alt="" />
                ) : key === 10 ? (
                    <img style={{ width: '1em' }} src={playerSubImg} alt="" />
                ) : (
                    <img style={{ width: '1em' }} src={playerChangeImg} alt="" />
                )}
            </div>
        );
    };
    // 开启、关闭声音
    handleRadio = (check) => {
        this.player.muted = !check;
    };

    handleMouseDown = (key) => {
        this.handleControl(key);
        const _this = this;
        function mouseUp() {
            _this.handleControlStop();
            document.removeEventListener('mouseup', mouseUp);
        }
        /** 松开鼠标停止摄像头转动 */
        document.addEventListener('mouseup', mouseUp);
    };
    /** 启动控制 */
    handleControl = (key, speed = 3) => {
        const { cameraIndexCode } = this.props.option;
        fetch(`${vmsroot}/vmserver.cgi?cmd=ptz&ipc_uuid=${cameraIndexCode}&ptz_cmd=${key}&ptz_speed=${speed}`);
    };
    /** 停止控制 */
    handleControlStop = () => {
        const { cameraIndexCode } = this.props.option;
        fetch(`${vmsroot}/vmserver.cgi?cmd=ptz&ipc_uuid=${cameraIndexCode}&ptz_stop=1`);
    };
    /** 截图 */
    handleDownLoad = () => {
        const { cameraIndexCode, fillName } = this.props.option;
        fetch(`${vmsroot}/vmserver.cgi?cmd=jpg&ipc_uuid=${cameraIndexCode}&jpg_quality=1`)
            .then((res) => {
                if (res instanceof Response) {
                    return res.json();
                }
            })
            .then((res) => {
                if (res.CODE == 0) {
                    this.setState({
                        doeToa: Toast.show(
                            {
                                content: `正在下载：${fillName}`,
                                duration: 0
                            }
                        ),
                    })
                    fetch(`${vmsroot}/${res.URL}`)
                        .then((response) => {
                            return response.blob();
                        })
                        .then((blob) => {
                            const blobUrl = window.URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = blobUrl;
                            a.target = '_blank';
                            a.style.display = 'none';
                            document.body.appendChild(a);
                            a.download = fillName;
                            a.click();
                            window.URL.revokeObjectURL(blobUrl);
                            document.body.removeChild(a);
                            this.state.doeToa.close();
                        })
                        .catch((err) => {
                            // message.error('下载失败')
                            this.state.doeToa.close();
                        });
                }
            });
    };

    /** 全屏 */
    handleDou = (e) => {//全屏
        e.stopPropagation();
        const div = document.getElementById(this.props.option.url);
        div.requestFullscreen();
        this.setState({ isFull: true })
    };
    exitFullScreens(e) {//退出全屏
        e.stopPropagation();
        this.setState({ isFull: false })
        const exitMethod = document.cancelFullScreen || document.webkitCancelFullScreen || document.mozCancelFullScreen || document.exitFullScreen;
        if (exitMethod) {
            exitMethod.call(document);
        } else if (typeof window.ActiveXObject !== "undefined") {
            const wscript = new ActiveXObject("WScript.Shell");
            if (wscript != null) {
                wscript.SendKeys("{F11}");
            }
        }
    }
    closeController = () => {//关闭控制
        clearTimeout(timer)
        timer = setTimeout(() => {
            this.setState({
                controller: false
            })
        }, 4000)
    }
    openController = () => {//打开控制
        this.setState({
            controller: true
        })
        this.closeController()
        //window.document.fullscreenElement
    }
    render() {
        const { option } = this.props;
        return (
            <div
                className={styles.box}
                style={{ fontSize: 20 * this.state.scale }}
                id={option.url}
                // onDoubleClick={this.handleDou}
                allowFullScreen={true}
                onClick={this.openController}
            >
                <video
                    crossOrigin="anonymous"
                    onContextMenu={() => false}
                    ref={(el) => (this.video = el)}
                    className={styles.player}
                    preload="none"
                    autoPlay
                    muted="muted"
                >
                    您的浏览器不支持 video 标签
                </video>
                <div
                    onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                    }}
                    onMouseDown={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                    }}
                    onDoubleClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                    }}
                    style={{ display: option.ISBall ? 'block' : 'none' }}
                    className={styles.position}
                >
                    <div className={styles.topLeft}>{this.handleIcon(1)}</div>
                    <div className={styles.top}>{this.handleIcon(2)}</div>
                    <div className={styles.topRight}>{this.handleIcon(3)}</div>
                    <div className={styles.left}>{this.handleIcon(4)}</div>
                    <div className={styles.right}>{this.handleIcon(5)}</div>
                    <div className={styles.bottomLeft}>{this.handleIcon(6)}</div>
                    <div className={styles.bottom}>{this.handleIcon(7)}</div>
                    <div className={styles.bottomRight}>{this.handleIcon(8)}</div>
                    <div className={styles.small}>{this.handleIcon(9)}</div>
                    <div className={styles.big}>{this.handleIcon(10)}</div>
                    <div className={styles.downLoad} title="截图" onClick={this.handleDownLoad}>
                        <span className="icon iconfont" style={{ fontWeight: 'bold', background: '#fff', color: 'rgb(111 99 99)' }}>
                            &#xe6a6;
                        </span>
                    </div>
                </div>
                {/* <div className={styles.video}>
                    声音：
                    <Switch style={{ width: 50 }} onChange={this.handleRadio} checkedText="开启" uncheckedText="关闭" />
                </div> */}
                {
                    this.state.controller ?
                        <div className={`${styles["controller-container"]} ${this.state.isFull ? styles["full-controller-container"]:""}`}>
                            {this.state.isFull === false ?
                                // 打开全屏
                                <span onClick={this.handleDou} className={styles["full-screen"]}></span>
                                :
                                // 关闭全屏
                                <span onClick={this.exitFullScreens.bind(this)} className={styles["exit-full-screen"]}></span>}
                        </div> : ""
                }
            </div>
        );
    }
}
