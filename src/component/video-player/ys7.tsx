import React from 'react';
import styles from './flv.less';
import 'ezuikit-js';
import { LeftOutline } from 'antd-mobile-icons'

type Props = {
    option: any;
    accessToken: string;
};
let flag = false;

let timer = null
export default class YSY extends React.PureComponent<Props> {
    video = null;

    state = {
        controller: false,
        player: null,
        scale: 1,
        id: '',
        changeText: '',
    };

    componentDidMount() {
        flag = true;
        const scale = this.video?.offsetWidth / 600;
        const id = `id${Math.random() * 1e16}`;
        this.setState({ scale, id });
        this.setState({ changeText: JSON.stringify(this.props.option) || {} }, () => {
            setTimeout(this.createPlayer, 300);
            document.addEventListener('fullscreenchange', this.addEsc);
        })

    }

    componentWillReceiveProps(nextProps) {
        const str = JSON.stringify(nextProps.option);
        if (flag && this.state.changeText !== str) {
            this.setState({
                changeText: str,
            })
            console.log('nextProps', nextProps);
            this.stopPlayer();
            setTimeout(() => this.createPlayer(), 300);
        }
    }

    componentWillUnmount() {
        this.stopPlayer();
        document.removeEventListener('fullscreenchange', this.addEsc);
    }

    addEsc = () => {
        const scale = this.video.offsetWidth / 600;
        this.setState({ scale });
    };

    handleDou = () => {
        console.log("全屏")
        const { player } = this.state;
        if (player && player.fullScreen) player.fullScreen();
    };

    createPlayer = () => {
        const { accessToken, option } = this.props;
        const { id } = this.state;
        const $id = document.getElementById(id);
        if ($id && window.EZUIKit?.EZUIKitPlayer) {
            var player = new window.EZUIKit.EZUIKitPlayer({
                id, // 视频容器ID
                template: 'security',
                accessToken,
                width: (this.video && this.video.offsetWidth) || '',
                height: (this.video && this.video.offsetHeight) || '',
                url: option.url,
                header: []
            });
            this.setState({ player, controller: true }, this.closeController);
        } else {
            console.log('延迟加载', window.EZUIKit?.EZUIKitPlayer)
            setTimeout(() => this.createPlayer(), 300);
        }
    };

    stopPlayer = () => {
        try {
            const { player } = this.state;
            if (player && player.stop) player.stop();
        } catch (error) {
            console.log(error);
        }
    };
    closeController = () => {//关闭控制
        clearTimeout(timer)
        timer = setTimeout(() => {
            this.setState({
                //controller: false
            })
        }, 4000)
    }
    openController = () => {//打开控制
        this.setState({
            controller: true
        })
        this.closeController()
    }
    render() {
        const { id } = this.state;
        return (
            <div
                data-id={id}
                ref={(el) => (this.video = el)}
                className={styles.box}
                style={{ fontSize: 20 * this.state.scale }}
            // onClick={this.openController}
            // onDoubleClick={this.handleDou}
            >
                <div className={styles.player}>
                    <div id={id} />
                </div>
                {/* {
                    this.state.controller ?
                        <div className={styles["controller-container"]}>
                            <span onClick={this.handleDou} className={styles["full-screen"]}></span>
                        </div> : ""
                } */}
            </div>
        );
    }
}
