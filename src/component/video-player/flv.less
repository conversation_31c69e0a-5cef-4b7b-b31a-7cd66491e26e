.box {
    position: relative;
    width: 100%;
    height: 100%;
    background: #000;

    &::before {
        position: absolute;
        top: 50%;
        left: 50%;
        color: #fff;
        font-size: 14px;
        transform: translate(-50%, -50%);
        content: '加载中...';
        // animation: loading 3s ease infinite;
        // opacity: 0;
    }

    .player {
        position: absolute !important;
        width: 100%;
        height: 100%;
        object-fit: fill;
    }

    .position {
        font-size: 1em;
        opacity: 0;

        >div {
            position: absolute;
        }

        .topLeft {
            top: 30px;
            left: 30px;
            transform: rotate(45deg);
            cursor: pointer;
        }

        .top {
            top: 30px;
            left: 50%;
            transform: translateX(-50%) rotate(90deg);
            cursor: pointer;
        }

        .topRight {
            top: 30px;
            right: 30px;
            transform: rotate(135deg);
            cursor: pointer;
        }

        .left {
            top: 50%;
            left: 30px;
            transform: translateY(-50%);
            cursor: pointer;
        }

        .right {
            top: 50%;
            right: 30px;
            transform: translateY(-50%) rotate(180deg);
            cursor: pointer;
        }

        .bottomLeft {
            bottom: 30px;
            left: 30px;
            transform: rotate(-45deg);
            cursor: pointer;
        }

        .bottom {
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%) rotate(-90deg);
            cursor: pointer;
        }

        .bottomRight {
            right: 30px;
            bottom: 30px;
            transform: rotate(-135deg);
            cursor: pointer;
        }

        .big {
            top: 50%;
            left: 25%;
            transform: translate(-25%, -50%);
            cursor: pointer;
        }

        .small {
            top: 50%;
            left: 75%;
            transform: translate(-75%, -50%);
            cursor: pointer;
        }

        .downLoad {
            right: 0;
            bottom: 0;
            cursor: pointer;
        }
    }

    &:hover .position {
        opacity: 1;
        transition: all 1s;
    }
}

@keyframes loading {
    0% {
        opacity: 0.2;
    }

    30% {
        opacity: 0.5;
    }

    50% {
        opacity: 1;
    }

    70% {
        opacity: 0.5;
    }

    100% {
        opacity: 0.2;
    }
}

.video {
    width: 130px;
    height: 30px;
    background: rgba(0, 0, 0, 0.509);
    position: absolute;
    bottom: 10px;
    right: 10px;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 5px;
}

.controller-container {
    position: absolute;
    top: 0;
    width: 100%;
    padding: 30px 30px;
    background: rgba(0, 0, 0, .5);
    text-align: right;
    box-sizing: border-box;
z-index: 100;

    .full-screen {
        display: inline-block;
        background: url(../../assets/video/full_screen.png) no-repeat;
        width: 100px;
        height: 100px;
        background-size: cover;
    }

    .exit-full-screen {
        display: inline-block;
        background: url(../../assets/video/exit_full_screen.png) no-repeat;
        width: 100px;
        height: 100px;
        background-size: cover;
    }
}

.full-controller-container {
    padding: 10px 20px!important;

    span {
        width: 70px!important;
        height: 70px!important;
    }
}