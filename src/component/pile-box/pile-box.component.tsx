import { FC } from 'react';

import <PERSON><PERSON> from '../canvas/pile';
import { totalValue } from '@/utils/data-processing-tools';
import styles from './pile-box.component.less';

interface Props {
  data: any[];
  unit: string;
  message?: { title: string; value: any };
}
const PileBox: FC<Props> = ({ data, unit, message }) => {
  return (
    <div className={styles.scheduleCard}>
      <div className={styles.scheduleLeft}>
        <Pile
          children={
            <div className={styles.piledays}>
              <span className={styles.days}>{totalValue(data, 'count')}</span>
              {unit}
              <div className={styles.describe}>共计</div>
            </div>
          }
          list={data}
          color={data?.map((item) => item.color)}
        />
      </div>
      <div className={styles.scheduleRight}>
        {data.map((item, index) => (
          <div className={styles['info-row']} style={{ width: '100%' }} key={index}>
            <div className={styles.radius} style={{ background: item?.color[1] }}></div>
            <div className={styles.name}>{item?.name?.substr(0, 4)}</div>
            <div className={styles.percent}>{item?.percent || '-%'}</div>
            <div className={styles.daysNum} style={{ color: item?.color[1] }}>{item?.count}{unit}</div>
          </div>
        ))}
        {message && (
          <div className={styles.message}>
            <div className={styles.title}>{message.title}</div>
            <span className={styles.value}>{message.value}</span>
          </div>
        )}
      </div>
    </div>
  );
};
export default PileBox;
