.scheduleCard {
  display: flex;
  width: 100%;
  height: 100%;

  .scheduleLeft {
    width: 48%;
  }

  .scheduleRight {
    width: 52%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    gap: 50px;

    .info-row {
      height: 40px;
      font-size: 32px;
      line-height: 40px;
      font-family: PingFang SC;
      font-weight: 500;
      display: flex;
      align-items: center;

      .radius {
        width: 24px;
        height: 24px;
        border-radius: 12px;
        margin-right: 20px;
      }

      .name {
        width: 20%;
        margin-right: 80px;
      }

      .percent {
        width: 30%;
        padding-left: 10px;
        line-height: 85px;
      }

      .daysNum {
        font-family: PangMenZhengDao;
        font-weight: 400;
      }

    }

    .message {
      font-size: 32px;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;

      .title {
        width: 96px;
      }

      .value {
        padding-left: 10px;
      }
    }
  }
}

.piledays {
  font-size: 36px;
  font-weight: 400;
  color: #757575;
  text-align: center;

  .days {
    font-family: PangMenZhengDao;
    font-weight: 400;
    margin-right: 16px;
    color: #333333;
  }

  .describe {
    font-family: PingFang SC;
    font-weight: 500;
    font-size: 32px;
    color: #333333;
  }
}