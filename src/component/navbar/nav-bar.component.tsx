import React from 'react';
import { useLocation, history } from 'umi';
import { NavBar } from 'antd-mobile'
import { getRouteObject } from '@/utils/get-route-object.utils';
import styles from './nav-bar.component.less';

export type PropsNav = {
  backArrow?: boolean | React.ReactNode
  defineTitle?: string | React.ReactNode;
  currentProjectName?: string;
  left?: React.ReactNode;
  right?: React.ReactNode;
  onBack?: () => void;
}

const NavBarComponent: React.FC<PropsNav> = ({
  backArrow = true,
  defineTitle,
  currentProjectName,
  left,
  right,
  onBack
}) => {
  const location = useLocation();
  const routeObject = getRouteObject(location.pathname);
  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      history.back();
    }
  }
  return (
    <React.Fragment>
      <NavBar className={styles.nbar}
        style={{ paddingTop: `${window.top.localStorage?.topHeight ?? 0}px` }}
        backArrow={backArrow}
        left={left}
        right={right}
        onBack={handleBack}>
        {defineTitle || (currentProjectName ? `${currentProjectName} - ${routeObject?.name}` : routeObject?.name)}
      </NavBar>
    </React.Fragment>
  )
}

export default NavBarComponent;
