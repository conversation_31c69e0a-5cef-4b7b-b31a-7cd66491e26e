.pageWrap {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .pieBox {
    width: 50%;
    height: 100%;
  }

  .customLegend {
    display: flex;
    flex-direction: column;
    gap: 50px;
    width: 50%;

    .item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      // gap: 30px;
      font-size: 32px;

      .status {
        display: flex;
        align-items: center;
        gap: 20px;
        width: 40%;

        .icon {
          display: inline-block;
          width: 24px;
          height: 24px;
          border-radius: 50%;
        }
      }

      .desc {
        font-weight: 800;
        width: 30%;
        text-align: right;
      }
    }
  }
}