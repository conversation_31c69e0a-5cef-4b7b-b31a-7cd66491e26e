import { useEffect, useState } from "react";
import styles from "./pie-component.less";
import { Pie } from "@ant-design/plots";
import { calculatePercentage, totalValue } from "@/utils/data-processing-tools";
interface Props {
  data: { value: number; type: string, percent: string; }[]
  colorMap: { [key: string]: { start: string, end: string } }
}
const PieComp: React.FC<Props> = ({ data = [], colorMap }) => {
  const config = {
    data,
    angleField: 'value',
    colorField: 'type',
    legend: false,
    innerRadius: 0.8,
    tooltip: (
      d, // 每一个数据项
      index, // 索引
      data, // 完整数据
      column, // 通道
    ) => ({
      name: d.type,
      color: colorMap[d.type]?.end,
      value: d.value + '个',
    }),
    // 环上文字
    // labels: [
    //   { text: '', style: { fontSize: 10, fontWeight: 'bold' } },
    //   {
    //     text: '',
    //   },
    // ],
    style: {
      stroke: '#fff',
      inset: 1,
      radius: 20,
      fill: ({ type }) => {
        if (colorMap) {
          return `radial-gradient(circle at 0% 0%, ${colorMap[type]?.start} 20%, ${colorMap[type]?.end} 100%)`
          // return `linear-gradient(0deg,  ${colorMap[type].start} 0% ,${colorMap[type].end}  100%)`
        } else {
          return;
        }
      }
    },
    // 环内文字
    annotations: [
      {
        type: 'text',
        style: {
          text: `${totalValue(data, 'value')} 个\n共计`,
          x: '50%',
          y: '50%',
          textAlign: 'center',
          fontSize: 20,
          fontStyle: 'normal',
        },
      },
    ],
    // 自定义色板,默认色板颜色顺序：#2389ff、#0dcccc、#f18e56、#d787ff、#7f6bff
    // scale: {
    //   color: {
    //     palette: 'sinebow',
    //     offset: (t) => {
    //       return t * 0.8 + 0.4
    //     },
    //   },
    // },
  };

  return (
    <div className={styles.pageWrap}>
      <div className={styles.pieBox}>
        <Pie {...config} />
      </div>

      <div className={styles.customLegend}>
        {data?.map((item) => (
          <div className={styles.item} key={item?.type}>
            <div className={styles.status}>
              <span className={styles.icon} style={{ background: colorMap[item.type]?.end }} />
              {item.type}
            </div>
            <div>{calculatePercentage(item.value, totalValue(data, 'value'), 1)}%</div>
            <div className={styles.desc} style={{ color: colorMap[item.type]?.end }}>
              {item.value}个
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
export default PieComp;

