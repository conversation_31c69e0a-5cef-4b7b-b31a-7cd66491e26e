import { Column } from '@ant-design/plots';
import React from 'react';
interface Props {
  data: { xData: string; yData: number; label: string }
  isGroup?: boolean
  scrollRatio?: number | null
  maxWidth?: number
  // slider?: [number, number] | null
}

const ColumnsComponent: React.FC<Props> = ({ data, isGroup = false, scrollRatio = null, maxWidth = 200 }) => {
  const config = {
    data,
    autoFit: true,
    margin: 0,
    marginTop: 10,
    xField: 'xData',
    yField: 'yData',
    group: isGroup,//分组
    sort: {//排序，如果是日期会自动排序
      // reverse: false,//倒序
      // by: 'y',
    },
    axis: {
      y: {
        tick: false//刻度线隐藏
      },
      x: {
        tick: false,
        labelAutoRotate: false
      }
    },
    legend: {
      // 图例位置
      color: {
        layout: {
          justifyContent: 'flex-end',
          alignItems: 'center',
          flexDirection: 'row',
        },
      },
    },
    scrollbar: (scrollRatio && scrollRatio < 1) ? {
      x: {
        ratio: scrollRatio,
      },
    } : {},

    style: {
      maxWidth,
    },
    // slider: slider ? {
    //   x: {
    //     values: slider,
    //   },
    // } : {},
  };

  const newConfig = isGroup ? { ...config, colorField: 'label' } : { ...config }

  return <Column {...newConfig} />


};
export default ColumnsComponent;