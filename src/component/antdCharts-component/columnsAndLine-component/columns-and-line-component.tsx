import { Column, DualAxes } from '@ant-design/plots';
import React from 'react';
interface Props {
  columnData: { xData: string; yData: number; label: string }[]
  lineData: { xData: string; yData: number; }[]
  lineLabel: string
  isGroup?: boolean
  scrollRatio?: number | null
  maxWidth?: numebr
}

const ColumnsAndLineComponent: React.FC<Props> = ({ columnData, lineData, lineLabel, isGroup = false, scrollRatio = null, maxWidth = 200 }) => {
  const config = {
    xField: 'xData',
    children: [
      {
        data: columnData,
        type: 'interval',
        yField: 'yData',
        colorField: 'label',
        group: isGroup,
        interaction: { elementHighlightByColor: { background: true } },
      },
      {
        data: lineData,
        type: 'line',
        yField: lineLabel,
        style: { lineWidth: 2 },
        axis: { y: { position: 'right' } },
        interaction: {
          tooltip: {
            crosshairs: false,
            marker: false,
          },
        },
      },
    ],
    legend: {
      // 图例位置
      color: {
        layout: {
          justifyContent: 'flex-end',
          alignItems: 'center',
          flexDirection: 'row',
        },
      },
    },
    scrollbar: (scrollRatio && scrollRatio < 1) ? {
      x: {
        ratio: scrollRatio,
      },
    } : {},
    style: {
      maxWidth,
    },
  };
  return <DualAxes {...config} />


};
export default ColumnsAndLineComponent;