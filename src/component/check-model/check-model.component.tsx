import { RightOutline } from "antd-mobile-icons";
import { useEffect, useState } from "react";
import { CloseOutline } from "antd-mobile-icons";
import styles from "./check-model.component.less";
import { <PERSON><PERSON>, CascaderView, El<PERSON>sis, Popup, Toast } from "antd-mobile";
import {
  getModelList,
  getNodeModelList,
  getNodeTree,
} from "@/services/quality-manage.service";
import { DeleteOutline } from "antd-mobile-icons";
import Tree from "rc-tree";
import "rc-tree/assets/index.css";

interface ItemProps {
  description: string;
  extendIds: string; //构件id
  modelId?: string; //构件所属的项目id
}
interface Props {
  value?: ItemProps[];
  onChange?: (value: ItemProps[]) => void;
  radioBox?: boolean;
}

// Map深序遍历树,callback必须带有返回值，用于代替原数组元素
function deepMapTreeFn(
  arr: any[],
  callback: (item?: any) => any,
  entryKey = "children"
) {
  if (!Array.isArray(arr)) {
    return arr;
  }
  // 递归遍历数组
  for (let i = 0; i < arr.length; i++) {
    const item = arr[i];
    // 修改每一项数据
    arr[i] = callback(item);
    // 递归遍历子数组
    if (Array.isArray(arr[i]?.[entryKey])) {
      deepMapTreeFn(arr[i]?.[entryKey], callback, entryKey);
    }
  }
  return arr;
}

// forEach深序遍历树，不返回新数组
const deepFn = (
  arr: any[],
  fn = (params: any) => {},
  children = "children"
) => {
  if (Array.isArray(arr)) {
    arr.forEach((item) => {
      if (Array.isArray(item[children])) {
        deepFn(item[children], fn, children);
      }
      if (fn) fn(item);
    });
  }
};

const CheckModel: React.FC<Props> = ({ value, onChange, radioBox }) => {
  const [visible, setVisible] = useState(false);
  const [visibleModel, setVisibleModel] = useState(false);
  const [nodetree, setNodetree] = useState<any>(); //项目树
  const [selectedKey, setSelectedKey] = useState<string[]>([]); //选中的项目id
  const [modelTree, setModelTree] = useState<any>(); //模型树
  const [checkedKeys, setCheckedKeys] = useState<string[]>([]); //勾选的构件ids
  const [checkedData, setCheckedData] = useState<ItemProps[]>([]); //勾选的构件数据

  // 接口获取-模型树
  const requestModelTree = () => {
    // 根据项目节点id获取模型id
    getNodeModelList(selectedKey?.[0])
      .then((res: any) => {
        if (res?.data?.data?.length > 0) {
          const modelId = res?.data?.data
            ?.filter((item: any) => item.currentVersion === 1)
            .map((item: any) => item.bimModelId);
          return modelId;
        }
      })
      .then((resp) => {
        // 根据模型id获取模型树
        return getModelList({ modelId: resp?.[0] || "" });
      })
      .then((response) => {
        if (response.data) {
          setModelTree(JSON.parse(response.data));
          setVisibleModel(true);
        } else {
          Toast.show({ content: "未查询到模型" });
        }
      });
  };

  // 弹出项目树
  const handleSelect = () => {
    setVisible(true);
    setSelectedKey(value?.map((item) => item?.modelId)); // 如果已选择，默认回填
  };

  // 选择项目
  const onSelect = (keys) => {
    setSelectedKey(keys);
  };

  // 取消项目选择
  const handleCancel = () => {
    setVisible(false);
  };

  // 获取项目节点对应构件
  const handleConfirmNode = () => {
    if (selectedKey.length) {
      // 获取接口数据
      requestModelTree();
      // 数据回显
      if (value?.length) {
        const checkedKeys = value?.map((item: ItemProps) => {
          return item.extendIds;
        });
        setCheckedKeys(checkedKeys);
        setCheckedData(value);
      } else {
        setCheckedKeys([]);
        setCheckedData([]);
      }
    } else {
      Toast.show({ content: "请选择项目" });
    }
  };

  // 勾选构件
  const onCheck = (keys: string[]) => {
    // 从模型树叶子节点中筛选勾中的构件id
    const filterKeys = [];
    const checkedData = [];
    deepFn(
      modelTree,
      (item: any) => {
        if (!item?.children?.length && keys.includes(item.key)) {
          filterKeys.push(item.key);
          checkedData.push({
            description: item.name,
            extendIds: item.key, //构件id
            modelId: selectedKey?.[0], //构件所属项目id
          });
        }
      },
      "children"
    );
    setCheckedKeys(filterKeys);
    setCheckedData(checkedData);
  };

  // 取消模型选择
  const handleCancelModel = () => {
    setVisibleModel(false);
    setSelectedKey([]);
  };

  // 确认选择构件
  const handleConfirmModel = () => {
    if (checkedKeys?.length) {
      setVisible(false);
      setVisibleModel(false);
      onChange?.(checkedData);
    } else {
      Toast.show({
        content: "请选择构件",
      });
    }
  };

  // 删除已选构件
  const handleDelete = (extendIds: string) => {
    const filter = value?.filter((item) => item.extendIds !== extendIds);
    onChange?.(filter);
  };

  useEffect(() => {
    // 获取项目树
    getNodeTree({}).then((res) => {
      const { data } = res.data;
      const filterData = data.filter((item: any) => {
        return item.type === "4";
      });
      const newData = deepMapTreeFn(
        filterData,
        (item) => {
          return {
            ...item,
            key: item?.id,
            title: item.modelUpload ? `${item?.name}*` : item?.name,
          };
        },
        "children"
      );
      setNodetree(newData);
    });
  }, []);

  return (
    <div className={styles.selectItem}>
      <div className={styles.selectTip} onClick={handleSelect}>
        <div>
          {value?.length ? (
            <span>已选择 {value.length} 个模型部位</span>
          ) : (
            "选择模型部位"
          )}
        </div>
        <RightOutline />
      </div>
      {value?.length ? (
        <div className={styles.selectList}>
          {value?.map((item) => (
            <div key={item.extendIds}>
              {" "}
              <div>{item.description}</div>
              <CloseOutline
                color="var(--adm-color-text)"
                onClick={() => handleDelete(item.extendIds)}
              />
            </div>
          ))}
        </div>
      ) : (
        ""
      )}

      {/* 项目树单选浮层 */}
      <Popup
        visible={visible}
        getContainer={null}
        onMaskClick={handleCancel}
        bodyStyle={{ borderRadius: "8px 8px 0 0" }}
      >
        <div className={styles.popupTitle}>
          <span />
          选择任务节点
          <CloseOutline onClick={handleCancel} />
        </div>
        <div
          style={{
            height: "auto",
            maxHeight: window.innerHeight * 0.8,
            minHeight: window.innerHeight * 0.4,
            overflow: "scroll",
          }}
        >
          <Tree
            treeData={nodetree}
            selectable
            checkable={false}
            defaultExpandAll
            onSelect={onSelect}
            selectedKeys={selectedKey}
            rootStyle={{
              maxHeight: window.innerHeight * 0.8,
              minHeight: window.innerHeight * 0.4,
            }}
          />
        </div>
        <div className={styles.btnGroups}>
          <Button color="primary" fill="outline" onClick={handleCancel}>
            取消
          </Button>
          <Button color="primary" onClick={handleConfirmNode}>
            获取模型树
          </Button>
        </div>
      </Popup>

      {/* 模型树多选浮层 */}
      <Popup
        visible={visibleModel}
        getContainer={null}
        onMaskClick={handleCancelModel}
        bodyStyle={{ borderRadius: "8px 8px 0 0" }}
      >
        <div className={styles.popupTitle}>
          <span />
          选择模型构件
          <CloseOutline onClick={handleCancelModel} />
        </div>
        <div
          style={{
            height: "auto",
            maxHeight: window.innerHeight * 0.8,
            minHeight: window.innerHeight * 0.4,
            overflow: "scroll",
          }}
        >
          <Tree
            treeData={modelTree}
            checkable
            selectable={false}
            rootStyle={{
              maxHeight: window.innerHeight * 0.8,
              minHeight: window.innerHeight * 0.4,
            }}
            onCheck={onCheck}
            checkedKeys={checkedKeys}
          />
        </div>
        <div className={styles.btnGroups}>
          <Button color="primary" fill="outline" onClick={handleCancelModel}>
            取消
          </Button>
          <Button color="primary" onClick={handleConfirmModel}>
            选择构件
          </Button>
        </div>
      </Popup>
    </div>
  );
};
export default CheckModel;
