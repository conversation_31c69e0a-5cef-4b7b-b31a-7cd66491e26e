import React from 'react';
import { Image, ImageProps } from 'antd-mobile';

type ImageIconProps = {
  /** 图片名称（assets/icons 下的 png 文件，不含扩展名） */
  name?: string;
  /** 图片 URL，优先级高于 name */
  url?: string;
  /** 图片 alt 属性 */
  alt?: string;
  /** 图片样式 */
  style?: React.CSSProperties;
  /** 图片类名 */
  className?: string;
} & ImageProps;

const ImageIcon: React.FC<ImageIconProps> = ({ name, url, alt = '', style, className, ...rest }) => {
  let src = '';
  let srcSet: string | undefined;

  if (url) {
    src = url;
  } else if (name) {
    src = require(`@/assets/icons/${name}.png`);
    srcSet = [
      `${require('@/assets/icons/'+name+'.png')} 1x`,
      `${require('@/assets/icons/'+name+'@2x.png')} 2x`,
      `${require('@/assets/icons/'+name+'@3x.png')} 3x`
    ].join(', ');
  }

  if (!src) return null;

  return (
    <Image
      src={src}
      srcSet={srcSet}
      alt={alt}
      style={style}
      className={className}
      fit="contain"
      {...rest}
    />
  );
};

export default ImageIcon;