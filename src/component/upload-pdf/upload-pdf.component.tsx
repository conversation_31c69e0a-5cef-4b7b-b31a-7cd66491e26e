import { Ellipsis } from 'antd-mobile';
import styles from './upload-pdf.component.less';
import { DeleteOutline } from 'antd-mobile-icons';
import { useEffect, useState } from 'react';
import { attachmentDelete, attachmentUpload } from '@/services/attachment';
import { urlPrefix } from '@/utils/request';

type FileProps = { id: number; fileId: string; name: string; url: string }
interface Props {
  value?: FileProps[],
  stylesInfo?: any;
  onChange?: (files: FileProps[]) => void;
}

const UploadPdf: React.FC<Props> = ({ value, stylesInfo, onChange }) => {
  const [pdfList, setPdfList] = useState<FileProps[]>([]);
  const [attachReportId, setAttachReportId] = useState<number>();

  useEffect(() => {
    if (value?.length) {
      setPdfList(value);
      setAttachReportId(value[0].id);
    }
  }, [value])

  const onUploadPdf = (event) => {
    const files = Object.values(event?.target?.files) ;
    files?.forEach(async (file: File) => {
      await attachmentUpload({files: [file], id: attachReportId ?? undefined}).then((res) => {
        if (res.code === '1') {
          setAttachReportId(res.data?.id)
          const fileId = res.data.attachmentContentList[res.data.attachmentContentList?.length - 1].id;
          pdfList.push({
            name: file.name as string,
            id: res.data?.id,
            fileId,
            url: `${urlPrefix}/Attachment/downloadAttachment/${fileId}`
          })
          setPdfList(JSON.parse(JSON.stringify(pdfList)))
        }
      })
      onChange?.(pdfList)
    })
  }

  const handleDeletePdf = (fileId) => {
    attachmentDelete({ id: fileId }).then((res) => {
      if (res.code === '1') {
        const files = pdfList.filter((item) => fileId !== item.fileId)
        setPdfList(files)
        onChange?.(files)
      }
    })

  }

  return (
    <div className={styles.uploadItem}>
      <div className={styles.uploadText}>
        支持扩展名：.pdf
        <span className={styles.uploadPdf}>上传<input type="file" accept=".pdf" multiple onChange={onUploadPdf} /></span>
      </div>
      {pdfList?.length ? (
        <div className={styles.fileList} style={stylesInfo}>
          { pdfList?.map((item, index)=> (
            <div key={index}>
              <Ellipsis direction='end' content={item.name} />
              <DeleteOutline color='var(--adm-color-text)' onClick={() => handleDeletePdf(item.fileId)} />
            </div>
          )) }
        </div>
      ) : ''}
    </div>
  )
};
export default UploadPdf;
