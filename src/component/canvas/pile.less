.chart {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;

  .cav {
    position: absolute;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100%;
    animation: rotate2 10s linear infinite;
  }

  .center {
    position: absolute;
    top: 50%;
    left: 50%;
    // z-index: -1;
    transform: translate(-50%, -50%);
  }
}

@keyframes rotate2 {
  form {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(359deg);
  }
}
