import React, { useRef, useEffect } from 'react';
import styles from './pile.less';

type Props = {
  list: { name: string; count: number }[];
  color?: string[][];
  children?: React.ReactNode;
  strokeStyle?: string; //中间环颜色
};

const Pile: React.FC<Props> = ({
  list,
  color = [
    ['#13a1f311', '#11BCFC'],
    ['#ffcf0d05', '#FFD93D'],
    ['#ffcf0d0f', ' #EC9E08'],
    ['#de453719', '#DE4637'],
  ],
  children,
  strokeStyle = '#f5f5f5',
}) => {
  const draw = () => {
    const eles = node.current?.childNodes || [];
    if (eles.length) {
      const canvas = eles[0];
      const canvas1 = eles[1];
      canvas.width = node.current.offsetWidth;
      canvas.height = node.current.offsetHeight;
      canvas1.width = node.current.offsetWidth;
      canvas1.height = node.current.offsetHeight;
      const width = canvas.width;
      const height = canvas.height;

      let r = width / 2 - 20;
      if (height < width) {
        r = height / 2 - 20;
      }

      const ctx = canvas.getContext('2d');
      const ctx1 = canvas1.getContext('2d');
      ctx.clearRect(0, 0, width, height);
      ctx.translate(width / 2, height / 2);
      ctx.beginPath();
      ctx.strokeStyle = strokeStyle;
      ctx.lineWidth = 6;
      ctx.arc(0, 0, r - 16, 0, Math.PI * 2);
      ctx.stroke();
      ctx.closePath();

      ctx1.clearRect(0, 0, width, height);
      ctx1.translate(width / 2, height / 2);

      let total = 0;
      list.forEach((item) => {
        total += item.count;
      });
      if (!total) return;
      const step = (Math.PI * 2 - Math.PI / 3) / total;
      let curpent = 0;

      for (let i = 0; i < list.length; i++) {
        ctx1.beginPath();
        ctx1.lineWidth = 10;
        const endPent = list[i].count * step;
        const x2 = r * Math.cos(curpent + endPent + (Math.PI / 12) * (i + 1));
        const y2 = r * Math.sin(curpent + endPent + (Math.PI / 12) * (i + 1));
        const x1 = r * Math.cos(curpent + (Math.PI / 12) * (i + 1));
        const y1 = r * Math.sin(curpent + (Math.PI / 12) * (i + 1));
        ctx1.lineCap = 'round';
        const grd = ctx1.createLinearGradient(x1, y1, x2, y2);
        grd.addColorStop('0', color[i][0]);
        grd.addColorStop('1.0', color[i][1]);
        ctx1.strokeStyle = grd;
        ctx1.arc(0, 0, r, curpent + (Math.PI / 12) * (i + 1), curpent + endPent + (Math.PI / 12) * (i + 1));
        curpent += endPent;
        ctx1.stroke();
        ctx1.closePath();
      }
    }
  };
  const resize = () => {
    draw();
  };
  useEffect(() => {
    setTimeout(() => {
      draw();
      window.addEventListener('resize', resize);
      const resizeObserver = new ResizeObserver((entries) => {
        for (let entry of entries) {
          resize();
        }
      });
      if (node.current) {
        resizeObserver?.observe(node.current);
      }
    }, 300);
  }, []);
  // 数据更改时重绘
  useEffect(() => {
    draw();
  }, [list]);

  const node = useRef(null);

  return (
    <div ref={node} className={styles.chart}>
      <canvas></canvas>
      <canvas className={styles.cav}></canvas>
      <div className={styles.center}>{children ? children : null}</div>
    </div>
  );
};

export default Pile;
