import { styled, history } from 'umi';
import { Card, Empty, Space } from 'antd-mobile';
import { CardProps } from 'antd';
import { RightOutline } from 'antd-mobile-icons';

// 定义 Props 类型
interface ListCardProps<T extends object> extends Omit<CardProps, 'title'> {
  data?: T[]; // 数据列表，默认为可选
  isMore?: boolean; // 是否显示“更多”按钮
  maxLength?: number; // 最大显示数量
  moreLink?: string; // “更多”链接
  children?: (data: T[]) => React.ReactNode; // 子组件或函数
  title: React.ReactNode | string; // 标题，必填
  subtitle?: React.ReactNode | string; // 副标题，可选
  type?: string; // 卡片类型
}
const ListCardWrapper = styled(Card)<{ subtitle?: React.ReactNode | string }>`
  .adm-card-header {
    padding-bottom: ${(props: { subtitle?: React.ReactNode | string }) => (props.subtitle ? '11.1111vw' : '')};
    font-size: 4.2592vw;
    font-weight: 500;
    letter-spacing: 0vw;
    line-height: 6.1675vw;
    color: rgba(51, 51, 51, 1);
  }
  .subTitle {
    position: absolute;
    left: 0;
    font-size: 3.3333vw;
    font-weight: 400;
    letter-spacing: 0vw;
    line-height: 4.8269vw;
    color: rgba(153, 153, 153, 1);
  }

  .footer {
    padding-top: 1.0185vw;
    border-top: .0926vw solid #e5e5e5;
    display: flex;
    justify-content: center;
    font-size: 3.8889vw;
    font-weight: 400;
    letter-spacing: 0vw;
    line-height: 5.6314vw;
    color: rgba(17, 84, 237, 1);
  }
`;

/**
 * 通用列表卡片
 * @param props
 * @param props.title 标题
 * @param props.data 数据
 * @param props.isMore 是否显示更多
 * @param props.subtitle 副标题
 * @param props.maxLength 卡片列表显示最大长度
 * @param props.moreLink 更多链接
 * @param props.children 子组件
 * @param props.type 卡片类型
 * @returns
 */
export const ListCard = <T extends object>(props: ListCardProps<T>) => {
  const {
    data = [], // 默认值为空数组，避免 undefined 导致错误
    isMore = false,
    maxLength = 3,
    moreLink = '',
    children,
    title,
    subtitle,
    type = null,
    ...rest // 捕获剩余的 CardProps 属性
  } = props;

  // 校验 maxLength 是否合法
  if (maxLength <= 0) {
    console.warn('maxLength should be greater than 0, defaulting to 3');
    return null; // 或者根据需求返回其他内容
  }
  // 提取重复使用的 subtitle
  const hasSubtitle = Boolean(subtitle);

  return (
    <ListCardWrapper
      title={
        <>
          {title}
          {hasSubtitle && <div className="subTitle">{subtitle}</div>}
        </>
      }
      subtitle={subtitle}
      extra={
        data?.length ? <RightOutline
          fontSize={'3.3333vw'}
          onClick={() => {
            history.push(moreLink);
          }}
        />:null
      }
      data-testid={"list-card_" + (type || title)}
      {...rest}
    >
      {/* 如果大于 maxLength 个，则截取 maxLength 个作为参数传递给子组件 */}
      {data?.length?children(data?.length > maxLength ? data?.slice(0, maxLength) : data):<Empty description="暂无数据" />}
      {/* 如果超出 maxLength 个，并且 isMore 为 true，则显示更多 */}
      {isMore && Array.isArray(data) && data.length > maxLength && (
        <div
          className="footer"
          onClick={() => {
            if (moreLink && typeof moreLink === 'string') {
              history.push(moreLink);
            } else {
              console.error('Invalid or missing moreLink:', moreLink);
            }
          }}
        >
          <Space>更多({data.length - maxLength})</Space>
        </div>
      )}
    </ListCardWrapper>
  );
};
