.boxLayout {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow-y: hidden;

  .content {
    flex-grow: 1;
    overflow-y: auto;
    // padding: 0 0 10px;
    background-color: #f5f5f5;
    position: relative;
  }

  .footer {
    // 未适配Safari，z-index在Safari不生效
    z-index: 999;
  }
}

//功能管理页面的layout样式
.functionBoxLayout {
  position: relative;

  .content {
    flex-grow: 1;
    background-color: #f5f5f5;
    position: unset;
  }

}