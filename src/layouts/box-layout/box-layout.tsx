import React from 'react';
import type { PropsNav } from '@/component/navbar/nav-bar.component';
import NavBarComponent from '@/component/navbar/nav-bar.component';
import styles from './box-layout.less';
import classnames from 'classnames';

interface Props {
  className?: string
  children?: React.ReactNode | string;
  footer?: React.ReactNode;
  header?: PropsNav & { hidden?: boolean };
}

const BoxLayout: React.FC<Props> = (props) => {

  return (
    <div className={classnames(styles.boxLayout, styles[props.className])} >
      <div className={styles.header} >{!props?.header?.hidden ? <NavBarComponent  {...props.header} ></NavBarComponent> : ''}</div>
      <div className={styles.content} >{props?.children}</div>
      <div className={styles.footer} >{props.footer}</div>
    </div>
  )
}

export default BoxLayout;
