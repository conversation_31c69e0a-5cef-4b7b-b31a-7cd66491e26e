import { Outlet, history, useLocation } from 'umi';
import { SafeArea, FloatingBubble } from 'antd-mobile';
import { AppstoreOutline } from 'antd-mobile-icons';
import styles from './index.less';
import { gethideHome } from '@/utils/get-route-object.utils';
import React, { useEffect } from 'react';

export default function Layout() {
  const location = useLocation();
  const { pathname } = location;

  useEffect(() => {
    if (pathname !== '/' && !pathname.includes('login')) {
      // 获取用户信息
      const userinfo = localStorage.getItem('userinfo');
    }
  }, []);

  return (
    <React.Fragment>
      <div className={styles.navs}>
        <div style={{ background: '#ace0ff' }}>
          <SafeArea position="top" />
        </div>
        <div>
            <Outlet context={{ some: 'p' }} />
        </div>
        <div style={{ background: '#ffcfac' }}>
          <SafeArea position="bottom" />
        </div>
      </div>
      {!gethideHome(pathname) && (
        <FloatingBubble
          axis="xy"
          magnetic="x"
          style={{
            '--initial-position-bottom': '100px',
            '--initial-position-right': '24px',
            '--edge-distance': '40px',
            '--z-index': '99999',
          }}
        >
          <AppstoreOutline onClick={() => history.push('/nav')} fontSize={32} />
        </FloatingBubble>
      )}
    </React.Fragment>
  );
}
