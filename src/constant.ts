import dayjs from 'dayjs';
import { color } from 'echarts';

export enum FORM_TYPE {
  ADD = 'ADD',
  EDIT = 'EDIT',
}
/** 表单类型 */
export const FORM_TYPE_MAP = {
  [FORM_TYPE.ADD]: '新增',
  [FORM_TYPE.EDIT]: '编辑',
};

/** QBS工程类型的枚举 */
export const QBS_PROJECT_TYPE_MAP = {
  '1': '单位工程',
  '2': '分部工程',
  '3': '分项工程',
  '4': '单元工程',
};

/** 用字符串0、1代表布尔的枚举 */
export const STRING_OF_BOOLEAN_MAP = {
  '0': '否',
  '1': '是',
};

/** 0、1代表布尔的枚举 */
export const NUMBER_OF_BOOLEAN_MAP = {
  0: '否',
  1: '是',
};

/** 验评状态枚举 */
export const WORKFLOW_STATE_MAP = {
  '0': '未开始',
  '1': '草稿',
  '2': '流程中',
  '3': '不通过',
  '4': '完成',
};
// 操作票 状态文字
export const WORKFLOW_STATE_MAP_TEXT = {
  0: '操作人填写',
  1: '操作人填写',
  2: '监护人审批',
  3: '值班负责人审批',
  4: '值长审批',
  5: '值长发令',
  6: '监护人汇报',
  7: '值长审批',
  8: '监护人终结',
  9: '监察审核',
  10: '已完成',
  11: '未执行完成',
  12: '已作废',
};
// 操作票 状态颜色
export const WORKFLOW_STATE_MAP_COLOR = {
  操作人填写: '#6c757d',
  监护人审批: '#d9534f',
  值班负责人审批: '#5bc0de',
  值长审批: '#417690', // 怎么有两个啊
  值长发令: '#f0ad4e',
  监护人汇报: '#d6a214',
  值长审批: '#0275d8',
  监护人终结: '#6f42c1',
  监察审核: '#a94442',
  已完成: '#5cb85c',
  未执行完成: '#FF5722',
  已作废: '#6c757d',
};

/**
 * 工作票状态集合
 *
 * WORKMNT_ELECTRICAL_STATUSES_MAP[ticketType][status] => 对应票种的状态数据
 *
 * 比如
 * ticketType => 1
 * status => 5(待值班负责人确认)
 * WORKMNT_ELECTRICAL_STATUSES_MAP[1][5] => { label: '待值班负责人确认', color: 'rgb(17, 84, 237)' }
 */
export const WORKMNT_ELECTRICAL_STATUSES_MAP = {
  // 电气一种工作票状态
  1: {
    '1': { label: '准备中', color: 'rgb(17, 84, 237)' },
    '2': { label: '待外委签发人签发', color: 'rgb(17, 84, 237)' },
    '3': { label: '待业主签发人签发', color: 'rgb(17, 84, 237)' },
    '4': { label: '待负责人确认提交', color: 'rgb(17, 84, 237)' },
    '5': { label: '待值班负责人确认', color: 'rgb(17, 84, 237)' },
    '6': { label: '待值长批准', color: 'rgb(17, 84, 237)' },
    '7': { label: '待许可人审核', color: 'rgb(17, 84, 237)' },
    '8': { label: '待执行确认', color: 'rgb(17, 84, 237)' },
    '9': { label: '执行中', color: 'rgb(17, 84, 237)' },
    '10': { label: '工作延期', color: 'rgb(17, 84, 237)' },
    '11': { label: '工作间断', color: 'rgb(17, 84, 237)' },
    '12': { label: '负责人变更', color: 'rgb(17, 84, 237)' },
    '13': { label: '待工作许可人审批', color: 'rgb(17, 84, 237)' },
    '14': { label: '待值长确认', color: 'rgb(17, 84, 237)' },
    '15': { label: '待许可人终结', color: 'rgb(17, 84, 237)' },
    '16': { label: '待负责人确认', color: 'rgb(17, 84, 237)' },
    '17': { label: '待监察审核', color: 'rgb(17, 84, 237)' },
    '18': { label: '已审察', color: 'rgba(1, 208, 102, 1)' },
    '19': { label: '已作废', color: 'rgba(252, 89, 90, 1)' },
    '20': { label: '已作废', color: 'rgba(253, 146, 40, 1)' },
  },
  // 电气二种
  2: {
    '1': { label: '准备中', color: 'rgb(17, 84, 237)' },
    '2': { label: '待外委签发人签发', color: 'rgb(17, 84, 237)' },
    '3': { label: '待业主签发人签发', color: 'rgb(17, 84, 237)' },
    '4': { label: '待负责人确认提交', color: 'rgb(17, 84, 237)' },
    '5': { label: '待值班负责人确认', color: 'rgb(17, 84, 237)' },
    '6': { label: '待值长批准', color: 'rgb(17, 84, 237)' },
    '7': { label: '待许可人审核', color: 'rgb(17, 84, 237)' },
    '8': { label: '待执行确认', color: 'rgb(17, 84, 237)' },
    '9': { label: '执行中', color: 'rgb(17, 84, 237)' },
    '10': { label: '工作延期', color: 'rgb(17, 84, 237)' },
    '11': { label: '工作间断', color: 'rgb(17, 84, 237)' },
    '12': { label: '负责人变更', color: 'rgb(17, 84, 237)' },
    '13': { label: '待工作许可人审批', color: 'rgb(17, 84, 237)' },
    '14': { label: '待值长确认', color: 'rgb(17, 84, 237)' },
    '15': { label: '待许可人终结', color: 'rgb(17, 84, 237)' },
    '16': { label: '待工作负责人确认', color: 'rgb(17, 84, 237)' },
    '17': { label: '待监察审核', color: 'rgb(17, 84, 237)' },
    '18': { label: '已审察', color: 'rgba(1, 208, 102, 1)' },
    '19': { label: '已作废', color: 'rgba(252, 89, 90, 1)' },
    '20': { label: '已作废', color: 'rgba(253, 146, 40, 1)' },
  },
  //机械一
  3: {
    '1': { label: '准备中', color: 'rgb(17, 84, 237)' },
    '2': { label: '外委签发人签发', color: 'rgb(17, 84, 237)' },
    '3': { label: '业主签发', color: 'rgb(17, 84, 237)' },
    '4': { label: '值班负责人确认', color: 'rgb(17, 84, 237)' },
    '5': { label: '值长批准', color: 'rgb(17, 84, 237)' },
    '6': { label: '许可人审批', color: 'rgb(17, 84, 237)' },
    '7': { label: '执行确认', color: 'rgb(17, 84, 237)' },
    '8': { label: '工作票执行', color: 'rgb(17, 84, 237)' },
    '9': { label: '工作延期', color: 'rgb(17, 84, 237)' },
    '10': { label: '负责人变更', color: 'rgb(17, 84, 237)' },
    '11': { label: '工作票交回', color: 'rgb(17, 84, 237)' },
    '12': { label: '值班负责人执行确定', color: 'rgb(17, 84, 237)' },
    '13': { label: '许可人终结', color: 'rgb(17, 84, 237)' },
    '14': { label: '监察审核', color: 'rgb(17, 84, 237)' },
    '15': { label: '已审察', color: 'rgba(1, 208, 102, 1)' },
    '16': { label: '已作废', color: 'rgba(252, 89, 90, 1)' },
    '17': { label: '已取消', color: 'rgba(253, 146, 40, 1)' },
  },
  //水工一
  4: {
    '1': { label: '准备中', color: 'rgb(17, 84, 237)' },
    '2': { label: '外委签发人确认', color: 'rgb(17, 84, 237)' },
    '3': { label: '业主签发人确认', color: 'rgb(17, 84, 237)' },
    '4': { label: '值班负责人确认', color: 'rgb(17, 84, 237)' },
    '5': { label: '值长批准', color: 'rgb(17, 84, 237)' },
    '6': { label: '许可人审批', color: 'rgb(17, 84, 237)' },
    '7': { label: '执行确认', color: 'rgb(17, 84, 237)' },
    '8': { label: '执行中', color: 'rgb(17, 84, 237)' },
    '9': { label: '工作延期', color: 'rgb(17, 84, 237)' },
    '10': { label: '负责人变更', color: 'rgb(17, 84, 237)' },
    '11': { label: '工作交回', color: 'rgb(17, 84, 237)' },
    '12': { label: '工作许可人审批', color: 'rgb(17, 84, 237)' },
    '13': { label: '值长确认', color: 'rgb(17, 84, 237)' },
    '14': { label: '工作许可人终结', color: 'rgb(17, 84, 237)' },
    '15': { label: '待负责人确认', color: 'rgb(17, 84, 237)' },
    '16': { label: '监察审核', color: 'rgb(17, 84, 237)' },
    '17': { label: '已审察', color: 'rgba(1, 208, 102, 1)' },
    '18': { label: '已作废', color: 'rgba(252, 89, 90, 1)' },
    '19': { label: '已取消', color: 'rgb(17, 84, 237)' },
  },
  //水工二
  5: {
    '1': { label: '准备中', color: 'rgb(17, 84, 237)' },
    '2': { label: '外委签发人确认', color: 'rgb(17, 84, 237)' },
    '3': { label: '业主签发人确认', color: 'rgb(17, 84, 237)' },
    '4': { label: '值班负责人确认', color: 'rgb(17, 84, 237)' },
    '5': { label: '值长批准', color: 'rgb(17, 84, 237)' },
    '6': { label: '许可人审批', color: 'rgb(17, 84, 237)' },
    '7': { label: '执行确认', color: 'rgb(17, 84, 237)' },
    '8': { label: '执行中', color: 'rgb(17, 84, 237)' },
    '9': { label: '工作延期', color: 'rgb(17, 84, 237)' },
    '10': { label: '负责人变更', color: 'rgb(17, 84, 237)' },
    '11': { label: '工作交回', color: 'rgb(17, 84, 237)' },
    '12': { label: '工作许可人审批', color: 'rgb(17, 84, 237)' },
    '13': { label: '待负责人确认', color: 'rgb(17, 84, 237)' },
    '14': { label: '监察审核', color: 'rgb(17, 84, 237)' },
    '15': { label: '已审察', color: 'rgba(1, 208, 102, 1)' },
    '16': { label: '已作废', color: 'rgba(252, 89, 90, 1)' },
    '17': { label: '已取消', color: 'rgba(253, 146, 40, 1)' },
  },
  //动火一
  6: {
    '0': { label: '准备中', color: 'rgb(17, 84, 237)' },
    '1': { label: '待签发', color: 'rgb(17, 84, 237)' },
    '2': { label: '业主签发', color: 'rgb(17, 84, 237)' },
    '2.5': { label: '动火部门负责人审核', color: 'rgb(17, 84, 237)' },
    '3': { label: '消防负责人审核', color: 'rgb(17, 84, 237)' },
    '3.5': { label: '安监主管审核', color: 'rgb(17, 84, 237)' },
    '4': { label: '安监负责人审核', color: 'rgb(17, 84, 237)' },
    '5': { label: '安全领导审批', color: 'rgb(17, 84, 237)' },
    '5.3': { label: '工作票接收确认', color: 'rgb(17, 84, 237)' },
    '5.6': { label: '值长批准', color: 'rgb(17, 84, 237)' },
    '6': { label: '许可人审批', color: 'rgb(17, 84, 237)' },
    '7': { label: '填写检测结果', color: 'rgb(17, 84, 237)' },
    '8': { label: '执行人签字', color: 'rgb(17, 84, 237)' },
    '9': { label: '监护人签字', color: 'rgb(17, 84, 237)' },
    '10': { label: '工作负责人签字', color: 'rgb(17, 84, 237)' },
    '11': { label: '部门负责人签字', color: 'rgb(17, 84, 237)' },
    '12': { label: '安监负责人签字', color: 'rgb(17, 84, 237)' },
    '13': { label: '领导确认动火时间', color: 'rgb(17, 84, 237)' },
    '14': { label: '负责人执行确认', color: 'rgb(17, 84, 237)' },
    '15': { label: '执行情况填报', color: 'rgb(17, 84, 237)' },
    '15.3': { label: '动火执行人确认', color: 'rgb(17, 84, 237)' },
    '15.6': { label: '消防监护人确认', color: 'rgb(17, 84, 237)' },
    '16': { label: '许可人终结', color: 'rgb(17, 84, 237)' },
    '20': { label: '监察审核', color: 'rgb(17, 84, 237)' },
    '21': { label: '结束', color: 'rgb(17, 84, 237)' },
    '22': { label: '已审察', color: 'rgba(1, 208, 102, 1)' },
    '23': { label: '已作废', color: 'rgba(252, 89, 90, 1)' },
    '24': { label: '已取消', color: 'rgba(253, 146, 40, 1)' },
  },
  //动火二
  7: {
    '0': { label: '准备中', color: 'rgb(17, 84, 237)' },
    '1': { label: '待签发', color: 'rgb(17, 84, 237)' },
    '2': { label: '业主签发', color: 'rgb(17, 84, 237)' },
    '3': { label: '动火安监部门人员审核', color: 'rgb(17, 84, 237)' },
    '4': { label: '动火部门负责人审批', color: 'rgb(17, 84, 237)' },
    '5': { label: '值长批准', color: 'rgb(17, 84, 237)' },
    '6': { label: '许可人审批', color: 'rgb(17, 84, 237)' },
    '7': { label: '填写检测结果', color: 'rgb(17, 84, 237)' },
    '8': { label: '执行人签字', color: 'rgb(17, 84, 237)' },
    '9': { label: '监护人签字', color: 'rgb(17, 84, 237)' },
    '10': { label: '工作负责人签字', color: 'rgb(17, 84, 237)' },
    '11': { label: '安监确认动火时间', color: 'rgb(17, 84, 237)' },
    '12': { label: '负责人执行确认', color: 'rgb(17, 84, 237)' },
    '13': { label: '执行情况填报', color: 'rgb(17, 84, 237)' },
    '13.3': { label: '动火执行人确认', color: 'rgb(17, 84, 237)' },
    '13.6': { label: '消防监护人确认', color: 'rgb(17, 84, 237)' },
    '14': { label: '许可人终结', color: 'rgb(17, 84, 237)' },
    '18': { label: '监察审核', color: 'rgb(17, 84, 237)' },
    '19': { label: '结束', color: 'rgb(17, 84, 237)' },
    '20': { label: '已审察', color: 'rgba(1, 208, 102, 1)' },
    '21': { label: '已作废', color: 'rgba(252, 89, 90, 1)' },
    '22': { label: '已取消', color: 'rgba(253, 146, 40, 1)' },
  },
  //机械二
  8: {
    '1': { label: '准备中', color: 'rgb(17, 84, 237)' },
    '2': { label: '外委签发人签发', color: 'rgb(17, 84, 237)' },
    '3': { label: '业主签发', color: 'rgb(17, 84, 237)' },
    '4': { label: '值班负责人确认', color: 'rgb(17, 84, 237)' },
    '5': { label: '许可人审批', color: 'rgb(17, 84, 237)' },
    '6': { label: '执行确认', color: 'rgb(17, 84, 237)' },
    '7': { label: '工作票执行', color: 'rgb(17, 84, 237)' },
    '8': { label: '工作延期', color: 'rgb(17, 84, 237)' },
    '9': { label: '负责人变更', color: 'rgb(17, 84, 237)' },
    '10': { label: '工作票交回', color: 'rgb(17, 84, 237)' },
    '11': { label: '许可人终结', color: 'rgb(17, 84, 237)' },
    '12': { label: '监察审核', color: 'rgb(17, 84, 237)' },
    '13': { label: '已审察', color: 'rgba(1, 208, 102, 1)' },
    '14': { label: '已作废', color: 'rgba(252, 89, 90, 1)' },
    '15': { label: '已取消', color: 'rgba(253, 146, 40, 1)' },
  },
};
// 班组
export const TEAM_GROUPS = [
  { value: 1, label: '未执行' },
  { value: 2, label: '执行中' },
  { value: 3, label: '已终结' },
];

// 优化：将类型与标题、ticketType的映射抽离为对象，并封装获取方法
const ticketTypeTitleMap = {
  electrical: {
    one: { title: '电气一种工作票', ticketType: '1' },
    two: { title: '电气二种工作票', ticketType: '2' },
  },
  hydraulic: {
    one: { title: '水工作业工作票（一）', ticketType: '4' },
    two: { title: '水工作业工作票（二）', ticketType: '5' },
  },
  mechanical: {
    one: { title: '机械工作票（一）', ticketType: '3' },
    two: { title: '机械工作票（二）', ticketType: '8' },
  },
  hotWork: {
    one: { title: '一级动火工作票', ticketType: '6' },
    two: { title: '二级动火工作票', ticketType: '7' },
  },
};

export const TICK_TYPE_KEY = ['electrical', 'electrical', 'hydraulic', 'hydraulic', 'mechanical', 'mechanical', 'hotWork', 'hotWork']

export const TICKET_TYPE = {
  one: [1, 3, 4, 6],
  two: [2, 5, 7, 8]
}

// 组合票类型
export const TICKET_TYPE_MAP = Object.values(ticketTypeTitleMap)
  ?.reduce((acc, cur) => acc.concat(Object.values(cur)), [])
  .reduce(
    (acc, cur) =>
      Object.assign(acc, {
        [cur.ticketType]: cur.title,
      }),
    {},
  );

export const showFormat = 'YYYY-MM-DD HH:mm'; // 显示日期格式

// 安全风险单等级
export const RISK_LEVEL_COLOR = {
  轻微风险: 'rgba(165, 214, 63, 1)',
  一般风险: 'rgba(235, 216, 52, 1)',
  较小风险: 'rgba(255, 195, 0, 1)',
  较大风险: 'rgba(253, 146, 40, 1)',
  重大风险: 'rgba(252, 89, 90, 1)',
};
export function getTicketTypeAndTitle(workmntType, type) {
  return ticketTypeTitleMap[workmntType]?.[type] || { title: '', ticketType: '' };
}
// 根据类型和id获取工作票的跳转路径
export function getTicketDetailPageUrl(workmntType: string, type: string, id: string, pageType: 'edit' | 'view') {
  return `/twotickets/workmnt/${workmntType}/${type}/detail/${id}/${pageType}`;
}

export const WORK_TYPE_ONE = [
  { label: '安装', value: '安装' },
  { label: '调试', value: '调试' },
  { label: '检查', value: '检查' },
  { label: '维护', value: '维护' },
  { label: '检修', value: '检修' },
  { label: '清扫', value: '清扫' },
];

export const WORK_TYPE_TWO = [
  { label: '动火作业', value: '动火作业' },
  { label: '高处作业', value: '高处作业' },
  { label: '起重作业', value: '起重作业' },
  { label: '临时用电', value: '临时用电' },
  { label: '水冲洗', value: '水冲洗' },
  { label: '化学清洗', value: '化学清洗' },
  { label: '射线深伤', value: '射线深伤' },
];

export const WORK_TYPE_THREE = [
  { label: '有限空间', value: '有限空间' },
    { label: '土方开挖', value: '土方开挖' },
    { label: '基坑支护', value: '基坑支护' },
    { label: '脚手架搭设', value: '脚手架搭设' },
    { label: '模板安装', value: '模板安装' },
]

export const WORK_TYPE_FOUR = [
  { label: '高处坠落', value: '高处坠落' },
  { label: '物体打击', value: '物体打击' },
  { label: '火灾/爆炸', value: '火灾/爆炸' },
  { label: '灼烫', value: '灼烫' },
  { label: '坍塌', value: '坍塌' },
  { label: '起重伤害', value: '起重伤害' },
  { label: '机械伤害', value: '机械伤害' },
  { label: '中毒/窒息', value: '中毒/窒息' },
  { label: '触电', value: '触电' },
  { label: '淹溺', value: '淹溺' },
  { label: '车辆伤害', value: '车辆伤害' },
  { label: '设备损坏', value: '设备损坏' },
]

// 工作票状态
export const StatusMap: any = {
  '0': '准备中',
  '1': '待签发',
  '2': '业主签发',
  '2.5': '动火部门负责人审核',
  '3': '消防负责人审核',
  '3.5': '安监主管审核',
  '4': '安监负责人审核',
  '5': '安全领导审批',
  '5.3': '工作票接收确认',
  '5.6': '值长批准',
  '6': '许可人审批',
  // '7': '填写检测结果',
  // '8': '执行人签字',
  // '9': '监护人签字',
  // '10': '工作负责人签字',
  // '11': '部门负责人签字',
  // '12': '安监负责人签字',
  // '13': '领导确认动火时间',
  '14': '负责人执行确认',
  '15': '执行情况填报',
  // '15.3': '动火执行人确认',
  // '15.6': '消防监护人确认',
  '16': '许可人终结',
  '20': '监察审核',
  '21': '结束',
  '22': '已审察',
  '23': '已作废',
  '24': '已取消',
}
// 工作票状态2
export const StatusMap2: any = {
  '0': '准备中',
  '1': '待签发',
  '2': '业主签发',
  '3': '动火安监部门人员审核',
  '4': '动火部门负责人审批',
  '5': '值长批准',
  '6': '许可人审批',
  // '7': '填写检测结果',
  // '8': '执行人签字',
  // '9': '监护人签字',
  // '10': '工作负责人签字',
  // '11': '安监确认动火时间',
  '12': '负责人执行确认',
  '13': '执行情况填报',
  // '13.3': '动火执行人确认',
  // '13.6': '消防监护人确认',
  '14': '许可人终结',
  '18': '监察审核',
  '19': '结束',
  '20': '已审察',
  '21': '已作废',
  '22': '已取消',
}

// 关联主票模块映射各类工作票状态文本
export const TicketStatusMaps: any = {
  '1': {//电气一
    '0': '草稿',
    '1': '准备中',
    '2': '待外委签发人签发',
    '3': '待业主签发人签发',
    '4': '待负责人确认提交',
    '5': '待值班负责人确认',
    '6': '待值长批准',
    '7': '待许可人审核',
    '8': '待执行确认',
    '9': '执行中',
    '10': '工作延期',
    '11': '工作间断',
    '12': '负责人变更',
    '13': '待工作许可人审批',
    '14': '待值长确认',
    '15': '待许可人终结',
    '16': '待工作负责人确认',
    '17': '待监察审核',
    '18': '已审查',
    '19': '已作废',
    '20': '已取消',
  },
  '2': {//电气二
    '0': '草稿',
    '1': '准备中',
    '2': '待外委签发人签发',
    '3': '待业主签发人签发',
    '4': '待负责人确认提交',
    '5': '待值班负责人确认',
    '6': '待值长批准',
    '7': '待许可人审核',
    '8': '待执行确认',
    '9': '执行中',
    '10': '工作延期',
    '11': '工作间断',
    '12': '负责人变更',
    '13': '待工作许可人审批',
    '14': '待值长确认',
    '15': '待许可人终结',
    '16': '待工作负责人确认',
    '17': '待监察审核',
    '18': '已审查',
    '19': '已作废',
    '20': '已取消',
  },
  '6': StatusMap,//动火一
  '7': StatusMap2,//动火二
  '3': {//机械一
    '1': '准备中',
    '2': '外委签发人签发',
    '3': '业主签发',
    '4': '值班负责人确认',
    '5': '值长批准',
    '6': '许可人审批',
    '7': '执行确认',
    '8': '工作票执行',
    '9': '工作延期',
    '10': '负责人变更',
    '11': '工作票交回',
    '12': '值班负责人执行确定',
    '13': '许可人终结',
    '14': '监察审核',
    '15': '已审察',
    '16': '已作废',
    '17': '已取消',
  },
  '8': {//机械二
    '1': '准备中',
    '2': '签发人签发',
    '3': '业主签发',
    '4': '值班负责人确认',
    '5': '许可人审批',
    '6': '执行确认',
    '7': '工作票执行',
    '8': '工作延期',
    '9': '负责人变更',
    '10': '工作票交回',
    '11': '许可人终结',
    '12': '监察审核',
    '13': '已审察',
    '14': '已作废',
    '15': '已取消',
  },
  '4': {//水工一
    '1': '准备中',
    '2': '外委签发人确认',
    '3': '业主签发人确认',
    '4': '值班负责人确认',
    '5': '值长批准',
    '6': '许可人审批',
    '7': '执行确认',
    '8': '执行中',
    '9': '工作延期',
    '10': '负责人变更',
    '11': '工作交回',
    '12': '工作许可人审批',
    '13': '值长确认',
    '14': '工作许可人终结',
    '15': '待负责人确认',
    '16': '监察审核',
    '17': '已审察',
    '18': '已作废',
    '19': '已取消'
  },
  '5': {//水工二
    '1': '准备中',
    '2': '外委签发人确认',
    '3': '业主签发人确认',
    '4': '值班负责人确认',
    '5': '值长批准',
    '6': '许可人审批',
    '7': '执行确认',
    '8': '执行中',
    '9': '工作延期',
    '10': '负责人变更',
    '11': '工作交回',
    '12': '工作许可人审批',
    '13': '待负责人确认',
    '14': '监察审核',
    '15': '已审察',
    '16': '已作废',
    '17': '已取消'
  },
}
// 根据date是否存在返回空的结果或者格式化之后的字符串
export const dateFormat = (date: dayjs.ConfigType, temp: string = 'YYYY-MM-DD HH:mm:ss', returnContent: any = null) => {
  return date ? dayjs(date).format(temp) : returnContent
}
