import { history } from "umi"
import { loginStatus } from "./services/user.service"
import { ServerStyleSheet } from 'styled-components';

import 'antd-mobile/es/global'

const loginPath = "/"

const getUserInfo = async () => {
  try {
    // 验证当前用户是否登录
    const response = await loginStatus()
    if (response?.code !== "1") {
      history.push(loginPath)
    }
  } catch (error) {
    history.push(loginPath)
  }
}
// 如果不是登录页面，下载页面，执行
if (location.hash && location.hash !== "#/" && location.hash !== "#/download") {
  getUserInfo()
}

