import { useMemo } from 'react';
import useUserInfo from "@/hooks/useUserInfo"
// 定义角色对象的类型
interface Role {
  code: string; // 角色的唯一标识符
}

// 定义用户信息的类型，包含角色列表
interface UserInfo {
  roleList: Role[]; // 用户角色列表
}

/**
 * 检查当前用户是否具有指定角色
 * @param roleCode - 要检查的角色的 code 标识符
 * @returns boolean - 返回布尔值，表示用户是否具有该角色
 */
const useHasRole = (roleCode: string): boolean => {
  // 获取当前用户信息
  const userInfo: UserInfo | null = useUserInfo();
  const roleList = userInfo?.roleList || [];
  return useMemo(() => {
    // 使用 some 方法检查角色列表中是否存在匹配的角色 code
    return roleList.some((role) => role.code === roleCode);
  }, [roleCode, roleList]);
};

export default useHasRole;