import { useMemo } from 'react';

/**
 * 将 items 数组按顺序和 separator 交织
 * @param {Array<any>} items - 源数组
 * @param {any} separator  - 分隔元素（可以是任何 React 节点）
 * @returns {Array<any>}    - 交织后的数组
 */
function useInterleave(items, separator) {
  return useMemo(() => {
    if (!Array.isArray(items) || items.length === 0) {
      return [];
    }
    // 从第二项开始，将 sep 与当前元素交替 concat
    return items.slice(1).reduce(
      (acc, cur) => acc.concat([separator, cur]),
      [items[0]]
    );
  }, [items, separator]);
}

export default useInterleave;
