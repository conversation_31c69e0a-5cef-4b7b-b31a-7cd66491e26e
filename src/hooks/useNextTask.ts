import { useEffect, useState } from 'react';
import { message } from 'antd';
import { getNextTasks } from '@/services/twotickets/operation/statistics';

export interface NextTaskProps {
  assignee?: string;
  code: string;
  name: string;
  conditionParams: {
    pass: string;
    behavior: string;
    status: string;
    isOwnerSignerAudit?: string;
    sub?: string;
    memo?: string;
    extra?: boolean;
    nextStatus?: string;
    code?: string;
    variables?: any;
  };
}
export default function useNextTask(defaultNextTask?: NextTaskProps[]) {
  const [nextTask, setNextTask] = useState<NextTaskProps[]>([]);
  const [loading, setLoading] = useState(false);
  // 获取下一步流程按钮
  const getNextTask = async (taskId: string) => {
    setLoading(true);
    const data = await getNextTasks(taskId);
    if (data) {
      // pass === 'true'的放前面
      setNextTask(
        data
          .map((item: any) => ({ ...item, conditionParams: JSON.parse(item.conditionParams) }))
          .sort((a, b) => {
            // 如果 a 的 sub 为 'cancel'，则 a 排在后面
            if (a.conditionParams?.sub === 'cancel') return 1;
            // 如果 b 的 sub 为 'cancel'，则 b 排在后面
            if (b.conditionParams?.sub === 'cancel') return -1;
            // 如果 a 的 pass 为 'true'，则 a 排在前面
            if (a.conditionParams?.pass === 'true' && b.conditionParams?.pass !== 'true') return -1;
            // 如果 b 的 pass 为 'true'，则 b 排在前面
            if (a.conditionParams?.pass !== 'true' && b.conditionParams?.pass === 'true') return 1;
            // 其他情况保持原顺序
            return 0;
          }),
      );
    } else {
      console.log('未获取到流程按钮');
    }
    setLoading(false);
  };

  useEffect(() => {
    if (defaultNextTask?.length) {
      setNextTask(defaultNextTask);
    }
  }, [defaultNextTask]);

  return {
    getNextTask,
    nextTask,
    setNextTask,
    loading,
  };
}
