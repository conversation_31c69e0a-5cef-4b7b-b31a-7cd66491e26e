import { useState, useEffect } from 'react';

/**
 * @param parentHeight 父元素高度
 * @param preChildHeight 影响高度的子元素高度
 * @param nextChildDiv 需要设置高度的子元素
 * @returns 需要设置高度的子元素高度
 */

export default function useHeightScroll(parentHeight: number, preChildHeight: number, nextChildDiv: any) {
  const [height, setHeight] = useState(0);

  useEffect(() => {
    const handleResize = () => {
      if (nextChildDiv) {
        nextChildDiv.current.style.height = `${parentHeight - preChildHeight}px`;
        setHeight(parentHeight - preChildHeight);
        nextChildDiv.current.style.overflowY = 'auto';
        nextChildDiv.current.style.overflowX = 'hidden';
      }
    };

    handleResize();

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  });

  return { height };
}
