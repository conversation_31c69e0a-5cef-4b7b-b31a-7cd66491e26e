import request from "@/utils/request";
import { useEffect, useState } from "react";

interface DictProps {
  label: string;
  value: string | number;
}
export default function useDict(parentCode: string) {
  const [dictOptData, setDictOptData] = useState<DictProps[]>([])

  const getDictByParentCode = () => {
    request.get(`/Dict/list?parentCode=${parentCode}`).then((response) => {
      if (response?.code==='1') {
        const { data = [] } = response;
        const newDictOptData: DictProps[] = [];
        data?.forEach((item: any) => {
          newDictOptData.push({
            label: item.dictName,
            value: item.dictCode,
          })
        })
        setDictOptData(newDictOptData)
      }
    });
  }

  useEffect(() => {
    getDictByParentCode()
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  return {
    dictOptData
  }
}