import { getBuildPlanTree } from "@/services/quality-manage.service";
import { useState } from "react";

export default function useBuildPlanTree() {
  const [buildPlanTree, setBuildPlanTree] = useState([])
  const [loading, setLoading] = useState(false)

  const deepFun = (data) => {
    data?.forEach((item) => {
      item.label = item.planName;
      item.value = item.id;
      if (item?.children?.length) {
        deepFun(item.children)
      }
    })
    return data
  }

  // 获取结构部位树列表
  const getBuildPlanTreeData = async () => {
    setLoading(true)
    const { code, data } = await getBuildPlanTree({});
    if (code === '1') {
      const newData = deepFun(data);
      // 其他选项
      const withOtherData = [...newData, {
        id: '00000000',
        value: '00000000',
        positionMemberId: '00000000',
        label: '其他',
        planName: '其他',
        positionMemberName: '其他',
        namePath: "root/其他",
        idPath: 'root/00000000',
        parentId: "root"
      }]
      setBuildPlanTree(withOtherData)
    }
    setLoading(false)
  }

  return {
    getBuildPlanTreeData,
    buildPlanTree,
    loading,
  }
}
