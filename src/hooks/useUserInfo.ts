import { useModel } from 'umi';

// 定义返回类型
export interface UserInfo {
  id: string;
  username: string;
  name?: string;
  roleList: {
    code: string;
    editDate: string;
    editUserId: string;
    id: string | number;
    memo: string;
    name: string;
    organization: string;
    rootId: string | number;
  }[];
}

const useUserInfo = (): UserInfo | null => {
  const masterProps = useModel('@@initialState');
  // console.log("🚀 ~ useUserInfo ~ masterProps:", masterProps)
  let userInfo: UserInfo | null = masterProps?.initialState?.microAppProps?.microService?.user || null;

  if (!userInfo) {
    try {
      userInfo = JSON.parse(localStorage.getItem('userinfo') || '{}');
      if (userInfo) {
        userInfo.id = `${userInfo.id}`
      }
    } catch (error) {
      console.log(error)
    }
  }

  // 你可以在这里根据 code 做额外的处理，如果需要的话
  // 比如从 userInfo 里筛选出某些字段

  return { ...userInfo, userInfo };
};

export default useUserInfo;
