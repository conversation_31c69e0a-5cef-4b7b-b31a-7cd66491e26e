import { useState, useCallback, useRef } from 'react';
import { message } from 'antd';

type MessageType = 'success' | 'error';

type UseMessageHandlerReturn = {
  setLoading: (msg: string) => void;
  showMessage: (msg: string, type: MessageType) => void;
  clearMessage: () => void;
  withLoading: (asyncFn: () => Promise<void>, successMsg?: string, errorMsg?: string) => void;
  submitLoading: boolean;
};

const useMessageHandler = (): UseMessageHandlerReturn => {
  const [submitLoading, setSubmitLoading] = useState(false);
  const loadingMessageVisible = useRef(false);

  // 设置加载消息
  const setLoading = useCallback((msg: string) => {
    if (!loadingMessageVisible.current) {
      message.loading(msg, 0);
      loadingMessageVisible.current = true;
    }
    setSubmitLoading(true);
  }, [loadingMessageVisible]);

  // 显示成功或错误消息
  const showMessage = useCallback((msg: string, type: MessageType) => {
    message.destroy();
    if (type === 'success') {
      message.success(msg);
    } else if (type === 'error') {
      message.error(msg);
    }
    setSubmitLoading(false);
    loadingMessageVisible.current = false;
  }, []);

  // 清除消息
  const clearMessage = useCallback(() => {
    message.destroy();
    setSubmitLoading(false);
    loadingMessageVisible.current = false;
  }, []);

  // 包装异步操作的通用方法
  const withLoading = useCallback(
    async <T>(asyncFn: () => Promise<T>, successMsg?: string, errorMsg?: string): Promise<T | undefined> => {
      setLoading("正在提交");
      try {
        const result = await asyncFn();
        if (result?.code === '1') {
          if (successMsg) showMessage(successMsg, 'success');
        } else {
          if (errorMsg) showMessage(result?.message || errorMsg, 'error');
        }
        return result;
      } catch (error) {
        if (errorMsg) showMessage(errorMsg, 'error');
        return undefined;
      }
    },
    [setLoading, showMessage, clearMessage]
  );

  return {
    setLoading,
    showMessage,
    clearMessage,
    withLoading,
    submitLoading,
  };
};

export default useMessageHandler;
