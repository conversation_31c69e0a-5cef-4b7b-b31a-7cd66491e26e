import { useCallback, useEffect, useMemo, useState } from 'react';
import useNextTask, { NextTaskProps } from './useNextTask';
import useMessageHandler from './useMessageHandler';
import useUserInfo, { UserInfo } from './useUserInfo';
import { cloneDeep, curry, curryRight } from 'lodash';
import { addStdOperation, exportWordUrl, saveOperateInfo } from '@/services/twotickets/operation/statistics';
import dayjs from 'dayjs';
import { Dialog, Form, Radio, Space, Toast } from 'antd-mobile';
import FileSaver from 'file-saver';

const STATE_COMPLETED = 10; // 已完成
const STATE_INCOMPLETE = 11; // 未执行完成
const STATE_VOIDED = 12; // 已作废
const defaultNextTask: NextTaskProps[] = [];

const assigneeRegex = /\${(.*?)}/;

// 审核状态
export const adopt = [
  { label: '通过', value: 1 },
  { label: '驳回', value: 0 },
];
// 监察状态
export const qualified = [
  { label: '合格', value: 1 },
  { label: '不合格', value: 0 },
];
// 标题映射配置
const titleMap: { [key: number]: string } = {
  2: '监护人审批',
  3: '值班负责人审批',
  4: '值长审批',
  6: '监护人汇报',
  7: '值长审批',
  9: '监察审核',
};
// state值必穿id和name的前缀
const stateUserMapping: Record<number, string> = {
  1: 'operator',
  2: 'guardian',
  3: 'watch',
  4: 'firstShiftSupervisor',
  5: 'orderShiftSupervisor',
  6: 'guardian',
  7: 'secondShiftSupervisor',
  8: 'guardian',
  10: 'secondShiftSupervisor',
};


/**
 * 1待提交  ['暂存', '提交']
 * 2监护人审核  ['处理']
 * 3值班负责人审核 ['处理']
 * 4值长审核 ['处理']
 * 5值长发令 ['发令']
 * 16值长发令后 ['作废', '打印'] （填写发令时间之后）
 * （
 *   值长发令后，监护人可以打印线下执行，
 *   监护人打印，由操作人、监护人、值班负责人、值长进行手工签字确认
 *   (打印时打开网页预览页面，打印的表单上加上二维码，手机通过扫面二维码可以查看表单信息)
 * ）
 * 26监护人填写 ['打印', '汇报填报']
 * 点击暂存后，这里的按钮又变回“打印”和“汇报填报'
 * 36监护人汇报 ['暂存','提交']
 * 7值长查看 ['打印' , '处理']
 * 8监护人终结 ['终结']
 * 9监测审核 ['打印', '处理']
 * 10已完成 ['生成标准票', '打印', '导出']
 * 已终结 ['生成标准票', '打印', '导出']
 * -11已作废 ['生成标准票', '打印', '导出']
 * 11未执行完成 （监护人汇报的时候填写的） ['生成标准票', '打印', '导出']
 * 12已作废 ['生成标准票', '打印', '导出']
 *
 * 创建人 -> createUser
 * 操作人 -> operator
 * 监护人 -> guardian
 * 值班负责人 -> watch
 * 第一次审核值长 -> firstShiftSupervisor
 * 发令值长 -> orderShiftSupervisor
 * 第二次审核值长 -> secondShiftSupervisor
 * @param info
 */
export const useTwoTicketAuth = (data, twoTicketType = null) => {
  const [form] = Form.useForm();
  const info = useMemo(() => {
    return data;
  }, [data]);
  // 消息提示
  const { showMessage } = useMessageHandler();
  const [submitLoading, setSubmitLoading] = useState<boolean>(false);
  const curryShowMessage = curryRight(showMessage)('error');
  // 获取当前用户信息
  const userInfo: UserInfo | null = useUserInfo();

  /**
   * 获取下一步流程按钮
   */
  const { getNextTask, nextTask, loading: nextTaskLoading } = useNextTask();

  useEffect(() => {
    if(!info?.taskId) return;
    getNextTask(info?.taskId);
  }, [info?.taskId]);

  // 创建保存数据的处理函数
  const createSaveDataHandler = (isAudit: number, behavior: string, state: number) => async () => {
    await handleSaveData({ state, behavior, isAudit });
  };

  const currySaveData = curry(createSaveDataHandler);
  const updateData = currySaveData(0, '');

  /**
   * 必穿字段id和name的字段进行拼接赋值
   * @param params
   * @param userInfo
   * @param state
   */
  const assignUserInfoByState = (params: Record<string, any>, userInfo: UserInfo, state?: number | string | undefined) => {
    const mapping = stateUserMapping[(state as number) || ((params?.state - 1) as keyof typeof stateUserMapping)];
    if (mapping) {
      const id = mapping + 'Id';
      const name = mapping + 'Name';
      params[id] = userInfo?.id || params[id] || '';
      params[name] = userInfo?.name || params[name] || '';
    }
  };
  /**
   * 补充表单提交的参数
   *
   * @param params - 表单的参数
   * @returns 处理后的 payload
   */
  const buildPayload = (params: any) => {
    // 全部删除时需传1，且对应的数据列表需传空

    const _info = cloneDeep(info);
    if (info.id) params.id = info.id;

    if (_info?.imgId) params.imgId = _info?.imgId || '';
    if (_info?.reportId) params.reportId = _info?.reportId || '';

    if (!params.id) {
      params.createUserId = userInfo?.id || '';
      params.createUserName = userInfo?.name || '';
    }
    if (
      params.isAudit === 1 && // 审核的时候才做【区别于保存数据按钮】
      params.id &&
      params.state &&
      userInfo
    ) {
      let _state = undefined;
      if (
        params.behavior === qualified?.find((item) => item.value === 0)?.label ||
        params.behavior === adopt?.find((item) => item.value === 0)?.label
      ) {
        _state = _info?.state;
        if (_info?.state !== 2) assignUserInfoByState(params, userInfo, _state);
      } else {
        assignUserInfoByState(params, userInfo, params.state === 1 ? 1 : undefined);
      }
    }
    let payload = { ...(_info || {}), ...params };

    let operateRiskDelete = 0;
    let operatorStepDelete = 0;
    let standardOperatorToolTypeDelete = 0;

    // 全部删除时需传1，且对应的数据列表需传空
    // 危险点与预防措施
    payload.operateRiskList =
      payload.operateRiskList?.map((item: any) => ({
        perilPoint: item.recommendedMeasure || item.perilPoint,
        measure: item.name || item.measure,
        memo: item.memo,
      })) || [];

    // 操作项
    payload.operatorStepList =
      payload.operatorStepList?.map((item: any) => ({
        operationStep: item.operationStep,
        operationTime: item?.operationTime?.format?.('YYYY-MM-DD HH:mm:ss') || item.operationTime || '',
        isMainOperation: item?.isMainOperation || '',
        remark: item?.remark || '',
      })) || [];
    // 工器具
    payload.standardOperatorToolTypeList =
      payload.standardOperatorToolTypeList?.map(({ id, ...item }) => ({
        ...item,
        toolName: item.toolName,
        model: item.model,
        type: item.typeName || item.type || '',
        kind: item.kind,
        responsibility: item.responsibilityName || item.responsibility || '',
      })) || [];

    return {
      ...payload,
      operateRiskDelete,
      operatorStepDelete,
      standardOperatorToolTypeDelete,
    };
  };
  const handleExport = async (info: any) => {
    try {
      Toast.show({
        icon: 'loading',
        content: '正在下载中…',
        duration: 0,
      });
      const res = await exportWordUrl(info);
      const pdfBlob = new Blob([res], { type: 'application/pdf' });
      // 将 Blob 转换为 Object URL
      const url = URL.createObjectURL(pdfBlob);
      await  FileSaver.saveAs(pdfBlob, '操作票.pdf'); // 使用 FileSaver.js 进行下载
    } catch (error) {
      
    } finally {
      Toast.clear();
    }
    // 1. 打开新标签页进行预览
    // window.open(url);
    // 手机h5下载
    // try {
    //   const a = document.createElement('a');
    //   a.href = url;
    //   a.download = '操作票.pdf'; // 设置下载文件名
    //   document.body.appendChild(a);
    //   a.click(); // 触发下载
    //   document.body.removeChild(a); // 移除临时链接
    //   URL.revokeObjectURL(url);
    //   alert("成功下载");
    // } catch (error) {
    //   alert(error);
    // }
    // window.location.href = url; // 直接下载
    // if(window.NativeBridge ) {
    //   // IE浏览器
    //   window.NativeBridge.downFile(pdfBlob, '操作票.pdf');
    // }
    
  };
  /**
   * 验证表单提交时的必要条件
   *
   * @param params - 表单的参数
   * @returns 是否验证通过
   */
  const validateFormSubmission = (payload: any): boolean => {
    if (!payload?.task) {
      curryShowMessage('请输入操作任务！');
      return false;
    }
    if (!payload?.type) {
      curryShowMessage('请选择操作票类型！');
      return false;
    }
    if (payload?.state == 6 && !payload?.orderTime) {
      curryShowMessage('请选择发令时间！');
      return false;
    }
    if (payload?.state == 7 && !payload?.operateBeginTime) {
      curryShowMessage('请选择操作开始时间！');
      return false;
    }
    if (payload?.state == 7 && !payload?.operateEndTime) {
      curryShowMessage('请选择操作结束时间！');
      return false;
    }
    // if (payload.operatorStepList.length) {
    //   if (payload.operatorStepList?.[payload.operatorStepList.length - 2]?.operationStep !== '全面检查') {
    //     curryShowMessage('倒数第二项操作项目应为："全面检查"');
    //     return false;
    //   }
    //   if (payload.operatorStepList?.[payload.operatorStepList.length - 1]?.operationStep !== '汇报批准人') {
    //     curryShowMessage('倒数第一项应为："汇报批准人"');
    //     return false;
    //   }
    // } else {
    //   curryShowMessage('倒数第二项操作项目应为："全面检查"， 第一项应为："汇报批准人"');
    //   return false;
    // }
    if (payload?.state == 7) {
      if (!payload?.operateBeginTime) {
        curryShowMessage('请选择操作开始时间！');
        return false;
      }
      const operateBeginTime = payload?.operateBeginTime ? dayjs(payload?.operateBeginTime) : ''; // 开始时间
      if (!payload?.operateEndTime) {
        curryShowMessage('请选择操作结束时间！');
        return false;
      }
      const operateEndTime = payload?.operateEndTime ? dayjs(payload?.operateEndTime) : ''; // 结束时间
      if (!payload?.orderTime) {
        curryShowMessage('请选择发令时间！');
        return false;
      }
      const orderTime = payload?.orderTime ? dayjs(payload?.orderTime) : ''; // 结束时间
      if (dayjs(operateEndTime).isBefore(dayjs(operateBeginTime))) {
        curryShowMessage('操作结束时间不能早于操作开始时间！');
        return false;
      }
      if (dayjs(operateBeginTime).isBefore(dayjs(orderTime))) {
        curryShowMessage('操作时间不能早于发令时间！');
        return false;
      }
      if(!payload?.operatorStepList?.filter((item: { isMainOperation: string; }) => item?.isMainOperation == '1')?.length) {
        curryShowMessage('请至少添加一项操作项！');
        return false;
      }
      // if (payload?.operatorStepList?.length) {
      //   for (let i = 0; i < payload?.operatorStepList?.length; i += 1) {
      //     if (payload?.operatorStepList[i].isMainOperation != 1 && !payload?.operatorRemark) {
      //       curryShowMessage(`请对未执行完毕的操作项【${payload?.operatorStepList[i].operationStep}】进行备注说明！`);
      //       return false;
      //     }
      //   }
      // }
    }

    return true;
  };
  const handleSaveData = async (otherParams = {} as any) => {
    try {
      setSubmitLoading(true);
      Toast.show({
        icon: 'loading',
        content: '加载中…',
        duration: 0,
      });
      if (otherParams?.behavior === '打印') {
        try {
          await handleExport({
            id: otherParams?.id,
            printType: otherParams.printType,
          });
          return true;  
        } catch (error) {
          
        }
      }
      if (otherParams?.behavior === '生成标准操作票') {
        const stdPayload = {
          operateType: info?.type || '',
          state: 0, // 审核状态
          isAudit: 0,
          operateRiskDelete: 0,
          operatorStepDelete: 0,
          reviewerId: '',
          reviewerName: '',
          standardOperatorToolTypeDelete: 0,
          status: 1, // 是否启用
          task: info?.task, // 操作任务
          taskId: '', // 操作任务
          remark: info?.remark, // 备注
          createTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
          operateRiskList: info?.operateRiskList?.map((item) => ({
            perilPoint: item.perilPoint, // item.perilPoint,
            measure: item.measure, // item.measure,
          })), // 危险点与预防措施
          operatorStepList: info?.operatorStepList?.map((item) => ({
            operationStep: item.operationStep,
            operationTime: item.operationTime,
            memo: item.id || item.memo,
            isMainOperation: item.isMainOperation,
            remark: item.remark || '',
          })), // 操作项
          standardOperatorToolTypeList: info?.standardOperatorToolTypeList?.map(({ id, ...item }) => ({
            ...item,
            toolName: item.toolName,
            model: item.model,
            type: item.typeName || item.type || '',
            kind: item.kind,
            responsibility: item.responsibilityName || item.responsibility || '',
          })), // 工器具
        };
        // setLoading('正在生成标准操作票...');
        try {
          const res = await addStdOperation(stdPayload);
          if (res) {
            showMessage(`已生成标准操作票[${info?.task || ''}]！`, 'success')
            return true;
          }else{
            return false;
          }
        } catch (error) {
          // clearMessage();
        }
      }
      const payload = buildPayload({ ...info, ...otherParams });
      const isValid = payload.isAudit === 0 ? true : validateFormSubmission(payload);
      if (isValid) {
        const res = await saveOperateInfo(payload);
        if (res.code === '1') {
          Toast.show({
            content: '成功',
            position: 'top',
            duration: 2000,
          });
          return true;
        } else {
          Toast.show({
            content: res.message,
            position: 'top',
            duration: 2000,
          });
          return false;
        }
      }else{
        return false;
      }
    } catch (error) {
      Toast.show({
        icon: 'fail',
        content: error.message || '错误',
      });
    } finally {
      Toast.clear();
      setSubmitLoading(false);
    }
  };
  /**
   * 汇报填报发起审批，监察审核时触发事件,值长发令触发事件
   */
  const supervisionOrReportOrSignal = useCallback(
    async (params: any) => {
      const { assignee = '', conditionParams: { variables = null, state = '', behavior = '' } = {} as any } = nextTask[0];
      if( !state )  return;
      const otherUserName = assignee ? assigneeRegex.exec(assignee)?.[1] : '';
      const payload: any = Object.assign(
        {
          isAudit: 1,
          opinions: params?.remark || '',
          behavior,
          state,
          variables: Object.assign(
            {},
            variables || {},
            otherUserName
              ? {
                  [otherUserName]: info?.[otherUserName],
                }
              : {},
          ),
        },
        otherUserName
          ? {
              [otherUserName]: info?.[otherUserName],
            }
          : {},
      );
      if (info?.state === 5) {
        payload.orderTime = params?.orderTime;
      }
      if (info?.state === 9) {
        payload.behavior = params.operateType === 1 ? '合格' : '不合格';
        payload.state = STATE_COMPLETED;
        payload.examineResult = params.operateType ? 1 : 0;
        payload.examineView = payload.behavior;
      }
      try {
       return await handleSaveData(payload);
      } catch (error) {
        
      }
    },
    [nextTask, info],
  );
  const handleAuth = useCallback(
    async (params: any) => {
      // 搜先获取驳回和通过的选项
      const passTask = nextTask.filter(
        (item: any) =>
          (item?.conditionParams?.variables || {})?.hasOwnProperty('pass') &&
          (item?.conditionParams?.variables?.pass === 'true' ||
             (!item?.conditionParams?.variables?.sub && item?.conditionParams?.variables?.pass === 'false')),
      );
      const activeTask: any =
        passTask?.find?.((item: any) => item?.conditionParams?.variables?.pass === (params?.operatorType === 1 ? 'true' : 'false')) || {};
      const { assignee = '', conditionParams = {} as any } = activeTask as any;
      const { variables = {}, state = '', behavior = '' } = conditionParams || {};
      if( !state )  return;
      const otherUserName = assignee ? assigneeRegex.exec(assignee)?.[1] : '';
      const payload: any = Object.assign(
        {
          isAudit: 1,
          behavior,
          state,
          opinions: params.remark,
          variables,
        },
        otherUserName
          ? {
              variables: {
                ...(variables || {}),
                [otherUserName]: info?.[otherUserName],
              },
            }
          : {},
      );
      await handleSaveData(payload);
    },
    [nextTask, info],
  );
  const methods = {
    save: async () => await updateData(1)(), // 保存
    adminSave: async (state) => await updateData(state)(), // 管理员保存
    print: () => {
      Dialog.confirm({
        title: '打印',
        content: (
          <Form
            form={form}
            initialValues={{
              printType: 19,
            }}
            onFinish={async (values) => {
              try {
                Toast.show({
                  content: '正在打印...',
                  duration: 0,
                });
                let printType = values.operateType;
                await handleSaveData({
                  behavior: '打印',
                  printType,
                  id: info?.id || '',
                });
              } catch (error) {
              } finally {
                Toast.clear();
              }
            }}
          >
            <Form.Item label="打印类型" name="printType">
              <Radio.Group>
                <Space direction="vertical">
                  <Radio value={19}>已填报操作票</Radio>
                  <Radio value={20}>操作票（模版）</Radio>
                </Space>
              </Radio.Group>
            </Form.Item>
          </Form>
        ),
        onConfirm: () => {
          form.submit();
        },
      });
    }, // 打印
    start: async () => await handleSaveData({ isAudit: 1, state: 2, behavior: '提交', variables: { pass: 'true' } }), // 发起审批
    voidOrder: async (params, activeConditionParams = {}) => {
      const { assignee = '', conditionParams: { variables = null, state = STATE_VOIDED + '', behavior = '作废' } = {} as any } =
        activeConditionParams as any;
      const otherUserName = assignee ? assigneeRegex.exec(assignee)?.[1] : '';
      const payload: any = Object.assign(
        {
          isAudit: 1,
          opinions: params.remark || '',
          behavior,
          state,
          variables: Object.assign(
            {},
            variables || {},
            otherUserName
              ? {
                  [otherUserName]: info?.[otherUserName],
                }
              : {},
          ),
        },
        otherUserName
          ? {
              [otherUserName]: info?.[otherUserName],
            }
          : {},
      );
      await handleSaveData(payload);
    },
    download: async () => {
      await handleExport({ id: info?.id, printType: 19 });
    },
    generateStandard: () => {
      Dialog.confirm({
        title: '生成标准操作票',
        content: '是否生成标准操作票',
        onOk: async () => {
          await handleSaveData({
            behavior: '生成标准操作票',
          });
        },
      });
    }, // 生成标准操作票
    auth: handleAuth, // 审核
    supervisionOrReport: supervisionOrReportOrSignal, // 监察 或者 汇报 或者 发令
  };

  return {
    loading: nextTaskLoading || submitLoading,
    nextTask, // 下一步任务
    operateTypeOptions: info?.state === 9 ? qualified : adopt, // 选项
    methods, // 方法
  };
};
