import { useCallback, useEffect, useRef } from 'react';
import { useThrottleFn } from 'ahooks';

export interface TabItem {
  key: string;
  title: string;
  [key: string]: any;
}

export interface UseTabScrollSyncOptions {
  /**
   * Tab items array with key property
   */
  tabItems: TabItem[];
  /**
   * Current active tab key
   */
  activeKey: string;
  /**
   * Function to set active tab key
   */
  setActiveKey: (key: string) => void;
  /**
   * Tab height for offset calculation (default: 42)
   */
  tabHeight?: number;
  /**
   * Throttle wait time in milliseconds (default: 100)
   */
  throttleWait?: number;
  /**
   * Timeout for tab click flag reset in milliseconds (default: 400)
   */
  tabClickTimeout?: number;
  /**
   * Scroll behavior for tab clicks (default: 'auto')
   */
  scrollBehavior?: ScrollBehavior;
}

export interface UseTabScrollSyncReturn {
  /**
   * Container ref to attach to the scrollable container
   */
  containerRef: React.RefObject<HTMLDivElement>;
  /**
   * Callback ref for setting up scroll event listeners
   */
  scrollContainerRef: (ref: HTMLDivElement | null) => void;
  /**
   * Handler for tab change events
   */
  handleTabChange: (key: string) => void;
}

/**
 * Custom hook for synchronizing tab active state with scroll position
 * 
 * This hook provides:
 * - Automatic tab activation based on scroll position
 * - Smooth scrolling to tab content when tab is clicked
 * - Proper event listener management and cleanup
 * - Conflict prevention between scroll and tab click events
 * 
 * @param options Configuration options
 * @returns Object with container ref and tab change handler
 */
export const useTabScrollSync = ({
  tabItems,
  activeKey,
  setActiveKey,
  tabHeight = 42,
  throttleWait = 100,
  tabClickTimeout = 400,
  scrollBehavior = 'auto'
}: UseTabScrollSyncOptions): UseTabScrollSyncReturn => {
  const containerRef = useRef<HTMLDivElement | null>(null);
  // Flag to prevent scroll event handling during tab clicks
  const isTabClickRef = useRef(false);

  // Throttled scroll handler to detect which section is currently visible
  const { run: handleScroll } = useThrottleFn(
    () => {
      // Skip if tab click is in progress to avoid conflicts
      if (isTabClickRef.current) return;

      // Find the currently visible tab section
      let currentKey = tabItems[0]?.key;
      for (const item of tabItems) {
        const element = document.getElementById(`anchor-${item.key}`);
        if (!element) continue;
        
        const rect = element.getBoundingClientRect();
        // Check if element is above or at the tab height threshold
        if (rect.top <= tabHeight) {
          currentKey = item.key;
        } else {
          break;
        }
      }
      
      // Update active key if it has changed
      if (currentKey !== activeKey) {
        setActiveKey(currentKey);
      }
    },
    {
      leading: true,
      trailing: true,
      wait: throttleWait,
    },
  );

  // Callback ref for setting up scroll event listeners
  const scrollContainerRef = useCallback((ref: HTMLDivElement | null) => {
    // Handle component unmount (ref becomes null)
    if (!ref) return;
    
    // Add scroll event listener
    ref.addEventListener('scroll', handleScroll);
    
    // Store ref for cleanup
    if (containerRef.current !== ref) {
      containerRef.current = ref;
    }
  }, [handleScroll]);

  // Handler for tab change events
  const handleTabChange = useCallback((key: string) => {
    // Immediately set active key to prevent visual flicker
    setActiveKey(key);

    // Set flag to prevent scroll event conflicts
    isTabClickRef.current = true;

    // Calculate and perform scroll to target element
    const targetElement = document.getElementById(`anchor-${key}`);
    if (targetElement && containerRef.current) {
      const rect = targetElement.getBoundingClientRect();
      const containerRect = containerRef.current.getBoundingClientRect();
      const currentScrollTop = containerRef.current.scrollTop;

      // Calculate target scroll position: current position + element offset - tab height
      const targetScrollTop = currentScrollTop + (rect.top - containerRect.top) - tabHeight;

      containerRef.current.scrollTo({
        top: targetScrollTop,
        behavior: scrollBehavior,
      });
    }

    // Reset flag after timeout to allow normal scroll handling
    setTimeout(() => {
      isTabClickRef.current = false;
    }, tabClickTimeout);
  }, [setActiveKey, tabHeight, scrollBehavior, tabClickTimeout]);

  // Cleanup effect
  useEffect(() => {
    return () => {
      // Remove scroll event listener on unmount
      containerRef.current?.removeEventListener('scroll', handleScroll);
    };
  }, [handleScroll]);

  return {
    containerRef,
    scrollContainerRef,
    handleTabChange,
  };
};
