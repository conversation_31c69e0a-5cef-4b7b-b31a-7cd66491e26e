import { treeByOrg } from "@/services/quality-manage.service";
import { useState } from "react";

export default function useTreeByConfigure() {
  const [treeData, setTreeData] = useState([])
  const [loading, setLoading] = useState(false)

  const deepFun = (data) => {
    data?.forEach((item) => {
      if (item?.children?.length) {
        deepFun(item.children)
      }
    })
    return data
  }

  // 获取组织机构树
  const getTreeData = async () => {
    setLoading(true)
    const { code, data } = await treeByOrg({ id: 0 });
    if (code === '1') {
      const newData = deepFun(data)
      setTreeData(newData)
    }
    setLoading(false)
  }
  return {
    treeData,
    loading,
    getTreeData
  }
}
