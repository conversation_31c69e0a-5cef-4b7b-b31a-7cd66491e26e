import React, { useEffect, useState } from 'react'
import type { Key } from 'react';

import { addModelId, deepForEachFn, deepMapTreeFn, mergeTree } from '@/utils/data-processing-tools';
import { getNodeModelList, getNodeTree, getModelList } from '@/services/quality-manage.service';

const deepTreeNodeList = (source: any[], parentId: Key = '') => {
  return source?.map((item) => {
    // 保持PC的逻辑,采用name拼接节点作为唯一标识
    const setKey = `${parentId}${parentId ? ',' : ''}${item.name}`;
    const finalCode = `${setKey}`;

    return { 
      ...item,
      title: item.name,
      oldId: item?.id,
      id: finalCode,
      children: deepTreeNodeList(item?.children, setKey),
    }
  });
}

export default function useEngineeringTree({ type = '10' }) {

  const [engineeringTree, setEngineeringTree] =  useState<any[]>([]);
  const [projectTree, setProjectTree] = useState<any[]>([]); //项目树

  // 获取项目树
  const getTreeData = () => {
    getNodeTree({}).then((res) => {
      const { data } = res.data;
      const filterData = data.filter((item: any) => {
        return item.type === type;
      });
      const defauilTree = deepMapTreeFn(
        filterData,
        (item) => {
          return {
            ...item,
            key: item?.id,
            title: item.modelUpload ? `${item?.name}*` : item?.name,
          };
        },
        "children"
      );

      let nodeIds: string[] = [];
      deepForEachFn(defauilTree, (node) => {
        // 项目树叶子节点ids
        if (!node.children.length) {
          nodeIds.push(node.id);
        }
      });

      if (nodeIds.length) { 
        const promise$ = nodeIds.map((nodeId: string) => {
          return getNodeModelList(nodeId);
        });
        Promise.all(promise$).then((res) => {
          if (res.every((item) => item.status)) {
            // 所有的模型信息
            const  modelObjs = res.reduce((pre, cur) => {
              if (cur?.data?.data?.[0]) {
                return pre.concat(cur.data.data[0]);
              }
              return pre;
            }, []);

            const modelIds = modelObjs?.map((it) => {
              return it.bimModelId;
            })

            const modelPromise$ = modelIds?.map((modelId: string) => {
              return getModelList({ modelId: modelId || "" });
            });

            Promise.all(modelPromise$).then((resData) => {
              const modelTree = resData?.map((item) => {
                if(item.status && item?.data) {
                  return JSON.parse(item?.data)?.[0] || []
                } else {
                  return []
                }
              })

              // 为项目树的叶子节点添加bimModelId属性并简洁对象属性
              const newProjectTree = addModelId(defauilTree, modelObjs);

              setProjectTree(newProjectTree);
  
              // 拼接项目树与构件树
              let finalTree = mergeTree(newProjectTree, modelTree, 'modelId', 'modelId', 2);
              // finalTree =  deepTreeNodeList(finalTree)
              setEngineeringTree(finalTree.slice(0, 1));
            })
            
          }
        })
      }
    });
  }
  
  useEffect(() => {
    getTreeData()
  }, [])

  return { engineeringTree }
}
