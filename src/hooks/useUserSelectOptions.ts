import request from '@/utils/request';
import { useEffect, useState } from 'react';

interface UseUserSelectOptionsConfig {
  enabled?: boolean; // 是否启用数据获取，默认为 true
}

export function useUserSelectOptions(
  useKey: 'id' | 'name' | 'username' = 'id', 
  config: UseUserSelectOptionsConfig = {}
) {
  const { enabled = true } = config;
  const [userOptions, setOptions] = useState<any[]>([]);

  let options: any[] = [];
  async function getUserList() {
    if (!enabled) {
      setOptions([]);
      return;
    }
    
    const response = await request.get(`/sys/User/list`);
    if (response.flag) {
      let { data = [] } = response;
      data?.forEach((item: any) => {
        options.push({
          label: item.name,
          value: item[useKey],
          disabled: !Boolean(item.locked),
        });
      });
      setOptions(options);
    } else {
      setOptions([]);
    }
  }

  useEffect(() => {
    getUserList();
  }, [enabled]); // 依赖 enabled 参数

  return userOptions;
}
