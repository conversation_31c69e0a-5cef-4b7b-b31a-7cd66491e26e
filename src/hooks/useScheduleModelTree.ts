import { getNodeTree } from "@/services/progress-manage.service";
import { useState } from "react";

export default function useScheduleTree() {
  const [scheduleTree, setScheduleTree] = useState([])
  const [loading, setLoading] = useState(false)

  const deepFun = (data) => {
    data?.forEach((item) => {
      item.label = item.name;
      item.value = item.id;
      
      if (item?.children?.length) {
        deepFun(item.children)
      }

      if(item?.children?.length ===0){
        delete item.children
      }
    })
    return data
  }

  // 获取董大进度模型树
  const getScheduleTreeData = async () => {
    setLoading(true)
    const { code, data } = await getNodeTree({});
    if (code === 200) {
      const schData = data?.data?.find(item => item?.name === '董大进度模型')
      const newData = deepFun([{...schData,parentId:'root'}]);
      setScheduleTree(newData)
    }
    setLoading(false)
  }

  return {
    getScheduleTreeData,
    scheduleTree,
    loading,
  }
}
