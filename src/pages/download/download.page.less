.downappbox {
  position: relative;
  width: 100vw;
  height: 100vh;
  color: #000;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-direction: column;
  background-image: url(../../assets/navigation_backimg.png);
  // background-size: cover;
  // background-repeat: no-repeat;
  // background-position: top center;
  background-color: #e2ebfd;


  background-position: center center;
  background-repeat: no-repeat;
  background-size: calc(100% + 20px) calc(100% + 20px);


  animation-name: float;
  animation-duration: 10s;
  animation-timing-function: ease-in-out;
  animation-iteration-count: infinite;


  img {
    width: 256px;
    min-height: 256px;
    margin-top: 300px;
    border-radius: 40px;
  }

  .title {
    font-size: 82px;
    font-family: pangmeng;
    font-weight: 400;
    color: #333;
    margin-top: 100px;
  }

  .btn {
    width: 70%;
    height: 120px;
    background: #1154ED;
    box-shadow: 0rem 0rem 0rem 0rem rgba(110, 144, 221, 0.35);
    border-radius: 60px;
    margin-top: 300px;
    border: 1px solid #1154ED;
    color: #fff;
    font-size: 46px;
    font-family: Adobe Heiti Std;
    font-weight: normal;
  }

  .small {
    font-family: 'pangmeng';
    font-weight: initial;
    font-size: 38px;
    margin-top: 30px;
    // color: #393939;
    color: #f44336;

    &.shake {
      animation: ~"shake" 0.5s;
      animation-iteration-count: 1;
    }
  }
}

@keyframes float {
  0% {
    background-position: 0 0;
    background-size: calc(100% + 60px) auto;
  }

  20% {
    background-position: -15px -15px;
    background-size: calc(100% + 80px) auto;
  }

  40% {
    background-position: -15px 0;
    background-size: calc(100% + 20px) auto;
  }

  60% {
    background-position: 0 0;
    background-size: calc(100% + 80px) auto;
  }

  80% {
    background-position: 0 -15px;
    background-size: calc(100% + 10px) auto;
  }

  100% {
    background-position: 0 0;
    background-size: calc(100% + 60px) auto;
  }
}


@keyframes shake {
  0% {
    transform: translateX(0);
  }

  10% {
    transform: translateX(-10px);
  }

  20% {
    transform: translateX(10px);
  }

  30% {
    transform: translateX(-10px);
  }

  40% {
    transform: translateX(10px);
  }

  50% {
    transform: translateX(-10px);
  }

  60% {
    transform: translateX(10px);
  }

  70% {
    transform: translateX(-10px);
  }

  80% {
    transform: translateX(10px);
  }

  90% {
    transform: translateX(-10px);
  }

  100% {
    transform: translateX(0);
  }
}
