import React, { useState, useEffect } from 'react';
import classnames from 'classnames'
import styles from './download.page.less'

const DownloadApp: React.FC = () => {
  const [isWeixin, setWeixin] = useState(false);
  const [isShake, setShake] = useState(false);

  const handleDownloadApp = () => {
    if (isWeixin) {
      setShake(true)
      setTimeout(() => {
        setShake(false)
      }, 500);
    } else {
      window.location.href = `${window.location.origin}/dxdsapi/app/v1`
    }
  }

  useEffect(() => {
    let ua = navigator.userAgent.toLowerCase()
    setWeixin(ua.indexOf('micromessenger') !== -1 && ua.indexOf('wxwork') === -1);
  }, [])

  return (
    <div className={styles.downappbox}>
      <img src={`/images/download/dd-icon.png`} />
      <div className={styles.title}>智慧管控平台</div>
      <button className={styles.btn} onClick={handleDownloadApp}>下载安卓APP</button>
      {isWeixin ? (<span className={classnames(styles.small, { [styles.shake]: isShake })}>请在浏览器打开此网页来下载文件！</span>) : null}
    </div >
  )
}

export default DownloadApp;
