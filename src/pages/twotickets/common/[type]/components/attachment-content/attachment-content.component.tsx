import useInterleave from '@/hooks/useInterleave';
import { OperationFileList } from '@/styles/twotickets.style';
import request from '@/utils/request';
import { handleDownload } from '@/utils/utils';
import { useRequest } from 'ahooks';
import { Empty } from 'antd';
import { ActionSheet, Ellipsis, List, Popup, Skeleton, Toast } from 'antd-mobile';
import dayjs from 'dayjs';
import { isEqual } from 'lodash';
import { memo, useCallback, useMemo, useRef, useState } from 'react';
import { styled } from 'umi';
import { RequestOptionsInit } from 'umi-request';

// 新增类型定义
type FileAction = 'preview' | 'download' | 'cancel';

// 类型定义优化
export interface AttachmentItem {
  name: string;
  url: string;
  uploadUserRealName?: string;
  memo?: string;
  uploadDate?: string;
  id: string; // 增加唯一标识
}

interface AttachmentContentProps {
  url?: string;
  dataList?: AttachmentItem[];
  options?: RequestOptionsInit;
}

// 样式常量提取
const LIST_STYLE = {
  '--border-top': 'none',
  '--border-bottom': 'none',
  backgroundColor: 'rgba(255, 255, 255, 1)',
} as React.CSSProperties;

const SPLIT_STYLE = {
  display: 'inline-block',
  width: '.7408vw',
  height: '.7408vw',
  borderRadius: '.3703vw',
  margin: '0 1.1111vw',
  backgroundColor: 'rgba(102, 102, 102, 1)',
};

const POPUP_BODY_STYLE = { borderRadius: 0, minWidth: '100vw' };
const IFRAME_STYLE = { width: '100%', height: '100%', border: 'none' };

const PreviewPopupContainer = styled.div`
  height: 100%;
  iframe {
    width: 100%;
    height: 100%;
  }
`;

// 抽离预览弹窗组件
const PreviewPopup: React.FC<{ visible: boolean; url: string; onClose: () => void }> = memo(
  ({ visible, url, onClose }: { visible: boolean; url: string; onClose: () => void }) => {
    const iframeRef = useRef(null);
    // 在iframe加载后注入移动端meta
    const injectMobileMeta = () => {
      if (iframeRef.current && iframeRef.current.contentDocument) {
        const iframeDocument = iframeRef.current?.contentDocument || iframeRef.current?.contentWindow?.document;
        if (iframeDocument) {
          setTimeout(() => {
            const body = iframeDocument.body;
            body.style.width = '100%';
            const iframes = body.getElementsByTagName('iframe');
            for (let i = 0; i < iframes.length; i++) {
              // iframes[i].style.width = '100%';
              iframes[i].setAttribute('height', '100%');
              const curIframe = iframes[i]?.contentDocument || iframes[i]?.contentWindow?.document;
              curIframe.body.style.width = '100%';
              curIframe.body.style.height = '100%';
              curIframe.body.style.overflow = 'auto';
              curIframe.body.style.boxSizing = 'border-box';
            }
          }, 500);

          const elementsToRemove = ['viewer-next', 'viewer-prev'];
          elementsToRemove.forEach((className) => {
            const elements = iframeDocument.getElementsByClassName(className);
            while (elements.length > 0) {
              elements[0].parentNode.removeChild(elements[0]);
            }
          });
        }
        // const meta = iframeRef.current.contentDocument.createElement('meta');
        // meta.name = 'viewport';
        // meta.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=yes';
        // iframeRef.current.contentDocument.head.appendChild(meta);
      }
    };

    return (
      <Popup
        visible={visible}
        onMaskClick={onClose}
        position="right"
        showCloseButton
        bodyStyle={POPUP_BODY_STYLE}
        className="attachment-popup"
        destroyOnClose
        onClick={onClose}
      >
        <PreviewPopupContainer>
          <iframe
            ref={iframeRef}
            title={`附件预览 - ${url}`}
            style={IFRAME_STYLE}
            src={visible ? `/preview/onlinePreview?url=${encodeURIComponent(url)}` : undefined}
            frameBorder="0"
            allow="fullscreen"
            sandbox="allow-same-origin allow-scripts allow-popups"
            onLoad={() => {
              injectMobileMeta();
            }}
          />
        </PreviewPopupContainer>
      </Popup>
    );
  },
);

// 优化后的列表项组件
export const AttachmentItemRow: React.FC<{ item: AttachmentItem }> = memo(
  ({ item }: { item: AttachmentItem }) => {
    const [visible, setVisible] = useState(false);
    const [previewVisible, setPreviewVisible] = useState(false);
    const [loading, setLoading] = useState(false);

    // 使用useCallback优化事件处理
    const showActionSheet = useCallback(() => {
      const handler = ActionSheet.show({
        actions: [
          { text: '在线预览', key: 'preview' },
          { text: '下载文件', key: 'download' },
        ],
        cancelText: '取消',
        onAction: async (action) => {
          switch (action.key) {
            case 'preview':
              handler.close();
              setPreviewVisible(true);
              break;

            case 'download':
              try {
                setLoading(true);
                await handleDownload(item.id, item.name);
              } catch (error) {
                Toast.show({ content: '下载失败，请重试' });
              } finally {
                setLoading(false);
                handler.close();
              }
              break;
          }
        },
      });
    }, [item]);
    // 缓存格式化后的内容
    const { contentElements } = useMemo(() => {
      const date = item.uploadDate ? dayjs(item.uploadDate).format('YYYY-MM-DD HH:mm') : null;
      return {
        contentElements: [item.uploadUserRealName, date].filter(Boolean),
      };
    }, [item.uploadUserRealName, item.uploadDate]);

    return (
      <>
        <List.Item key={item.id} onClick={showActionSheet} aria-label={`附件：${item.name}`} tabIndex={0} disabled={loading}>
          <div className="file_name" title={item.name}>
            {item.name}
            {loading && <Skeleton.Title animated style={{ width: '100%' }} />}
          </div>
          <div className="file_info">{useInterleave(contentElements, <span style={SPLIT_STYLE} />)}</div>
        </List.Item>

        <PreviewPopup visible={previewVisible} url={item.url} onClose={() => setPreviewVisible(false)} />
      </>
    );
  },
  (prev, next) => {
    // 精确控制重渲染条件
    return isEqual(prev.item, next.item);
  },
);

/**
 * 附件列表
 * @param {AttachmentContentComponentProps} props
 * @param {string} url 附件下载地址
 * param {Array} dataList 附件列表数据
 * @returns
 */
const AttachmentContentComponent: React.FC<AttachmentContentProps> = ({ url, options = {}, dataList: propDataList = [] }) => {
  // 请求逻辑优化
  const {
    data: fetchedData = [],
    loading,
    error,
  } = useRequest<AttachmentItem[], any>(
    async () => {
      if (!url) return [];
      const res: { code: string; data: AttachmentItem[] } = await request(url, options);
      if (res?.code === '1') {
        return res.data;
      } else {
        return [];
      }
    },
    {
      manual: !url,
      refreshDeps: [url], // URL变化时自动刷新
      onError: (e) => console.error('附件加载失败:', e),
    },
  );

  // 数据合并策略优化
  const mergedData = useMemo(() => {
    return url ? fetchedData : propDataList;
  }, [url, fetchedData, propDataList]);

  // 加载状态处理
  if (loading) {
    return <div className="loading-indicator">加载附件中...</div>;
  }

  // 错误处理
  if (error) {
    return <div className="error-indicator">附件加载失败: {error.message}</div>;
  }

  // 空状态处理
  if (!mergedData?.length) {
    return <Empty description="暂无附件" />;
  }

  return (
    <OperationFileList style={LIST_STYLE}>
      {mergedData.map((item) => (
        <AttachmentItemRow key={item.id} item={item as any} />
      ))}
    </OperationFileList>
  );
};

export default memo(AttachmentContentComponent);
