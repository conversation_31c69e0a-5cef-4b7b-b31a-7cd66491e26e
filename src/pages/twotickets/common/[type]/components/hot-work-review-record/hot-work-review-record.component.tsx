import GridList from '@/component/grid-list/grid-list.component';
import { dateFormat, showFormat } from '@/constant';
import { Ellipsis, Grid, List, Space } from 'antd-mobile';
import dayjs from 'dayjs';
import { useEffect, useMemo, useRef, useState } from 'react';
import { styled } from 'styled-components';
interface FieldsProps {
  title: string;
  name: string;
  type?: string;
}
export interface RecordFieldsProps {
  title: string;
  name: string;
  status: number;
  fields: FieldsProps[];
}

interface Props {
  data?: any;
  recordFields?: RecordFieldsProps[];
}
const ReviewRecordList = styled(List)`
  padding: 2.4802vw;
  --adm-color-background: transparent;
  div[class^='.listHeader > div'] {
    height: 5vw;
  }
  background: #fff;
  .record-name {
    font-size: 1.5333rem;
    font-weight: 400;
    letter-spacing: 0rem;
    line-height: 2.2203rem;
    color: rgba(51, 51, 51, 1);
    margin-bottom: 2.4802vw;
  }
  .record-info {
    font-size: 1.2rem;
    font-weight: 400;
    letter-spacing: 0rem;
    line-height: 1.7377rem;
    color: rgba(102, 102, 102, 1);
    display: flex;
    flex-direction: row;
    align-items: center;
    flex-wrap: wrap;
    gap: 0 4vw;
    width: 100%;
  }
  .record-label {
    color: rgba(153, 153, 153, 1);
    text-align: right;
  }
  .record-value {
    color: rgba(51, 51, 51, 1);
    text-align: 'center';
  }
  .record-textArea {
    flex-direction: column;
    .record-label {
      position: absolute;
      color: rgba(17, 84, 237, 1);
    }
    .record-value {
      text-indent: 6rem;
    }
  }
  .remark {
    margin: 5.373vw 0;
    font-size: 4.1667vw;
    font-weight: 400;
    letter-spacing: 0vw;
    line-height: 6.0337vw;
    color: rgba(51, 51, 51, 1);
    width: 100%;
    label {
      color: rgba(17, 84, 237, 1);
    }
  }
`;

const EllipsisDescription = ({ title, value }: any) => {
  const [isExpand, setIsExpand] = useState(false);
  const labelRef = useState<HTMLLabelElement | null>(null);
  const [labelWidth, setLabelWidth] = useState(0);

  // 动态获取label宽度
  useMemo(() => {
    if (labelRef[0]) {
      setLabelWidth(labelRef[0].offsetWidth);
    }
  }, [title, labelRef[0]]);

  const EllipsisDescriptionWrapper = styled.div`
    margin: 5.373vw 0;
    font-size: 4.1667vw;
    font-weight: 400;
    letter-spacing: 0vw;
    line-height: 6.0337vw;
    color: rgba(51, 51, 51, 1);
    width: 100%;
    display: flex;
    align-items: flex-start;
    > label {
      color: rgba(17, 84, 237, 1);
      white-space: nowrap;
      margin-right: 0.5em;
      flex-shrink: 0;
    }
    .ellipsis-content {
      flex: 1;
      min-width: 0;
      text-indent: ${labelWidth ? `${labelWidth + 8}px` : '0'}; // 8px为margin-right
      display: block;
    }
  `;

  return (
    <EllipsisDescriptionWrapper>
      <label ref={(el: HTMLLabelElement | null) => (labelRef[0] = el)}>{title}：</label>
      <span className="ellipsis-content">
        <Ellipsis
          content={value || ''}
          rows={2}
          expandText="展开"
          collapseText="收起"
          onExpand={() => setIsExpand(true)}
          onCollapse={() => setIsExpand(false)}
        />
      </span>
    </EllipsisDescriptionWrapper>
  );
};

// 自定义展开隐藏多行div内的dom（dom中包含label这些HTML的dom）
// 新增参数 maxRows，指定展示多少行显示省略号，默认为2
const EllipsisRemark = ({ value, maxRows = 2 }: { value: string; maxRows?: number }) => {
  const [expanded, setExpanded] = useState(false);
  const [showExpand, setShowExpand] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (contentRef.current) {
      const lineHeight = parseFloat(getComputedStyle(contentRef.current).lineHeight || '20');
      const maxHeight = lineHeight * maxRows;
      setShowExpand(contentRef.current.scrollHeight > maxHeight);
    }
  }, [value, maxRows]);

  return (
    <div className="remark" style={{ position: 'relative', overflow: 'hidden', paddingBottom: showExpand ? '2em' : undefined }}>
      <div
        ref={contentRef}
        style={{
          maxHeight: !expanded && showExpand ? `${1.4 * maxRows}em` : 'none',
          overflow: !expanded && showExpand ? 'hidden' : 'visible',
          display: '-webkit-box',
          WebkitLineClamp: !expanded && showExpand ? maxRows : 'unset',
          WebkitBoxOrient: 'vertical',
          whiteSpace: 'pre-line',
        }}
        dangerouslySetInnerHTML={{ __html: value }}
      />
      {showExpand && (
        <div
          style={{
            position: 'absolute',
            right: 0,
            bottom: 0,
            background: 'linear-gradient(to top, #fff 60%, rgba(255,255,255,0))',
            padding: '0 0.5em',
            cursor: 'pointer',
            color: '#1154ED',
            fontSize: '4.1667vw',
            lineHeight: 2,
            display: 'flex',
            alignItems: 'center',
            height: '2em',
            zIndex: 1,
            justifyContent: 'flex-end',
            width: '100%',
            pointerEvents: 'auto',
            userSelect: 'none',
          }}
        >
          <span onClick={() => setExpanded(true)} style={{ marginRight: expanded ? 0 : 16, display: expanded ? 'none' : 'inline' }}>
            展开
          </span>
          <span onClick={() => setExpanded(false)} style={{ display: expanded ? 'inline' : 'none' }}>
            收起
          </span>
        </div>
      )}
    </div>
  );
};

export const HotWorkReviewRecordComponent = (props: Props) => {
  const { data, recordFields } = props;
  const recordValues: any = useMemo(
    () => ({
      // 工作票签发
      signName: data?.workFlowSign?.signName || null, //工作签发人
      signTime: dateFormat(data?.workFlowSign?.signTime, 'YYYY/MM/DD HH:mm', '-'), //签发日期
      ownerSignName: data?.workFlowSign?.ownerSignName || null, //工作票业主签发人
      ownerSignTime: dateFormat(data?.workFlowSign?.ownerSignTime, 'YYYY/MM/DD HH:mm', '-'), //签发日期
      // 审批
      fireHeadName1: data?.workFireDepartmentHead?.fireHeadNane || null, //动火部门负责人
      fireHeadTime1: dateFormat(data?.workFireDepartmentHead?.approveTime, 'YYYY/MM/DD HH:mm', '-'), //审核时间
      fireFightingHeadName: data?.workFireFightingHead?.fireFightingHeadName || null, //企业消防管理部门负责人
      reviewTime: dateFormat(data?.workFireFightingHead?.reviewTime, 'YYYY-MM-DD HH:mm', '-'), //审核时间
      // managerName: data?.workFireSafeManager?.managerName || null, //企业安监部门主管
      // managerTime: dateFormat(data?.workFireSafeManager?.reviewTime, 'YYYY-MM-DD HH:mm', '-'), //审核时间
      safetySupervisionName: data?.workFireSafetySupervision?.safetySupervisionName || null, //企业安监部门负责人
      examineTime: dateFormat(data?.workFireSafetySupervision?.examineTime, 'YYYY-MM-DD HH:mm', '-'), //审核时间
      fireHeadName: data?.workFireDepartment?.fireHeadName || null, //企业主管安全生产的领导(总工程师)
      headTime: dateFormat(data?.workFireDepartment?.headTime, 'YYYY-MM-DD HH:mm', '-'), //审核时间
      fireTime:
        data?.workFireDepartment?.fireBeginTime || data?.workFireDepartment?.fireEndTime
          ? dateFormat(data?.workFireDepartment?.fireBeginTime, 'YYYY-MM-DD HH:mm', '-') +
            ' 至 ' +
            dateFormat(data?.workFireDepartment?.fireEndTime, 'YYYY-MM-DD HH:mm', '-')
          : null, //批准动火时间
      // 工作票接收、确认
      confirmDesc:
       data?.workFireAcceptConfirm?.confirmTime ? '动火现场安全技术措施已复查、确认，符合工作要求，确认时间：' +
        `<label>${dateFormat(data?.workFireAcceptConfirm?.confirmTime, 'YYYY-MM-DD HH:mm', '-')}</label>`: null, //工作票接收、确认描述
      confirmHeadName: data?.workFireAcceptConfirm?.confirmHeadName || null, //工作票接收、确认负责人
      acceptTime: dateFormat(data?.workFireAcceptConfirm?.acceptTime, 'YYYY-MM-DD HH:mm', '-'), //接收、确认时间
      // 值长批准
      shiftSupervisorName: data?.workFlowRatify?.shiftSupervisorName || null, //值长
      shiftSupervisorTime: dateFormat(data?.workFlowRatify?.approveTune, 'YYYY-MM-DD HH:mm', '-'), //值长批准时间
      // 工作票许可
      allowBeginTime: dateFormat(data?.workFlowLicense?.allowBeginTime, 'YYYY-MM-DD HH:mm', '-'), //运行许可动火时间
      allowName: data?.workFlowLicense?.allowName || null, //工作许可人
      allowTime: data?.workFlowLicense?.allowTime ? dateFormat(data?.workFlowLicense?.allowTime, 'YYYY-MM-DD HH:mm', '-'): null, //许可动火时间
      // 动火部门审核意见
      checkResult:
        '应配备的消防设施和采取的消防措施、安全措施已符合要求。可燃性、易爆气体含量或粉尘浓度测定合格。测定值：<label>（' +
        data?.workCheckResult?.checkResult +
        ')<label>',
      signName1: data?.workCheckResult?.checkUserName, //动火执行人
      signTime1: dateFormat(data?.workCheckResult?.checkTime, 'YYYY-MM-DD HH:mm', '-'),
      // signName1: data?.workFireSignConfirmList?.find((item: any) => item.type === 1)?.signName, //动火执行人
      // signTime1: dateFormat(data?.workFireSignConfirmList?.find((item: any) => item.type === 1)?.signTime, 'YYYY-MM-DD HH:mm', '-'), //时间
      signName2: data?.workFireSignConfirmList?.find((item: any) => item.type === 2)?.signName, //消防监护人
      signTime2: dateFormat(data?.workFireSignConfirmList?.find((item: any) => item.type === 2)?.signTime, 'YYYY-MM-DD HH:mm', '-'), //时间
      signName3: data?.workFireSignConfirmList?.find((item: any) => item.type === 3)?.signName, //动火工作负责人
      signTime3: dateFormat(data?.workFireSignConfirmList?.find((item: any) => item.type === 3)?.signTime, 'YYYY-MM-DD HH:mm', '-'), //时间
      signName4: data?.workFireSignConfirmList?.find((item: any) => item.type === 4)?.signName, //动火部门负责人
      signTime4: dateFormat(data?.workFireSignConfirmList?.find((item: any) => item.type === 4)?.signTime, 'YYYY-MM-DD HH:mm', '-'), //时间
      signName5: data?.workFireSignConfirmList?.find((item: any) => item.type === 5)?.signName, //企业安监部负责人
      signTime5: dateFormat(data?.workFireSignConfirmList?.find((item: any) => item.type === 5)?.signTime, 'YYYY-MM-DD HH:mm', '-'), //时间
      leaderName: data?.workFireFirstResponsiblePerson?.leaderName, //企业主管安全生产的领导(总工程师)
      reviewTime2: dateFormat(data?.workFireFirstResponsiblePerson?.reviewTime, 'YYYY-MM-DD HH:mm', '-'), //reviewTime2
      allowBeginTime2: dateFormat(data?.workFireFirstResponsiblePerson?.allowBeginTime, 'YYYY-MM-DD HH:mm', '-'), //允许动火时间
      // 动火票终结
      allowTime2:
        '动火工作于 ' +
        dateFormat(data?.workFlowEnd?.allowTime, 'YYYY-MM-DD HH:mm', '-') +
        ' 结束，材料、工具已清理完毕，现场确无残留火种，参与现场动火工作的有关人员已全部撤离，动火工作结束。',
      allowName2: data?.workFlowEnd?.allowName,
      submitTime: dateFormat(data?.workFlowEnd?.submitTime, 'YYYY-MM-DD HH:mm', '-'),
      workMatter: data?.workFlowEnd?.workMatter,
      finalName0: data?.workFlowExecute?.submitHeadName, //工作负责人
      finalTime0: dateFormat(data?.workFlowExecute?.submitTime, 'YYYY-MM-DD HH:mm', '-'), //
      finalName1: data?.workFireFinalConfirmList?.find((item: any) => item.type === 1)?.signName, //动火执行人
      finalTime1: dateFormat(data?.workFireFinalConfirmList?.find((item: any) => item.type === 1)?.signTime, 'YYYY-MM-DD HH:mm', '-'), //时间
      finalName2: data?.workFireFinalConfirmList?.find((item: any) => item.type === 2)?.signName, //消防监护人
      finalTime2: dateFormat(data?.workFireFinalConfirmList?.find((item: any) => item.type === 2)?.signTime, 'YYYY-MM-DD HH:mm', '-'), //时间
      // finalName3: data?.workFireFinalConfirmList?.find((item: any) => item.type === 3)?.signName, //动火工作负责人（结束填报）
      // finalTime3: dateFormat(data?.workFireFinalConfirmList?.find((item: any) => item.type === 3)?.signTime, 'YYYY-MM-DD HH:mm', '-'), //时间
      // 审查
      auditName: data?.workFlowAudit?.auditName || null, //审核人
      auditTime: dateFormat(data?.workFlowAudit?.auditTime, 'YYYY-MM-DD HH:mm', '-'), //审核日期
      auditResult: data?.workFlowAudit?.auditResult || null, //审核结果
      auditOpinion: data?.workFlowAudit?.auditOpinion || null, //审核意见
    }),
    [data],
  );
  return (
    <ReviewRecordList
      style={{
        '--border-top': 'none',
        '--border-bottom': 'none',
        '--padding-left': '0',
      }}
    >
      {recordFields
        ?.filter((recordField: any) => data?.status && Number(recordField.status) < Number(data.status))
        ?.map((group: any, index: number) =>
          Number(data?.status) > group.status ? (
            <List.Item key={index} extra={false}>
              <div className="record-name">{group?.title}</div>
              <div className="record-info">
                {group.columns.map((item: any, index: string | number | undefined) =>
                  item?.type === 'group' ? (
                    <Space
                      block
                      wrap
                      style={{
                        '--gap-horizontal': '.9921vw',
                        '--gap-vertical': '.9921vw',
                        marginBottom: '.0992vw',
                      }}
                    >
                      {(item?.columns || []).map((field: any) => (
                        // <Grid.Item>
                        <Space className={`record-${item.type}`}>
                          <div className="record-label">{field.title}：</div>
                          <div className="record-value">
                            {field.type === 'textArea' ? (
                              <Ellipsis
                                direction="end"
                                content={recordValues?.[field.name] || ''}
                                expandText="展开"
                                collapseText="收起"
                                rows={3}
                              />
                            ) : field.type === 'date' ? (
                              <Ellipsis content={recordValues?.[field.name] ? dayjs(recordValues?.[field.name]).format(showFormat) : ''} />
                            ) : (
                              <Ellipsis content={recordValues?.[field.name] || ''} expandText="展开" collapseText="收起" />
                            )}
                          </div>
                        </Space>
                      ))}
                    </Space>
                  ) : item?.type === 'table' && item?.columns?.length  ? (
                    <div
                      style={{
                        width: '100%',
                      }}
                    >
                      <GridList
                        columns={item?.columns?.map((i: any) => ({
                          title: i.title,
                          dataIndex: i.title,
                          span: 2,
                          align: 'center',
                        }))}
                        dataSource={item?.columns?.[0]?.values?.map((_v: any, i: any) =>
                          item?.columns?.reduce(
                            (arr: any, cur: { title: any; values: { [x: string]: any }; fields: { [x: string]: string | number } }) => ({
                              ...arr,
                              [cur.title]: cur?.values ? cur?.values[i] : recordValues?.[cur?.fields[i]],
                            }),
                            {},
                          ),
                        )?.filter((_v: any, _index: number)=>item?.status[_index]< Number(data?.status) && _v?.['人员'])}
                        rowKey={item?.columns?.[0]?.title}
                        gridConfig={{
                          columns: 6,
                          gap: 0,
                        }}
                      ></GridList>
                    </div>
                  ) : item?.type === 'description' && item?.status < Number(data?.status) ? (
                    item?.columns?.map((column: any) => (
                      <EllipsisDescription title={column?.title || ''} value={recordValues?.[column?.name]} />
                    ))
                  ) : item?.type === 'remark' ? (
                    item?.columns?.map((column: any) =>
                      recordValues?.[column?.name] && item?.status < Number(data?.status) ? (
                        <EllipsisRemark
                          value={(column?.title ? `<label>${column?.title}：</label>` : '') + recordValues?.[column?.name]}
                          maxRows={2}
                        />
                      ) : null,
                    )
                  ) : recordValues?.[item.name] && item?.status < Number(data?.status) ? (
                    <Grid
                      key={index}
                      className={`record-${item.type}`}
                      columns={2}
                      style={{
                        width: '100%',
                      }}
                    >
                      <Grid.Item>
                        <div className="record-label">{item.title}：</div>
                      </Grid.Item>
                      <Grid.Item>
                        <div className="record-value">
                          {item.type === 'textArea' ? (
                            <Ellipsis
                              direction="end"
                              content={recordValues?.[item.name] || ''}
                              expandText="展开"
                              collapseText="收起"
                              rows={3}
                            />
                          ) : item.type === 'date' ? (
                            dayjs(recordValues[item.name]).format(showFormat)
                          ) : (
                            recordValues[item.name]
                          )}
                        </div>
                      </Grid.Item>
                    </Grid>
                  ) : null,
                )}
              </div>
            </List.Item>
          ) : null,
        )}
    </ReviewRecordList>
  );
};
