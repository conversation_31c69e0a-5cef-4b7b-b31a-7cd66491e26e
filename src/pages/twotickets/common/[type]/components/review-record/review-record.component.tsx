import { showFormat } from '@/constant';
import { Ellipsis, List, Space } from 'antd-mobile';
import dayjs from 'dayjs';
import { styled } from 'styled-components';
interface FiledsProps {
  title: string;
  name: string;
  type?: string;
}
export interface RecordFiledsProps {
  title: string;
  name: string;
  fileds: FiledsProps[];
}

interface Props {
  data?: any;
  recordFileds?: RecordFiledsProps[];
}
const ReviewRecordList = styled(List)`
  .record-name {
    font-size: 1.5333rem;
    font-weight: 400;
    letter-spacing: 0rem;
    line-height: 2.2203rem;
    color: rgba(51, 51, 51, 1);
  }
  .record-info {
    font-size: 1.2rem;
    font-weight: 400;
    letter-spacing: 0rem;
    line-height: 1.7377rem;
    color: rgba(102, 102, 102, 1);
    display: flex;
    flex-direction: row;
    align-items: center;
    flex-wrap: wrap;
    gap: 0 4vw;
  }
  .record-label {
    color: rgba(153, 153, 153, 1);
  }
  .record-value {
    color: rgba(51, 51, 51, 1);
  }
  .record-textArea {
    flex-direction: column;
    .record-label {
      position: absolute;
      color: rgba(17, 84, 237, 1);
    }
    .record-value {
      text-indent: 6rem;
    }
  }
`;
export const ReviewRecordComponent = (props: Props) => {
  const { data, recordFileds } = props;
  return (
    <ReviewRecordList
      style={{
        '--border-top': 'none',
        '--border-bottom': 'none',
      }}
    >
      {recordFileds?.map((group: any, index: number) =>
        data[group.name] ? (
          <List.Item key={index} extra={false}>
            <div className="record-name">{group?.title}</div>
            <div className="record-info">
              {group.fileds.map((item, index) =>
                data[group.name]?.[item.name] ? (
                  <Space key={index} style={{ '--gap': '0px' }} className={`record-${item.type}`}>
                    <div className="record-label">{item.title}：</div>
                    <div className="record-value">
                      {item.type === 'textArea' ? (
                        <Ellipsis direction="end" content={data[group.name][item.name]} expandText="展开" collapseText="收起" rows={3} />
                      ) : item.type === 'date' ? (
                        dayjs(data[group.name][item.name]).format(showFormat)
                      ) : (
                        data[group.name][item.name]
                      )}
                    </div>
                  </Space>
                ) : null,
              )}
            </div>
          </List.Item>
        ) : null,
      )}
    </ReviewRecordList>
  );
};
