import useInterleave from '@/hooks/useInterleave';
import { OperatorStandardTool } from '@/styles/twotickets.style';
import { List } from 'antd-mobile';
import stringUtils from '@/utils/str';

interface StandardOperatorToolListComponentProps {
  dataList: any[];
}

const StandardOperatorToolListComponent = (props: StandardOperatorToolListComponentProps) => {
  const { dataList } = props;
  const SplitPoint = (
    <div
      style={{
        display: 'inline-block',
        width: '.7408vw',
        height: '.7408vw',
        borderRadius: '.3703vw',
        margin: '0 1.1111vw',
        backgroundColor: 'rgba(102, 102, 102, 1)',
      }}
    />
  );
  return (
    <OperatorStandardTool
      style={{
        '--border-top': 'none',
        '--border-bottom': 'none',
        marginTop: '3.7036vw',
      }}
    >
      {dataList.map((item: any, index: number) => {
        const formattedInfo = stringUtils.join([item.type, item.responsibility]?.filter((i) => i));
        return (
          <List.Item key={index}>
            <div className="tool_name">{item.toolName}</div>
            {/* 插入html */}
            <div className="tool_info" dangerouslySetInnerHTML={{
              __html: formattedInfo
            }}></div>
          </List.Item>
        );
      })}
    </OperatorStandardTool>
  );
};

export default StandardOperatorToolListComponent;
