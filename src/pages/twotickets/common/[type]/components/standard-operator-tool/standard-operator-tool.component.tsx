import useInterleave from '@/hooks/useInterleave';
import { getDeviceType } from '@/services/twotickets/operation/statistics';
import { OperatorStandardTool } from '@/styles/twotickets.style';
import { CheckList, Dropdown, Form, Grid, List, SearchBar, Space } from 'antd-mobile';
import React, {  useEffect, useMemo, useState } from 'react';
import { styled } from 'umi';
import StandardOperatorToolListComponent from './standard-operator-tool-list.component';

const StandardOperatorToolComponentWrapper = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  background-color: #fff;
  padding: 0;
  box-sizing: border-box;
  .adm-list-item-content-main {
    padding: 0;
  }
`;

interface OperationRiskComponentProps {
  dataList: { toolName: string; type: string; responsibility: string }[];
}

/**
 * 构建选择列表格式
 * @param data
 * @returns
 */
function handleTreeSelect(data: any): any[] {
  if (!data) return [];
  return Object.entries(data).map(([value, label]) => {
    return { label: label, value: label };
  });
}


const StandardOperatorToolComponent: React.FC<OperationRiskComponentProps> = (props: OperationRiskComponentProps) => {
  const { dataList } = props;
  const [form] = Form.useForm();
  const [typeDict, setTypeDict] = useState<any>([]);
  const filterType = Form.useWatch('type', form);
  const filterName = Form.useWatch('name', form);
  useEffect(() => {
    getDeviceType().then((res) => {
      if (res.code === '1') {
        setTypeDict([
          {
            value: '',
            label: '全部',
          },
          ...handleTreeSelect(res.data),
        ]);
      }
    });
  }, []);

  const list = useMemo(() => {
    return (
      dataList?.filter((item: any) => {
        // 根据filterOptions的type和name，过滤列表数据
        // 如果type为空则放弃type的过滤,否则，就判断与当前数据的type是否相同
        // 如果name为空则放弃name的过滤，就判断与当前数据的toolName是否包含name输入的数据
        if (filterType?.[0] && filterType[0] !== item.type) {
          return false;
        } else if (filterName && !item?.toolName?.includes(filterName)) {
          return false;
        } else {
          return true;
        }
      }) || []
    );
  }, [dataList, filterType, filterName]);

  return (
    <>
      <Form
        style={{
          '--border-bottom': 'none',
          '--border-top': 'none',
          '--border-inner': 'none',
          position: "sticky",
          top: 0,
          zIndex: 1,
        }}
        form={form}
      >
        <StandardOperatorToolComponentWrapper>
            <Dropdown>
              <Dropdown.Item key="type" title={filterType?.[0] || "分类"}>
                <div style={{ padding: 12 }}>
                <Form.Item name="type" initialValue=''>
                  <CheckList
                    style={{
                      '--border-bottom': 'none',
                      '--border-top': 'none',
                      // "--border-inner": 'none'
                    }}
                  >
                    {typeDict.map((item) => (
                      <CheckList.Item key={item.value} value={item.value}>
                        {item.label}
                      </CheckList.Item>
                    ))}
                  </CheckList>
                  </Form.Item>
                </div>
              </Dropdown.Item>
            </Dropdown>
          <Form.Item
            name="name"
            style={{
              flex: 1,
            }}
          >
            <SearchBar
              style={{
                '--border-radius': '3.7036vw',
                '--padding-left': '3.7036vw',
              }}
              onlyShowClearWhenFocus
              clearable
              placeholder="请输入"
            />
          </Form.Item>
        </StandardOperatorToolComponentWrapper>
      </Form>
      <StandardOperatorToolListComponent dataList={list} />
    </>
  );
};

export default StandardOperatorToolComponent;
