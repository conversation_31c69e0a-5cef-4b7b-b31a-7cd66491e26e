import { useEffect, useState } from 'react';
import { <PERSON>, Collapse, Divider, Ellip<PERSON>, Space } from 'antd-mobile';
import { DownOutline, UpOutline } from 'antd-mobile-icons'
import styles from './safety-risk.page.less';
import { RISK_LEVEL_COLOR } from '@/constant';

interface Props {
  dataList: any[]; // 安全控制卡数据
}
function groupByField(arr, field) {
  return arr.reduce((acc, obj) => {
    const key = obj[field];
    if (!acc[key]) {
      acc[key] = [];
    }
    acc[key].push(obj);
    return acc;
  }, {});
}

const SafetyRiskComponent = ((props: Props) => {
  const [dataSource, setDataSource] = useState<Record<string, any[]>>({});
  const { dataList } = props;

  useEffect(() => {
    if (dataList?.length) {
      const groupedByCategory  = groupByField(dataList, 'process');
      setDataSource(groupedByCategory );
    }
  }, [dataList])

  return (
    <div className={styles.safetyAnalysisContent}>
      <Space direction='vertical'>
        {Object.keys(dataSource)?.map((process, index) => (
          <Collapse defaultActiveKey={process.slice(0,1)}>
            <Collapse.Panel key={process.slice(0,1)} title={process} className={`process${index}`}>
              {dataSource[process]?.map((item, index) => (
                <div key={index}>
                  <Space direction='vertical'>
                    <div className={styles.riskDescription}>
                      <div className={styles.subTitle}>风险描述及后果：</div>
                      <Ellipsis
                        direction='end'
                        rows={2}
                        content={item.riskDescription}
                        expandText={
                          <>
                            展开
                            <DownOutline />
                          </>
                        }
                        collapseText={
                          <>
                            收起
                            <UpOutline />
                          </>
                        }
                      />
                    </div>
                    <div className={styles.controlMeasure}>
                      <div className={styles.subTitle}>管控措施：</div>
                      <Ellipsis
                        direction='end'
                        rows={2}
                        content={item.controlMeasure}
                        expandText={
                          <>
                            展开
                            <DownOutline />
                          </>
                        }
                        collapseText={
                          <>
                            收起
                            <UpOutline />
                          </>
                        }
                      />
                    </div>
                    <div className={styles.riskLevel}>
                      <div className={styles.subTitle}>风险等级：</div>
                      <div className={styles.level} style={{ color: RISK_LEVEL_COLOR[item.riskLevel] }}>{item.riskLevel}</div>
                    </div>
                  </Space>
                  {index !== dataSource[process].length - 1 ? <Divider /> : null}
                </div>
              ))}
            </Collapse.Panel>
          </Collapse>
        ))}
      </Space>
    </div>
  );
});

export default SafetyRiskComponent;
