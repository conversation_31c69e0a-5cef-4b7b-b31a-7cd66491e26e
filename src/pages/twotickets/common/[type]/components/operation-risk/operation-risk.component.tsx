import { OperateRiskList } from '@/styles/twotickets.style';
import { List, Space } from 'antd-mobile';

export interface OperationRiskComponentProps {
  dataList: { perilPoint: string, measure: string }[];
}

const OperationRiskComponent = (props: OperationRiskComponentProps) => {
  const { dataList } = props;
  return (
    <OperateRiskList
      style={{
        '--border-bottom': 'none',
        '--border-top': 'none',
        '--border-inner': 'none',
        '--align-items': 'flex-start',
      }}
    >
      {dataList?.map((item: any, index: number) => (
        <List.Item key={index} prefix={<div className="prefix">{index + 1}、</div>}>
          <div className="operate_risk">
            <Space className="label">危&ensp;险&ensp;点：</Space>
            {item?.perilPoint}
          </div>
          <div className="operate_risk">
            <Space className="label">预防措施：</Space>
            {item?.measure}
          </div>
        </List.Item>
      ))}
    </OperateRiskList>
  );
};

export default OperationRiskComponent;
