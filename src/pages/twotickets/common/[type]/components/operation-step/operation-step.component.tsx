import { OperateStepsList } from '@/styles/twotickets.style';
import { Divider, List, Tag } from 'antd-mobile';
import { useMemo, useCallback } from 'react';
import { styled } from 'umi';
const OperationStepExplanation = styled.div`
  font-size: 3.3333vw;
  font-weight: 400;
  letter-spacing: 0vw;
  line-height: 4.8269vw;
  color: rgba(102, 102, 102, 1);
  padding: 2.3147vw;
`;

const OperationStepExplanated = styled.div`
  background-color: #fff;
  margin: 2.3147vw 0;
  padding-bottom: 3.2408vw;
  .title {
    font-size: 4.2592vw;
    font-weight: 500;
    letter-spacing: 0vw;
    line-height: 6.1675vw;
    color: rgba(51, 51, 51, 1);
    padding: 3.2408vw 3.2408vw .9258vw;
    .executed {
      font-size: 3.8889vw;
      font-weight: 400;
      letter-spacing: 0vw;
      line-height: 5.6314vw;
      color: rgba(51, 51, 51, 1);
    }
  }
  .attention {
    font-size: 3.3333vw;
    font-weight: 400;
    letter-spacing: 0vw;
    line-height: 4.8269vw;
    color: rgba(153, 153, 153, 1);
    margin: 0 3.2408vw .9258vw;
  }
`;
export interface OperationStepComponentProps {
  dataList: { operationStep: string }[];
  remark?: string;
}

const OperationStepComponent = (props: OperationStepComponentProps) => {
  const { dataList, remark = null } = props;
  const hasExecuted = useMemo(() => !!dataList?.filter((step) => step?.isMainOperation === '1')?.length, [dataList]);
  const OperateStepsListRender = useCallback(() => {
    return (
      <OperateStepsList
        style={{
          '--border-top': 'none',
          '--border-bottom': 'none',
          '--align-items': 'flex-start',
        }}
      >
        {dataList?.map((item: any, index: number) => (
          <List.Item key={index} prefix={<div className="prefix">{index + 1}</div>}>
            <div className="operate_steps">
              {hasExecuted && item?.isMainOperation !== '1' ? (
                <Tag color="rgba(253, 146, 40, 1)" fill="outline" style={{ '--border-radius': '.5556vw', marginRight: '.9258vw' }}>
                  未执行
                </Tag>
              ) : null}
              {item?.operationStep}
            </div>
            {(item?.operationTime || item?.remark) && (
              <div className="body">
                {item?.operationTime && <div className="operationTime">操作时间：{item?.operationTime}</div>}
                {item?.remark && <div className="remark">备注：{item?.remark}</div>}
              </div>
            )}
          </List.Item>
        ))}
      </OperateStepsList>
    );
  }, [dataList, hasExecuted]);
  return (
    <>
      {!hasExecuted ? (
        <div>
          <OperationStepExplanation>注：默认倒数第二项操作项目为“全面检查”，倒数第一项为“汇报批准人”</OperationStepExplanation>
          {OperateStepsListRender()}
        </div>
      ) : (
        <OperationStepExplanated>
          <div className="title">
            操作项
            {hasExecuted ? (
              <span className="executed">
                （
                <span
                  style={{
                    color: 'rgba(17, 84, 237, 1)',
                  }}
                >
                  {dataList?.filter((item) => item?.isMainOperation === '1')?.length}
                </span>
                /{dataList?.length}）
              </span>
            ) : (
              ''
            )}
          </div>
          <div className="attention">注：默认倒数第二项操作项目为“全面检查”，倒数第一项为“汇报批准人”</div>
          <Divider
            style={{
              margin: '0',
            }}
          />
          {OperateStepsListRender()}
        </OperationStepExplanated>
      )}
      {hasExecuted && (
        <OperationStepExplanated>
          <div className="title">备注</div>
          <div className="attention">{remark || '无'}</div>
        </OperationStepExplanated>
      )}
    </>
  );
};

export default OperationStepComponent;
