import TwoTicketsNavBar from '@/component/twotickets-navbar/twotickets-navbar.component';
import { useParams, useSearchParams } from 'umi';

import styles from './check-list.page.less';
import AttachmentContentComponent, { AttachmentContentComponentProps } from './components/attachment-content/attachment-content.component';
import { useRequest } from 'ahooks';
import { getOperationDetail } from '@/services/twotickets/operation/statistics';
import { Skeleton } from 'antd-mobile';
import OperationStepComponent, { OperationStepComponentProps } from './components/operation-step/operation-step.component';
import OperationRiskComponent from './components/operation-risk/operation-risk.component';
import StandardOperatorToolComponent from './components/standard-operator-tool/standard-operator-tool.component';
import ProcessViewerComponent from '@/component/process-viewer/process-viewer.component';
import { IToolTip } from '@/component/process-viewer/process-viewer.types';
import { useMemo } from 'react';
import { getWorkBaseDetail } from '@/services/twotickets/workmnt/workmnt';
import SafetyRiskComponent from './components/safety-risk/safety-risk.page';
import { ReviewRecordComponent } from './components/review-record/review-record.component';
import { RecordFieldArrays as HotWorkRecordFileldArrays, RecordFieldArraysTwo as HotWorkRecordFieldArraysTwo } from '../../workmnt/hotWork/components/basic-info/review-record-config';
import { HotWorkReviewRecordComponent } from './components/hot-work-review-record/hot-work-review-record.component';
import AssociatedMainTicket from '../../workmnt/components/associated-main-ticket/associated-main-ticket.component';
import { RecordFileds } from './components/review-record/review-record-config';

interface CheckListPageParams extends Record<string, string> {
  id: string;
  type: string;
}

function CHECK_LIST_TYPE_MAP(ticketType?: string, dataKey?: string) {
  return {
    //
    'standard-operator-tool': {
      title: '安全工器具',
      key: ticketType ? 'workBaseTools' : 'standardOperatorToolTypeList',
      render: (data: { toolName: string; type: string; responsibility: string }[]) => <StandardOperatorToolComponent dataList={data} />,
    },
    'operation-risk': {
      title: '危险点与预防措施',
      key: 'operateRiskList',
      render: (data: { perilPoint: string; measure: string }[]) => <OperationRiskComponent dataList={data} />,
    },
    'operation-step': {
      title: '操作项',
      key: ['operatorStepList', 'remark'],
      render: (data: {
        operatorStepList: { operationStep: string; isMainOperation: string; operationTime: string; remark: string }[];
        remark?: string;
      }) => <OperationStepComponent dataList={data.operatorStepList} remark={data.remark} />,
    },
    'attachment-content': {
      title: '附件',
      key: dataKey || 'attachmentContentList',
      render: (data: { name: string; url: string; uploadUserRealName: string; memo: string; uploadDate: string }[]) => (
        <AttachmentContentComponent dataList={data} />
      ),
    },
    'process-viewer': {
      title: '流程图',
      isHideBottom: true,
      key: 'processViewer',
      // 是否所有数据作为参数传递
      isRoot: true,
      render: (data: { processInstanceId: string; modelId: string; workflowApprovalRecordList: string }) =>
        data?.processInstanceId || data?.modelId ? (
          <ProcessViewerComponent
            processInstanceId={data?.processInstanceId}
            modelId={data?.modelId}
            toolTips={
              (data?.workflowApprovalRecordList?.map((item: any) => ({
                userTaskId: item.taskDefKey,
                approver: item.approverName,
                comment: item.opinion,
              })) as IToolTip[]) || []
            }
          />
        ) : null,
    },
    'safety-risk': {
      title: '过程风险预控',
      key: 'workBaseRisks',
      render: (data) => <SafetyRiskComponent dataList={data} />,
    },
    'review-record': {
      title: '流程记录',
      key: 'reviewRecord',
      isRoot: true,
      render: (data) => <ReviewRecordComponent data={data} recordFileds={RecordFileds?.filter((item: any) => data?.[item.name])} />,
    },
    'hot-work-review-record': {
      title: '流程记录',
      key: 'reviewRecord',
      isRoot: true,
      render: (data) => <HotWorkReviewRecordComponent data={data} recordFields={Number(data?.ticketType) === 6 ? HotWorkRecordFileldArrays: HotWorkRecordFieldArraysTwo as any} />,
    },
    'associated-mainTicket': {
       title: '流程记录',
      key: 'relationList',
      isRoot: true,
      render: (data) => <AssociatedMainTicket list={data?.relationList || []} />,
    }
  }
}

/**
 * 公共查看列表页面
 * standard-operator-tool： 全部安全工器具
 * operation-risk： 危险点与预防措施
 * operation-step： 操作项
 * attachment-content： 附件
 * @returns
 */
const CheckListPage: React.FC = () => {
  // 首先获取参数
  const params = useParams<CheckListPageParams>();
  const [searchParams] = useSearchParams();
  const ticketType = searchParams.get('ticketType');
  const dataKey = searchParams.get('dataKey');
  // 使用useRequest获取数据
  const API = ticketType ? getWorkBaseDetail : getOperationDetail;
  const { data, loading } = useRequest(() => API({ id: params.id, ticketType }).then((res) => res.data), {});

  // 防止 params.type 不存在或无效
  const typeInfo = CHECK_LIST_TYPE_MAP(ticketType, dataKey)?.[params.type] ?? { title: '未知类型' };
  // 参数提取
  console.log("🚀 ~ typeInfo:", typeInfo)
  const renderParams = useMemo(() => {
    // 如果是根节点，直接返回 data
    if (typeInfo?.isRoot) {
      return data;
    } else {
      // 如果不是根节点，先判断是否是数组 再进行提取参数 返回 data[typeInfo.key]
      if (Array.isArray(typeInfo?.key)) {
        return typeInfo?.key.reduce((acc, key) => {
          acc[key] = data?.[key];
          return acc;
        }, {});
      } else {
        return data?.[typeInfo.key];
      }
    }
  }, [data, typeInfo]);
  return (
    <div className={styles.container}>
      <TwoTicketsNavBar title={typeInfo.title} bgcolor="#fff" color="#000" />
      <div className={styles.content}>
        {!loading ? typeInfo?.render?.(renderParams) : <Skeleton.Paragraph />}
        {!typeInfo?.isHideBottom && <div className={styles.bottom}>已经到底了</div>}
      </div>
    </div>
  );
};

export default CheckListPage;
