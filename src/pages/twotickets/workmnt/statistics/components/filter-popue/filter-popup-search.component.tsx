import { FilterPopup } from '@/component/filter-popup/filter-popup.component';
import { getOrganizationTree } from '@/services/sys.service';
import { useRequest } from 'ahooks';
import { CheckList, Form, SideBar, TextArea } from 'antd-mobile';
import { FilterOutline } from 'antd-mobile-icons';
import { useEffect, useRef, useState } from 'react';
import { TICKET_TYPE_MAP } from '@/constant';
import { styled } from 'umi';

const FilterWrapper = styled.div`
  flex: 1;
  height: 100%;
  background-color: #ffffff;
  display: flex;
  justify-content: flex-start;
  align-items: stretch;
  [data-prefers-color='dark'] & {
    background-color: unset;
  }
  .side {
    flex: none;
  }
  .content {
    box-sizing: border-box;
    font-family: PingFang SC;
    width: 100%;
    :global {
      // .adm-list-body {
      //   border: none;
      .adm-form-item-label {
        font-size: 48px;
        font-weight: 500;
        letter-spacing: 0px;
        line-height: 69.999px;
        color: rgba(51, 51, 51, 1);
        margin-bottom: 39px;
      }
      .adm-text-area {
        box-sizing: border-box;
        padding: 21.999px 44.001px;
        border-radius: 20.001px;
        background: rgba(245, 245, 245, 1);
        // 提示文字改小点
        .adm-text-area-element::placeholder {
          font-size: 36px;
        }
      }
      .adm-list-default .adm-list-body {
        border: none;
      }
      .adm-selector .adm-space.adm-space {
        --gap: 15px;
      }
      .adm-selector-item {
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        width: 216px;
        height: 102px;
        font-size: 36px;
        font-weight: 400;
        letter-spacing: 0px;
      }
    }
    // }
  }
`;

const CheckListWrapper = styled(CheckList)`
  .adm-list-item-content-main {
    font-size: 3.8889vw;
    font-weight: 400;
    letter-spacing: 0vw;
    line-height: 5.6315vw;
    color: rgba(51, 51, 51, 1);
  }
`;

/**
 * 筛选侧边栏
 * @returns
 */
const FilterSideBar = ({ onSubmit, onReset }: any) => {
  const [activeTab, setActiveTab] = useState('1');
  const SIDEBAR_ITEM = [
    {
      key: '1',
      title: '搜索',
    },
    {
      key: '2',
      title: '类型',
    },
  ];
  return (
    <FilterWrapper>
      <div className="side">
        <SideBar activeKey={activeTab} onChange={(key: string) => setActiveTab(key)}>
          {SIDEBAR_ITEM.map((item) => (
            <SideBar.Item key={item.key} title={item.title} data-testid={`filter-sidebar-${item.title}`} />
          ))}
        </SideBar>
      </div>
      <div className="content" data-testid="filter-sidebar-content">
        <div
          style={{
            display: activeTab === '1' ? '' : 'none',
            overflowY: 'auto',
            height: '100%',
          }}
          data-testid="filter-sidebar-content-搜索"
        >
          <Form.Item name="workTask" label="工作任务" data-testid="filter-workTask">
            <TextArea placeholder="请输入" rows={2} />
          </Form.Item>
          <Form.Item name="human" label="人员" data-testid="filter-human">
            <TextArea placeholder="请输入" rows={2} />
          </Form.Item>
        </div>
        <div
          style={{
            display: activeTab === '2' ? '' : 'none',
            overflowY: 'auto',
            height: '100%',
          }}
        >
          <Form.Item name="ticketType" label="类型" noStyle data-testid="filter-ticketType">
            <CheckListWrapper
              style={{
                '--border-bottom': 'none',
                '--border-top': 'none',
                '--border-inner': 'none',
              }}
            >
              {Object.entries(TICKET_TYPE_MAP).map(([key, title], index) => (
                <CheckList.Item key={index} value={key}>
                  {title}
                </CheckList.Item>
              ))}
            </CheckListWrapper>
          </Form.Item>
        </div>
      </div>
    </FilterWrapper>
  );
};

/* 顶部按钮组件，负责状态选择和筛选按钮 */
const FilterPopupSearch = ({ filterValue, onFilter }: any) => {
  const formRef = useRef(null)
  const [filterPopupVisible, setFilterPopupVisible] = useState(false);
  return (
    <FilterPopup
      ref={formRef}
      iconRender={<FilterOutline color="#fff" fontSize={24} />}
      onSubmit={(values) => {
        onFilter(values);
      }}
      onVisible={(visible) => {
        setFilterPopupVisible(visible);
        if(visible){
          console.log(formRef.current)
          const { ticketType, ...rest } = filterValue;
          formRef.current?.form?.setFieldsValue?.({
            ticketType: ticketType ? [ticketType]:[],
            ...rest, 
          });
        }
      }}
    >
      {filterPopupVisible && <FilterSideBar />}
    </FilterPopup>
  );
};

export default FilterPopupSearch;
