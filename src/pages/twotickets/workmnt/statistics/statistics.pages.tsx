import { Suspense, useMemo, useState } from 'react';
import { Card, Grid, Skeleton, Space, Loading, ErrorBlock, List, Empty } from 'antd-mobile';

import TwoTicketsNavBar from '@/component/twotickets-navbar/twotickets-navbar.component';
import { FilterPopueComponent } from './components/filter-popue/filter-popue.component';
import styles from './statistics.pages.less';
import DropActionSheet from '../components/drop-action-sheet/drop-action-sheet.component';
import { TICKET_TYPE_MAP } from '@/constant';
import { useReactive } from 'ahooks';
import ImplementationStatus from '../../operation/statistics/components/implementation-status/implementation-status.components';
import StatisticalNumber from '../../operation/statistics/components/statistical-number/statistical-number.component';
import QualifiedProgress from '../../operation/statistics/components/statistical-number/qualified-progress.component';
import { CombinedData, TicketCountAll, TicketCountAllParams } from '../types/statistics';
import React from 'react';
import { averageExecutionAPI, getTicketAllAPI, getTicketAuditAPI, getTicketCountAllAPI } from '@/services/twotickets/workmnt/statistics';
import { useCombinedData } from '../hooks/useCombinedData';
import F2PlotColumnGroup from '@/component/f2Plot/column-group/column-group.component';
import { F2ColumnsComponent } from '@/component/f2Plot/columns-comonpent/columns-component';
import { RightOutline } from 'antd-mobile-icons';
import useInterleave from '@/hooks/useInterleave';
import GridList, { ColumnsProps } from '@/component/grid-list/grid-list.component';
import { styled } from 'umi';
import FilterPopupSearch from './components/filter-popue/filter-popup-search.component';
import dayjs from 'dayjs';

const STATISTICALNUMBERMAP = [
  {
    name: '发票总数',
    value: 'ticketCountAll',
  },
  {
    name: '已终结总数',
    value: 'endingTicketCount',
  },
  {
    name: '工作票已审核',
    value: 'audited',
  },
];

/**
 * 工作票统计分析
 * <AUTHOR> <<EMAIL>></<EMAIL>>
 * @date    2021-07-05
 * @version 1.0.0
 */
const WorkmntStatisticsPage = () => {
  const [filters, setFilters] = useState({
    type: '', // 工作票类型
    isEnding: '',
  });
  const [isOnlyReqStats, setIsOnlyReqStats] = useState(false);
  const params = useMemo(() => {
    // filters过滤，过滤去掉为空的数据
    const newFilters = Object.keys(filters).reduce((prev, cur) => {
      if (filters[cur] !== null && filters[cur] !== '') {
        prev[cur] = filters[cur];
      }

      if (isOnlyReqStats) {
        prev['fetcherIndexes'] = [2];
      }
      if (filters?.startDate) {
        prev['startTime'] = dayjs(filters?.startDate).format('YYYY-MM-DD HH:mm:ss');
      }
      if (filters?.endDate) {
        prev['endTime'] = dayjs(filters?.endDate).format('YYYY-MM-DD HH:mm:ss');
      }
      if (filters?.timeUnit) {
        prev['durationUnit'] = filters?.timeUnit;
      }
      return {
        ...prev,
        ticketType: 0,
      };
    }, {});
    delete newFilters?.startDate
    delete newFilters?.endDate
    delete newFilters?.timeUnit
    return newFilters;
  }, [filters]);
  const { data, loading, error } = useCombinedData<TicketCountAllParams, CombinedData>(
    {
      fetchers: [getTicketCountAllAPI, getTicketAuditAPI, getTicketAllAPI, averageExecutionAPI],
      onResponse: (res) => {
        if (isOnlyReqStats) {
          setIsOnlyReqStats(false);
        }
        return {
          ticketCount: res[0],
          audit: res[1].map((item: { unit: string; ydata: any }) => {
            return {
              ...item,
              ydata: item.unit !== '%' ? Number(item.ydata) : undefined,
              value: item.unit === '%' ? Number(item.ydata) : undefined,
            };
          }),
          ticketAll: res[2],
          averageExecution: Object.fromEntries(
            Object.entries(res[3]?.reduce((_all: any, item: any) => ({ ..._all, ...item }), {})).map(([key, value]: any) => [
              (key === '水工二种工作票(二)' ? '水工作业工作票(二)' : key).replace('工作票', ''),
              value,
            ]),
          ),
        };
      },
    },
    params,
  );

  const TABLE_COLUMNS: ColumnsProps[] = [
    {
      dataIndex: 'name',
      title: '工作票类型',
      align: 'left',
      span: 2,
    },
    {
      title: '已结票',
      dataIndex: 'closeTicketCount',
      align: 'center',
      span: 2,
    },
    {
      title: '未结票',
      dataIndex: 'unfinishedTicketCount',
      align: 'center',
      span: 2,
    },
    {
      title: '合计/结票率',
      dataIndex: 'amount',
      align: 'center',
      span: 2,
      render: (text, record, index) => (
        <>
          {record.amount}
          <span
            className={styles.amountRate}
            style={{
              color: record.rate > 60 ? 'rgba(1, 208, 102, 1)' : 'rgba(17, 84, 237, 1)',
            }}
          >
            /{Math.round(record.rate * 100) / 100}%
          </span>
        </>
      ),
    },
  ];

  if (loading) return <Skeleton.Paragraph />;

  return (
    <div className={styles.container} style={{ paddingTop: `${window.top?.localStorage?.topHeight ?? 0}px` }}>
      <TwoTicketsNavBar
        className={styles.statisticsNavbar}
        title={
          <DropActionSheet
            prefix="统计分析-"
            value={filters.type}
            options={[
              {
                text: '总览',
                key: '',
              },
              // 展开对象values
              ...Object.keys(TICKET_TYPE_MAP).map((key) => {
                return {
                  text: TICKET_TYPE_MAP[key],
                  key,
                };
              }),
            ]}
            onChange={function (key: string): void {
              setFilters({
                ...filters,
                type: key,
              });
            }}
            dropOptions={{
              title: '切换统计类型',
            }}
          />
        }
        right={
          <Space>
            <FilterPopueComponent
              filterValue={filters}
              onFilter={(values) => {
                setFilters({ ...filters, ...values });
              }}
            />
            {/* <FilterPopupSearch
              filterValue={filters}
              onFilter={(values: { [x: string]: any; isCustom: any; ticketType: any; }) => {
                const { isCustom, ticketType, ...rest } =  values;
                setFilters({ ...filters, ...rest, ticketType: ticketType?.[0] || '0'});
              }}
            /> */}
          </Space>
        }
      />
      <Suspense fallback={<Skeleton.Paragraph />}>
        <div className={styles.body}>
          <Card
            title="工作票统计"
            className={styles.card}
            style={{
              marginTop: '5.4631vw',
            }}
          >
            {/* 展示统计数字 */}
            <Grid columns={3} gap={8}>
              {STATISTICALNUMBERMAP.map((item, index) => (
                <Grid.Item key={'statistica_number' + index} style={{ textAlign: 'center' }}>
                  <StatisticalNumber num={data?.ticketCount?.[item.value]} title={item.name} />
                </Grid.Item>
              ))}
            </Grid>
            {/* 展示审核合格率进度条 */}
            <QualifiedProgress num={data?.ticketCount?.passRate} />
          </Card>
          {!filters?.type && (
            <Card title="工作票平均执行时间统计" className={styles.card}>
              <Grid columns={2} gap={[8, 12]}>
                {data?.averageExecution &&
                  Object.entries(data?.averageExecution || {})
                    .sort(([a], [b]) => {
                      // 根据指定包含字母排序
                      const AVERAGE_EXECUTION_ORDER = ['电气', '机械', '水工', '动火'];
                      const getOrderIndex = (name: string) => {
                        for (let i = 0; i < AVERAGE_EXECUTION_ORDER.length; i++) {
                          if (name.includes(AVERAGE_EXECUTION_ORDER[i])) return i;
                        }
                        return AVERAGE_EXECUTION_ORDER.length;
                      };
                      return getOrderIndex(a) - getOrderIndex(b);
                    })
                    .map(([key, value], index: number) => (
                      <Grid.Item key={key}>
                        <StatisticalNumber num={value} title={key.replace('工作票', '')} unit="小时" position="top" />
                      </Grid.Item>
                    ))}
              </Grid>
            </Card>
          )}
          <Card title="工作票审核统计" className={styles.card}>
            {/* 操作人员执行情况统计排行 */}
            {/* <ImplementationStatus filterValue={filters} /> */}
            <div
              style={{
                height: '61.6667vw',
              }}
            >
              {data?.audit?.length ? (
                <F2PlotColumnGroup
                  data={data?.audit}
                  items={[
                    { color: '#4A61F3', name: '已审核' },
                    { color: '#F8BC05', name: '合格' },
                    { color: 'rgba(1, 208, 102, 1)', name: '合格率', type: 'line' },
                  ]}
                />
              ) : (
                <Empty description="暂无数据" />
              )}
            </div>
          </Card>
          <Card title="工作票结票统计" className={styles.card}>
            {data?.ticketAll?.figures?.filter((item) => ['已结票', '未结票', '结票率', '已终结', '未终结', '终结率']?.includes(item.name))
              ?.length ? (
              <F2PlotColumnGroup
                type="stack"
                color={['rgba(73, 97, 243, 1)', 'rgba(248, 188, 5, 1)']}
                items={[
                  { color: '#4A61F3', name: '已结票' },
                  { color: '#F8BC05', name: '未结票' },
                  { color: 'rgba(1, 208, 102, 1)', name: '结票率', type: 'line' },
                ]}
                data={
                  data?.ticketAll?.figures
                    ?.filter((item) => ['已结票', '未结票', '结票率', '已终结', '未终结', '终结率']?.includes(item.name))
                    ?.map((item) => {
                      if (item.name === '已终结' || item.name === '已结票') {
                        return {
                          name: '已结票',
                          xdata: item.xdata,
                          unit: '个',
                          ydata: item?.ydata ? Number(item.ydata) : 0,
                        };
                      } else if (item.name === '未终结' || item.name === '未结票') {
                        return {
                          name: '未结票',
                          xdata: item.xdata,
                          unit: '个',
                          ydata: item?.ydata ? Number(item.ydata) : 0,
                        };
                      } else {
                        return {
                          name: '结票率',
                          xdata: item.xdata,
                          ydata: null,
                          unit: '%',
                          value: item?.ydata ? Number(item.ydata) : 0,
                        };
                      }
                      // return {
                      //   ...item,
                      //   ydata: Number(item.ydata),
                      // };
                    }) || []
                }
              />
            ) : (
              <Empty description="暂无数据" />
            )}
            {!filters?.type && (
              <div
                className={styles.table_card}
                style={{
                  '--adm-color-box': '#fff',
                }}
              >
                <GridList
                  columns={TABLE_COLUMNS}
                  dataSource={Object.entries(data?.averageExecution || {})
                    .sort(([a], [b]) => {
                      // 根据指定包含字母排序
                      const AVERAGE_EXECUTION_ORDER = ['电气', '机械', '水工', '动火'];
                      const getOrderIndex = (name: string) => {
                        for (let i = 0; i < AVERAGE_EXECUTION_ORDER.length; i++) {
                          if (name.includes(AVERAGE_EXECUTION_ORDER[i])) return i;
                        }
                        return AVERAGE_EXECUTION_ORDER.length;
                      };
                      return getOrderIndex(a) - getOrderIndex(b);
                    })
                    .map(([item], index) => {
                      return {
                        name: item,
                        closeTicketCount:
                          data?.ticketAll?.figures?.find(
                            (figure) => figure.xdata.replace('工作票', '') === item && figure.name === '已终结',
                          )?.ydata || 0,
                        unfinishedTicketCount:
                          data?.ticketAll?.figures?.find(
                            (figure) => figure.xdata.replace('工作票', '') === item && figure.name === '未终结',
                          )?.ydata || 0,
                        amount:
                          data?.ticketAll?.figures?.find((figure) => figure.xdata.replace('工作票', '') === item && figure.name === '全部')
                            ?.ydata || 0,
                        rate:
                          data?.ticketAll?.figures?.find(
                            (figure) => figure.xdata.replace('工作票', '') === item && figure.name === '终结率',
                          )?.ydata || 0,
                      };
                    })}
                  rowKey={'index'}
                  gridConfig={{
                    columns: 8,
                    gap: 0,
                  }}
                />
              </div>
            )}
          </Card>
          <div className={styles.bottom}>已经到底了</div>
        </div>
      </Suspense>
    </div>
  );
};

export default WorkmntStatisticsPage;
