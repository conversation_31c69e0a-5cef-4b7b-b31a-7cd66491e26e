.container{
  font-family: PingFang SC;
  width: 100%;
  height: 100vh;
  padding: 9.999px 36px 0;
  border: 0;
  background: linear-gradient(0deg, #F5F5F5 0%, #1154ED 50%, #1154ED 100%);
  background-size: 100% 270px;
  background-repeat: no-repeat;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  .statisticsNavbar{
    :global {
      .adm-nav-bar-left, .adm-nav-bar-right{
        flex: none;
      }
    }
  }
  .body{
    flex: 1;
    overflow-y: auto;
    .card{
      margin-top: 24px;
      :global{
        .drop-action-sheet{
          // width: 210px;
          height: 72px;
          opacity: 1;
          border-radius: 72px;
          background: rgba(245, 245, 245, 1);
          font-size: 36px;
          font-weight: 400;
          letter-spacing: 0px;
          line-height: 72px;
          color: rgba(51, 51, 51, 1);
          padding: 9px 24px 9px 54px;
        }
      }
    }
    .table_card{
      margin-top: 4.4444vw;
      :global{
        [class^="listHeader"]{
          > div{
            line-height: 9.25926vw;
            border-radius: .9259vw;
            background: rgba(245, 245, 245, 1);
          }
        }
        .amountRate{
          font-size: 3.8889vw;
          font-weight: 400;
          letter-spacing: 0vw;
          line-height: 5.6315vw;
          color: rgba(1, 208, 102, 1);
        }
      }
    }
    .bottom{
      height: 144px;
      font-size: 39.999px;
      font-weight: 400;
      letter-spacing: -1.599px;
      line-height: 144px;
      color: rgba(153, 153, 153, 1);
      text-align: center;
    }
  }
}