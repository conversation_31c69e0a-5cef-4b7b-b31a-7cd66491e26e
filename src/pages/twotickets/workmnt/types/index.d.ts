/**
* WorkBaseVo
*/
export interface WorkBaseVo {
  /**
   * 实际结束时间
   */
  actualEndTime?: string;
  /**
   * 许可人名称
   */
  allowName?: string;
  /**
   * 许可时间
   */
  allowTime?: string;
  /**
   * 附件id
   */
  attachId?: number;
  /**
   * 审核结果（和审核结果表保持一致）
   */
  auditResult?: string;
  /**
   * 流程编码
   */
  businessType?: string;
  /**
   * 监测表
   */
  checkList?: WorkFireCheck[];
  /**
   * 创建人名称
   */
  createName?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 创建人id
   */
  createUser?: number;
  /**
   * 延期次数 （不能大于1）
   */
  delayNum?: number;
  /**
   * 设备id
   */
  deviceId?: number;
  /**
   * kks编码
   */
  deviceKks?: string;
  /**
   * 设备名称
   */
  deviceName?: string;
  /**
   * 值班负责人
   */
  dutyName?: string;
  /**
   * 机电设备监护人id
   */
  electromechanicalUserId?: number;
  /**
   * 机电设备监护人名称
   */
  electromechanicalUserName?: string;
  /**
   * 执行状态
   */
  executeStatus?: string;
  /**
   * 附件
   */
  files?: AttachmentContent[];
  /**
   * 终结工作许可人
   */
  finalAllowUserName?: string;
  /**
   * 终结时间
   */
  finalTime?: string;
  /**
   * 动火执行人id
   */
  fireExecuteId?: number;
  /**
   * 动火执行人姓名
   */
  fireExecuteName?: string;
  /**
   * 动火方式
   */
  fireMethod?: string;
  formKey?:    string;
  /**
   * 处理过的用户集合(eg: 11,22,33)
   */
  handleUserIds?: string;
  /**
   * 负责人id
   */
  headId?: number;
  /**
   * 工作负责人
   */
  headName?: string;
  /**
   * 工作负责人用户名
   */
  headUserName?: string;
  /**
   * id
   */
  id?: number;
  /**
   * 图片id
   */
  imgId?: number;
  /**
   * 图片
   */
  imgList?: AttachmentContent[];
  /**
   * 初始负责人id
   */
  initialHeadId?: number;
  /**
   * 是否可发起变更申请
   */
  isChanged?: number;
  isCurTask?: boolean;
  /**
   * 是否终结(1-是)
   */
  isEnd?: number;
  /**
   * 是否超期
   */
  isExpired?: number;
  /**
   * 监测人/负责人判断
   */
  isFireCheck?: null;
  /**
   * 是否经过外委
   */
  isPass?: string;
  /**
   * 是否为标准工作票(1-是,0-否)
   */
  isStandardTicket?: number;
  /**
   * 操作证编号
   */
  licenseNumber?: string;
  /**
   * 检修应采取的安全措施
   */
  maintenanceMeasures?: string;
  /**
   * 工作成员（手填，可选班组外）
   */
  member?: string;
  /**
   * memo
   */
  memo?: string;
  /**
   * 流程模型id
   */
  modelId?: string;
  /**
   * 序号
   */
  num?: number;
  /**
   * 运行应采取的安全措施
   */
  operationalMeasures?: string;
  /**
   * 组织id
   */
  orgId?: number;
  /**
   * 组织名称
   */
  orgName?: string;
  /**
   * 业主签发人名称
   */
  ownerSignerName?: string;
  /**
   * 业主签发时间
   */
  ownerSignerTime?: string;
  /**
   * 人数
   */
  peopleNumber?: number;
  /**
   * 计划开始时间
   */
  planBeginTime?: string;
  /**
   * 计划结束时间
   */
  planEndTime?: string;
  /**
   * 注意事项
   */
  precaution?: string;
  /**
   * 流程状态
   */
  processInstanceId?: string;
  /**
   * 附属票关联列表
   */
  relationList?: WorkBase[];
  /**
   * 备注
   */
  remark?: string;
  /**
   * 审查结果
   */
  result?: string;
  /**
   * 收工时间
   */
  returnTime?: string;
  /**
   * 作业风险管控内容(机械)
   */
  riskControl?: string;
  /**
   * 机组信息
   */
  setInfo?: string;
  /**
   * 签发人名称
   */
  signerName?: string;
  /**
   * 签发时间
   */
  signTime?: string;
  /**
   * 安全措施票工作条件
   */
  smWorkCondition?: string;
  /**
   * 状态
   */
  status?: string;
  /**
   * 填票时间
   */
  submitTime?: string;
  /**
   * 系统编码(暂时没用)
   */
  systemNum?:    string;
  taskDefKey?:   string;
  taskId?:       string;
  taskName?:     string;
  taskUserName?: string;
  /**
   * 班组/作业小组id
   */
  teamsId?: string;
  /**
   * 班组/作业小组名称
   */
  teamsName?: string;
  /**
   * 被测试设备及保护名称
   */
  testDevice?: string;
  /**
   * 工作票编号
   */
  ticketCode?: string;
  /**
   * 工作票类型
   */
  ticketType?: string;
  /**
   * 单位id
   */
  unitId?: number;
  /**
   * 单位名称
   */
  unitName?: string;
  /**
   * 安全风险控制卡
   */
  workBaseJobType?: WorkBaseJobType;
  /**
   * 工作票风险预控
   */
  workBaseRisks?: WorkBaseRisk[];
  /**
   * 安全分析单步骤
   */
  workBaseSafeSteps?: WorkBaseSafeStep[];
  /**
   * 安全工器具
   */
  workBaseTools?: WorkBaseTool[];
  /**
   * 监测结果
   */
  workCheckResult?: WorkCheckResult;
  /**
   * 工作条件
   */
  workCondition?: string;
  /**
   * 工作票安全措施
   */
  workElectricalSafeMeasure?: WorkElectricalSafeMeasure;
  /**
   * 工作票接收确认
   */
  workFireAcceptConfirm?: WorkFireAcceptConfirm;
  /**
   * 安全领导审核
   */
  workFireDepartment?: WorkFireDepartment;
  /**
   * 动火部门负责人审核
   */
  workFireDepartmentHead?: WorkFireDepartmentHead;
  /**
   * 动火执行人信息
   */
  workFireExecuteList?: null;
  /**
   * 消防审核
   */
  workFireFightingHead?: WorkFireFightingHead;
  /**
   * 动火终结确认表
   */
  workFireFinalConfirmList?: WorkFireFinalConfirm[];
  /**
   * 总工程师确认动火时间
   */
  workFireFirstResponsiblePerson?: WorkFireFirstResponsiblePerson;
  /**
   * 安监主管审核
   */
  workFireSafeManager?: WorkFireSafeManager;
  /**
   * 安监审核
   */
  workFireSafetySupervision?: WorkFireSafetySupervision;
  /**
   * 动火签字确认表
   */
  workFireSignConfirmList?: WorkFireSignConfirm[];
  workFireUpdateDelete?:    WorkFireUpdateDelete;
  /**
   * 处理流程记录
   */
  workflowApprovalRecordList?: WorkflowApprovalRecord[];
  /**
   * 工作票审核表
   */
  workFlowAudit?: WorkFlowAudit;
  /**
   * 工作票确认
   */
  workFlowConfirm?: WorkFlowConfirm;
  /**
   * 工作票中终结表
   */
  workFlowEnd?: WorkFlowEnd;
  /**
   * 动火填报
   */
  workFlowExecute?: WorkFlowExecute;
  /**
   * 工作票执行确认
   */
  workFlowExecuteConfirm?: WorkFlowExecuteConfirm;
  /**
   * 工作票许可表
   */
  workFlowLicense?: WorkFlowLicense;
  /**
   * 工作票值长批准表
   */
  workFlowRatify?: WorkFlowRatify;
  /**
   * 工作票签发
   */
  workFlowSign?: WorkFlowSign;
  /**
   * 流程id
   */
  workflowState?:     number;
  workFlowStateName?: string;
  /**
   * 水工工作票安全措施
   */
  workIrrigationSafeMeasure?: WorkIrrigationSafeMeasure;
  /**
   * 工作地点
   */
  workLocation?: string;
  /**
   * 工作编号（序号）
   */
  workNum?: number;
  /**
   * 安全措施票(电气/水工)
   */
  workSafeMeasureTickets?: WorkSafeMeasureTicket[];
  /**
   * 工作任务
   */
  workTask?: string;
  /**
   * 工作票执行相关信息
   */
  workTicketExecute?: WorkTicketExecute;
  [property: string]: any;
}

/**
* 动火检测表
*
* WorkFireCheck
*/
export interface WorkFireCheck {
  /**
   * 检测项目
   */
  checkItem?: string;
  /**
   * 检测结果
   */
  checkResult?: string;
  /**
   * 检测时间
   */
  checkTime?: string;
  /**
   * 检测人
   */
  checkUserId?: number;
  /**
   * 检测人姓名
   */
  checkUserName?: string;
  /**
   * id
   */
  id?: number;
  /**
   * main_id
   */
  mainId?: number;
  /**
   * 主流程id
   */
  mainProcessInstanceId?: string;
  /**
   * memo
   */
  memo?: string;
  /**
   * 备注
   */
  remark?: string;
  [property: string]: any;
}

/**
* com.nwh.framework.attachment.entity.AttachmentContent
*
* AttachmentContent
*/
export interface AttachmentContent {
  businesstype?:       string;
  endTime?:            string;
  format?:             string;
  genre?:              string;
  id?:                 number;
  ids?:                number[];
  length?:             number;
  memo?:               string;
  name?:               string;
  startTime?:          string;
  thumbnail?:          string;
  type?:               string;
  uploadDate?:         string;
  uploadUserName?:     string;
  uploadUserRealName?: string;
  url?:                string;
  userId?:             number;
  [property: string]: any;
}

/**
* 工作票基础字段
*
* WorkBase
*/
export interface WorkBase {
  /**
   * 实际结束时间
   */
  actualEndTime?: string;
  attachId?:      number;
  auditResult?:   string;
  /**
   * 流程编码
   */
  businessType?:              string;
  createName?:                string;
  createTime?:                string;
  createUser?:                number;
  delayNum?:                  number;
  deviceId?:                  number;
  deviceKks?:                 string;
  deviceName?:                string;
  electromechanicalUserId?:   number;
  electromechanicalUserName?: string;
  /**
   * 动火执行人id
   */
  fireExecuteId?: number;
  /**
   * 动火执行人姓名
   */
  fireExecuteName?: string;
  /**
   * 动火方式
   */
  fireMethod?: string;
  formKey?:    string;
  /**
   * 处理过的用户集合(eg: 11,22,33)
   */
  handleUserIds?: string;
  headId?:        number;
  headName?:      string;
  /**
   * 工作负责人用户名
   */
  headUserName?: string;
  id?:           number;
  imgId?:        number;
  /**
   * 初始负责人id
   */
  initialHeadId?: number;
  isCurTask?:     boolean;
  /**
   * 是否终结(1-是)
   */
  isEnd?: number;
  /**
   * 是否超期
   */
  isExpired?: number;
  /**
   * 是否为标准工作票(1-是,0-否)
   */
  isStandardTicket?: number;
  /**
   * 操作证编号
   */
  licenseNumber?: string;
  /**
   * 检修应采取的安全措施
   */
  maintenanceMeasures?: string;
  member?:              string;
  memo?:                string;
  num?:                 number;
  /**
   * 运行应采取的安全措施
   */
  operationalMeasures?: string;
  orgId?:               number;
  orgName?:             string;
  peopleNumber?:        number;
  planBeginTime?:       string;
  planEndTime?:         string;
  precaution?:          string;
  processInstanceId?:   string;
  remark?:              string;
  /**
   * 作业风险管控内容(机械)/(电气风险遗留问题)
   */
  riskControl?: string;
  setInfo?:     string;
  /**
   * 安全措施票工作条件
   */
  smWorkCondition?: string;
  status?:          string;
  submitTime?:      string;
  systemNum?:       string;
  taskDefKey?:      string;
  taskId?:          string;
  taskName?:        string;
  taskUserName?:    string;
  teamsId?:         string;
  teamsName?:       string;
  /**
   * 被测试设备及保护名称
   */
  testDevice?:        string;
  ticketCode?:        string;
  ticketType?:        string;
  unitId?:            number;
  unitName?:          string;
  workCondition?:     string;
  workflowState?:     number;
  workFlowStateName?: string;
  workLocation?:      string;
  workNum?:           number;
  workTask?:          string;
  [property: string]: any;
}

/**
* 安全风险控制卡
*
* WorkBaseJobType
*/
export interface WorkBaseJobType {
  /**
   * id
   */
  id?: number;
  /**
   * 作业风险
   */
  jobRisk?: string;
  /**
   * 作业类别
   */
  jobType?: string;
  /**
   * main_id
   */
  mainId?: number;
  /**
   * memo
   */
  memo?: string;
  /**
   * 其他
   */
  other?: string;
  /**
   * 其他风险
   */
  otherRisk?: string;
  /**
   * 备注
   */
  remark?: string;
  [property: string]: any;
}

/**
* 工作票风险预控
*
* WorkBaseRisk
*/
export interface WorkBaseRisk {
  /**
   * 管控措施
   */
  controlMeasure?: string;
  /**
   * 执行情况
   */
  execute?: string;
  /**
   * 执行人id
   */
  executeId?: number;
  /**
   * 执行人姓名
   */
  executeName?: string;
  /**
   * 执行时间
   */
  executeTime?: string;
  /**
   * id
   */
  id?: number;
  /**
   * main_id
   */
  mainId?: number;
  /**
   * memo
   */
  memo?: string;
  /**
   * 序号
   */
  num?: number;
  /**
   * 真实序号
   */
  orderNum?: number;
  /**
   * 作业过程
   */
  process?: string;
  /**
   * 风险描述
   */
  riskDescription?: string;
  /**
   * 风险等级
   */
  riskLevel?: string;
  [property: string]: any;
}

/**
* 安全分析单步骤
*
* WorkBaseSafeStep
*/
export interface WorkBaseSafeStep {
  /**
   * 作业步骤
   */
  homeworkSteps?: string;
  /**
   * id
   */
  id?: number;
  /**
   * main_id
   */
  mainId?: number;
  /**
   * memo
   */
  memo?: string;
  /**
   * 序号
   */
  num?: number;
  /**
   * 真实序号
   */
  orderNum?: number;
  /**
   * 风险描述或分析
   */
  risk?: string;
  /**
   * 风险与遗留问题
   */
  riskProblem?: string;
  /**
   * 具体控制措施
   */
  specificMeasure?: string;
  /**
   * 是否更新项目
   */
  updateState?: number;
  /**
   * 验证人
   */
  verifierId?: number;
  /**
   * 验证人名称
   */
  verifierName?: string;
  [property: string]: any;
}

/**
* 工作票工器具
*
* WorkBaseTool
*/
export interface WorkBaseTool {
  /**
   * 器具柜
   */
  equipmentCabinet?:     string;
  equipmentCabinetName?: string;
  /**
   * 器具室
   */
  equipmentRoom?:     string;
  equipmentRoomName?: string;
  /**
   * id
   */
  id?: number;
  /**
   * 种类
   */
  kind?: string;
  /**
   * 主单据id
   */
  mainId?: number;
  /**
   * memo
   */
  memo?: string;
  /**
   * 规格型号
   */
  model?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 责任专业
   */
  responsibility?:     string;
  responsibilityName?: string;
  /**
   * 编码
   */
  toolCode?: string;
  /**
   * 器具id
   */
  toolId?: number;
  /**
   * 名称
   */
  toolName?: string;
  /**
   * 类型
   */
  type?:     string;
  typeName?: string;
  [property: string]: any;
}

/**
* 监测结果
*
* WorkCheckResult
*/
export interface WorkCheckResult {
  /**
   * 检测结果
   */
  checkResult?: null;
  /**
   * 检测时间
   */
  checkTime?: null;
  /**
   * 检测人
   */
  checkUserId?: null;
  /**
   * 检测人姓名
   */
  checkUserName?: null;
  /**
   * id
   */
  id?: null;
  /**
   * main_id
   */
  mainId?: null;
  /**
   * 主流程id
   */
  mainProcessInstanceId?: null;
  /**
   * memo
   */
  memo?: null;
  [property: string]: any;
}

/**
* 工作票安全措施
*
* WorkElectricalSafeMeasure
*/
export interface WorkElectricalSafeMeasure {
  /**
   * 补充措施
   */
  additionalMeasure?: string;
  /**
   * 附件id
   */
  attachId?: number;
  /**
   * 附件内容
   */
  attachmentContents?: AttachmentContent[];
  /**
   * 动火部门安全措施
   */
  fireMeasure?: string;
  /**
   * id
   */
  id?: number;
  /**
   * main_id
   */
  mainId?: number;
  /**
   * 注意事项
   */
  matter?: string;
  /**
   * memo
   */
  memo?: string;
  /**
   * 运行部门安全措施
   */
  runMeasure?: string;
  /**
   * 应设遮拦
   */
  shouldBlock?: string;
  /**
   * 应设遮拦执行情况
   */
  shouldBlockExecute?: string;
  /**
   * 应设遮拦执行人
   */
  shouldBlockUser?: string;
  /**
   * 应接地线
   */
  shouldConnect?: string;
  /**
   * 应断地线执行情况
   */
  shouldConnectExecute?: string;
  /**
   * 应接地线执行人
   */
  shouldConnectUser?: string;
  /**
   * 应作措施
   */
  shouldMeasure?: string;
  /**
   * 应断开关
   */
  shouldOff?: string;
  /**
   * 应断开关执行情况
   */
  shouldOffExecute?: string;
  /**
   * 应断开关执行人
   */
  shouldOffUser?: string;
  [property: string]: any;
}

/**
* 工作票接收确认
*
* WorkFireAcceptConfirm
*/
export interface WorkFireAcceptConfirm {
  /**
   * 审核接收时间
   */
  acceptTime?: null;
  /**
   * 确认人id
   */
  confirmHeadId?: null;
  /**
   * 负责人确认姓名
   */
  confirmHeadName?: null;
  /**
   * 确认时间
   */
  confirmTime?: null;
  /**
   * id
   */
  id?: null;
  /**
   * 许可人id
   */
  licenseId?: null;
  /**
   * 许可人姓名
   */
  licenseName?: null;
  /**
   * main_id
   */
  mainId?: null;
  /**
   * 主流程id
   */
  mainProcessInstanceId?: null;
  /**
   * memo
   */
  memo?: null;
  /**
   * 审核意见
   */
  opinion?: null;
  /**
   * 值长id
   */
  ratifyId?: null;
  /**
   * 值长姓名
   */
  ratifyName?: null;
  [property: string]: any;
}

/**
* 安全领导审核
*
* WorkFireDepartment
*/
export interface WorkFireDepartment {
  /**
   * 测定结果
   */
  checkResult?: string;
  /**
   * 执行人允许时间
   */
  executeTime?: string;
  /**
   * 允许动火开始时间
   */
  fireBeginTime?: string;
  /**
   * 允许动火结束时间
   */
  fireEndTime?: string;
  /**
   * 动火执行人id
   */
  fireExecuteId?: number;
  /**
   * 动火执行人名称
   */
  fireExecuteName?: string;
  /**
   * 消防监护人id
   */
  fireFightingId?: number;
  /**
   * 消防监护人名称
   */
  fireFightingName?: string;
  /**
   * 消防时间
   */
  fireFightingTime?: string;
  /**
   * 动火负责人id
   */
  fireHeadId?: number;
  /**
   * 动火负责人名称
   */
  fireHeadName?: string;
  /**
   * 负责人时间
   */
  headTime?: string;
  /**
   * id
   */
  id?: number;
  /**
   * main_id
   */
  mainId?: number;
  /**
   * 主流程id
   */
  mainProcessInstanceId?: string;
  /**
   * memo
   */
  memo?: null;
  /**
   * 动火安监人id
   */
  safetySupervisionId?: number;
  /**
   * 动火安监人名称
   */
  safetySupervisionName?: string;
  /**
   * 安监时间
   */
  safetySupervisionTime?: null;
  [property: string]: any;
}

/**
* 动火部门负责人审核
*
* WorkFireDepartmentHead
*/
export interface WorkFireDepartmentHead {
  /**
   * 审核时间
   */
  approveTime?: null;
  /**
   * 批准开始时间
   */
  fireBeginTime?: null;
  /**
   * 批准结束时间
   */
  fireEndTime?: null;
  /**
   * 动火负责人id
   */
  fireHeadId?: null;
  /**
   * 动火负责人名称
   */
  fireHeadNane?: null;
  /**
   * id
   */
  id?: null;
  /**
   * main_id
   */
  mainId?: null;
  /**
   * 主流程id
   */
  mainProcessInstanceId?: null;
  /**
   * memo
   */
  memo?: null;
  [property: string]: any;
}

/**
* 消防审核
*
* WorkFireFightingHead
*/
export interface WorkFireFightingHead {
  /**
   * 安全负责人id
   */
  fireFightingHeadId?: number;
  /**
   * 安全负责人姓名
   */
  fireFightingHeadName?: string;
  /**
   * id
   */
  id?: number;
  /**
   * main_id
   */
  mainId?: number;
  /**
   * 主流程id
   */
  mainProcessInstanceId?: string;
  /**
   * memo
   */
  memo?: string;
  /**
   * 审核意见
   */
  opinion?: string;
  /**
   * 审核时间
   */
  reviewTime?: string;
  [property: string]: any;
}

/**
* com.nwh.ticket.entity.workTicket.WorkFireFinalConfirm
*
* WorkFireFinalConfirm
*/
export interface WorkFireFinalConfirm {
  /**
   * id
   */
  id?: number;
  /**
   * main_id
   */
  mainId?: number;
  /**
   * 流程id
   */
  mainProcessInstanceId?: string;
  /**
   * memo
   */
  memo1?: string;
  memo2?: string;
  memo3?: string;
  /**
   * 签字人id
   */
  signId?: number;
  /**
   * 签字人姓名
   */
  signName?: string;
  /**
   * 签字时间
   */
  signTime?: string;
  /**
   * 提交时间
   */
  submitTime?: string;
  /**
   * 类型
   */
  type?: number;
  /**
   * 签字人员类型名称
   */
  typeName?: string;
  [property: string]: any;
}

/**
* 总工程师确认动火时间
*
* WorkFireFirstResponsiblePerson
*/
export interface WorkFireFirstResponsiblePerson {
  /**
   * 许可开始工作时间
   */
  allowBeginTime?: string;
  /**
   * 批准结束工作时间
   */
  allowEndTime?: string;
  /**
   * id
   */
  id?: number;
  /**
   * 领导id
   */
  leaderId?: number;
  /**
   * 领导名称
   */
  leaderName?: string;
  /**
   * main_id
   */
  mainId?: number;
  /**
   * 主流程id
   */
  mainProcessInstanceId?: string;
  /**
   * memo
   */
  memo?: string;
  /**
   * 意见
   */
  opinion?: string;
  /**
   * 审核时间
   */
  reviewTime?: string;
  [property: string]: any;
}

/**
* 安监主管审核
*
* WorkFireSafeManager
*/
export interface WorkFireSafeManager {
  /**
   * id
   */
  id?: number;
  /**
   * main_id
   */
  mainId?: number;
  /**
   * 主流程id
   */
  mainProcessInstanceId?: string;
  /**
   * 主管id
   */
  managerId?: number;
  /**
   * 主管姓名
   */
  managerName?: string;
  /**
   * memo
   */
  memo?: string;
  /**
   * 审核意见
   */
  opinion?: string;
  /**
   * 审核时间
   */
  reviewTime?: string;
  [property: string]: any;
}

/**
* 安监审核
*
* WorkFireSafetySupervision
*/
export interface WorkFireSafetySupervision {
  /**
   * 审核时间
   */
  examineTime?: string;
  /**
   * id
   */
  id?: number;
  /**
   * main_id
   */
  mainId?: number;
  /**
   * 主流程id
   */
  mainProcessInstanceId?: string;
  /**
   * memo
   */
  memo?: string;
  /**
   * 安监人员id
   */
  safetySupervisionId?: number;
  /**
   * 安监人员姓名
   */
  safetySupervisionName?: string;
  [property: string]: any;
}

/**
* com.nwh.ticket.entity.workTicket.WorkFireSignConfirm
*
* WorkFireSignConfirm
*/
export interface WorkFireSignConfirm {
  /**
   * id
   */
  id?: number;
  /**
   * main_id
   */
  mainId?: number;
  /**
   * 流程id
   */
  mainProcessInstanceId?: string;
  /**
   * memo
   */
  memo1?: string;
  memo2?: string;
  memo3?: string;
  /**
   * 签字人id
   */
  signId?: number;
  /**
   * 签字人姓名
   */
  signName?: string;
  /**
   * 签字时间
   */
  signTime?: string;
  /**
   * 提交时间
   */
  submitTime?: string;
  /**
   * 类型
   */
  type?: number;
  /**
   * 签字人员类型名称
   */
  typeName?: string;
  [property: string]: any;
}

/**
* WorkFireUpdateDelete
*/
export interface WorkFireUpdateDelete {
  canDelete?: number;
  canUpdate?: number;
  [property: string]: any;
}

/**
* 工作票审核表
*
* WorkFlowAudit
*/
export interface WorkFlowAudit {
  /**
   * 附件
   */
  attachId?: null;
  /**
   * 审核人id
   */
  auditId?: null;
  /**
   * 审核人姓名
   */
  auditName?: null;
  /**
   * 审核意见
   */
  auditOpinion?: null;
  /**
   * 审核结论(主表审核结果)
   */
  auditResult?: null;
  /**
   * 审核日期
   */
  auditTime?: null;
  /**
   * id
   */
  id?: null;
  /**
   * main_id
   */
  mainId?: null;
  /**
   * 流程id
   */
  mainProcessInstanceId?: null;
  /**
   * memo
   */
  memo?: null;
  [property: string]: any;
}

/**
* 工作票确认
*
* WorkFlowConfirm
*/
export interface WorkFlowConfirm {
  /**
   * 接受时间
   */
  accessTime?: string;
  /**
   * 运行值班负责人id
   */
  dutyId?: number;
  /**
   * 运行值班负责人姓名
   */
  dutyName?: string;
  /**
   * 负责人id
   */
  headId?: number;
  /**
   * 负责人姓名
   */
  headName?: string;
  /**
   * id
   */
  id?: number;
  /**
   * main_id
   */
  mainId?: number;
  /**
   * 流程id
   */
  mainProcessInstanceId?: string;
  /**
   * memo
   */
  memo?: string;
  /**
   * 提交时间
   */
  submitTime?: string;
  [property: string]: any;
}

/**
* 工作票中终结表
*
* WorkFlowEnd
*/
export interface WorkFlowEnd {
  /**
   * 工作许可人id
   */
  allowId?: number;
  /**
   * 工作许可人姓名
   */
  allowName?: string;
  /**
   * 工作许可人提交时间
   */
  allowTime?: string;
  /**
   * 指定监护人id
   */
  appointHeadId?: number;
  /**
   * 指定监护人姓名
   */
  appointHeadName?: string;
  /**
   * 其他事项
   */
  appointWork?: string;
  /**
   * 附件id
   */
  attachId?: number;
  /**
   * 终结说明
   */
  endDescribe?: string;
  /**
   * 工作结票时间
   */
  endTime?: string;
  /**
   * 执行时间
   */
  executeTime?: string;
  /**
   * 动火执行人姓名
   */
  fireExecuteName?: string;
  /**
   * 动火执行人id
   */
  fireExecuteUd?: number;
  /**
   * 消防监护人id
   */
  fireFightingId?: number;
  /**
   * 消防监护人姓名
   */
  fireFightingName?: string;
  /**
   * 消防时间
   */
  fireFightingTime?: string;
  /**
   * 未接地线编号
   */
  groundWireCode?: string;
  /**
   * 未拆除地线数
   */
  groundWireNum?: string;
  /**
   * 工作负责人id
   */
  headId?: number;
  /**
   * 工作负责人姓名
   */
  headName?: string;
  /**
   * 工作负责人提交时间
   */
  headTime?: string;
  /**
   * id
   */
  id?: number;
  /**
   * 接地刀闸
   */
  knifeSwitch?: string;
  /**
   * 接地刀闸数
   */
  knifeSwitchNum?: string;
  /**
   * main_id
   */
  mainId?: number;
  /**
   * 流程id
   */
  mainProcessInstanceId?: string;
  /**
   * memo
   */
  memo?: string;
  /**
   * 其他
   */
  other?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 提交时间
   */
  submitTime?: string;
  /**
   * 地点及具体工作事项
   */
  workMatter?: string;
  [property: string]: any;
}

/**
* 动火填报
*
* WorkFlowExecute
*
* 工作票执行表
*/
export interface WorkFlowExecute {
  /**
   * 附件id
   */
  attachId?: number;
  /**
   * 是否可运行 1-可 0-不可
   */
  canRun?: number;
  /**
   * id
   */
  id?: number;
  /**
   * 主表id
   */
  mainId?: number;
  /**
   * 流程id
   */
  mainProcessInstanceId?: string;
  /**
   * memo
   */
  memo?: string;
  /**
   * 检修交待
   */
  repairOrder?: string;
  /**
   * 递交负责人id
   */
  submitHeadId?: number;
  /**
   * 递交负责人名称
   */
  submitHeadName?: string;
  /**
   * 递交时间
   */
  submitTime?: string;
  /**
   * 工作后状态
   */
  workState?: string;
  [property: string]: any;
}

/**
* 工作票执行确认
*
* WorkFlowExecuteConfirm
*/
export interface WorkFlowExecuteConfirm {
  /**
   * 许可人id
   */
  allowId?: number;
  /**
   * 许可人姓名
   */
  allowName?: string;
  /**
   * 许可人确认时间
   */
  allowTime?: string;
  /**
   * id
   */
  id?: number;
  /**
   * main_id
   */
  mainId?: number;
  /**
   * 流程id
   */
  mainProcessInstanceId?: string;
  /**
   * memo
   */
  memo?: string;
  /**
   * 值班负责人id
   */
  shiftSupervisorId?: number;
  /**
   * 值班负责人姓名
   */
  shiftSupervisorName?: string;
  /**
   * 值长确认时间
   */
  shiftSupervisorTime?: string;
  [property: string]: any;
}

/**
* 工作票许可表
*
* WorkFlowLicense
*/
export interface WorkFlowLicense {
  /**
   * 许可开始工作时间
   */
  allowBeginTime?: string;
  /**
   * 工作许可人id
   */
  allowId?: number;
  /**
   * 工作许可人名称
   */
  allowName?: string;
  /**
   * 许可时间
   */
  allowTime?: string;
  /**
   * 附件id
   */
  attachId?: number;
  /**
   * 确认的措施
   */
  confirmMeasure?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 工作负责人id
   */
  headId?: number;
  /**
   * 工作负责人名称
   */
  headName?: string;
  /**
   * id
   */
  id?: number;
  /**
   * main_id
   */
  mainId?: number;
  /**
   * 主流程id
   */
  mainProcessInstanceId?: string;
  /**
   * memo
   */
  memo?: string;
  /**
   * 意见
   */
  opinion?: string;
  /**
   * 提交时间
   */
  submitTime?: string;
  /**
   * 工作班组人员签名
   */
  workerName?: string;
  [property: string]: any;
}

/**
* 工作票值长批准表
*
* WorkFlowRatify
*/
export interface WorkFlowRatify {
  /**
   * 批准工作开始时间
   */
  approveBeginTime?: string;
  /**
   * 批准工作结束时间
   */
  approveEndTime?: string;
  /**
   * 批准时间
   */
  approveTune?: string;
  /**
   * id
   */
  id?: number;
  /**
   * main_id
   */
  mainId?: number;
  /**
   * 流程id
   */
  mainProcessInstanceId?: string;
  /**
   * memo
   */
  memo?: string;
  /**
   * 意见
   */
  opinion?: string;
  /**
   * 值长名称
   */
  shiftSupervisorName?: string;
  /**
   * 值长id
   */
  shiftUpervisorId?: number;
  [property: string]: any;
}

/**
* 工作票签发
*
* WorkFlowSign
*/
export interface WorkFlowSign {
  /**
   * id
   */
  id?: number;
  /**
   * 主表id
   */
  mainId?: number;
  /**
   * 流程id
   */
  mainProcessInstanceId?: string;
  /**
   * memo
   */
  memo?: string;
  /**
   * 业主签发人id
   */
  ownerSignId?: number;
  /**
   * 业主签发人姓名
   */
  ownerSignName?: string;
  /**
   * 业主签发时间
   */
  ownerSignTime?: string;
  /**
   * 签发人id(签发人为外部签发人，可不用id，姓名和时间手填)
   */
  signId?: number;
  /**
   * 签发人姓名
   */
  signName?: string;
  /**
   * 签发人签发时间
   */
  signTime?: string;
  [property: string]: any;
}

/**
* 水工工作票安全措施
*
* WorkIrrigationSafeMeasure
*/
export interface WorkIrrigationSafeMeasure {
  /**
   * 补充安全措施
   */
  addMeasure?: string;
  /**
   * 补充安全措施执行情况
   */
  addMeasureExecute?: string;
  /**
   * 值班补充
   */
  dutyAddMeasure?: string;
  /**
   * 值班补充执行情况
   */
  dutyAddMeasureExecute?: string;
  /**
   * id
   */
  id?: number;
  /**
   * main_id
   */
  mainId?: number;
  /**
   * 主要危险点
   */
  mainRislk?: string;
  /**
   * memo
   */
  memo?: string;
  /**
   * 采取的安全措施
   */
  shouldMeasure?: string;
  /**
   * 安全措施执行情况
   */
  shouldMeasureExecute?: string;
  [property: string]: any;
}

/**
* 安全措施票(电气/水工)
*
* WorkSafeMeasureTicket
*/
export interface WorkSafeMeasureTicket {
  /**
   * 是否执行 1-执行 0-未执行
   */
  execute?: number;
  /**
   * id
   */
  id?: number;
  /**
   * main_id
   */
  mainId?: number;
  /**
   * memo
   */
  memo?: string;
  /**
   * 序号
   */
  num?: number;
  /**
   * 是否恢复 1-恢复 0-未恢复
   */
  restore?: number;
  /**
   * 应采取措施
   */
  shouldMeasure?: string;
  /**
   * 类型 1-检修执行 2-检修自理 3-运行补充 4-运行补充自检
   */
  type?: number;
  [property: string]: any;
}

/**
* 工作票执行相关信息
*
* WorkTicketExecute
*/
export interface WorkTicketExecute {
  /**
   * 工作票ID
   */
  mainId?: null;
  /**
   * 工作票类型
   */
  ticketType?: null;
  /**
   * 工作票执行试运行/恢复记录(机械) 详情查看用
   */
  workExecuteRecord?: WorkExecuteRecord;
  /**
   * 工作票执行试运行/恢复记录(机械)
   */
  workExecuteRecordList?: null;
  /**
   * 工作票执行表
   */
  workFlowExecute?: WorkFlowExecute;
  /**
   * 工作票执行延期表
   */
  workFlowExecuteDelay?: WorkFlowExecuteDelay;
  /**
   * 工作票执行负责人变更表
   */
  workFlowExecuteHeadExchange?: WorkFlowExecuteHeadExchange;
  /**
   * 工作票执行成员变更表
   */
  workFlowExecuteUserExchanges?: null;
  /**
   * 工作票交回记录
   */
  workFlowReturns?: null;
  /**
   * 工作票试运行表 恢复工作申请
   */
  workFlowTryRecover?: WorkFlowTryRun;
  /**
   * 工作票试运行表 试运行
   */
  workFlowTryRun?: WorkFlowTryRun;
  [property: string]: any;
}

/**
* 工作票执行试运行/恢复记录(机械) 详情查看用
*
* WorkExecuteRecord
*/
export interface WorkExecuteRecord {
  /**
   * 许可人id
   */
  allowId?: null;
  /**
   * 许可人名称
   */
  allowName?: null;
  /**
   * 时间
   */
  allowTime?: null;
  /**
   * 负责人id
   */
  headId?: null;
  /**
   * 负责人名称
   */
  headName?: null;
  /**
   * id
   */
  id?: null;
  /**
   * main_id
   */
  mainId?: null;
  /**
   * memo
   */
  memo?: null;
  /**
   * 类型 1-允许试运行 2-检修工作恢复
   */
  type?: null;
  [property: string]: any;
}

/**
* 工作票执行延期表
*
* WorkFlowExecuteDelay
*/
export interface WorkFlowExecuteDelay {
  /**
   * 许可人id
   */
  allowId?: null;
  /**
   * 许可人姓名
   */
  allowName?: null;
  /**
   * 许可人提交使劲按
   */
  allowTime?: null;
  formKey?:   null;
  /**
   * 负责人id
   */
  headId?: null;
  /**
   * 负责人姓名
   */
  headName?: null;
  /**
   * 负责人提交时间
   */
  headTime?: null;
  /**
   * id
   */
  id?:        null;
  isCurTask?: null;
  /**
   * main_id
   */
  mainId?: null;
  /**
   * 主流程id
   */
  mainProcessInstanceId?: null;
  /**
   * memo
   */
  memo?: null;
  /**
   * 新结束时间
   */
  newEndTime?: null;
  /**
   * 工作票原结束时间
   */
  originEndTime?: null;
  /**
   * 子流程id
   */
  processInstanceId?: null;
  /**
   * 延期理由
   */
  reason?: null;
  /**
   * 值长id
   */
  shiftSupervisorId?: null;
  /**
   * 值长姓名
   */
  shiftSupervisorName?: null;
  /**
   * 值长同意时间
   */
  shiftSupervisorTime?: null;
  /**
   * 签发人提交时间
   */
  siginTime?: null;
  /**
   * 签发人id
   */
  signId?: null;
  /**
   * 签发人姓名
   */
  signName?:     null;
  taskDefKey?:   null;
  taskId?:       null;
  taskName?:     null;
  taskUserName?: null;
  /**
   * 子流程状态
   */
  workflowState?:     null;
  workFlowStateName?: null;
  [property: string]: any;
}

/**
* 工作票执行负责人变更表
*
* WorkFlowExecuteHeadExchange
*/
export interface WorkFlowExecuteHeadExchange {
  /**
   * 工作许可人id
   */
  allowId?: null;
  /**
   * 工作许可人姓名
   */
  allowName?: null;
  /**
   * 工作许可人批准时间
   */
  allowTime?: null;
  /**
   * 附件id
   */
  attachId?: null;
  /**
   * 工作负责人变动时间
   */
  changeTime?: null;
  /**
   * 工作票审查人
   */
  checkId?: null;
  /**
   * 工作票审查人姓名
   */
  checkName?: null;
  /**
   * 审查时间
   */
  checkTime?: null;
  formKey?:   null;
  /**
   * 负责人id
   */
  headerId?: null;
  /**
   * 负责人姓名
   */
  headerName?: null;
  /**
   * 负责人提交时间
   */
  headerTime?: null;
  /**
   * id
   */
  id?:        null;
  isCurTask?: null;
  /**
   * main_id
   */
  mainId?: null;
  /**
   * 主流程id
   */
  mainProcessInstanceId?: null;
  /**
   * memo
   */
  memo?: null;
  /**
   * 变更后负责人id
   */
  nowHeadId?: null;
  /**
   * 变更后负责人姓名
   */
  nowHeadName?: null;
  /**
   * 原负责人id
   */
  originHeadId?: null;
  /**
   * 原负责人姓名
   */
  originHeadName?: null;
  /**
   * 业主签发人姓名
   */
  ownerSiginName?: null;
  /**
   * 业主签发时间
   */
  ownerSiginTime?: null;
  /**
   * 业主签发人id
   */
  ownerSignId?: null;
  /**
   * 子流程id
   */
  processInstanceId?: null;
  /**
   * 签发人id
   */
  signId?: null;
  /**
   * 签发人名称
   */
  signName?: null;
  /**
   * 签发人签发时间
   */
  signTime?: null;
  /**
   * 变动情况说明
   */
  situationDescription?: null;
  taskDefKey?:           null;
  taskId?:               null;
  taskName?:             null;
  taskUserName?:         null;
  /**
   * 子流程状态
   */
  workflowState?:     null;
  workFlowStateName?: null;
  [property: string]: any;
}

/**
* 工作票试运行表 恢复工作申请
*
* WorkFlowTryRun
*
* 工作票试运行表 试运行
*/
export interface WorkFlowTryRun {
  /**
   * 许可人id
   */
  allId?: null;
  /**
   * 许可人姓名
   */
  allowName?: null;
  /**
   * 许可时间
   */
  allowTime?: null;
  /**
   * 附件id
   */
  attachId?: null;
  /**
   * 创建时间
   */
  createTime?: null;
  /**
   * 电气编码
   */
  electricalCode?: null;
  /**
   * 电气负责人
   */
  electricalName?: null;
  formKey?:        null;
  /**
   * 负责人id
   */
  headId?: null;
  /**
   * 负责人姓名
   */
  headName?: null;
  /**
   * id
   */
  id?:        null;
  isCurTask?: null;
  /**
   * 机械编码
   */
  machineCode?: null;
  /**
   * 机械负责人
   */
  machineName?: null;
  /**
   * main_id
   */
  mainId?: null;
  /**
   * 流程id
   */
  mainProcessInstanceId?: null;
  /**
   * memo
   */
  memo?: null;
  /**
   * 审核意见
   */
  opinion?: null;
  /**
   * 部门id
   */
  orgId?: null;
  /**
   * 部门名称
   */
  orgName?: null;
  /**
   * 其他编码
   */
  otherCode?: null;
  /**
   * 其他负责人
   */
  otherName?: null;
  /**
   * 子流程id
   */
  processInstanceId?: null;
  taskDefKey?:        null;
  taskId?:            null;
  taskName?:          null;
  taskUserName?:      null;
  /**
   * 班组id
   */
  teamsId?: null;
  /**
   * 班组名称
   */
  teamsNmae?: null;
  /**
   * 热控编码
   */
  thermalControlCode?: null;
  /**
   * 热控负责人
   */
  thermalControlHead?: null;
  /**
   * 试运行时间
   */
  tryTime?: null;
  /**
   * 类型 1-试运行 2-恢复工作
   */
  type?: null;
  /**
   * 子流程状态
   */
  workflowState?:     null;
  workFlowStateName?: null;
  /**
   * 工作重运行安全措施
   */
  workFlowTryRunSafe?: null;
  /**
   * 地点
   */
  workLocation?: null;
  /**
   * 内容
   */
  workTask?: null;
  [property: string]: any;
}

/**
* com.nwh.framework.flowable.entity.WorkflowApprovalRecord
*
* WorkflowApprovalRecord
*/
export interface WorkflowApprovalRecord {
  /**
   * 审批id
   */
  approvalId?: number;
  /**
   * 审批参数
   */
  approvalParam?: string;
  /**
   * 审批结果
   */
  approvalResult?: string;
  /**
   * 审批时间
   */
  approvalTime?: string;
  /**
   * 审批人id
   */
  approver?: number;
  /**
   * 审批人姓名
   */
  approverName?: string;
  /**
   * 文件id
   */
  attachId?: number;
  /**
   * 图片资源列表
   */
  attachList?: AttachmentContent[];
  /**
   * 业务类型
   */
  businessType?: string;
  /**
   * 业务类型名称
   */
  businessTypeName?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 创建人
   */
  createUser?: number;
  /**
   * 描述
   */
  description?: string;
  /**
   * id
   */
  id?: number;
  /**
   * 图片id
   */
  imgId?: number;
  /**
   * 报告资源列表
   */
  imgList?: AttachmentContent[];
  /**
   * 备用字段1
   */
  memo1?: string;
  /**
   * 备用字段2
   */
  memo2?: string;
  /**
   * 备用字段3
   */
  memo3?: string;
  /**
   * 备注
   */
  meomo?: string;
  /**
   * 下一个审批人
   */
  nextApprover?: number;
  /**
   * 审批意见
   */
  opinion?: string;
  /**
   * 流程实例id
   */
  processInstanceId?: string;
  /**
   * 审批角色
   */
  role?: string;
  /**
   * 任务定义编码
   */
  taskDefKey?: string;
  /**
   * 任务节点id
   */
  taskId?: string;
  /**
   * 任务名称
   */
  taskName?: string;
  [property: string]: any;
}