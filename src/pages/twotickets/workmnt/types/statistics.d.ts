export interface TicketCountAllParams {
  /**
   * 审核结果
   */
  auditResult?: string;
  /**
   * 当前时间
   */
  currentTime?: string;
  /**
   * 结束日期
   */
  endTime?: string;
  /**
   * 人员
   */
  human?: string;
  /**
   * 是否终结 0 未终结  1 已终结
   */
  isEnding?: number;
  /**
   * 是否为标准票
   */
  isStandardTicket?: number;
  /**
   * 图 0 /表 1
   */
  showType?: number;
  /**
   * 开始日期
   */
  startTime?: string;
  /**
   * 状态
   */
  status?: string;
  /**
   * 票种类型
   */
  ticketType?: string;
  /**
   * 时间格式
   */
  timeFormat?: string;
  /**
   * 票类型
   */
  type?: string;
  /**
   * 流程状态
   */
  workFlowState?: string;
  /**
   * 工作任务
   */
  workTask?: string;
  [property: string]: any;
}



// 工作票审核总览
export interface TicketCountAll {
  /**
   * 已审核
   */
  audited?: number;
  /**
   * 已终结数量
   */
  endingTicketCount?: number;
  /**
   * 合格
   */
  pass?: number;
  /**
   * 合格率
   */
  passRate?: number;
  /**
   * 工作票发票总数/工作票发票数
   */
  ticketCountAll?: number;
  [property: string]: any;
}

export interface CombinedData {
  ticketCount?: TicketCountAll;
  averageExecution: Record<string, number>;
}