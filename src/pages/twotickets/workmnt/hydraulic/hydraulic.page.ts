export type childProcessType = 'delay' | 'change' | 'return' | undefined;

export type DatePickerConfigType = {
  label: string;
  name: string;
  initValue?: Date;
};

export type RemarkConfigType = {
  label: string;
  name: string;
  placeholder?: string;
};

export interface baseReviewProps {
  title?: string;
  okText?: string;
  cancelText?: string;
  datePickerConfig?: DatePickerConfigType;
  remarkConfig?: RemarkConfigType;
  submitObj?: string;
  custom?: any;
}

export interface variablesData {
  behavior: string;
  extra: boolean;
  pass: string;
  status: string;
  sub?: string;
  memo?: string;
}

export const CHILD_TASK_STATUS = ['8', '9', '10']; // 子流程状态

export type childTaskType = 'delay' | 'change' | 'return' | undefined;

