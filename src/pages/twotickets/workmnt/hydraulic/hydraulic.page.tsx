import { DatePicker, DatePickerRef, Form, Radio, Tabs, TextArea, Toast } from 'antd-mobile';
import { useRequest, useThrottleFn } from 'ahooks';
import { useParams, history } from 'umi';
import { RefObject, useEffect, useMemo, useRef, useState } from 'react';
import { BasicInfoComponent } from './components/basic-info/basic-info.component';
import SecurityRiskControlContent from '../components/security-risk-control-content/security-risk-control.content';
import { delayAudit, getWorkBaseDetail, headerExchangeAudit, ticketReturnAudit, updateWorkBase } from '@/services/twotickets/workmnt/workmnt';
import SafeToolComponent from '../components/safe-tool/safe-tool.component';
import { dateFormat, getTicketTypeAndTitle } from '@/constant';
import { OperationHeaderBtn } from '../../operation/detail/components/operation-btn/operation-header-btn.component';
import TwoTicketsNavBar from '@/component/twotickets-navbar/twotickets-navbar.component';
import { ProcessCard } from '@/component/process-card/process-card.component';
import styles from './hydraulic.page.less';
import SafetyMeasureComponent from './components/safety-measure/safety-measure.component';
import ActionButtons from '../components/action-buttons/action-buttons.component';
import ExecuteComponent from './components/execute/execute.component';
import dayjs from 'dayjs';
import FlowPopup from '../components/flow-popue/flow-popue.component';
import { baseReviewProps, CHILD_TASK_STATUS, childTaskType, variablesData } from './hydraulic.page';
import UserPicker from '../components/user-picker/user-picker.component';
import _ from 'lodash';
import useUserInfo from '@/hooks/useUserInfo';

const TAB_HEIGHT = 42;
const HydraulicDetail = () => {
  const [activeKey, setActiveKey] = useState('1');
  const [variablesValue, setVariablesValue] = useState<variablesData>();
  const [flowButtonText, setFlowButtonText] = useState<string>();
  const [childTaskType, setChildTaskType] = useState<childTaskType>();
  const [flowInitValue, setFlowInitValue] = useState({});
  const [visible, setVisible] = useState(false);
  const safetyMeasureRef = useRef<any>(null);
  const executeRef = useRef<any>(null);
  const userInfo = useUserInfo();

  // 获取url参数
  const { type, id, pageType } = useParams();
  const { title, ticketType } = getTicketTypeAndTitle('hydraulic', type);
  const getDetail = () => {
    return getWorkBaseDetail({
      id,
      ticketType
    }).then((res) => res.data)
  }

  const { data, loading } = useRequest(getDetail)

  const { run: handleScroll } = useThrottleFn(
    () => {
      let currentKey = TAB_ITEMS[0].key;
      for (const item of TAB_ITEMS) {
        const element = document.getElementById(`anchor-${item.key}`);
        if (!element) continue;
        const rect = element.getBoundingClientRect();
        if (rect.top <= TAB_HEIGHT) {
          currentKey = item.key;
        } else {
          break;
        }
      }
      setActiveKey(currentKey);
    },
    {
      leading: true,
      trailing: true,
      wait: 100,
    },
  );

  useEffect(() => {
    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  const TAB_ITEMS = useMemo(() => (
    [
      {
        key: '1',
        title: '基本信息',
        content: <BasicInfoComponent data={data} />,
      },
      {
        key: '2',
        title: '工作票执行',
        content: data?.status && Number(data.status) >= 8 && <ExecuteComponent ref={executeRef} data={data} />,
      },
      {
        key: '3',
        title: '安全风险控制卡',
        content: <SecurityRiskControlContent data={data} />,
      },
      {
        key: '4',
        title: '安全措施',
        content: <SafetyMeasureComponent ref={safetyMeasureRef} data={data} />,
      },
      {
        key: '5',
        title: '安全工器具',
        content: <SafeToolComponent data={data} />,
      },
    ]
  ), [data]);

  /**
   * 验证表单提交时的必要条件
   * @param status - 状态
   * @returns 是否验证通过
   */
  const validateFormSubmission = (payload: any) => {
    const status = data?.status ? Number(data?.status) : 0; // 当前流程状态
    const { planEndTime, workFlowConfirm, workFlowRatify, workFlowLicense, workFlowExecuteConfirm, workFlowEnd } = payload;
    return new Promise((resolve) => {
      Promise.all(
        [
          safetyMeasureRef?.current?.form.validateFields(),
          executeRef?.current?.workFlowExecuteValidateFields(),
        ]
      ).then(() => {
        const planEndTimeD = dayjs(planEndTime),
          accessTimeD = dayjs(workFlowConfirm?.accessTime),
          approveEndTimeD = dayjs(workFlowRatify?.approveEndTime),
          approveTuneD = dayjs(workFlowRatify?.approveTune),
          allowBeginTimeD = dayjs(workFlowLicense?.allowBeginTime),
          endTimeD = dayjs(workFlowEnd?.endTimeD),
          shiftSupervisorTimeD = dayjs(workFlowExecuteConfirm?.shiftSupervisorTime);

        let errorMessage = '';
        if (status === 5 && (approveEndTimeD.isBefore(accessTimeD) || approveEndTimeD.isAfter(planEndTimeD))) {
          // 值长审批：批准工作结束时间默认是计划结束日期（修改时要求不能早于确认时间，晚于计划结束日期，之前页面设计时写的默认当前时间，需要修改一下； 备注：一般批准工作结束时间就是计划结束日期 ）
          errorMessage = '批准工作结束时间不能早于运行值班负责人确认时间，晚于计划结束时间';
        } else if (status === 6 && (allowBeginTimeD.isBefore(approveTuneD) || allowBeginTimeD.isAfter(approveEndTimeD))) {
          // 许可人审批：许可开始工作时间默认是当前时间（修改时要求：不能早于值长批准时间，晚于批准工作结束时间）
          errorMessage = '许可开始工作时间不能早于值长批准时间，晚于批准工作结束时间';
        } else if (status === 12 && (endTimeD.isBefore(shiftSupervisorTimeD) || allowBeginTimeD.isAfter(approveEndTimeD))) {
          // 许可人终结：工作结束时间默认是当前时间 （修改时要求：不能早于工作票执行确认时间，晚于批准作业结束时间）
          errorMessage = '许可开始工作时间不能早于值长批准时间，晚于批准工作结束时间';
        }

        if (errorMessage) {
          Toast.show({
            icon: 'fail',
            content: errorMessage,
          });
          resolve(false);
        } else {
          resolve(true);
        }

      }).catch((error) => {
        Toast.show({
          icon: 'fail',
          content: `请填写必填项`,
        });
        resolve(false)
        console.log(error);
      })
    })
  };

  // 补全后端所需字段，格式化日期，组装数据
  const formatSubmitParams = (values: any) => {
    let taskType = 'base';
    if (variablesValue?.memo && variablesValue?.sub) {
      taskType = variablesValue.sub;
    }
    const curReviewConfig = reviewConfig[taskType];
    const configStatus: string = variablesValue?.status || variablesValue?.memo || '0';
    const remarkConfig = curReviewConfig[+configStatus]?.remarkConfig;
    const submitObj = curReviewConfig[+configStatus]?.submitObj;
    const datePickerConfig = curReviewConfig[+configStatus]?.datePickerConfig;
    const formatValues = submitObj
      ? datePickerConfig
        ? {
          ...values,
          [datePickerConfig.name]: dateFormat(values[datePickerConfig.name]),
          [submitObj]: {
            [datePickerConfig.name]: dateFormat(values[datePickerConfig.name]),
          },
        }
        : {
          ...values,
          [submitObj]: values,
        }
      : datePickerConfig
        ? {
          ...values,
          [datePickerConfig.name]: dateFormat(values[datePickerConfig.name]),
        }
        : values;
    if (remarkConfig) { // 工作票终结, 工作票发放等特殊地方，由于备注和后端流程记录字段不一致，需要传 opinion，流程记录显示需要
      formatValues.opinion = values[remarkConfig.name];
    }
    return formatValues;
  }

  // 暂存、提交
  const handleSubmit = async (submitParams: any, variablesParams?: variablesData) => {
    console.log(submitParams)
    const variablesData = variablesValue || variablesParams;
    const safetyMeasureFormData = safetyMeasureRef?.current?.getValue(); // 安全措施
    let executeData = null;
    if (data?.status && CHILD_TASK_STATUS.includes(data.status)) {
      executeData = executeRef?.current.getWorkTicketExecute(); // 工作票执行
    }
    // 补全流程所需字段，格式化日期等，组装数据
    const submitValues = formatSubmitParams(submitParams);
    const params = {
      ...data,
      ticketType,
      status: variablesData?.status || data?.status,
      workIrrigationSafeMeasure: safetyMeasureFormData,
      workTicketExecute: executeData,
      isStandardTicket: 0,
      isAudit: 1,
      id: data?.id || null,
      ...submitValues,
      ...variablesData,
      variables: {
        ...variablesData,
      }
    };

    if ((type === 'one' && variablesData?.status === '17') || (type === 'two' && variablesData?.status === '15')) {
      params.behavior = submitValues.auditResult;
    }

    // 校验
    if (params.isAudit === 1 && (variablesData?.status === '2' || variablesData?.status === '12' || variablesData?.behavior === '通过') && data?.isExpired !== 1) {
      // 走通过的审批或者终结的时候做校验
      const validateForm = await validateFormSubmission({ ...params });
      if (!validateForm) {
        return;
      }
    }

    // 审批接口
    // 走保存或者审核流程
    updateWorkBase(params).then((res: Response) => {
      if (res.code === '1') {
        Toast.show({
          icon: 'success',
          content: params.isAudit === 1 ? '已处理' : '已保存',
        });
        if (params.isAudit === 1) history.push(`/twotickets/workmnt/hydraulic/${type}`);
      } else {
        Toast.show({
          icon: 'fail',
          content: res?.message || '提交失败',
        });
      }
    });
  };

  // 子流程提交数据
  const handleChildTaskSubmit = async (submitParams: any) => {
    if (!childTaskType) return;
    const childTask: Record<string, Record<string, any>> = {
      'delay': {
        api: delayAudit,
        data: data?.workTicketExecute?.workFlowExecuteDelay,
      },
      'change': {
        api: headerExchangeAudit,
        data: data?.workTicketExecute?.workFlowExecuteHeadExchange,
      },
      'return': {
        api: ticketReturnAudit,
        data: variablesValue?.memo === '2' ? null : data?.workTicketExecute?.workFlowReturns?.[0]
      },
    }
    // 补全流程所需字段，格式化日期等，组装数据
    const submitValues = formatSubmitParams(submitParams);
    const params = {
      ...childTask[childTaskType].data,
      ...submitValues,
      memo: variablesValue?.memo,
      mainId: data.id,
      taskId: data.taskId,
      ticketType,
      ...variablesValue,
      variables: {
        ...variablesValue,
      }
    };
    if (childTaskType === 'change' && variablesValue?.memo === '2') {
      params.nowHeadId = submitValues?.nowHeadId?.[0]?.userId;
      params.nowHeadName = submitValues?.nowHeadId?.[0]?.name;
      const approveEndTimeD = dayjs(data?.workFlowRatify?.actualEndTime);
      const planBeginTimeD = dayjs(data?.planBeginTime);
      const changeTimeD = dayjs(submitValues?.changeTime);
      if (changeTimeD.isBefore(planBeginTimeD) || changeTimeD.isAfter(approveEndTimeD)) {
        Toast.show({
          icon: 'fail',
          content: '变更时间不能早于计划开始日期，不能晚于批准工作结束日期',
        });
        return;
      }
    }

    if (childTaskType === 'delay' && variablesValue?.memo === '3') {
      const approveEndTimeD = dayjs(data?.workFlowRatify?.approveEndTime);
      const newEndTimeD = dayjs(submitValues?.newEndTime);
      if (newEndTimeD.isBefore(approveEndTimeD)) {
        // 有效期延长到时间必须晚于批准工作结束时间。（备注：如果延期，计划结束日期和批准作业结束时间为延期时间）
        Toast.show({
          icon: 'fail',
          content: '有效期延长到时间必须晚于批准工作结束时间',
        });
        return;
      }
    }
    childTask[childTaskType].api(params).then((res: Response) => {
      if (res.code === '1') {
        Toast.show({
          icon: 'success',
          content: '操作成功',
        });
        history.push(`/twotickets/workmnt/hydraulic/${type}`);
      } else {
        Toast.show({
          icon: 'fail',
          content: res?.message || '提交失败',
        });
      }
    });
  }

  const handleClose = () => {
    console.log('弹窗关闭');
    setVisible(false);
    setChildTaskType(undefined);
    setVariablesValue(undefined);
  };

  const baseReviewConfig: Record<number, baseReviewProps> = { // app这儿用的状态是接口返回的下一步流程的状态，跟pc不一样，pc是当前流程状态
    5: { // 运行值班负责人确认
      submitObj: 'workFlowConfirm',
      datePickerConfig: {
        label: '确认时间',
        name: 'accessTime',
        initValue: new Date(),
      },
    },
    6: { // 值长审批
      submitObj: 'workFlowRatify',
      datePickerConfig: {
        label: '批准工作结束时间',
        name: 'approveEndTime',
        initValue: data?.planEndTime ? new Date(data.planEndTime) : new Date(),
      },
    },
    7: { // 工作票许可
      submitObj: 'workFlowLicense',
      datePickerConfig: {
        label: '许可开始工作时间',
        name: 'allowBeginTime',
        initValue: new Date(),
      },
    },
    [type === 'one' ? 16 : 14]: { // 工作票终结
      submitObj: 'workFlowEnd',
      datePickerConfig: {
        label: '结束工作时间',
        name: 'endTime',
        initValue: new Date(),
      },
      remarkConfig: {
        label: '备注',
        name: 'endDescribe',
        placeholder: '请输入'
      },
    },
    [type === 'one' ? 17 : 15]: { // 监察审核
      custom: () => (
        <>
          <Form.Item layout='horizontal' label="审核结果" name="auditResult" rules={[{ required: true, message: '此项必选' }]}>
            <Radio.Group>
              <Radio value="合格">合格</Radio>
              <Radio value="不合格">不合格</Radio>
            </Radio.Group>
          </Form.Item>
        </>
      )
    },
  };

  const delayReviewConfig: Record<number, baseReviewProps> = {
    2: { // 发起工作票延期
      remarkConfig: {
        label: '延期理由',
        name: 'reason',
        placeholder: '请输入'
      },
    },
    3: { // 工作票延期审核
      datePickerConfig: {
        label: '有效期延长至',
        name: 'newEndTime',
      },
    },
  };

  const returnReviewConfig: Record<number, baseReviewProps> = {
    2: { // 交回
      datePickerConfig: {
        label: '时间',
        name: 'submitTime',
        initValue: new Date(),
      },
      remarkConfig: {
        label: '备注',
        name: 'remark',
        placeholder: '请输入'
      },
    },
    3: { // 审核
      datePickerConfig: {
        label: '时间',
        name: 'submitTime',
        initValue: new Date(),
      },
    },
    4: { // 发放
      datePickerConfig: {
        label: '时间',
        name: 'grantTime',
        initValue: new Date(),
      },
      remarkConfig: {
        label: '备注',
        name: 'grantRemark',
        placeholder: '请输入'
      }
    },
  };

  const changeReviewConfig: Record<number, baseReviewProps> = {
    2: { // 发起负责人变更
      custom: () => (
        <>
          <Form.Item layout='horizontal' label="变更后负责人" name="nowHeadId" rules={[{ required: true, message: '此项必选' }]}>
            <UserPicker
              label=''
              name='nowHeadId'
              showUsers={true}
              customMode={false}
              placeholder='请选择'
              title='用户选择'
            />
          </Form.Item>
        </>
      ),
      datePickerConfig: {
        label: '变更时间',
        name: 'changeTime',
        initValue: new Date(),
      },
      remarkConfig: {
        label: '变动情况说明',
        name: 'situationDescription',
        placeholder: '请输入'
      }
    },
  };

  const reviewConfig: Record<string, Record<number, baseReviewProps>> = {
    base: baseReviewConfig,
    back: baseReviewConfig,
    delay: delayReviewConfig,
    return: returnReviewConfig,
    change: changeReviewConfig,
  }

  const renderFormItem = useMemo(() => {
    if (!data?.taskId || !visible) return;
    let curReviewConfig = null;
    if (variablesValue?.memo && variablesValue?.sub) {
      curReviewConfig = reviewConfig[variablesValue.sub];
    } else {
      curReviewConfig = reviewConfig['base'];
    }
    const configStatus: string = variablesValue?.status || variablesValue?.memo || '0';
    const custom = curReviewConfig[+configStatus]?.custom;
    const datePickerConfig = curReviewConfig[+configStatus]?.datePickerConfig;
    const remarkConfig = curReviewConfig[+configStatus]?.remarkConfig;
    if (datePickerConfig && variablesValue?.sub !== 'back' && datePickerConfig?.initValue && !Object.keys(flowInitValue)?.length) {
      console.log('---------================--------------')
      setFlowInitValue({
        [datePickerConfig.name]: datePickerConfig.initValue
      })
    }

    return (
      <>
        {custom && custom()}
        {(datePickerConfig && variablesValue?.sub !== 'back') && (
          <Form.Item
            layout='horizontal'
            name={datePickerConfig.name}
            label={datePickerConfig.label}
            rules={[{ required: true, message: '此项必选' }]}
            trigger='onConfirm'
            onClick={(_e, datePickerRef: RefObject<DatePickerRef>) => {
              datePickerRef.current?.open()
            }}
          >
            <DatePicker precision='second' defaultValue={datePickerConfig?.initValue}>
              {value => value ? dayjs(value).format('YYYY-MM-DD HH:mm:ss') : '请选择日期'}
            </DatePicker>
          </Form.Item>
        )}
        <Form.Item layout='horizontal' label={remarkConfig?.label ?? "审核意见"} name={remarkConfig?.name ?? 'opinion'}>
          <TextArea showCount maxLength={100} placeholder={remarkConfig?.placeholder ?? "确认本工作上述各项内容"} />
        </Form.Item>
      </>
    )
  }, [data, visible, variablesValue, reviewConfig])


  return (
    <div className={styles.container} style={{ paddingTop: `${window.top?.localStorage?.topHeight ?? 0}px` }}>
      <TwoTicketsNavBar title={data?.taskName} subTitle={title} bgcolor={'#fff'} color={'#000'} right={<OperationHeaderBtn data={data} />} />
      <div className={styles.body}>
        <div className={styles.content}>
          <ProcessCard
            id={data?.id}
            records={_.cloneDeep(data?.workflowApprovalRecordList || []).reverse()}
            onProcess={function (id: number): void {
              history.push(`/twotickets/common/check-list/process-viewer/${data?.id}?ticketType=${ticketType}`);
            }}
            taskUserName={data?.taskUserName}
          />
        </div>

        <div className={styles.tabsContainer}>
          <Tabs
            activeKey={activeKey}
            onChange={(key) => {
              setActiveKey(key)
              document.getElementById(`anchor-${key}`)?.scrollIntoView();
              window.scrollTo({
                top: window.scrollY - TAB_HEIGHT,
              });
            }}
          >
            {TAB_ITEMS.map((item) => item.content && (
              <Tabs.Tab title={item.title} key={item.key} />
            ))}
          </Tabs>
        </div>

        <div className={styles.tabsContent}>
          {TAB_ITEMS.map((item: any) => item.content && (
            <div key={item.key} id={`anchor-${item.key}`}>
              {item.content}
            </div>
          ))}
        </div>
        <div className={styles.bottom}>已经到底了</div>
      </div>
      <ActionButtons
        onClick={(actionType, variables, title) => {
          console.log(actionType, variables, title, '点击的按钮传出来的参树');
          setVariablesValue(variables);
          setFlowButtonText(title);
          // 记录子流程类型，用来子流程审核判断
          if (variables?.memo) {
            if (!variables?.sub) { // 子流程进入审核之后，没有sub，需要根据status判断
              variables.sub = data.status === '9' ? 'delay' : data.status === '10' ? 'change' : data.status === '11' ? 'return' : undefined
            }
            setChildTaskType(variables.sub)
          }
          if (actionType === '保存') { // 保存数据
            handleSubmit({ isAudit: 0 }, { status: data?.status, ...variables });
          } else if (variables?.extra) { // 弹框审核（处理）（主流程。子流程都有）
            setVisible(true)
          } else {
            handleSubmit({}, variables); // 审核（处理）
          }
        }}
        showSaveBtn={pageType !== 'view' && ((data?.status && Number(data.status) <= 6) || (data?.status === '8' && userInfo?.id === data?.headId))}
        taskId={data?.taskId}
        disabledConfig={{
          "工作票延期申请": data?.workTicketExecute?.workFlowExecuteDelay ? true : false,
          "工作票负责人变更": data?.workTicketExecute?.workFlowExecuteHeadExchange ? true : false
        }}
        loading={false}
        detailData={data}
      />
      {/* 审核流程弹窗 */}
      {data?.taskId && (
        <FlowPopup title={flowButtonText} visible={visible} initialValues={flowInitValue} onClose={handleClose} onSubmit={(data) => {
          if (childTaskType) {
            handleChildTaskSubmit(data)
          } else {
            handleSubmit(data)
          }
        }}>
          {
            renderFormItem
          }
        </FlowPopup>
      )}
    </div>
  );
};

export default HydraulicDetail;
