import { Card, Grid, Divider, Space, List } from 'antd-mobile';
import { styled, useParams } from 'umi';

import { SHOW_TIME_FORMAT } from '@/utils/constant';
import dayjs from 'dayjs';
import ImageView from '@/component/image-view/image-view.component';
import useInterleave from '@/hooks/useInterleave';
import { ListCard } from '@/component/list-card/list-card.component';
import { OperationFileList } from '@/styles/twotickets.style';
import { ReviewRecordComponent } from '@/pages/twotickets/common/[type]/components/review-record/review-record.component';
import { AttachmentItemRow } from '@/pages/twotickets/common/[type]/components/attachment-content/attachment-content.component';
import { RecordFileds } from '@/pages/twotickets/common/[type]/components/review-record/review-record-config';

const BasicInfoComponentWrapper = styled(Space)`
  width: 100%;
  font-family: PingFang SC;
  .adm-card-body {
    padding-bottom: 1.8rem;
  }
  .title {
    font-size: 1.6rem;
    font-weight: 500;
    letter-spacing: 0rem;
    line-height: 2.3333rem;
    color: rgba(56, 56, 56, 1);
    text-align: left;
  }
  .item {
    font-size: 1.2rem;
    font-weight: 400;
    letter-spacing: 0rem;
    line-height: 1.7377rem;
    color: rgba(153, 153, 153, 1);
    .name {
      font-size: 1.2rem;
      font-weight: 400;
      letter-spacing: 0rem;
      line-height: 1.7377rem;
      color: rgba(51, 51, 51, 1);
    }
  }
  .remark {
    font-size: 1.5333rem;
    font-weight: 400;
    letter-spacing: 0rem;
    line-height: 2.2203rem;
    color: rgba(51, 51, 51, 1);
  }
`;

type BasicInfoComponentProps = {
  data: any;
};

export const BasicInfoComponent = (props: BasicInfoComponentProps) => {
  // 获取url参数
  const { type = 'one' } = useParams();
  const { data } = props;
  const SplitPoint = (
    <div
      style={{
        display: 'inline-block',
        width: '.2667rem',
        height: '.2667rem',
        borderRadius: '.1333rem',
        margin: '0 .4rem',
        backgroundColor: 'rgba(102, 102, 102, 1)',
      }}
    />
  );
  return (
    <BasicInfoComponentWrapper direction="vertical">
      <Card>
        <div className="title">{data?.name}</div>
        <Divider />
        <div className="item">
          单位：<span className="name">{data?.unitName}</span>
        </div>
        <div className="item">
          成员：<span className="name">{data?.member}（参加工作人数共{data?.peopleNumber}人）</span>
        </div>
        {type === 'one' && (
          <div className="item">
            机电设备监护人：<span className="name">{data?.electromechanicalUserName}</span>
          </div>
        )}
        <div className="item">
          班组：<span className="name">{data?.teamsName}</span>
        </div>
        <Divider />
        <div className="item">
          计划时间：
          <span className="name">{`${data?.planBeginTime ? dayjs(data.planBeginTime).format(SHOW_TIME_FORMAT) : ''} ~ ${
            data?.planEndTime ? dayjs(data.planEndTime).format(SHOW_TIME_FORMAT) : ''
          }`}</span>
        </div>
        <div className="item">
          工作地点：<span className="name">{data?.location}</span>
        </div>
        <div className="item">
          创建时间：<span className="name">{data?.createTime}</span>
        </div>
        <Divider />
        <div className="remark">
          <div className="title">备注</div>
          <div className="content">{data?.remark}</div>
        </div>
      </Card>
      {data?.imgList?.length && (
        <Card title="图片">
          {ImageView({ imgList: data?.imgList?.map((item: any) => item.id) })}
        </Card>
      )}
      {data?.files?.length && <ListCard title="附件" data={data?.files || []} moreLink={`/twotickets/common/check-list/attachment-content/${data.id}?ticketType=${data?.ticketType}&dataKey=files`} isMore maxLength={5}>
        {(dataList) => (
          <OperationFileList
            style={{
              '--border-top': 'none',
              '--border-bottom': 'none',
            }}
          >
            {dataList?.map((item: any, index: number) => (<AttachmentItemRow key={item.id} item={item as any} />))}
          </OperationFileList>
        )}
      </ListCard>}
      {data?.status && Number(data.status) > 2 && (
        <ListCard title="流程记录" data={RecordFileds
          ?.filter((item: any) => data?.[item.name])} moreLink={`/twotickets/common/check-list/review-record/${data?.id}?ticketType=${data?.ticketType}`} isMore maxLength={3}>
          {(dataList) => <ReviewRecordComponent recordFileds={dataList} data={data} />}
        </ListCard>
      )}
    </BasicInfoComponentWrapper>
  );
};
