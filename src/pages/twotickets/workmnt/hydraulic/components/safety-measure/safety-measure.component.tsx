import { Form } from 'antd';
import React, { useEffect, useImperativeHandle } from 'react';
import { Card, Divider, Ellipsis, TextArea } from 'antd-mobile';
import { useParams } from 'umi';
import styles from './safety-measure.component.less';
import { DownOutline, UpOutline } from 'antd-mobile-icons';

interface Props {
  data?: any;
}

const dutyStatus = [4,5]; // 值班负责人，值长填写
const licenseStatus = 6; // 许可人填写

const SafetyMeasureComponent = React.forwardRef((props: Props, ref) => {
  const [form] = Form.useForm();
  const { data } = props;

  // 获取url参数
  const { type = 'one', pageType } = useParams();

  const status = data?.status ? Number(data.status) : 0;

  const dutyBack = data?.workflowApprovalRecordList?.filter(
    (item: any) => ['Activity_1ljle5q', 'Activity_0prmnww'].includes(item.taskDefKey) && item.approvalResult === '退回',
  ); // 值班负责人退回，值长退回
  const licenseBack = data?.workflowApprovalRecordList?.filter(
    (item: any) => ['Activity_0tbl2j6'].includes(item.taskDefKey) && item.approvalResult === '退回',
  ); // 许可人退回

  const dutyReadonly = pageType === 'view' || !(dutyStatus.includes(status));
  const licenseReadonly = pageType === 'view' || status !== licenseStatus;

  /* 表单配置 */
  const safetyMeasure: any = {
    one: [
      {
        label: '应采取安全技术措施（工作负责人填写）',
        name: 'shouldMeasure',
        disabled: true,
      },
      {
        label: '机电设备监护人员补充的安全措施',
        name: 'addMeasure',
        disabled: true,
      },
    ],
    two: [
      {
        label: '必须采取的安全措施',
        name: 'shouldMeasure',
        disabled: true,
      },
    ]
  }

  const dutySafetyMeasure = [
    ...safetyMeasure[type],
    {
      label: '运行值班人员补充的安全措施',
      name: 'dutyAddMeasure',
      disabled: dutyReadonly, // 值班负责人和值长可以填写
    },
  ];

  /* 工作许可人表单配置 */
  const licenseSafetyMeasure: any = {
    one: [
      dutySafetyMeasure[0],
      {
        label: '已采取安全技术措施（工作许可人填写）',
        name: 'shouldMeasureExecute',
        rules: [{ required: status >= licenseStatus, message: '此项必填' }],
        disabled: licenseReadonly,
      },
      dutySafetyMeasure[1],
      {
        label: '主要危险点及预控措施（可附在“三措中”）',
        name: 'mainRislk',
        rules: [{ required: status >= licenseStatus, message: '此项必填' }],
        disabled: licenseReadonly,
      },
      dutySafetyMeasure[2],
      {
        label: '运行人员补充的安全措施执行情况',
        name: 'addMeasureExecute',
        disabled: licenseReadonly,
      },
    ],
    two: [
      dutySafetyMeasure[0],
      {
        label: '安全措施执行情况',
        name: 'shouldMeasureExecute',
        rules: [{ required: status >= licenseStatus, message: '此项必填' }],
        disabled: licenseReadonly,
      },
      dutySafetyMeasure[1],
      {
        label: '补充安全措施执行情况',
        name: 'addMeasureExecute',
        disabled: licenseReadonly,
      },
    ]
  }

  /* 暴露的提交事件及元素 */
  useImperativeHandle(ref, () => ({
    form,
    getValue: () => {
      return data?.workIrrigationSafeMeasure ? { ...data.workIrrigationSafeMeasure, ...form.getFieldsValue() } : form.getFieldsValue();
    },
  }));

  useEffect(() => {
    if (data?.workIrrigationSafeMeasure) {
      form.setFieldsValue(data.workIrrigationSafeMeasure);
    }
  }, [data]);

  const dataList = (status >= licenseStatus || licenseBack?.length) ? licenseSafetyMeasure[type] : (status >= dutyStatus[0] || dutyBack?.length) ? dutySafetyMeasure : safetyMeasure[type];

  return (
    <div className={styles.container}>
      <div className={styles.moduleName}>安全措施</div>
      <Card title='安全技术措施'>
        <Form form={form}>
          {
            dataList?.map((item, index) => (
              <>
                <Form.Item {...item} layout='horizontal'>
                  {item.disabled ? (
                    <Ellipsis
                      direction='end'
                      rows={3}
                      content={data?.workIrrigationSafeMeasure?.[item.name] ?? '无'}
                      expandText={
                        <>
                          展开
                          <DownOutline />
                        </>
                      }
                      collapseText={
                        <>
                          收起
                          <UpOutline />
                        </>
                      }
                    />
                  ) : (
                    <TextArea placeholder='请输入' />
                  )}

                </Form.Item>
                {index !== dataList.length - 1 ? <Divider /> : null}
              </>
            ))
          }
        </Form>
      </Card>
    </div>
  );
});

export default SafetyMeasureComponent;
