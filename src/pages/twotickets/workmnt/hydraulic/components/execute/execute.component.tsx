import WorkFlowReturns from "./components/workFlowReturns";
import WorkTicketExtension from "./components/workTicketExtension";
import MaintenanceIstructions from "../../../components/maintenanceIstructions";
import WorkMemberChange from "../../../components/workMemberChange";
import { useImperativeHandle, useMemo, useRef } from "react";
import styles from './execute.component.less';
import React from "react";
import ChangeUser from "../../../components/changgeUser";
import { useParams } from "umi";
interface Props {
  data?: any;
}
const ExecuteComponent = React.forwardRef((props: Props, ref) => {
  const { data } = props;
  const maintenanceIstructionsRef = useRef(null);
  const workMemberChangeRef = useRef(null);
  // 获取url参数
  const { pageType } = useParams();

  //获取工作班成员
  const workClass = useMemo(() => {
    const memo = data?.member?.split(',') || [];
    const member = data?.member?.split(',') || [];

    return memo?.map((el: string[], index: number) => ({ value: el, label: member[index] }));
  }, [data]);

  /* 暴露的提交事件 */
  useImperativeHandle(ref, () => ({
    getWorkTicketExecute: () => {
      return {
        workFlowExecuteUserExchanges: workMemberChangeRef?.current?.getData()?.map((item: any) => ({
          ...item,
           mainId: data.id,
           id: item?.id
        })),
        workFlowExecute: {
          ...maintenanceIstructionsRef?.current?.getFieldsValue(),
          mainId: data.id,
          id: data?.workTicketExecute?.workFlowExecute?.id,
        },
      }
    },
    workFlowExecuteValidateFields: () => maintenanceIstructionsRef?.current?.validateFields()
  }));

  return (
    <div className={styles.container}>
      <div className={styles.moduleName}>工作票执行</div>
      {/* 【工作负责人变更】 */}
      <ChangeUser current={data} />
      {/* 【每日开工和收工记录】 */}
      {data?.workTicketExecute?.workFlowReturns?.length ? (
        <WorkFlowReturns current={data} />
      ) : null}
      {/* 【工作成员变更 */}
      {(data?.status === '8' && data?.curTask) || data?.workTicketExecute?.workFlowExecuteUserExchanges ? (
        <WorkMemberChange ref={workMemberChangeRef} current={data} workClass={workClass} isAddAndEdit={pageType !== 'view' && data?.status === '8' && data?.curTask} />
      ) : null}
      {/* 【工作票延期】 */}
      <WorkTicketExtension current={data} />
      {/* 【检修交代】 */}
      {(data?.status === '8' && data?.curTask) || data?.workTicketExecute?.workFlowExecute ? (
        <MaintenanceIstructions ref={maintenanceIstructionsRef} disabled={pageType === 'view' || Number(data.status) >= 11 || !data?.curTask} current={data} />
    ) : null}
    </div>
  );
});
export default ExecuteComponent;
