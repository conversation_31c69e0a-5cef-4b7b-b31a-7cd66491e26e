import type { SelectOption, StatusMapItem } from '../types/selectOptions';

/**
 * 选择器选项常量配置
 */

// 通用的"全部"选项
export const ALL_OPTION: SelectOption = { label: '全部', value: '' };

// 审查结果选项
export const AUDIT_RESULT_OPTIONS: SelectOption[] = [
  ALL_OPTION,
  { label: '合格', value: '合格' },
  { label: '不合格', value: '不合格' }
];

// 电气执行状态选项
export const ELECTRICAL_EXECUTE_STATUS_OPTIONS: SelectOption[] = [
  ALL_OPTION,
  { label: '未执行', value: '1' },
  { label: '已执行', value: '2' },
  { label: '已终结', value: '3' },
];

// 搜索类型选项
export const SEARCH_TYPES: SelectOption[] = [
  ALL_OPTION,
  { label: '待办', value: '1' },
  { label: '已办', value: '0' },
];

/**
 * 创建包含"全部"选项的选项数组
 * @param options 其他选项
 * @returns 包含"全部"选项的完整数组
 */
export const createOptionsWithAll = (options: SelectOption[]): SelectOption[] => [
  ALL_OPTION,
  ...options
];

/**
 * 根据状态映射创建选项
 * @param statusMap 状态映射对象
 * @returns 选项数组
 */
export const createStatusOptions = (statusMap: Record<string, StatusMapItem>): SelectOption[] => {
  const options = Object.keys(statusMap)
    .filter((key) => key)
    .map((key) => ({
      label: statusMap[key].label,
      value: key,
    }));
  
  return createOptionsWithAll(options);
}; 