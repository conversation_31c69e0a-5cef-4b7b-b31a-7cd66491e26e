import { getOrganizationTree } from '@/services/sys.service';
import { useEffect, useState } from 'react';

/**
 * 组织树节点类型定义
 */
interface OrgTreeNode {
  id: string | number;
  name: string;
  children?: OrgTreeNode[];
  users?: UserNode[];
  [key: string]: any;
}

/**
 * 用户节点类型定义
 */
interface UserNode {
  id: string | number;
  name: string;
  [key: string]: any;
}

/**
 * 处理后的树节点类型定义
 */
interface ProcessedTreeNode {
  id: string | number;
  name: string;
  type: 'organization' | 'user';
  children: ProcessedTreeNode[];
  [key: string]: any;
}

/**
 * 处理树节点，将users合并到children中并添加类型标识
 * @param treeData 原始树数据
 * @param mergeUsersToChildren 是否将用户合并到children数组中
 * @returns 处理后的树数据
 */
const processTreeData = (treeData: OrgTreeNode[], mergeUsersToChildren: boolean = true): ProcessedTreeNode[] => {
  if (!treeData || !Array.isArray(treeData)) return [];

  const processNode = (node: OrgTreeNode): ProcessedTreeNode => {
    // 为组织节点添加类型标识
    const orgNode: ProcessedTreeNode = { ...node, type: 'organization', children: [] };

    // 处理子组织
    if (node.children && Array.isArray(node.children)) {
      // 递归处理子组织，并将结果添加到新的children数组
      node.children.forEach((child) => {
        orgNode.children.push(processNode(child));
      });
    }

    // 处理用户
    if (mergeUsersToChildren && node.users && Array.isArray(node.users)) {
      // 将用户添加到children数组，并添加类型标识
      node.users.forEach((user) => {
        orgNode.children.push({ 
          ...user, 
          type: 'user', 
          id: user.id, // 直接使用用户ID
          orgId: node.id, // 添加组织ID字段
          originalId: node.id + '_' + user.id, // 保留原始组合ID以备需要
          // 添加key属性作为React渲染的唯一标识
          key: `${node.id}_${user.id}`,
          children: [], 
          userId: user.id 
        } as ProcessedTreeNode);
      });
    }

    // 删除原始users数组
    if ('users' in orgNode) {
      delete orgNode.users;
    }

    return orgNode;
  };

  // 处理每个根节点
  return treeData.map((node) => processNode(node));
};

interface OrgTreeProps {
  mergeUsersToChildren?: boolean;
}

/**
 * 获取组织树的Hook
 * @returns [树数据, 加载状态, 刷新函数]
 */
const useOrgTree = (props: OrgTreeProps): [ProcessedTreeNode[], boolean, () => Promise<void>] => {
  const { mergeUsersToChildren = false } = props || {}; // 新增mergeUsersToChildren参数，默认合并
  const [tree, setTree] = useState<ProcessedTreeNode[]>([]);
  const [loading, setLoading] = useState(false);

  const getTree = async (): Promise<void> => {
    setLoading(true);
    try {
      const res = await getOrganizationTree({});
      console.log({ res });
      if (res?.code === '1') {
        const data = res?.data || [];
        // 处理树数据，将users合并到children中并添加类型标识
        const processedData = processTreeData(data, mergeUsersToChildren);
        setTree(processedData);
      }
    } catch (error) {
      console.error('获取组织树失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getTree();
  }, []);

  return [tree, loading, getTree];
};

export default useOrgTree;
