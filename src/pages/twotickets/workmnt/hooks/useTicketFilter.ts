import { useState, useCallback } from 'react';
import _ from 'lodash';

interface FilterValues {
  isDone?: string;
  auditResult?: string;
  status?: string;
  unitId?: string;
  [key: string]: any;
}

interface FilterSubmitParams {
  allowTime?: [string, string];
  finalTime?: [string, string];
  auditResult?: string[];
  status?: string[];
  unitId?: string[];
  [key: string]: any;
}

/**
 * 票据筛选 Hook
 * @returns 筛选相关的状态和方法
 */
export const useTicketFilter = () => {
  const [filterValues, setFilterValues] = useState<FilterValues>({});

  // 处理筛选提交
  const handleFilterSubmit = useCallback(({ allowTime, finalTime, auditResult, status, unitId, ...values }: FilterSubmitParams) => {
    const [allowStartTime, allowEndTime] = allowTime || [];
    const [finalStartTime, finalEndTime] = finalTime || [];

    // 准备要设置的参数
    const paramsToSet: FilterValues = {
      ...values,
      auditResult: auditResult?.join?.('') || '',
      status: status?.join?.('') || '',
      unitId: unitId?.[1] || unitId?.[0] || '',
    };

    // 仅当开始和结束时间都存在时才添加
    if (allowStartTime && allowEndTime) {
      Object.assign(paramsToSet, { allowStartTime, allowEndTime });
    }

    if (finalStartTime && finalEndTime) {
      Object.assign(paramsToSet, { finalStartTime, finalEndTime });
    }

    // 使用 pickBy 移除空值或 null/undefined 值
    setFilterValues(_.pickBy(paramsToSet, (v) => v !== '' && !_.isNil(v)));
  }, []);

  // 处理筛选类型变更
  const handleFilterTypeChange = useCallback((isDone = '') => {
    setFilterValues(prevValues => _.pickBy({ ...prevValues, isDone }, (v) => v !== '' && !_.isNil(v)));
  }, []);

  return {
    filterValues,
    setFilterValues,
    handleFilterSubmit,
    handleFilterTypeChange,
  };
}; 