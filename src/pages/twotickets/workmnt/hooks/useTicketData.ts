import { useMemo } from 'react';
import { useInfiniteScroll } from 'ahooks';
import { getElectricalOnceListAPI } from '@/services/twotickets/workmnt/electrical';

interface FilterValues {
  [key: string]: any;
}

const PAGE_SIZE = 20;

/**
 * 票据数据获取 Hook
 * @param ticketType 票据类型
 * @param filterValues 筛选条件
 * @returns 数据列表和相关操作方法
 */
export const useTicketData = (ticketType: string, filterValues: FilterValues) => {
  // 分页配置
  const {
    data: dataList,
    loading,
    loadMore,
    loadingMore,
    reload: handleReload,
  } = useInfiniteScroll(
    (d) => {
      // 设置参数
      const page = d ? Math.ceil(d.list.length / PAGE_SIZE) + 1 : 1;

      return getElectricalOnceListAPI({ 
        ticketType: ticketType, 
        pageNum: page, 
        pageSize: PAGE_SIZE, 
        ...filterValues 
      }).then((res) => ({
        total: res?.data?.total ? Number(res?.data.total) : 0,
        list: res?.data?.list || [],
      }));
    },
    {
      threshold: 1000,
      reloadDeps: [filterValues],
    },
  );

  // 下拉刷新处理
  const onRefresh = async () => {
    await handleReload();
  };

  // 判断是否还有更多数据
  const hasMore = useMemo(() => {
    return dataList && dataList.list.length < dataList.total;
  }, [dataList, loadingMore, loading]);

  return {
    dataList,
    loading,
    loadMore,
    loadingMore,
    onRefresh,
    hasMore,
  };
}; 