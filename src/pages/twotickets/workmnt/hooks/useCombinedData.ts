import { useDeepCompareEffect } from 'ahooks';
import { useEffect, useState } from 'react';

type UseCombinedDataResult<T> = {
  data: T | null;
  loading: boolean;
  error: Error | null;
};

type UseCombinedDataProps<P> = {
  fetchers: Array<(params: P) => Promise<any>>;
  onResponse?: (responses: any[]) => any; // 新增：接口返回处理
};

export function useCombinedData<P, T>({
  fetchers,
  onResponse,
}: UseCombinedDataProps<P>, params: P): UseCombinedDataResult<T> {
  const [data, setData] = useState<T>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useDeepCompareEffect(() => {
    setLoading(true);
    setError(null);
    // 新增：支持只调用部分 fetcher，未调用的 fetcher 返回上次结果
    // 用于缓存上次的 responses
    const prevResponsesRef = (useCombinedData as any)._prevResponsesRef || { current: [] };
    (useCombinedData as any)._prevResponsesRef = prevResponsesRef;

    // params 里可选传递 fetcherIndexes: number[] 指定要请求的 fetcher 下标
    const fetcherIndexes: number[] | undefined = (params as any).fetcherIndexes;
    delete (params as any).fetcherIndexes;
    // 构造 fetcher 调用的 Promise 数组
    const promises = fetchers.map((fetcher, idx) => {
      if (!fetcherIndexes || fetcherIndexes.includes(idx)) {
      // 需要请求
      return fetcher(params).then((res) => res.data);
      } else {
      // 不请求，直接返回上次的结果
      return Promise.resolve(prevResponsesRef.current[idx]);
      }
    });

    Promise.all(promises)
      .then((responses) => {
      prevResponsesRef.current = responses; // 缓存本次 responses
      const processed = onResponse ? onResponse(responses) : responses;
      setData(processed);
      })
      .catch((err) => setError(err as Error))
      .finally(() => setLoading(false));
  }, [params]);
  return { data, loading, error };
}
