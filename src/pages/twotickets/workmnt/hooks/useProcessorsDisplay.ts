import { useCallback } from 'react';

/**
 * 处理人显示 Hook
 * @param ticketType 票据类型
 * @param userOptions 用户选项数据（可选，仅动火票需要）
 * @returns getProcessorsDisplay 函数
 */
export const useProcessorsDisplay = (ticketType: string, userOptions: any[] = []) => {
  // 只在动火票时使用用户选项数据
  const usersOption2 = (ticketType === '6' || ticketType === '7') ? userOptions : [];

  // 处理动火执行人的特殊逻辑
  const processFireExecuteName = useCallback((fireExecuteName?: string) => {
    if (!fireExecuteName) return '';

    const names: string[] = [];
    fireExecuteName.split(',').forEach((id: string) => {
      if (id.includes('custom')) {
        names.push(id.split('-')?.[2] || '');
      } else {
        const user = usersOption2.find((item: any) => item.value === id);
        if (user) {
          names.push(user.label);
        }
      }
    });
    return names.join(',');
  }, [usersOption2]);

  // 根据ticketType生成处理人显示
  const getProcessorsDisplay = useCallback((item: any, ticketType: string) => {
    const processors: string[] = [];

    switch (ticketType) {
      case '1': // 电气一
        if (item.headName) processors.push(`${item.headName}（负责人）`);
        if (item.ownerSignerName) processors.push(`${item.ownerSignerName}（业主签发人）`);
        if (item.allowName) processors.push(`${item.allowName}（开工许可人）`);
        if (item.finalAllowUserName) processors.push(`${item.finalAllowUserName}（终结许可人）`);
        if (item.dutyName) processors.push(`${item.dutyName}（值班负责人）`);
        break;
      case '2': // 电气二
        if (item.headName) processors.push(`${item.headName}（负责人）`);
        if (item.ownerSignerName) processors.push(`${item.ownerSignerName}（业主签发人）`);
        if (item.allowName) processors.push(`${item.allowName}（开工许可人）`);
        if (item.finalAllowUserName) processors.push(`${item.finalAllowUserName}（终结许可人）`);
        break;
      case '3': // 机械1
        if (item.headName) processors.push(`${item.headName}（负责人）`);
        if (item.ownerSignerName) processors.push(`${item.ownerSignerName}（业主签发人）`);
        if (item.allowName) processors.push(`${item.allowName}（开工许可人）`);
        if (item.finalAllowUserName) processors.push(`${item.finalAllowUserName}（终结许可人）`);
        if (item.dutyName) processors.push(`${item.dutyName}（值班负责人）`);
        break;
      case '8': // 机械2
        if (item.headName) processors.push(`${item.headName}（负责人）`);
        if (item.ownerSignerName) processors.push(`${item.ownerSignerName}（业主签发人）`);
        if (item.allowName) processors.push(`${item.allowName}（开工许可人）`);
        if (item.finalAllowUserName) processors.push(`${item.finalAllowUserName}（终结许可人）`);
        if (item.dutyName) processors.push(`${item.dutyName}（值班负责人）`);
        break;
      case '4': // 水工作业工作票一
        if (item.headName) processors.push(`${item.headName}（负责人）`);
        if (item.ownerSignerName) processors.push(`${item.ownerSignerName}（业主签发人）`);
        if (item.allowName) processors.push(`${item.allowName}（开工许可人）`);
        if (item.finalAllowUserName) processors.push(`${item.finalAllowUserName}（终结许可人）`);
        if (item.dutyName) processors.push(`${item.dutyName}（值班负责人）`);
        break;
      case '5': // 水工作业工作票二
        if (item.headName) processors.push(`${item.headName}（负责人）`);
        if (item.ownerSignerName) processors.push(`${item.ownerSignerName}（业主签发人）`);
        if (item.allowName) processors.push(`${item.allowName}（开工许可人）`);
        if (item.finalAllowUserName) processors.push(`${item.finalAllowUserName}（终结许可人）`);
        if (item.dutyName) processors.push(`${item.dutyName}（值班负责人）`);
        break;
      case '6': // 动火1
        if (item.headName) processors.push(`${item.headName}（负责人）`);
        if (item.fireExecuteName) {
          const processedName = processFireExecuteName(item.fireExecuteName);
          if (processedName) processors.push(`${processedName}（动火执行人）`);
        }
        if (item.ownerSignerName) processors.push(`${item.ownerSignerName}（签发人）`);
        if (item.allowName) processors.push(`${item.allowName}（开工许可人）`);
        if (item.finalAllowUserName) processors.push(`${item.finalAllowUserName}（终结许可人）`);
        break;
      case '7': // 动火2
        if (item.headName) processors.push(`${item.headName}（负责人）`);
        if (item.fireExecuteName) {
          const processedName = processFireExecuteName(item.fireExecuteName);
          if (processedName) processors.push(`${processedName}（动火执行人）`);
        }
        if (item.ownerSignerName) processors.push(`${item.ownerSignerName}（签发人）`);
        if (item.allowName) processors.push(`${item.allowName}（许可人）`);
        if (item.finalAllowUserName) processors.push(`${item.finalAllowUserName}（最终许可人）`);
        break;
      default:
        // 如果没有匹配的类型，使用原来的member字段
        if (item.member) {
          return item.member.split(',').join('，');
        }
        break;
    }

    return processors.length > 0 ? processors.join('、') : '暂无成员';
  }, [processFireExecuteName]);

  return { getProcessorsDisplay };
}; 