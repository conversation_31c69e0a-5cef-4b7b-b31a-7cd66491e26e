import { useCallback } from 'react';
import { history } from 'umi';
import { getTicketDetailPageUrl } from '@/constant';

/**
 * 票据操作 Hook
 * @param workmntType 工作票类型
 * @param type 票据子类型
 * @returns 操作方法
 */
export const useTicketActions = (workmntType: string, type: string) => {
  // 处理流程
  const handleProcess = useCallback((ticketId: string) => {
    history.push(getTicketDetailPageUrl(workmntType, type, ticketId, 'edit'));
  }, [workmntType, type]);

  // 查看详情
  const handleView = useCallback((ticketId: string) => {
    history.push(getTicketDetailPageUrl(workmntType, type, ticketId, 'view'));
  }, [workmntType, type]);

  return {
    handleProcess,
    handleView,
  };
}; 