import { useMemo } from 'react';
import { FormItemType } from '@/component/dynamic-form-items/dynamic-form-items.component';
import { MenuItem } from '@/component/header-filter/header-filter.component';
import { AUDIT_RESULT_OPTIONS, ELECTRICAL_EXECUTE_STATUS_OPTIONS } from '../constants/selectOptions';

/**
 * 表单配置 Hook
 * @param ticketStatusOptions 票据状态选项
 * @param organizationTreeData 组织树数据
 * @returns 表单配置项和菜单项
 */
export const useFormConfig = (ticketStatusOptions: any[], organizationTreeData: any) => {
  const DEFAULT_TEXT_AREA_ROWS = 2;

  // 表单项配置抽离
  const GENERAL_FORM_ITEMS = useMemo(() => [
    {
      type: FormItemType.SELECTOR,
      name: 'auditResult',
      label: '审查结果',
      options: AUDIT_RESULT_OPTIONS,
      defaultValue: [''],
      fieldProps: {
        columns: 3,
      },
    },
    {
      type: FormItemType.SELECTOR,
      name: 'electricalExecuteStatus',
      label: '审查结果',
      options: ELECTRICAL_EXECUTE_STATUS_OPTIONS,
      defaultValue: [''],
      fieldProps: {
        columns: 3,
      },
    },
    {
      type: FormItemType.SELECTOR,
      name: 'status',
      label: '工作票状态',
      options: ticketStatusOptions,
      defaultValue: [''],
      fieldProps: {
        columns: 3,
      },
    },
  ], [ticketStatusOptions]);

  const UNIT_FORM_ITEMS = useMemo(() => [
    {
      type: FormItemType.LINKED_SELECTION,
      name: 'unitId',
      columns: 2,
      data: organizationTreeData || [],
      style: {
        height: '100%',
      },
      defaultValue: [], // 添加默认值以满足 FormItemConfig 类型要求
    },
  ], [organizationTreeData]);

  // 搜索表单项配置
  const SEARCH_FORM_ITEMS = useMemo(() => [
    {
      type: FormItemType.TEXT_AREA,
      name: 'workTask',
      label: '工作任务',
      placeholder: '请输入',
      rows: DEFAULT_TEXT_AREA_ROWS,
      defaultValue: '',
    },
    {
      type: FormItemType.TEXT_AREA,
      name: 'ticketCode',
      label: '工作票编号',
      placeholder: '请输入',
      rows: DEFAULT_TEXT_AREA_ROWS,
      defaultValue: '',
    },
    {
      type: FormItemType.TEXT_AREA,
      name: 'operatorName',
      label: '设备名称',
      placeholder: '请输入',
      rows: DEFAULT_TEXT_AREA_ROWS,
      defaultValue: '',
    },
    {
      type: FormItemType.TEXT_AREA,
      name: 'headName',
      label: '工作负责人',
      placeholder: '请输入',
      rows: DEFAULT_TEXT_AREA_ROWS,
      defaultValue: '',
    },
    {
      type: FormItemType.TEXT_AREA,
      name: 'ownerSignerName',
      label: '签发人',
      placeholder: '请输入',
      rows: DEFAULT_TEXT_AREA_ROWS,
      defaultValue: '',
    },
    {
      type: FormItemType.TEXT_AREA,
      name: 'allowName',
      label: '开工许可人',
      placeholder: '请输入',
      rows: DEFAULT_TEXT_AREA_ROWS,
      defaultValue: '',
    },
    {
      type: FormItemType.TEXT_AREA,
      name: 'finalAllowUserName',
      label: '终结许可人',
      placeholder: '请输入',
      rows: DEFAULT_TEXT_AREA_ROWS,
      defaultValue: '',
    },
  ], []);

  // 自定义菜单项及表单配置
  const customMenuItems: MenuItem[] = useMemo(
    () => [
      {
        label: '综合筛选',
        value: 'general',
        formItems: GENERAL_FORM_ITEMS,
      },
      {
        label: '操作单位',
        value: 'unitId',
        formItems: UNIT_FORM_ITEMS,
      },
      {
        label: '搜索',
        value: 'search',
        formItems: SEARCH_FORM_ITEMS,
      },
    ],
    [GENERAL_FORM_ITEMS, UNIT_FORM_ITEMS, SEARCH_FORM_ITEMS],
  );

  return {
    GENERAL_FORM_ITEMS,
    UNIT_FORM_ITEMS,
    SEARCH_FORM_ITEMS,
    customMenuItems,
  };
}; 