import { useMemo } from 'react';
import { WORKMNT_ELECTRICAL_STATUSES_MAP } from '@/constant';

// 状态对应的颜色映射
const StatusColors: Record<string, string> = {
  '1': '#FA8C16', // 准备中
  '2': '#1890FF', // 待外委签发人签发
  '3': '#1890FF', // 待业主签发人签发
  '4': '#1890FF', // 待负责人确认提交
  '5': '#1890FF', // 待值班负责人确认
  '6': '#1890FF', // 待值长批准
  '7': '#1890FF', // 待许可人审核
  '8': '#1890FF', // 待执行确认
  '9': '#52C41A', // 执行中
  '10': '#FA8C16', // 工作延期
  '11': '#FA8C16', // 工作间断
  '12': '#FA8C16', // 负责人变更
  '13': '#1890FF', // 待工作许可人审批
  '14': '#1890FF', // 待值长确认
  '15': '#1890FF', // 待许可人终结
  '16': '#1890FF', // 待负责人确认
  '17': '#1890FF', // 待监察审核
  '18': '#52C41A', // 已审查
  '19': '#F5222D', // 已作废
  '20': '#F5222D', // 已取消
  default: '#999999', // 默认颜色
};

/**
 * 根据状态值获取状态信息的hook
 * @returns 状态信息处理工具对象
 */
export const useStatusInfo = (ticketType = '1') => {
  const statusMap = useMemo(() => {
    // 构建状态映射，方便查询
    const map = new Map();
    for (let item in WORKMNT_ELECTRICAL_STATUSES_MAP[ticketType]) {
      if (WORKMNT_ELECTRICAL_STATUSES_MAP[ticketType][item]) {
        const status = WORKMNT_ELECTRICAL_STATUSES_MAP[ticketType][item];
        map.set(item, status);
      }
    }
    return map;
  }, []);

  /**
   * 获取状态对象
   * @param state 状态值
   * @returns 状态对象或undefined
   */
  const getStatusObject = (state?: string | number) => {
    if (!state) return undefined;
    return statusMap.get(String(state));
  };

  /**
   * 获取状态标签
   * @param state 状态值
   * @returns 状态标签或空字符串
   */
  const getStatusLabel = (state?: string | number) => {
    if (!state) return '';
    const status = statusMap.get(String(state));
    return status ? status.label : '';
  };

  /**
   * 获取状态颜色
   * @param state 状态值
   * @returns 状态颜色代码
   */
  const getStatusColor = (state?: string | number) => {
    if (!state) return StatusColors.default;
    return WORKMNT_ELECTRICAL_STATUSES_MAP?.[ticketType]?.[state]?.color || StatusColors.default;
  };

  return {
    getStatusObject,
    getStatusLabel,
    getStatusColor,
    statusMap,
    statusColors: StatusColors,
  };
};
