.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
  background-color: #F5F5F5;
}

.content {
  flex: 1;
  overflow: auto;
  -webkit-overflow-scrolling: touch; /* 增强iOS滚动体验 */

  :global {
    .adm-list-card {
      position: relative;
    }
  }
}

.pullingText {
  font-size: 38px;
}

.row {
  .title {
    line-height: 69.5px;
    font-size: 48px;
    color: rgba(17, 84, 237, 1);
    margin-bottom: 15px;
    display: flex;
    flex-direction: row;
    .label{
      flex: 1;
      margin-right: 1.3889vw;
    }
  }

  .dealwith {
    position: absolute;
    right: 36px;
    top: 22px;
    z-index: 2;
    :global {
      .adm-button.adm-button-small {
        height: 82px;
        line-height: 52px;
        padding: 15px 40px;
        font-size: 36px !important;
      }
    }
  }

  .item {
    display: flex;
    font-size: 36px;
    line-height: 52px;
    min-height: 52px;
    color: rgba(153, 153, 153, 1);

    &+.item {
      margin-top: 18px;
    }

    .label {
      flex-shrink: 0;
      flex-grow: 0;
    }

    .value {
      color: rgba(51, 51, 51, 1);
    }

    .name-value {
      color: rgba(51, 51, 51, 1);
      display: -webkit-box;
      -webkit-line-clamp: 2;
      font-size: 42px;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
      line-height: 1.4;
    }
  }
}