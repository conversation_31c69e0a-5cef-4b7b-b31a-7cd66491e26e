import { <PERSON>up, Grid, Button, Form, DatePicker, DatePickerRef, Input, ActionSheet, TextArea, Toast } from 'antd-mobile';
import { CloseOutline, DeleteOutline, RightOutline } from 'antd-mobile-icons';
import dayjs from 'dayjs';
import { memo, RefObject, useState } from 'react';
import { styled } from 'umi';
import SignatureUserPicker from '../../../components/dynamic-form/items/signature-selection/signature-selection.component';
import { strategyFactory } from '../../../components/dynamic-form/strategy-factory';
import { DynamicFormItem } from '../../../components/dynamic-form/dynamic-form-item';

const CHECK_RESULT = ['不合格', '合格'];

// 点击触发ActionSheet选择CAN_RUN_STATUS上滑动选择
// 注册canRun类型，点击触发ActionSheet选择CAN_RUN_STATUS
strategyFactory.registerStrategy('canRun', {
  component: (props: any) => {
    const { value, onChange, disabled } = props;
    const handleClick = () => {
      if (disabled) return;
      const actionSheet = ActionSheet.show({
        actions: CHECK_RESULT.map((item: { label: any }, idx: any) => ({
          text: item,
          key: idx,
          onClick: () => {
            onChange?.(idx);
            actionSheet.close();
          },
        })),
        cancelText: '取消',
      });
    };

    return (
      <div
        onClick={handleClick}
        style={{
          cursor: disabled ? 'not-allowed' : 'pointer',
          width: '100%',
          textAlign: 'right',
          color: CHECK_RESULT?.[value] ? '#333333' : '#999999',
          lineHeight: '6.1676vw',
        }}
      >
        {CHECK_RESULT[value] || '请选择'}
      </div>
    );
  },
});

interface TestingRecordsPopupProps {
  type: 'add' | 'edit';
  record?: any;
  children: React.ReactNode;
  onDelete: () => void;
  onSubmit: (values: any) => void;
}

const TestingRecordsPopupWrapper = styled(Popup)`
  .popup-title {
    box-sizing: border-box;
    font-family: PingFang SC;
    font-size: 4.4444vw;
    font-weight: 400;
    letter-spacing: 0vw;
    line-height: 6.4353vw;
    color: rgba(0, 0, 0, 1);
    text-align: center;
    vertical-align: top;
    margin-top: 2.9631vw;
    margin-bottom: 2.9631vw;
  }
  .popup-container {
    flex: 1;
    box-sizing: border-box;
    width: 100%;
    position: relative;
    margin-bottom: 3.3333vw;
    border-top: 0.0925vw solid rgba(230, 230, 230, 1);
    border-bottom: 0.0925vw solid rgba(230, 230, 230, 1);
    .body {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;
      background-color: #fff;
      .adm-list,
      .adm-list-body,
      .adm-list-body-inner {
        height: 100%;
      }
      .adm-form-item-label {
        font-size: 4.4444vw;
        font-weight: 500;
        letter-spacing: 0vw;
        line-height: 6.4814vw;
        color: rgba(51, 51, 51, 1);
        margin-bottom: 3.6111vw;
      }
      .adm-text-area {
        box-sizing: border-box;
        padding: 2.0369vw 4.0742vw;
        border-radius: 1.8519vw;
        background: rgba(245, 245, 245, 1);
        // 提示文字改小点
        .adm-text-area-element::placeholder {
          font-size: 3.3333vw;
        }
      }
    }
    .adm-input-placeholder {
      font-size: 4.2593vw;
      font-weight: 400;
      letter-spacing: 0vw;
      line-height: 6.1676vw;
      color: rgba(153, 153, 153, 1);
      text-align: right;
    }
    .adm-text-area {
      font-size: 4.2593vw;
      font-weight: 400;
      letter-spacing: 0vw;
      line-height: 6.1676vw;
      color: rgba(51, 51, 51, 1);
      text-align: right;
      --text-align: 'right';
    }
  }
  .popup-footer {
    box-sizing: border-box;
    overflow: hidden;
    /* border-radius: 3.3333vw; */
    margin: 0 3.3333vw 2.2222vw;
    /* border: 0.3703vw solid rgba(17, 84, 237, 1); */
    /* .btn {
      display: inline-block;
      width: 50%;
      text-align: center;
      height: 11.1111vw;
      line-height: 11.1111vw;
      font-size: 3.8881vw;
      font-weight: 400;
      letter-spacing: 0vw;
      color: #2b488c;
    
    } */
  }
`;

const TestingRecordsPopup = (props: TestingRecordsPopupProps) => {
  const { record = {}, children, onDelete, onSubmit } = props;
  const [form] = Form.useForm();
  const [visible, setVisible] = useState(false);
  const onClose = () => setVisible(false);
  return (
    <>
      <div onClick={() => setVisible(true)}>{children}</div>
      <TestingRecordsPopupWrapper
        visible={visible}
        onMaskClick={() => {
          setVisible(false);
        }}
        closeIcon={record?.checkItem ? <DeleteOutline /> : <CloseOutline />}
        showCloseButton
        destroyOnClose
        onClose={() => {
          if (record?.checkItem) {
            onDelete();
          }
          setVisible(false);
        }}
        bodyStyle={{
          borderRadius: '0 0 .7408vw .7408vw',
          minHeight: '90vh',
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        <div className="popup-title">检测记录</div>
        <div className="popup-container">
          <Form
            layout="horizontal"
            form={form}
            onFinish={(values) => {
              const { checkTime, checkUser, ...rest } = values;
              if (checkTime === '' || checkTime === undefined || checkTime === null) {
                Toast.show('请选择检测时间');
                return;
              } else if (checkUser === '' || checkUser === undefined || checkUser === null) {
                Toast.show('请选择检测人员');
                return;
              } else if (rest?.checkResult === '' || rest?.checkResult === undefined || rest?.checkResult === null) {
                Toast.show('请选择检测结果');
                return;
              } else if (rest?.checkItem === '' || rest?.checkItem === undefined || rest?.checkItem === null) {
                Toast.show('请填写检测项目');
                return;
              }

              const newValues = {
                id: Date.now(),
                ...rest,
                memo: Number(rest?.memo),
                checkResult: `${rest?.checkResult}`,
                checkUserId: checkUser?.userId,
                checkUserName: checkUser?.name,
                checkTime: checkTime ? dayjs(checkTime).format('YYYY-MM-DD HH:mm:ss') : null,
              };
              onSubmit(newValues);
              onClose();
            }}
            initialValues={{
              checkTime: record?.checkTime ? new Date(record?.checkTime) : null,
              checkUser: record?.checkUserId ? { id: record?.checkUserId, name: record?.checkUserName } : null,
              checkResult: record?.checkResult,
              checkRemark: record?.checkRemark,
              memo: record?.memo,
              checkItem: record?.checkItem,
              remark: record?.remark,
            }}
          >
            <Form.Item
              name="checkTime"
              label="检测时间"
              trigger="onConfirm"
              // initialValue={data?.checkTime ? dayjs(data?.checkTime, 'YYYY-MM-DD HH:mm:ss').toDate() : null}
              arrow={false}
              extra={<RightOutline />}
              onClick={(e, datePickerRef: RefObject<DatePickerRef>) => {
                datePickerRef.current?.open();
              }}
            >
              <DatePicker precision="minute">
                {(value) => (
                  <div
                    className={value ? 'adm-text-area' : 'adm-input-placeholder'}
                    style={{
                      textAlign: 'right',
                    }}
                  >
                    {value ? dayjs(value).format('YYYY/MM/DD HH:mm') : '请选择'}
                  </div>
                )}
              </DatePicker>
            </Form.Item>
            <Form.Item
              name="checkUser"
              label="检测人"
              trigger="onChange"
              normalize={(value) => {
                return value[0];
              }}
              // initialValue={data?.checkResult}
              arrow={false}
              extra={<RightOutline />}
            >
              <SignatureUserPicker
                placeholder="请选择"
                multiple={false}
                render={(value: any) => {
                  console.log(value);
                  if (!value) return <div className="adm-input-placeholder">请选择</div>;
                  return <div className="adm-text-area">{value.name}</div>;
                }}
              />
            </Form.Item>
            <Form.Item
              name="checkItem"
              label="检测项目"
              trigger="onChange"
              // initialValue={data?.checkRemark}
              arrow={false}
            >
              <Input className="adm-text-area" placeholder="请输入" />
            </Form.Item>
            <Form.Item
              name="memo"
              label="检测值"
              trigger="onChange"
              // initialValue={data?.checkRemark}
              arrow={false}
            >
              <Input type='number' className="adm-text-area" placeholder="请输入" />
            </Form.Item>
            <DynamicFormItem strategyType={'canRun'} label="检测结果" name="checkResult" arrow />
            <Form.Item
              name="remark"
              label="备注"
              trigger="onChange"
              // initialValue={data?.checkRemark}
              layout="vertical"
            >
              <TextArea
                className="adm-text-area"
                style={{
                  '--text-align': 'left',
                }}
                placeholder="请输入"
                maxLength={100}
                rows={5}
                showCount
              />
            </Form.Item>
          </Form>
        </div>
        <div className="popup-footer">
          <Grid columns={12} gap={15}>
            <Grid.Item span={5}>
              <Button
                color="primary"
                block
                fill="outline"
                style={{
                  '--border-radius': '3.3333vw',
                }}
                className="btn"
                onClick={() => {}}
              >
                取消
              </Button>
            </Grid.Item>
            <Grid.Item span={7}>
              <Button
                color="primary"
                block
                style={{
                  '--border-radius': '3.3333vw',
                }}
                className="btn btn-submit"
                onClick={() => {
                  form.submit();
                }}
              >
                提交
              </Button>
            </Grid.Item>
          </Grid>
        </div>
      </TestingRecordsPopupWrapper>
    </>
  );
};

export default TestingRecordsPopup;
