import StringUtils from '@/utils/str';
import { WorkFireCheck } from '../../../types';
import { List, Space } from 'antd-mobile';
import { styled } from 'umi';
import { useUserSelectOptions } from '@/hooks/useUserSelectOptions';
import { useMemo } from 'react';
import dayjs from 'dayjs';
import { color } from 'echarts';
import { EditFill } from 'antd-mobile-icons';
import TestingRecordsPopup from './testing-record-popue.component';
import _ from 'lodash';

const TEST_REUST = [
  {
    label: '不合格',
    color: 'rgba(252, 89, 90, 1)',
  },
  {
    label: '合格',
    color: 'rgba(0, 172, 193, 1)',
  },
];

interface TestingRecordProps {
  index: number;
  data: WorkFireCheck;
  canEdit?: boolean;
  onChange: (data: WorkFireCheck, type: 'del' | 'edit') => void;
}

const TestingRecordsWrapper = styled(List)`
  --border-top: none;
  --border-bottom: none;
  .header {
    display: flex;
    font-size: 3.8889vw;
    font-weight: 400;
    letter-spacing: 0vw;
    line-height: 5.6315vw;
    color: rgba(51, 51, 51, 1);
    margin-bottom: 1.2963vw;
    position: relative;
    padding-right: 3.7037vw;
    &-status {
      font-size: 3.8889vw;
      font-weight: 400;
      letter-spacing: 0vw;
      line-height: 5.6315vw;
      color: rgba(1, 208, 102, 1);
    }
    .edit-icon {
      position: absolute;
      right: 1.8519vw;
      top: 50%;
      transform: translateY(-50%);
    }
  }
  .describe {
    display: flex;
    align-items: center;
    font-size: 3.3333vw;
    font-weight: 400;
    letter-spacing: 0vw;
    line-height: 4.8269vw;
    color: rgba(102, 102, 102, 1);
    padding-left: 6.6667vw;
  }
  .remark {
    margin-top: 1.8519vw;
    border-radius: 1.6667vw;
    background: rgba(245, 245, 245, 1);
    padding: 2.2222vw 3.2407vw;
    font-size: 3.3333vw;
    font-weight: 400;
    letter-spacing: 0vw;
    line-height: 4.8269vw;
    color: rgba(51, 51, 51, 1);
  }
`;

const TestingRecord = (props: TestingRecordProps) => {
  const { index, data, canEdit = false, onChange } = props;
  return (
    <div>
      <Space className="header">
        <span className="header-index">{index}、</span>
        <span className="header-title">{data.checkItem}</span>
        <span
          className="header-status"
          style={{
            color: TEST_REUST[data.checkResult].color,
          }}
        >
          {TEST_REUST[data.checkResult].label}
        </span>
        {canEdit ? <TestingRecordsPopup
          type={'add'}
          record={{
            index,
            ...data,
          }}
          onDelete={() => {
            onChange(data, 'del');
          }}
          onSubmit={(values) => {
            onChange(values, 'edit');
          }}
        >
          <EditFill className="edit-icon" />
        </TestingRecordsPopup>:null}
      </Space>
      <div
        className="describe"
        dangerouslySetInnerHTML={{
          __html: StringUtils.join([
            `<div class="time">${dayjs(data.checkTime).format('YYYY/MM/DD HH:mm')}</div>`,
            `<div class="name">${data.checkUserName}(检测人)</div>`,
          ]),
        }}
      ></div>
      <div className="remark">备注：{data.remark}</div>
    </div>
  );
};

interface TestingRecordsProps {
  data: WorkFireCheck[];
  canEdit?: boolean;
  onChange: (data: WorkFireCheck, index: number, type: 'del' | 'edit') => void;
}

const TestingRecords = (props: TestingRecordsProps) => {
  const { data, canEdit = false, onChange } = props;
  const usersOption = useUserSelectOptions('id');
  const list = useMemo(() => {
    return data?.map((item) => ({
      ...item,
      checkUserName: usersOption?.find((user) => user.value === item.checkUserId)?.label || '--',
    }));
  }, [data, usersOption]);
  return (
    <TestingRecordsWrapper>
      {data.map((item, index) => (
        <List.Item key={item.id}>
          <TestingRecord data={item} index={index + 1} onChange={(data, type) => onChange(data, index, type)} canEdit={canEdit} />
        </List.Item>
      ))}
    </TestingRecordsWrapper>
  );
};

export default TestingRecords;
