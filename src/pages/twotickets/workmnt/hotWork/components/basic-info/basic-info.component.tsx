// ...省略 import 和 styled 代码
import { Card, Grid, Divider, Space, List, Form, Checkbox } from 'antd-mobile';
import { styled } from 'umi';
import dayjs from 'dayjs';
import ImageView from '@/component/image-view/image-view.component';
import useInterleave from '@/hooks/useInterleave';
import { ListCard } from '@/component/list-card/list-card.component';
import { OperationFileList } from '@/styles/twotickets.style';
import React, { useEffect, useMemo, useState } from 'react';
import { getDictList } from '@/services/common.service';
import renderFieldFactory, { FieldConfig } from '../../../components/render-strategy/render-strategy.component';
import RenderFields from '../../../components/render-strategy/render-strategy.component';
import { ReviewRecordComponent } from '@/pages/twotickets/common/[type]/components/review-record/review-record.component';
import { RecordFieldArrays, RecordFieldArraysTwo } from './review-record-config';
import { HotWorkReviewRecordComponent } from '@/pages/twotickets/common/[type]/components/hot-work-review-record/hot-work-review-record.component';
import { AttachmentItemRow } from '@/pages/twotickets/common/[type]/components/attachment-content/attachment-content.component';
import { getUserList } from '@/services/nav.service';
import { useRequest } from 'ahooks';

const BasicInfoComponentWrapper = styled(Space)`
  width: 100%;
  font-family: PingFang SC;
  .adm-card-body {
    padding-bottom: 1.8rem;
  }
  .title {
    font-size: 1.6rem;
    font-weight: 500;
    letter-spacing: 0rem;
    line-height: 2.3333rem;
    color: rgba(56, 56, 56, 1);
    text-align: left;
  }
  .device {
    font-size: 3.3333vw;
    font-weight: 400;
    letter-spacing: 0vw;
    line-height: 4.8269vw;
    color: rgba(102, 102, 102, 1);
    margin-top: 1.1111vw;
    .name {
      font-size: 3.3333vw;
      font-weight: 400;
    }
  }

  .remark {
    font-size: 1.5333rem;
    font-weight: 400;
    letter-spacing: 0rem;
    line-height: 2.2203rem;
    color: rgba(51, 51, 51, 1);
  }
`;

const SplitPoint = (
  <div
    style={{
      display: 'inline-block',
      width: '.7408vw',
      height: '.7408vw',
      borderRadius: '.3703vw',
      margin: '0 1.1111vw',
      backgroundColor: 'rgba(102, 102, 102, 1)',
    }}
  />
);

type BasicInfoComponentProps = {
  data: any;
  pageType: 'view' | 'edit';
  mutate: any;
};

const BASIC_DATA: FieldConfig[][] = [
  [
    { label: '单位', field: 'unitName' },
    { label: '成员', field: 'numbers', type: 'members' },
    { label: '班组', field: 'teamsName' },
  ],
  [
    { label: '工作票编号', field: 'ticketCode' },
    { label: '计划时间', field: 'planTime', type: 'planTime' },
    { label: '工作地点', field: 'workLocation' },
    { label: '创建时间', field: 'createTime', type: 'date' },
  ],
  [
    {
      label: <div className="fire_method" style={{
        marginBottom: '3.1746vw',
      }}>动火方式</div>,
      field: 'fireMethod',
      type: 'fill_check',
      hideLabel: true,
      hideSemicolon: true,
      layout: 'vertical',
      options: async () => {
        const { data: fire_method } = await getDictList('fire_method');
        return [
          ...(fire_method || [])?.map((item: any) => ({
            label: item.dictName,
            value: item['dictName'],
            type: item.dictValue,
          })),
        ];
      },
    },
    {
      label: null,
      field: 'fireMethodHasOther',
      type: 'fill_check',
      hideLabel: true,
      hideSemicolon: true,
      layout: 'vertical',
      options: async () => {
        return [
          {
            label: '其他',
            value: 'other',
          },
        ];
      },
    },
    {
      label: <span className="describe">补充说明: </span>,
      field: 'fireMethodOther',
      hideSemicolon: true,
      type: 'display_render',
      styles: {
        item: {
          display: 'inline-block',
        },
      },
      render: (value, data) => {
        if (!value) return null;
        return (
          <span
            className="describe_info"
            style={{
              flex: 1,
            }}
          >
            {value}
          </span>
        );
      },
    },
  ],
  [
    {
      label: <div className="subtitle">运行部门应采取的安全措施</div>,
      field: 'operationalMeasures',
      hideSemicolon: true,
      type: 'display_render',
      layout: 'vertical',
      render: (value, data) => {
        return <div className="describe_info">{data?.operationalMeasures ?? '-'}</div>;
      },
    },
  ],
  [
    {
      label: <div className="subtitle">动火部门应采取的安全措施</div>,
      field: 'maintenanceMeasures',
      hideSemicolon: true,
      type: 'display_render',
      layout: 'vertical',
      render: (value, data) => {
        return <div className="describe_info">{data?.maintenanceMeasures ?? '-'}</div>;
      },
    },
  ],
  [
    {
      label: <div className="subtitle">备注</div>,
      field: 'memo',
      hideSemicolon: true,
      type: 'display_render',
      layout: 'vertical',
      render: (value, data) => {
        return <div className="describe_info">{data?.memo ?? '-'}</div>;
      },
    },
  ],
];

export const BasicInfoComponent = (props: BasicInfoComponentProps) => {
  const { data, pageType = 'view' } = props;
  const { data: userList } = useRequest(() => getUserList().then((res) => res.data));
  // 根据fireExecuteName字段使用split(',')分割，将fireExecuteName的id转换成Name并重新拼接
  const newData = useMemo(() => {
    if (!data?.fireExecuteName) return data;
    return {
      ...data,
      fireExecuteName: data?.fireExecuteName
        ?.split(',')
        .map((id: any) => !id?.includes("custom-")?(userList?.find((user: { id: any; }) => Number(user.id) === Number(id))?.name || id ):id.split("-")[2])
        .join(','),
    };
  }, [data, userList]);
  return (
    <BasicInfoComponentWrapper direction="vertical">
      <Card>
        <div className="title">{data?.name}</div>
        <div className="device">
          设备信息：<span className="name">{data?.deviceName || '无'}</span>
        </div>
        <Divider />
        <RenderFields fields={BASIC_DATA || []} data={newData} />
      </Card>
      {data?.imgList?.length && <Card title="图片">{ImageView({ imgList: data?.imgList?.map((item) => item.id) })}</Card>}
      {data?.files?.length && (
        <ListCard
          title="附件"
          data={data?.files || []}
          moreLink={`/twotickets/common/check-list/attachment-content/${data.id}?ticketType=${data?.ticketType}&dataKey=files`}
          maxLength={5}
        >
          {(dataList) => (
            <OperationFileList
              style={{
                '--border-top': 'none',
                '--border-bottom': 'none',
              }}
            >
              {dataList?.map((item: any, index: number) => (
                <AttachmentItemRow key={item.id} item={item as any} />
              ))}
            </OperationFileList>
          )}
        </ListCard>
      )}
      {data?.status && Number(data.status) > 2 && (
        <ListCard
          title="流程记录"
          data={
            Number(data?.ticketType) === 6
              ? RecordFieldArrays?.filter((item) => Number(item?.status) < Number(data?.status))
              : RecordFieldArraysTwo?.filter((item) => Number(item?.status) < Number(data?.status))
          }
          moreLink={`/twotickets/common/check-list/hot-work-review-record/${data?.id}?ticketType=${data?.ticketType}`}
          isMore
          maxLength={3}
        >
          {(dataList) => <HotWorkReviewRecordComponent recordFields={dataList as any} data={data} />}
        </ListCard>
      )}
    </BasicInfoComponentWrapper>
  );
};
