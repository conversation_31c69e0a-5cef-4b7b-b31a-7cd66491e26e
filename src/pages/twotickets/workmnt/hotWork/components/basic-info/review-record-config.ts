export const RecordFieldArrays = [
  {
    title: '工作票签发',
    name: '工作票签发',
    status: 1,
    columns: [
      {
        type: 'group',
        columns: [
          {
            title: '工作签发人',
            name: 'signName',
            type: 'input',
            colProps: { span: 12 },
            status: 1,
          },
          {
            title: '签发日期',
            name: 'signTime',
            type: 'input',
            colProps: { span: 12 },
            status: 1,
          }
        ]
      },
      {
        type: 'group',
        columns: [
          {
            title: '工作票业主签发人',
            name: 'ownerSignName',
            type: 'input',
            colProps: { span: 12 },
            status: 2,
          },
          {
            title: '签发日期',
            name: 'ownerSignTime',
            type: 'input',
            colProps: { span: 12 },
            status: 2,
          }]
      }
    ]
  },
  {
    title: '审批',
    name: '审批',
    status: 2.5,
    columns: [
      {
        type: 'table',
        columns: [{
          title: '角色',
          values: ['动火部门负责人', '企业消防管理部门负责人', '企业安监部门主管','企业安监部门负责人', '企业主管安全生产的领导(总工程师)']
        }, {
          title: '人员',
          fields: ['fireHeadName1', 'fireFightingHeadName', 'managerName', 'safetySupervisionName', 'fireHeadName']
        }, {
          title: '审核时间',
          fields: ['fireHeadTime1', 'reviewTime', 'managerTime', 'examineTime', 'headTime']
        }],
        status: [2.5, 3, 3.5, 4, 5]
      },
      // {
      //   title: '动火部门负责人',
      //   name: 'fireHeadName1',
      //   type: 'input',
      //   colProps: { span: 12 },
      //   status: 2.5,
      // },
      // {
      //   title: '审核时间',
      //   name: 'fireHeadTime1',
      //   type: 'input',
      //   colProps: { span: 12 },
      //   status: 2.5,
      // },
      // {
      //   title: '企业消防管理部门负责人',
      //   name: 'fireFightingHeadName',
      //   type: 'input',
      //   colProps: { span: 12 },
      //   status: 3,
      // },
      // {
      //   title: '审核时间',
      //   name: 'reviewTime',
      //   type: 'input',
      //   colProps: { span: 12 },
      //   status: 3,
      // },
      // {
      //   title: '企业安监部门主管',
      //   name: 'managerName',
      //   type: 'input',
      //   colProps: { span: 12 },
      //   status: 3.5,
      // },
      // {
      //   title: '审核时间',
      //   name: 'managerTime',
      //   type: 'input',
      //   colProps: { span: 12 },
      //   status: 3.5,
      // },
      // {
      //   title: '企业安监部门负责人',
      //   name: 'safetySupervisionName',
      //   type: 'input',
      //   colProps: { span: 12 },
      //   status: 4,
      // },
      // {
      //   title: '审核时间',
      //   name: 'examineTime',
      //   type: 'input',
      //   colProps: { span: 12 },
      //   status: 4,
      // },
      // {
      //   title: '企业主管安全生产的领导(总工程师)',
      //   name: 'fireHeadName',
      //   type: 'input',
      //   colProps: { span: 12 },
      //   status: 5,
      // },
      // {
      //   title: '审核时间',
      //   name: 'headTime',
      //   type: 'input',
      //   colProps: { span: 12 },
      //   status: 5,
      // },
      {
        type: 'description',
        columns: [
          {
            title: '批准动火时间',
            name: 'fireTime',
            type: 'input',
            colProps: { span: 12 },
            status: 5,
          }]
      },
    ]
  },
  {
    title: '工作票接收、确认',
    status: 5.3,
    columns: [
      {
        type: 'remark',
        columns: [
          {
            title: null,
            name: 'confirmDesc',
            type: 'textArea',
            colProps: { span: 24 },
            labelCol: { span: 3 },
            status: 5.3,
          },
        ]
      },
      {
        type: 'group',
        columns: [
          {
            title: '接收人',
            name: 'confirmHeadName',
            type: 'input',
            colProps: { span: 12 },
            status: 5.3,
          },
          {
            title: '接收时间',
            name: 'acceptTime',
            type: 'input',
            colProps: { span: 12 },
            status: 5.3,
          }]
      },
    ]
  },
  {
    title: '值长批准',
    status: 5.6,
    columns: [
      {
        type: 'group',
        columns: [
          {
            title: '值长',
            name: 'shiftSupervisorName',
            type: 'input',
            colProps: { span: 12 },
            status: 5.6,
          },
          {
            title: '接收时间',
            name: 'shiftSupervisorTime',
            type: 'input',
            colProps: { span: 12 },
            status: 5.6,
          }]
      },
    ]
  },
  {
    title: '工作票许可',
    status: 6,
    columns: [
      {
        type: 'group',
        columns: [
          {
            title: '运行许可动火时间',
            name: 'allowBeginTime',
            type: 'input',
            colProps: { span: 8 },
            status: 6,
          }]
      },
      {
        type: 'group',
        columns: [
          {
            title: '工作许可人',
            name: 'allowName',
            type: 'input',
            colProps: { span: 8 },
            status: 6,
          },
          {
            title: '许可时间',
            name: 'allowTime',
            type: 'input',
            colProps: { span: 8 },
            status: 6,
          },]
      },
    ]
  },
  {
    title: '动火部门审核意见',
    status: 7,
    columns: [
      {
        type: 'remark',
        columns: [
          {
            title: null,
            name: 'checkResult',
            type: 'textArea',
            colProps: { span: 24 },
            labelCol: { span: 3 },
            wrapperCol: { span: 21 },
            status: 7,
          }]
      }, {
        type: 'table',
        columns: [{
          title: '角色',
          values: ['检测人', '动火执行人', '消防监护人', '动火工作负责人', '动火部门负责人', '企业安监部负责人', '企业主管安全生产的领导(总工程师)']
        }, {
          title: '人员',
          fields: ['checkName', 'signName1', 'signName2', 'signName3', 'signName4', 'signName5', 'leaderName']
        }, {
          title: '审核时间',
          fields: ['checkTime', 'signTime1', 'signTime2', 'signTime3', 'signTime4', 'signTime5', 'reviewTime2']
        }],
        status: [7, 8, 9, 10, 11, 12, 13]
      },
      // {
      //   title: '检测人',
      //   name: 'checkName',
      //   type: 'input',
      //   colProps: { span: 12 },
      //   status: 7,
      // },
      // {
      //   title: '时间',
      //   name: 'checkTime',
      //   type: 'input',
      //   colProps: { span: 12 },
      //   status: 7,
      // },
      // {
      //   title: '动火执行人',
      //   name: 'signName1',
      //   type: 'input',
      //   colProps: { span: 12 },
      //   status: 15,
      // },
      // {
      //   title: '时间',
      //   name: 'signTime1',
      //   type: 'input',
      //   colProps: { span: 12 },
      //   status: 15,
      // },
      // {
      //   title: '消防监护人',
      //   name: 'signName2',
      //   type: 'input',
      //   colProps: { span: 12 },
      //   status: 15,
      // },
      // {
      //   title: '时间',
      //   name: 'signTime2',
      //   type: 'input',
      //   colProps: { span: 12 },
      //   status: 15,
      // },
      // {
      //   title: '动火工作负责人',
      //   name: 'signName3',
      //   type: 'input',
      //   colProps: { span: 12 },
      //   status: 15,
      // },
      // {
      //   title: '时间',
      //   name: 'signTime3',
      //   type: 'input',
      //   colProps: { span: 12 },
      //   status: 15,
      // },
      // {
      //   title: '动火部门负责人',
      //   name: 'signName4',
      //   type: 'input',
      //   colProps: { span: 12 },
      //   status: 15,
      // },
      // {
      //   title: '时间',
      //   name: 'signTime4',
      //   type: 'input',
      //   colProps: { span: 12 },
      //   status: 15,
      // },
      // {
      //   title: '企业安监部负责人',
      //   name: 'signName5',
      //   type: 'input',
      //   colProps: { span: 12 },
      //   status: 15,
      // },
      // {
      //   title: '时间',
      //   name: 'signTime5',
      //   type: 'input',
      //   colProps: { span: 12 },
      //   status: 15,
      // },
      // {
      //   title: '企业主管安全生产的领导(总工程师)',
      //   name: 'leaderName',
      //   type: 'input',
      //   colProps: { span: 12 },
      //   status: 15,
      // },
      // {
      //   title: '时间',
      //   name: 'reviewTime2',
      //   type: 'input',
      //   colProps: { span: 12 },
      //   status: 15,
      // },
      {
        type: 'description',
        columns: [{
          title: '允许动火时间',
          name: 'allowBeginTime2',
          type: 'input',
          colProps: { span: 12 },
          status: 15,
        }]
      }
    ]
  },
  {
    title: '动火票终结确认',
    status: 15,
    columns: [{
      type: 'remark',
      columns: [{
        title: '工作终结',
        name: 'allowTime2',
        type: 'textArea',
        colProps: { span: 24 },
        labelCol: { span: 3 },
        status: 16,
      }]
    },
    {
      type: 'table',
      columns: [{
        title: '角色',
        values: ['工作负责人', '动火执行人', '消防监护人', '运行许可人']
      }, {
        title: '人员',
        fields: ['finalName0', 'finalName1', 'finalName2', 'allowName2']
      }, {
        title: '审核时间',
        fields: ['finalTime0', 'finalTime1', 'finalTime2', 'submitTime']
      }],
      status: [15, 15.3, 15.6, 16]
    },
    // {
    //   title: '工作负责人',
    //   name: 'finalName0',
    //   type: 'input',
    //   colProps: { span: 12 },
    //   status: 15,
    // },
    // {
    //   title: '结束填报时间',
    //   name: 'finalTime0',
    //   type: 'input',
    //   colProps: { span: 12 },
    //   status: 15,
    // },
    // {
    //   title: '动火执行人',
    //   name: 'finalName1',
    //   type: 'input',
    //   colProps: { span: 12 },
    //   status: 15,
    // },
    // {
    //   title: '确认时间',
    //   name: 'finalTime1',
    //   type: 'input',
    //   colProps: { span: 12 },
    //   status: 15,
    // },
    // {
    //   title: '消防监护人',
    //   name: 'finalName2',
    //   type: 'input',
    //   colProps: { span: 12 },
    //   status: 15,
    // },
    // {
    //   title: '确认时间',
    //   name: 'finalTime2',
    //   type: 'input',
    //   colProps: { span: 12 },
    //   status: 15,
    // },
    // {
    //   title: '运行许可人',
    //   name: 'allowName2',
    //   type: 'input',
    //   colProps: { span: 12 },
    //   status: 16,
    // },
    // {
    //   title: '终结工作票时间',
    //   name: 'submitTime',
    //   type: 'input',
    //   colProps: { span: 12 },
    //   status: 16,
    // },
    {
      types: 'remark',
      columns: [{
        title: '其他事项',
        name: 'workMatter',
        type: 'textArea',
        colProps: { span: 24 },
        labelCol: { span: 3 },
        status: 16,
      }]
    }
    ]
  },
  {
    title: '审核',
    status: 20,
    columns: [
      {
        type: 'group',
        columns: [
          {
            title: '审核人',
            name: 'auditName',
            type: 'input',
            colProps: { span: 12 },
            status: 20,
          },
          {
            title: '审核日期',
            name: 'auditTime',
            type: 'input',
            colProps: { span: 12 },
            status: 20,
          }]
      },
      {
        type: 'remark',
        columns: [
          {
            title: '审核结果',
            name: 'auditResult',
            type: 'input',
            colProps: { span: 12 },
            status: 18,
          },
          {
            title: '审核意见',
            name: 'auditOpinion',
            type: 'textArea',
            colProps: { span: 24 },
            labelCol: { span: 3 },
            status: 20,
          }]
      }
    ]
  },
]

export const RecordFieldArraysTwo = [
  {
    title: '工作票签发',
    status: 1,
    columns: [
      {
        type: 'group',
        columns: [
          {
            title: '工作签发人',
            name: 'signName',
            type: 'input',
            colProps: { span: 12 },
            status: 1,
          },
          {
            title: '签发日期',
            name: 'signTime',
            type: 'input',
            colProps: { span: 12 },
            status: 1,
          }]
      },
      {
        type: 'group',
        columns: [
          {
            title: '工作票业主签发人',
            name: 'ownerSignName',
            type: 'input',
            colProps: { span: 12 },
            status: 2,
          },
          {
            title: '签发日期',
            name: 'ownerSignTime',
            type: 'input',
            colProps: { span: 12 },
            status: 2,
          }]
      }
    ]
  },
  {
    title: '动火部门安监人员审核',
    status: 3,
    columns: [
      {
        type: 'group',
        columns: [
          {
            title: '动火部门安监人员',
            name: 'safetySupervisionName',
            type: 'input',
            colProps: { span: 12 },
            status: 3,
          },
          {
            title: '审核时间',
            name: 'examineTime',
            type: 'input',
            colProps: { span: 12 },
            status: 3,
          }]
      },
    ]
  },
  {
    title: '动火部门负责人审核',
    status: 4,
    columns: [
      {
        type: 'group',
        columns: [

          {
            title: '批准动火时间',
            name: 'fireTime',
            type: 'input',
            colProps: { span: 12 },
            status: 4,
          },]
      },
      {
        type: 'group',
        columns: [
          {
            title: '动火部门负责人',
            name: 'fireHeadName',
            type: 'input',
            colProps: { span: 12 },
            status: 4,
          },
          {
            title: '审核时间',
            name: 'headTime',
            type: 'input',
            colProps: { span: 12 },
            status: 4,
          },]
      },

    ]
  },
  {
    title: '值长批准',
    status: 5,
    columns: [
      {
        type: 'group',
        columns: [{
          title: '值长',
          name: 'shiftSupervisorName',
          type: 'input',
          colProps: { span: 12 },
          status: 5,
        },
        {
          title: '审核时间',
          name: 'shiftSupervisorTime',
          type: 'input',
          colProps: { span: 8 },
          status: 5,
        },]
      }
    ]
  },
  {
    title: '工作票许可',
    status: 6,
    columns: [
      {
        type: 'group',
        columns: [{
          title: '运行许可动火时间',
          name: 'allowBeginTime',
          type: 'input',
          colProps: { span: 8 },
          status: 6,
        }]
      },
      {
        type: 'group',
        columns: [{
          title: '工作许可人',
          name: 'allowName',
          type: 'input',
          colProps: { span: 8 },
          status: 6,
        },
        {
          title: '许可时间',
          name: 'allowTime',
          type: 'input',
          colProps: { span: 8 },
          status: 6,
        }]
      }
    ]
  },
  {
    title: '动火部门审核意见',
    status: 13,
    columns: [
      {
        type: 'remark',
        columns: [{
          title: null,
          name: 'checkResult',
          type: 'textArea',
          colProps: { span: 24 },
          labelCol: { span: 3 },
          wrapperCol: { span: 21 },
          status: 13,
        }]
      },
      // {
      //   title: '检测人',
      //   name: 'checkName',
      //   type: 'input',
      //   colProps: { span: 12 },
      //   status: 7,
      // },
      // {
      //   title: '时间',
      //   name: 'checkTime',
      //   type: 'input',
      //   colProps: { span: 12 },
      //   status: 7,
      // },
      {
        type: 'table',
        columns: [{
          title: '角色',
          values: ['检测人', '动火执行人', '消防监护人', '动火工作负责人', '动火部门安监人员']
        }, {
          title: '人员',
          fields: ['checkName', 'signName1', 'signName2', 'signName3', 'leaderName']
        }, {
          title: '审核时间',
          fields: ['checkTime', 'signTime1', 'signTime2', 'signTime3', 'reviewTime2']
        }],
        status: [7, 8, 9, 10, 11]
      },
      // {
      //   title: '动火执行人',
      //   name: 'signName1',
      //   type: 'input',
      //   colProps: { span: 12 },
      //   status: 13,
      // },
      // {
      //   title: '时间',
      //   name: 'signTime1',
      //   type: 'input',
      //   colProps: { span: 12 },
      //   status: 13,
      // },
      // {
      //   title: '消防监护人',
      //   name: 'signName2',
      //   type: 'input',
      //   colProps: { span: 12 },
      //   status: 13,
      // },
      // {
      //   title: '时间',
      //   name: 'signTime2',
      //   type: 'input',
      //   colProps: { span: 12 },
      //   status: 13,
      // },
      // {
      //   title: '动火工作负责人',
      //   name: 'signName3',
      //   type: 'input',
      //   colProps: { span: 12 },
      //   status: 13,
      // },
      // {
      //   title: '时间',
      //   name: 'signTime3',
      //   type: 'input',
      //   colProps: { span: 12 },
      //   status: 13,
      // },
      // {
      //   title: '动火部门安监人员',
      //   name: 'leaderName',
      //   type: 'input',
      //   colProps: { span: 12 },
      //   status: 13,
      // },
      // {
      //   title: '时间',
      //   name: 'reviewTime2',
      //   type: 'input',
      //   colProps: { span: 12 },
      //   status: 13,
      // },
      {
        type: 'remark',
        columns: [{
          title: '允许动火时间',
          name: 'allowBeginTime2',
          type: 'input',
          colProps: { span: 12 },
          status: 13,
        }]
      }
    ]
  },
  {
    title: '动火票终结',
    status: 13,
    columns: [
      {
        type: 'remark',
        columns: [{
          title: '工作终结',
          name: 'allowTime2',
          type: 'textArea',
          colProps: { span: 24 },
          labelCol: { span: 3 },
          status: 14,
        }]
      },
      {
        type: 'table',
        columns: [{
          title: '角色',
          values: ['工作负责人', '动火执行人', '消防监护人', '运行许可人']
        }, {
          title: '人员',
          fields: ['finalName0', 'finalName1', 'finalName2', 'allowName2']
        }, {
          title: '审核时间',
          fields: ['finalTime0', 'finalTime1', 'finalTime2', 'submitTime']
        }],
        status: [13, 13.3, 13.6, 14]
      },
      // {
      //   title: '工作负责人',
      //   name: 'finalName0',
      //   type: 'input',
      //   colProps: { span: 12 },
      //   status: 13,
      // },
      // {
      //   title: '结束填报时间',
      //   name: 'finalTime0',
      //   type: 'input',
      //   colProps: { span: 12 },
      //   status: 13,
      // },
      // {
      //   title: '动火执行人',
      //   name: 'finalName1',
      //   type: 'input',
      //   colProps: { span: 12 },
      //   status: 13,
      // },
      // {
      //   title: '确认时间',
      //   name: 'finalTime1',
      //   type: 'input',
      //   colProps: { span: 12 },
      //   status: 13,
      // },
      // {
      //   title: '消防监护人',
      //   name: 'finalName2',
      //   type: 'input',
      //   colProps: { span: 12 },
      //   status: 13,
      // },
      // {
      //   title: '确认时间',
      //   name: 'finalTime2',
      //   type: 'input',
      //   colProps: { span: 12 },
      //   status: 13,
      // },
      // {
      //   title: '运行许可人',
      //   name: 'allowName2',
      //   type: 'input',
      //   colProps: { span: 12 },
      //   status: 14,
      // },
      // {
      //   title: '终结工作票时间',
      //   name: 'submitTime',
      //   type: 'input',
      //   colProps: { span: 12 },
      //   status: 14,
      // },
      {
        type: 'remark',
        columns: [{
          title: '其他事项',
          name: 'workMatter',
          type: 'textArea',
          colProps: { span: 24 },
          labelCol: { span: 3 },
          status: 14,
        }]
      }
      ,
    ]
  },
  {
    title: '审核',
    status: 18,
    columns: [
      {
        type: 'group',
        columns: [{
          title: '审核人',
          name: 'auditName',
          type: 'input',
          colProps: { span: 12 },
          status: 18,
        },
        {
          title: '审核日期',
          name: 'auditTime',
          type: 'input',
          colProps: { span: 12 },
          status: 18,
        }]
      },
      {
        type: 'remark',
        columns: [{
          title: '审核结果',
          name: 'auditResult',
          type: 'input',
          colProps: { span: 12 },
          status: 18,
        },
        {
          title: '审核意见',
          name: 'auditOpinion',
          type: 'textArea',
          colProps: { span: 24 },
          labelCol: { span: 3 },
          status: 18,
        },]
      }
    ]
  },
]