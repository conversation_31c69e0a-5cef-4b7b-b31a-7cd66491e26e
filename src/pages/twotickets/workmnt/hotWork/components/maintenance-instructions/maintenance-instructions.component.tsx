import { ListCard } from '@/component/list-card/list-card.component';
import { Card, Form, Input, List, TextArea } from 'antd-mobile';
import dayjs from 'dayjs';
import { styled } from 'umi';
import { strategyFactory } from '../../../components/dynamic-form/strategy-factory';
import { DynamicFormItem } from '../../../components/dynamic-form/dynamic-form-item';
import { ActionSheet } from 'antd-mobile';
import { RightOutline } from 'antd-mobile-icons';

// 输入多行数据
strategyFactory.registerStrategy('textarea', {
  component: TextArea,
});

const CAN_RUN_STATUS = [
  {
    label: '不可投运',
  },
  {
    label: '可投运',
  },
];

// 点击触发ActionSheet选择CAN_RUN_STATUS上滑动选择
// 注册canRun类型，点击触发ActionSheet选择CAN_RUN_STATUS
strategyFactory.registerStrategy('canRunTou', {
  component: (props: any) => {
    const { value, onChange, disabled } = props;
    const handleClick = () => {
      if (disabled) return;
      const actionSheet = ActionSheet.show({
        actions: [{ label: '不可投运' }, { label: '可投运' }].map((item, idx) => ({
          text: item.label,
          key: idx,
          onClick: () => {
            onChange?.(idx);
            actionSheet.close();
          },
        })),
        cancelText: '取消',
      });
    };

    return (
      <div
        onClick={handleClick}
        style={{
          cursor: disabled ? 'not-allowed' : 'pointer',
          width: '100%',
          textAlign: 'right',
          color: CAN_RUN_STATUS?.[value] ? '#333333' : '#999999',
          lineHeight: '6.1676vw',
        }}
      >
        {CAN_RUN_STATUS[value]?.label || '请选择'}
      </div>
    );
  },
});
interface MaintenanceInstructionsProps {
  ticketType: number | string;
  data: any;
  onChange: (values: any) => void;
  pageType: 'view' | 'edit';
}

const MaintenanceInstructionsWrapper = styled(Card)`
  margin-top: 2.5926vw;
  .adm-form-item-label {
    font-size: 4.2593vw;
    font-weight: 400;
    letter-spacing: 0vw;
    line-height: 6.1676vw;
    color: rgba(51, 51, 51, 1);
  }
  .adm-form-item-child-inner {
    font-size: 3.8889vw;
    font-weight: 400;
    letter-spacing: 0vw;
    line-height: 5.6315vw;
    color: rgba(51, 51, 51, 1);
    /* margin-top: 1.4815vw; */
    .status {
      font-size: 4.2593vw;
      font-weight: 700;
      letter-spacing: 0vw;
      line-height: 6.1676vw;
      color: rgba(51, 51, 51, 1);
    }
    .info {
      margin-top: 0.3704vw;
      font-size: 3.3333vw;
      font-weight: 400;
      letter-spacing: 0vw;
      line-height: 4.8269vw;
      color: rgba(153, 153, 153, 1);
      .value {
        color: rgba(51, 51, 51, 1);
      }
    }
  }
`;

const MaintenanceInstructionsItems = [
  {
    strategyType: 'textarea',
    label: '检修交代',
    name: 'repairOrder',
    layout: 'vertical',
    required: true,
    placeholder: '请输入',
    rules: [
      {
        required: true,
        message: '请输入检修交代',
      },
    ],
    strategyProps: {
      placeholder: '请输入检修交代',
      rows: 3,
      style: {
        borderRadius: '3.3333vw',
        background: 'rgba(245, 245, 245, 1)',
        '--font-size': '3.3333vw',
        lineHeight: '1.2',
        padding: '2.2222vw',
        boxSizing: 'border-box',
      },
    },
  },
  // canRun
  {
    strategyType: 'canRunTou',
    label: '设备是否可运行',
    name: 'canRun',
    required: true,
    layout: 'horizontal',
    extra: <RightOutline />,
    rules: [
      {
        required: true,
        message: '请选择设备是否可运行',
      },
    ],
  },
];

/**
 * 检修交代
 * @returns
 */
const MaintenanceInstructions = (props: MaintenanceInstructionsProps) => {
  const { ticketType, data = {}, onChange } = props;
  const canRun = data?.workFlowExecute?.canRun || '0';
  if (props.pageType === 'edit' && ((data?.status === '13' && ticketType === '7') || (data?.status === '15' && ticketType === '6')))
    return (
      <MaintenanceInstructionsWrapper title={'检修交代（请填写设备是否可运行）'}>
        <Form
          initialValues={data?.workFlowExecute}
          onValuesChange={(value, allValue) => {
            onChange(allValue);
          }}
          style={{
            '--border-bottom': 'none',
            '--border-top': 'none',
          }}
        >
          {MaintenanceInstructionsItems.map((v, i) => (
            <DynamicFormItem key={`${i}`} {...v} />
          ))}
        </Form>
      </MaintenanceInstructionsWrapper>
    );
  return (
    <MaintenanceInstructionsWrapper title={'检修交代（请交代设备是否可运行）'}>
      <Form
        style={{
          '--border-bottom': 'none',
          '--border-top': 'none',
        }}
      >
        <Form.Item label="检修交代" name="workFlowExecute.repairOrder" required layout="vertical">
          {data?.workFlowExecute?.repairOrder}
        </Form.Item>
        <Form.Item label="设备是否可投运" name="canRun" required>
          <div className="status">{CAN_RUN_STATUS?.[canRun]?.label}</div>
          <div className="info">
            工作负责人：<span className="value">{data?.workFlowExecute?.submitHeadName}</span>，递交时间：
            <span className="value">{dayjs(data?.workFlowExecute?.submitTime).format('YYYY/MM/DD HH:mm')}</span>
          </div>
        </Form.Item>
      </Form>
    </MaintenanceInstructionsWrapper>
  );
};

export default MaintenanceInstructions;
