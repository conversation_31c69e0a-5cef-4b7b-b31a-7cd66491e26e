.listContent>div {
  &:nth-last-child(n+2) {
    border-bottom: 1px solid rgb(238, 238, 238);
  }
}


.addBtn {
  font-size: 1.6rem;
}

.myTextArea {
  background-color: rgba(245, 245, 245, 1);
  border-radius: .6rem;
  padding: .6rem;
}

/* 下面是新增的 */
.container {
  font-family: PingFang SC;
  width: 100%;
  height: 100vh;
  border: 0;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  .content {
    flex: 1;
    overflow-y: auto;
    padding: 1.2rem 0;

    :global {
      .adm-form-item-label {
        font-size: 1.5333rem;
        font-weight: 400;
        letter-spacing: 0rem;
        line-height: 2.2203rem;
        color: rgba(51, 51, 51, 1);
      }

      .adm-radio-content {
        font-size: 1.5333rem;
        font-weight: 400;
        letter-spacing: 0rem;
        line-height: 2.2203rem;
        color: rgba(51, 51, 51, 1);
      }

      .adm-checkbox-content {
        flex: 1;
      }

      .adm-text-area {
        box-sizing: border-box;
        padding: 0.7333rem 1.4667rem;
        border-radius: 0.6667rem;
        background: rgba(245, 245, 245, 1);

        // 提示文字改小点
        .adm-text-area-element::placeholder {
          font-size: 1.2rem;
        }
      }
    }

    .operatorTitle {
      position: relative;
      font-size: 1.5333rem;
      font-weight: 500;
      letter-spacing: 0rem;
      line-height: 2.2203rem;
      color: rgba(51, 51, 51, 1);

      .num {
        font-size: 1.4rem;
        font-weight: 400;
        letter-spacing: 0rem;
        line-height: 2.0273rem;
        color: rgba(51, 51, 51, 1);

        .checked {
          color: rgba(17, 84, 237, 1);
        }
      }
    }

    .note {
      margin-top: .5rem;
      width: 100%;
      font-size: 1.2rem;
      font-weight: 400;
      letter-spacing: 0rem;
      line-height: 1.7377rem;
      color: rgba(153, 153, 153, 1);
    }

    .operators {
      :global {
        .adm-space-item {
          padding-top: 1.1667rem;
          padding-bottom: 1.1667rem;
          margin-bottom: 0;
          border-bottom: .0333rem solid rgba(229, 229, 229, 1);

          .adm-checkbox {
            align-items: flex-start;

            .adm-checkbox-icon {
              margin-top: .1667rem;
            }
          }
        }
      }
    }

    .bottom {
      height: 4.8rem;
      font-size: 1.3333rem;
      font-weight: 400;
      letter-spacing: -0.0533rem;
      line-height: 4.8rem;
      color: rgba(153, 153, 153, 1);
      text-align: center;
    }
  }

  .footer {
    padding: 1.0667rem 1.1667rem;
    box-sizing: border-box;
    background-color: #fff;

    .btn {
      font-size: 1.3997rem;
      font-weight: 400;
      letter-spacing: 0rem;
      line-height: 1.8667rem;
      color: rgba(255, 255, 255, 1);
    }
  }
}

.operatorItemEditPopup {
  :global {
    .adm-form-item-label {
      font-size: 1.6rem;
      font-weight: 500;
      letter-spacing: 0rem;
      line-height: 2.3333rem;
      color: rgba(51, 51, 51, 1);
      margin-bottom: 1.3rem;
    }

    .adm-text-area {
      box-sizing: border-box;
      padding: 0.7333rem 1.4667rem;
      border-radius: 0.6667rem;
      background: rgba(245, 245, 245, 1);

      // 提示文字改小点
      .adm-text-area-element::placeholder {
        font-size: 1.2rem;
      }
    }
  }
}