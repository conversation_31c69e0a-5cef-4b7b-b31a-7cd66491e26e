/**
 * @description 工作票执行-工作成员变更
 */
import React, { forwardRef, useImperativeHandle, useState, useEffect, useRef, useMemo } from 'react';
import { ListCard } from '@/component/new-list-card/list-card.component';
import { EditSOutline, AddOutline } from 'antd-mobile-icons';
import styles from './test-record.component.less';
import MorePage from '@/pages/twotickets/workmnt/components/morePage/index';

import styled from 'styled-components';

import FormPopup from '@/pages/twotickets/workmnt/components/form-popup/index';


export const ChangeDivItem = styled.div`
  font-size: 1.6rem;
  padding: 0.8333rem 0;
  .item_top {
    display: flex;
    /* align-items: center; */
    font-size: 1.4rem;
    margin-bottom: 0.6333rem;
    .item_top_left {
      flex: 1;
    }
    .item_top_right {
      width: 3rem;
      font-size: 1.5333rem;
      text-align: right;
    }
  }
  .item_bottom {
    padding-left: 2rem;
    background: rgba(245, 245, 245, 1);
    border-radius: 0.6rem;
    height: 3.4rem;
    display: flex;
    align-items: center;
    font-size: 1.2rem;
    color: rgba(153, 153, 153, 1);
    span {
      &:nth-child(1) {
        display: inline-block;
        border: 1px solid rgba(253, 146, 40, 1);
        padding: 0 0.2667rem;
        color: rgba(253, 146, 40, 1);
        margin-right: 0.8333rem;
        border-radius: 0.4rem 0.4rem 0.4rem 0;
      }
      &:nth-child(3) {
        padding: 0 0.7667rem;
      }
      &:nth-child(4) {
        margin-right: 0.7667rem;
      }
    }
  }
  .item_top_flex {
    display: flex;

    .item_top_flex_right_new {
      font-size: 1.2rem;
      display: inline-block;
      border: 1px solid rgba(17, 84, 237, 1);
      padding: 0 0.2667rem;
      color: rgba(17, 84, 237, 1);
      margin-right: 0.8333rem;
      border-radius: 0.4rem 0.4rem 0.4rem 0;
    }
    .item_top_flex_right_name {
      font-size: 1.4rem;
      color: rgba(51, 51, 51, 1);
      font-weight: 500;
    }
  }
  .item_top_time {
    span {
      font-size: 1.2rem;
      color: rgba(102, 102, 102, 1);
      &:nth-child(2) {
        padding: 0 0.7667rem;
      }
    }
  }
`;

import { Form, Input, TextArea } from 'antd-mobile';
import SelectPopup from '../../../components/select-popup';

interface Iprops {
  current: any; //详情数据
  isAddAndEdit?: boolean; //是否有新增和编辑权限(默认false)
  ref: any;
}

const TextRecord: React.FC<Iprops> = forwardRef((props, ref) => {
  const { current, isAddAndEdit = false } = props;

  const [data, setData] = useState([]);
  const [visible, setVisible] = useState(false);
  const [popupValue, setPoputValue] = useState(null);

  const morePageRef = useRef(null);
  const editFn = (record = null) => {
    console.log(record, '点击了编辑或新增按钮');
    setPoputValue(record);
    setVisible(true);
    //判断是新增或编辑
  };

  useImperativeHandle(ref, () => ({
    getData: () => data,
    setData: (val: any[]) => setData(val),
  }));

  useEffect(() => {
    if (current) {
      setData(current?.workTicketExecute?.workFlowExecuteUserExchanges);
    }
  }, [current]);

  return (
    <div style={{ marginBottom: '0.8rem' }}>
      <ListCard
        title={<div>检测记录</div>}
        isMore={true}
        refresh={[isAddAndEdit]}
        maxLength={3}
        rightButtonFn={isAddAndEdit ? editFn : null}
        moreLink={() => {
          morePageRef.current.morePage();
        }}
        data={current?.workTicketExecute?.workFlowExecuteUserExchanges || []}
        isExpandAndMore={true}
        children={(item: any[]) => {
          return (
            <div className={styles.listContent}>
              {item?.map((el, index) => {
                return (
                  <ChangeDivItem key={index}>
                    <div className="item_top">
                      <div>{`${index + 1}、`}</div>
                      <div className="item_top_left">
                        <div className="item_top_flex">
                          <span className="item_top_flex_right_new">新</span>
                          <span className="item_top_flex_right_name">{el.addUserName}</span>
                        </div>
                        <div className="item_top_time">
                          <span>{el.addTime}</span>
                          <span>·</span>
                          <span>{el.addHeadName}</span>
                          <span>(负责人)</span>
                        </div>
                      </div>
                      {isAddAndEdit ? (
                        <div
                          className="item_top_right"
                          onClick={() => {
                            editFn(el);
                          }}
                        >
                          <EditSOutline />
                        </div>
                      ) : null}
                    </div>
                    <div className="item_bottom">
                      <span>离</span>
                      <span>{el.leaveUserName}</span>
                      <span>·</span>
                      <span>{el.leaveTime}</span>
                      <span>{el.leaveHeadName}(负责人)</span>
                    </div>
                  </ChangeDivItem>
                );
              })}
            </div>
          );
        }}
      />
      {/* 【列表页面写此处】 */}
      <MorePage
        ref={morePageRef}
        title="检测记录"
        right={
          isAddAndEdit ? (
            <div
              className={styles.addBtn}
              onClick={() => {
                editFn();
              }}
            >
              <AddOutline />
              新增
            </div>
          ) : null
        }
      >
        <div className={styles.listContent} style={{ backgroundColor: '#FFF', padding: '0 1rem' }}>
          {data?.map((el, index) => {
            return (
              <ChangeDivItem key={index}>
                <div className="item_top">
                  <div>{`${index + 1}、`}</div>
                  <div className="item_top_left">
                    <div className="item_top_flex">
                      <span className="item_top_flex_right_new">新</span>
                      <span className="item_top_flex_right_name">{el.addUserName}</span>
                    </div>
                    <div className="item_top_time">
                      <span>{el.addTime}</span>
                      <span>·</span>
                      <span>{el.addHeadName}</span>
                      <span>(负责人)</span>
                    </div>
                  </div>
                  {isAddAndEdit ? (
                    <div
                      className="item_top_right"
                      onClick={() => {
                        editFn(el);
                      }}
                    >
                      <EditSOutline />
                    </div>
                  ) : null}
                </div>
                <div className="item_bottom">
                  <span>离</span>
                  <span>{el.leaveUserName}</span>
                  <span>·</span>
                  <span>{el.leaveTime}</span>
                  <span>{el.leaveHeadName}(负责人)</span>
                </div>
              </ChangeDivItem>
            );
          })}
        </div>
      </MorePage>
      {/* 【弹窗组件写此处】弹出编辑/新增 */}
      <FormPopup
        visible={visible}
        setVisible={setVisible}
        title={popupValue ? '编辑' : '新增'}
        onSubmit={(values) => {
          console.log(values, '提交表单获取的数据');
          /* 判断修改还是新增，并写入组件data */
        }}
      >
        <Form.Item label="检测室见">{/* 选人组件（可输入） */}</Form.Item>
        <Form.Item label="检测人">{/* 时间组件 */}</Form.Item>
        <Form.Item label="检测项目"><Input /></Form.Item>
        <Form.Item name="leaveUserName" label="检测值"><Input /></Form.Item>
        <Form.Item label="检测结果">{/* 时间组件 */}</Form.Item>
        <Form.Item label="备注" layout="vertical" ><TextArea readOnly={false} className={styles.myTextArea} placeholder="请输入内容" /></Form.Item>
      </FormPopup>
    </div>
  );
});
export default TextRecord;
