import { styled } from 'umi';
import { Input, DatePicker, DatePickerRef, TextArea, CalendarPicker, Form, Toast, ActionSheet, Radio, Space } from 'antd-mobile';

import { DynamicFormItem, DynamicFormItemProps } from '../../../components/dynamic-form/dynamic-form-item';
import SignatureUserPicker from '../../../components/dynamic-form/items/signature-selection/signature-selection.component';
import { strategyFactory } from '../../../components/dynamic-form/strategy-factory';
import dayjs from 'dayjs';
import { nanoid } from 'nanoid';
import { RefObject, useCallback, useMemo, useRef } from 'react';
import { FooterWrapper, SelectWrapper } from '@/styles/twotickets.style';
import { DatePickerRange } from '@/component/date-picker-range/date-picker-range.component';
import { registerWorkTicketStrategies, WorkTicketFactory } from '../../../components/form-builder/work-ticket-factory';
import { FormContainer } from '../../../components/form-builder/form-container';

export const registerBaseStrategies = () => {
  // 基础策略注册
  strategyFactory.registerStrategy('input', {
    component: Input,
    defaultValue: '',
    // validator: (value) => !!value || ('不能为空' as any),
  });

  strategyFactory.registerStrategy('radio', {
    component: (props: any) => {
      const { value, onChange, disabled, options, formatter } = props;
      return (
        <div
          style={{
            textAlign: 'right',
          }}
        >
          <Radio.Group onChange={onChange} value={value} disabled={disabled}>
            <Space wrap>
              {options.map((item: any, idx: number) => (
                <Radio key={idx} value={item?.value ?? item}>
                  {formatter ? formatter(item, idx) : item.label ?? item}
                </Radio>
              ))}
            </Space>
          </Radio.Group>
        </div>
      );
    },
    defaultValue: '',
  });

  strategyFactory.registerStrategy('textarea', {
    component: TextArea,
    defaultValue: '',
    // validator: (value) => !!value || ('不能为空' as any),
  });

  strategyFactory.registerStrategy('datepicker', {
    component: DatePicker,
    defaultValue: null,
    formatter: (value: string | number | Date | dayjs.Dayjs | null | undefined) => dayjs(value).format('YYYY/MM/DD HH:mm'),
    // validator: (value) => !!value || ('请选择时间' as any),
  });

  strategyFactory.registerStrategy('signature', {
    component: SignatureUserPicker,
    defaultValue: []
    // validator: (value) => (value?.length > 0 ? true : '请签名') as any,
  });

  strategyFactory.registerStrategy('date-range', {
    component: DatePickerRange,
    defaultValue: { startDate: null, endDate: null },
    formatter: (value: {
      startDate: string | number | Date | dayjs.Dayjs | null | undefined;
      endDate: string | number | Date | dayjs.Dayjs | null | undefined;
    }) =>
      `${dayjs(value.startDate).format('YYYY/MM/DD HH:mm')} ~ 
       ${dayjs(value.endDate).format('YYYY/MM/DD HH:mm')}`,
    // validator: (value) =>  (value?.startDate && value?.endDate) || '请选择时间范围',
  });

  // 注册canRun类型，点击触发ActionSheet选择CAN_RUN_STATUS
  strategyFactory.registerStrategy('select', {
    component: (props: any) => {
      const sheet = useRef<any>(null);
      const { value, onChange, disabled, options, formatter } = props;
      // 4. 预先生成 actions 数组
      const actions = useMemo(
        () =>
          options.map((text: any, idx: any) => ({
            text,
            key: text,
            onClick: () => {
              onChange?.(idx);
              sheet.current.close();
            },
          })),
        [options, onChange],
      );

      // 5. 缓存点击处理器
      const handleClick = useCallback(() => {
        if (disabled) return;
        sheet.current = SelectWrapper.show({
          actions,
          cancelText: '取消',
        });
      }, [disabled, actions]);

      // 6. 渲染
      return (
        <div
          onClick={handleClick}
          style={{
            cursor: disabled ? 'not-allowed' : 'pointer',
            width: '100%',
            textAlign: 'right',
            color: options?.[value] ? '#333333' : '#999999',
            lineHeight: '6.1676vw',
          }}
        >
          {formatter ? formatter(options[value], value) : options[value] || '请选择'}
        </div>
      );
    },
  });
};
registerBaseStrategies();

export const CHECK_RESULT = ['合格', '不合格'];

interface IEndFillingProps {
  nextState: number | string | null;
  data: any;
  type: number;
}

/**
 * 审核弹窗
 * 0: 准备中,
 * 1: 签发人签发,
 * 2: 业主签发,
 * 2.5: 动火部门负责人审核,
 * 3: 消防负责人审核,
 * 3.5: 安监主管审核,
 * 4: 安监负责人审核,
 * 5: 安全领导审批,
 * 5.3: 工作票接收确认,
 * 5.6: 值长批准,
 * 6: 许可人审批,
 * 7: 填写检测结果,
 * 8: 执行人签字,
 * 9: 监护人签字,
 * 10: 工作负责人签字,
 * 11: 部门负责人签字,
 * 12: 安监负责人签字,
 * 13: 领导确认动火时间,
 * 14: 负责人执行确认,
 * 15: 执行情况填报,
 * 15.3: 动火执行人确认,
 * 15.6: 消防监护人确认,
 * 16: 许可人终结,
 * 20: 监察审核,
 * 21: 结束,
 * 22: 已审核,
 * 23: 已作废,
 * 24: 已取消,
 * @returns
 */
const EndFilling = (props: IEndFillingProps) => {
  const { nextState, data, type } = props;
  registerWorkTicketStrategies(Number(type), nextState);
  const formConfig = WorkTicketFactory.createFormConfig('hotWork', nextState);
  return (
    <div
      className="content"
      style={{
        height: '100%',
        overflowY: 'auto',
        '--adm-color-background': 'transparent',
      }}
    >
      <FormContainer fields={formConfig} />
    </div>
  );
};

export default EndFilling;
