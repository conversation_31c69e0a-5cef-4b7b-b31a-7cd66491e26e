import { Card, Divider, Form, Input } from 'antd-mobile';
import { history, styled } from 'umi';
import renderFieldFactory, { FieldConfig } from '../../../components/render-strategy/render-strategy.component';
import React, { useMemo } from 'react';
import { getDictList } from '@/services/common.service';
import { WORK_TYPE_FOUR, WORK_TYPE_ONE, WORK_TYPE_THREE, WORK_TYPE_TWO } from '@/constant';
import RenderFields from '../../../components/render-strategy/render-strategy.component';
import { RightOutline } from 'antd-mobile-icons';
import SafetyRiskControlComponent from '../../../components/safety-risk-control/safety-risk-control.component';
import { ListCard } from '@/component/list-card/list-card.component';

const SecurityRiskControlContentWrapper = styled.div`
  .title {
    font-size: 4.2593vw;
    font-weight: 500;
    letter-spacing: 0vw;
    line-height: 6.1676vw;
    color: rgba(51, 51, 51, 1);
  }
  .module-name {
    width: 100%;
    font-family: PingFang SC;
    margin: 4.537vw 3.4259vw 2.5926vw;
    font-size: 4.2593vw;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 6.1676vw;
    color: rgba(51, 51, 51, 1);
  }
  .index {
    font-size: 3.8889vw;
    font-weight: 400;
    letter-spacing: 0vw;
    line-height: 1.3;
    color: rgba(17, 84, 237, 1);
    margin-right: 3.3333vw;
  }
  .card {
    margin-top: 3.3333vw;
  }
`;

const BASIC_DATA: FieldConfig[][] = [
  [
    {
      label: <span className="index">(1)</span>,
      hideSemicolon: true,
      hideLabel: true,
      field: 'workTypeOne',
      type: 'fill_check',
      layout: 'horizontal',
      options: WORK_TYPE_ONE,
    },
    {
      label: <span className="index">(2)</span>,
      hideSemicolon: true,
      hideLabel: true,
      field: 'workTypeTwo',
      type: 'fill_check',
      options: WORK_TYPE_TWO,
    },
    {
      label: <span className="index">(3)</span>,
      hideSemicolon: true,
      hideLabel: true,
      field: 'workTypeThree',
      type: 'fill_check',
      options: WORK_TYPE_THREE,
    },
    {
      label: (
        <span
          className="index"
        >
          (4)
        </span>
      ),
      hideSemicolon: true,
      hideLabel: true,
      field: 'workTypeOther',
      type: 'fill_check',
      options: async () => {
        return [
          {
            label: '其他',
            value: 'other',
          },
        ];
      },
    },
    {
      label: null,
      field: 'otherWorkType',
      hideSemicolon: true,
      type: 'display_render',
      styles: {
        item: {
          display: 'inline-block',
        },
      },
      render: (value, data) => {
        if (!value) return null;
        return (
          <>
            {' '}
            <span className="describe">补充说明: </span>
            <span
              className="describe_info"
              style={{
                flex: 1,
              }}
            >
              {value}
            </span>
          </>
        );
      },
    }
  ],
  [
    {
      label: <div className="title"  style={{
        marginBottom: '3.1746vw',
      }}>作业风险</div>,
      field: 'jobRisk',
      type: 'fill_check',
      hideLabel: true,
      hideSemicolon: true,
      layout: 'vertical',
      options: WORK_TYPE_FOUR,
      // styles: {
      //   name: {
      //     paddingLeft: 30,
      //   },
      // },
    },
    {
      label: null,
      field: 'jobRiskHasOther',
      type: 'fill_check',
      hideLabel: true,
      hideSemicolon: true,
      layout: 'vertical',
      options: async () => {
        return [
          {
            label: '其他',
            value: 'other',
          },
        ];
      },
      // styles: {
      //   name: {
      //     paddingLeft: 30,
      //   },
      // },
    },
    {
      label: null,
      field: 'otherRisk',
      hideSemicolon: true,
      type: 'display_render',
      styles: {
        item: {
          display: 'inline-block',
        },
      },
      render: (value, data) => {
        if (!value) return null;
        return (
          <>
            {' '}
            <span className="describe">补充说明: </span>
            <span
              className="describe_info"
              style={{
                flex: 1,
              }}
            >
              {value}
            </span>
          </>
        );
      },
    },
  ],
];

interface SecurityRiskControlContentProps {
  data: any;
}

const SecurityRiskControlContent = (props: SecurityRiskControlContentProps) => {
  const { data } = props;
  const fieldList = useMemo(() => {
    return (
      BASIC_DATA?.map(
        (d) =>
          d?.map((item) => {
            if (item.field === 'other') {
              return {
                ...item,
                options: (data?.workBaseJobType?.other?.split(',') || []).map((opt) => ({
                  label: opt,
                  value: opt,
                })),
              };
            } else {
              return item;
            }
          }) || [],
      ) || []
    );
  }, [BASIC_DATA, data]);
  return (
    <SecurityRiskControlContentWrapper>
      {/* 安全风险控制卡 */}
      <div className="module-name">安全风险控制卡</div>
      <Card className="card">
        <div className="title" style={{
        marginBottom: '3.1746vw',
      }}>作业类别</div>
        <RenderFields fields={fieldList} data={data} />
      </Card>
      {data?.workBaseRisks?.length ? (
        <ListCard
          title="过程风险预控"
          className="card"
          data={data?.workBaseRisks}
          isMore
          maxLength={2}
          moreLink={`/twotickets/common/check-list/safety-risk/${data?.id}?ticketType=${data?.ticketType}`}
        >
          {(workBaseRisks) => <SafetyRiskControlComponent workBaseRisks={workBaseRisks} />}
        </ListCard>
      ) : null}
      <Card className="card">
        <div className="title">备注</div>
        <div className="describe">{data?.workBaseJobType?.remark}</div>
      </Card>
    </SecurityRiskControlContentWrapper>
  );
};

export default SecurityRiskControlContent;
