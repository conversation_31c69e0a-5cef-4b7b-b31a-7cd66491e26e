import { Button, Form, Input, Popup, Tabs, Toast } from 'antd-mobile';
import { useReactive, useRequest, useThrottleFn } from 'ahooks';
import { useParams, history } from 'umi';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import SecurityRiskControlContent from './components/security-risk-controlcontent/security-risk-controlcontent.component';
import { getTicketTypeAndTitle, TicketStatusMaps, WORK_TYPE_FOUR, WORK_TYPE_ONE, WORK_TYPE_THREE, WORK_TYPE_TWO } from '@/constant';
import { OperationHeaderBtn } from '../../operation/detail/components/operation-btn/operation-header-btn.component';
import TwoTicketsNavBar from '@/component/twotickets-navbar/twotickets-navbar.component';
import { ProcessCard } from '@/component/process-card/process-card.component';
import styles from './hotWork.page.less';
import ActionButtons from '../components/action-buttons/action-buttons.component';
import SafeToolComponent from '../components/safe-tool/safe-tool.component';
import { BasicInfoComponent } from './components/basic-info/basic-info.component';
import { editWorkBase, getWorkBaseDetail } from '@/services/twotickets/workmnt/hotWork';
import MaintenanceIstructions from '../components/maintenanceIstructions';
import TextRecord from './components/test-record/test-record.component';
import AssociatedMainTicket from '../components/associated-main-ticket/associated-main-ticket.component';
import { ListCard } from '@/component/list-card/list-card.component';
import { AddOrFillBtn, DetailTabTitleWrapper } from '@/styles/twotickets.style';
import MaintenanceInstructions from './components/maintenance-instructions/maintenance-instructions.component';
import TestingRecords from './components/testing-records/testing-records';
import FlowPopup from '../components/flow-popue/flow-popue.component';
import { DynamicFormItem } from '../components/dynamic-form/dynamic-form-item';
import EndFilling, { CHECK_RESULT } from './components/end-filling/end-filling.component';
import { updateWorkBase } from '@/services/twotickets/workmnt/workmnt';
import dayjs from 'dayjs';
import { AddOutline } from 'antd-mobile-icons';
import TestingRecordsPopup from './components/testing-records/testing-record-popue.component';
import _ from 'lodash';
import { getUserName } from '@/utils/data-processing-tools';
import { getDictList } from '@/services/common.service';

const TAB_HEIGHT = 42;
const HotWorkPage = () => {
  const [flowPopupVisible, setFlowPopupVisible] = useState(false);
  const [activeKey, setActiveKey] = useState('1');
  const [form] = Form.useForm();
  const containerRef = useRef<HTMLDivElement>(null);
  const nextInfo = useReactive<any>({
    info: null,
  });
  // 是否已经滚动改变
  const isTabClickRef = useRef(false);
  // 获取url参数
  const { type, id, pageType } = useParams();
  console.log(type, id, pageType === 'view');
  const { title, ticketType } = getTicketTypeAndTitle('hotWork', type);
  const {
    data,
    run: getDetail,
    mutate,
    loading,
  } = useRequest(() => getWorkBaseDetail(id, { ticketType }).then((res) => res.data), {
    manual: true,
  });
  const { data: fire_method } = useRequest(() => getDictList('fire_method').then((res) => res.data));
  const TAB_ITEMS = [
    {
      key: '1',
      title: '基本信息',
      content: (data: any) => <BasicInfoComponent data={data} pageType={pageType} mutate={mutate} />,
    },
    {
      key: '2',
      title: '工作票执行',
      isShow: (data: any) =>
        (Number(data?.status) >= 13 && Number(ticketType) === 7) || (Number(data?.status) >= 15 && Number(ticketType) === 6), // 1.是检测人不是负责人 2.不是检测人是负责人  3.都是 null.都不是
      content: (data: any) => (
        <MaintenanceInstructions
          ticketType={ticketType}
          data={data}
          onChange={(values) => {
            const workFlowExecute = _.cloneDeep(data?.workFlowExecute || {});
            workFlowExecute.canRun = values?.canRun;
            workFlowExecute.repairOrder = values?.repairOrder;
            workFlowExecute.submitHeadName = workFlowExecute?.submitHeadName || getUserName?.();
            workFlowExecute.submitTime = workFlowExecute?.submitTime || dayjs().format('YYYY-MM-DD HH:mm:ss');
            mutate((oldData: any) => ({
              ...oldData,
              workFlowExecute,
            }));
          }}
          pageType={pageType as any}
        />
      ),
    },
    {
      key: '3',
      title: '检测记录',
      isShow: (data: any) =>
        (Number(data?.status) >= 13 && Number(ticketType) === 7) || (Number(data?.status) >= 15 && Number(ticketType) === 6),
      content: (data: any, mutate: any) => (
        <>
          <DetailTabTitleWrapper>检测记录</DetailTabTitleWrapper>
          <ListCard
            data={data?.checkList || []}
            title={'检测记录'}
            isMore
            moreLink="#"
            maxLength={3}
            extra={
              // 1.是检测人不是负责人 2.不是检测人是负责人  3.都是 null.都不是
              pageType === 'edit' &&
                ((data?.status === '13' && ticketType === '7') || (data?.status === '15' && ticketType === '6')) &&
                data?.isFireCheck ? (
                <TestingRecordsPopup
                  type={'add'}
                  ticketType={ticketType}
                  onSubmit={(values) => {
                    const checkList = _.cloneDeep(data?.checkList || []);
                    checkList.push(values);
                    mutate({
                      ...data,
                      checkList: checkList?.map((item: any, index: number) => ({ ...item, index })),
                    });
                  }}
                >
                  <AddOrFillBtn>
                    新增 <AddOutline />
                  </AddOrFillBtn>
                </TestingRecordsPopup>
              ) : null
            }
          >
            {(checks) => (
              <TestingRecords
                data={checks}
                canEdit={
                  pageType === 'edit' &&
                  ((Number(data?.status) >= 13 && Number(ticketType) === 7) || (Number(data?.status) >= 15 && Number(ticketType) === 6)) &&
                  data?.isFireCheck
                }
                onChange={(values: any, index: number, type: string) => {
                  if (type === 'del') {
                    const checkList = _.cloneDeep(data?.checkList || []);
                    checkList.splice(index, 1);
                    mutate({
                      ...data,
                      checkList,
                    });
                  } else {
                    const checkList = _.cloneDeep(data?.checkList || []);
                    checkList[index] = { ...checkList[index], ...values };
                    mutate({
                      ...data,
                      checkList,
                    });
                  }
                }}
              />
            )}
          </ListCard>
        </>
      ),
    },
    {
      key: '4',
      title: '安全风险控制卡',
      content: (data: any) => <SecurityRiskControlContent data={data} />,
    },
    {
      key: '5',
      title: '关联主票',
      content: (data: any) => (
        <>
          <DetailTabTitleWrapper>关联主票</DetailTabTitleWrapper>
          <ListCard
            data={data?.relationList || []}
            title={'关联主票'}
            isMore
            moreLink={`/twotickets/common/check-list/associated-mainTicket/${data?.id}?ticketType=${data?.ticketType}`}
            maxLength={4}
          >
            {(data) => <AssociatedMainTicket list={data} />}
          </ListCard>
        </>
      ),
    },
    {
      key: '6',
      title: '安全工器具',
      content: (data: any) => <SafeToolComponent data={data} />,
    },
  ];

  //#region- tab切换页面滚动
  const { run: handleScroll } = useThrottleFn(
    () => {
      if (isTabClickRef.current) return; // 如果是tab点击引起的滚动，忽略

      let currentKey = TAB_ITEMS[0].key;
      for (const item of TAB_ITEMS?.filter((item) => item?.isShow?.(data) || !item?.isShow)) {
        const element = document.getElementById(`anchor-${item.key}`);
        if (!element) continue;
        const rect = element.getBoundingClientRect();
        if (rect.top <= TAB_HEIGHT) {
          currentKey = item.key;
        } else {
          break;
        }
      }
      setActiveKey(currentKey);
    },
    {
      leading: true,
      trailing: true,
      wait: 100,
    },
  );

  const onActionBtnClick = useCallback(
    (actionType: '处理' | '保存', variables?: any, title?: string) => {
      // 当状态为填报时要对页面表单进行校验
      console.log(actionType, data, title, 'onActionBtnClick======');
      // 不在弹框中0 回退， 21 作废
      nextInfo.info = {
        actionType: actionType,
        data: variables,
        title: title,
      };
      if (actionType === '处理') {
        // if (!variables?.extra) {
        //   // 弹框直接触发
        //   handleSubmit({});
        // } else {
        if (
          ((Number(data?.status) === 13 && type === 'two') || (Number(data?.status) === 15 && type === 'one')) &&
          !data?.workFlowExecute?.repairOrder
        ) {
          onTabActive('2');
          Toast.show('检测交代未填写，请先填写检测交代');
        } else if (
          ((Number(data?.status) === 13 && type === 'two') || (Number(data?.status) === 15 && type === 'one')) &&
          !data?.checkList?.length
        ) {
          onTabActive('3');
          Toast.show('执行情况未填写，请先填写执行情况填报');
        } else {
          setFlowPopupVisible(true);
        }
        // }
      } else {
        handleSubmit({});
      }
    },
    [data],
  );

  // 对象去除指定字段
  const omit = (obj: any, keys: any) => {
    if (!obj) return {};
    return Object.keys(obj).reduce((acc: any, cur: any) => {
      if (!keys.includes(cur)) {
        acc[cur] = obj[cur];
      }
      return acc;
    }, {});
  };

  const handleSubmit = async (values: any) => {
    let params = {
      ...data,
      checkList: data?.checkList?.map(({ checkUserName, ...item }: any) => item),
      imgId: data?.imgId || null,
      headId: data?.headId || null,
      headName: data?.headName || null,
      handleUserIds: data?.handleUserIds || null,
      workBaseSafeSteps: data?.workBaseSafeSteps?.map(({ id, ...item }: any) => item) || null, //安全分析单
      workFireExecuteList:
        data?.workFireExecuteList?.map(({ executeId, executeName, licenseNumber }: any) => ({ executeId, executeName, licenseNumber })) ||
        null,
      workTicketExecute: data?.workTicketExecute, //安全分析单
      workBaseJobType: omit(data?.workBaseJobType, ['id', 'mainId']),
      workBaseRisks: data?.workBaseRisks?.map(({ id, ...item }: any) => item), //安全分析单
      workSafeMeasureTickets: data?.workSafeMeasureTickets, //安全分析单
      workBaseTools: data?.workBaseTools?.map(({ id, ...item }: any) => item), //安全分析单
      id: pageType === 'edit' ? id : null,
      processInstanceId: data?.processInstanceId || null,
      isAudit: 0,
      relationList: (data?.relationList || [])?.map((relation: any) => ({
        fireTicketId: data.id,
        recordId: relation.id,
      })),
    };
    if (nextInfo.info.actionType === '处理') {
      params = {
        ...params,
        isAudit: 1,
        ticketType: type === 'one' ? '6' : '7',
        ...nextInfo.info.data,
        variables: nextInfo.info.data,
        ...values,
      };

      // 工作票执行信息表单校验
      let workTicketFormData;
    }
    let res: any = await updateWorkBase(params);
    if (res.code === '1') {
      Toast.show('操作成功');
      history.go(-1);
    } else {
      Toast.show({
        icon: 'error',
        content: res.message || '操作失败',
        duration: 2000,
      });
    }
  };
  // #region 接口-获取详情
  const callbackRef = useCallback((ref) => {
    // 并且卸载组件时，会再次传入null调用该函数，会引发报错
    // 所以：遇到null阻止运行
    if (!ref) return;
    // 给dom绑定事件
    ref.addEventListener('scroll', handleScroll);
    // 保留ref，便于组件卸载时清除副作用
    containerRef.current = ref;
  }, []);
  useEffect(() => {
    getDetail();
    // window.addEventListener('scroll', handleScroll);
    return () => {
      containerRef.current?.removeEventListener('scroll', handleScroll);
    };
  }, []);
  useEffect(() => {
    let jobType = data?.workBaseJobType?.jobType?.split(',');
    let workTypeCheckedOne = jobType?.filter((el: any) => WORK_TYPE_ONE.some((obj) => obj.value === el));
    let workTypeCheckedTwo = jobType?.filter((el: any) => WORK_TYPE_TWO.some((obj) => obj.value === el));
    let workTypeCheckedThree = jobType?.filter((el: any) => WORK_TYPE_THREE.some((obj) => obj.value === el));
    // 动火方式是否存在补充说明
    const fireMethod = data?.fireMethod?.split('、')?.filter((el: any) => (fire_method || [])?.find((obj: any) => obj.dictName === el));
    const fireHasOtherExplanation = data?.fireMethod
      ?.split('、')
      ?.filter((el: any) => !(fire_method || [])?.find((obj: any) => obj.dictName === el));
    let other = data?.workBaseJobType?.other && data?.workBaseJobType.other.length ? data?.workBaseJobType?.other?.split(',') : [];
    const jobRisk = data?.workBaseJobType?.jobRisk
      ?.split(',')
      ?.filter((el: any) => (WORK_TYPE_FOUR || [])?.find((obj: any) => obj.value === el));
    const jobRiskHasOtherExplanation = data?.workBaseJobType?.jobRisk
      ?.split(',')
      ?.filter((el: any) => !(WORK_TYPE_FOUR || [])?.find((obj: any) => obj.value === el));
    form.setFieldsValue({
      fireMethod,
      fireMethodHasOther: fireHasOtherExplanation?.length ? ['other'] : [],
      fireMethodOther: fireHasOtherExplanation?.join('、'),
      workTypeOne: workTypeCheckedOne,
      workTypeTwo: workTypeCheckedTwo,
      workTypeThree: workTypeCheckedThree,
      workTypeOther: other?.length ? ['other'] : [],
      jobRisk: jobRisk,
      jobRiskHasOther: data?.workBaseJobType?.otherRisk ? ['other'] : [],
      otherRisk: data?.workBaseJobType?.otherRisk,
      otherWorkType: other.join(','),
    });
  }, [form, data, fire_method]);

  const processRecords = useMemo(() => {
    const arr = data?.workflowApprovalRecordList?.length > 0 ? [...data?.workflowApprovalRecordList] : [];
    arr?.push({
      taskName: TicketStatusMaps?.[data?.ticketType]?.[data?.status] || data?.taskName || '-',
      approvalResult: data?.workflowState === '2' ? '待提交' : '',
      approvalTime: data?.status === '0' ? data?.createTime : '',
    });
    return arr;
  }, [data]);

  const onTabActive = (key: any) => {
    setActiveKey(key);
    isTabClickRef.current = true;
    const targetElement = document.getElementById(`anchor-${key}`);
    const rect = targetElement.getBoundingClientRect();
    const containerRect = containerRef.current.getBoundingClientRect();
    const currentScrollTop = containerRef.current.scrollTop;

    // 计算目标滚动位置：当前滚动位置 + 目标元素相对于容器的偏移 - TAB_HEIGHT
    const targetScrollTop = currentScrollTop + (rect.top - containerRect.top) - TAB_HEIGHT;

    containerRef.current.scrollTo({
      top: targetScrollTop,
      behavior: 'auto',
    });
    setTimeout(() => {
      isTabClickRef.current = false;
    }, 400); // 400ms后自动解锁
  };

  return (
    <div className={styles.container} style={{ paddingTop: `${window.top?.localStorage?.topHeight ?? 0}px` }}>
      <TwoTicketsNavBar
        title={data?.taskName}
        subTitle={title}
        bgcolor={'#fff'}
        color={'#000'}
        right={<OperationHeaderBtn data={data} />}
      />
      <div className={styles.body} ref={callbackRef}>
        <div className={styles.content}>
          <ProcessCard
            id={data?.id}
            records={_.cloneDeep(processRecords).reverse()}
            onProcess={function (id: number): void {
              history.push(`/twotickets/common/check-list/process-viewer/${data?.id}?ticketType=${ticketType}`);
            }}
            taskUserName={data?.taskUserName}
          />
        </div>

        <div className={styles.tabsContainer}>
          <Tabs
            activeKey={activeKey}
            onChange={(key) => {
              onTabActive(key);
            }}
          >
            {TAB_ITEMS?.filter(item=>item.isShow?.(data) || !item.isShow).map((item) =><Tabs.Tab title={item.title} key={item.key} />)}
          </Tabs>
        </div>

        <div className={styles.tabsContent}>
          {!loading && (
            <Form
              form={form}
              // disabled={pageType === 'view'}
              style={{
                '--border-top': 'none',
                '--border-bottom': 'none',
                '--border-inner': 'none',
              }}
              onValuesChange={(changedValues, allValues) => {
                mutate({
                  ...data,
                  workBaseJobType: {
                    ...(data?.workBaseJobType || {}),
                  },
                });
              }}
            >
              {TAB_ITEMS?.filter(item=>item.isShow?.(data) || !item.isShow).map((item: any) => (
                <div key={item.key} id={`anchor-${item.key}`}>
                  {item.content(data, mutate)}
                </div>
              ))}
            </Form>
          )}
        </div>
        <div className={styles.bottom}>已经到底了</div>
      </div>
      {pageType === 'edit' && !loading && data?.workFireUpdateDelete?.canUpdate && (
        <ActionButtons
          onClick={onActionBtnClick}
          showSaveBtn={
            (ticketType === '7' && ['0', '1', '2', '13'].includes(data?.status + '')) ||
            (ticketType === '6' && ['0', '1', '2', '15'].includes(data?.status + ''))
            // (data?.workFireUpdateDelete?.canUpdate === 1 && data?.workflowState === '4')
          }
          taskId={data?.taskId}
          // disabledConfig={{ "作废": true }}
          loading={loading}
          detailData={data}
        />
      )}

      <FlowPopup
        title={nextInfo.info?.title}
        visible={flowPopupVisible}
        onClose={() => {
          nextInfo.info = null;
          setFlowPopupVisible(false);
        }}
        onSubmit={async (values: any) => {
          /**·
           * 审批弹窗数据进行处理
           */
          const newValues = {
            ...values,
          };
          // 批准动火时间
          if (values?.fireTime?.startDate && values?.fireTime?.endDate) {
            newValues.workFireDepartment = {
              fireBeginTime: dayjs(values?.fireTime?.startDate).format('YYYY-MM-DD HH:mm:ss'),
              fireEndTime: dayjs(values?.fireTime?.endDate).format('YYYY-MM-DD HH:mm:ss'),
            };
          }
          // 运行许可动火时间
          if (values?.allowTime && data.status === '6') {
            newValues.workFlowLicense = {
              allowBeginTime: dayjs(values?.allowTime).format('YYYY-MM-DD HH:mm:ss'),
            };
          }
          // 确认时间
          if (values?.allowBeginTime) {
            newValues.workFireFirstResponsiblePerson = {
              allowBeginTime: dayjs(values.allowBeginTime).format('YYYY-MM-DD HH:mm:ss'),
            };
          }
          // 工作票终结时间
          if (values?.submitTime) {
            newValues.workFlowExecute = {
              submitTime: values?.submitTime,
            };
          }
          if (values?.allowTime && data.status === '14') {
            newValues.workFlowEnd = {
              allowTime: dayjs(values?.allowTime).format('YYYY-MM-DD HH:mm:ss'),
              workMatter: values?.workMatter,
            };
          }
          if (values?.auditResult) {
            newValues.workFlowAudit = {
              auditResult: CHECK_RESULT[values?.auditResult],
              auditOpinion: values?.auditOpinion,
            };
          }

          // 完成填报
          if ((ticketType === '7' && data.status === '13') || (ticketType === '6' && data.status === '15')) {
            // 获取人员信息
            const nameFormat = (name: any, key: 'name' | 'id' = 'name') => {
              return typeof name === 'string' ? name : name?.map((item: any) => item[key])?.join(',');
            };
            // #region 动火执行人，时间和气体测定值
            newValues.workCheckResult = {
              checkResult: values?.checkResult,
              // checkUserId: typeof modalData?.checkUserId === 'string' ? modalData?.checkUserId : modalData?.checkUserId?.map((item: any) => item.id)?.join(','),
              checkUserName: nameFormat(values?.checkUserName, 'userId'),
              checkTime: dayjs(values?.checkTime).format('YYYY-MM-DD HH:mm:ss'),
            };
            newValues.workFireSignConfirmList = [
              {
                type: '2',
                signTime: dayjs(values.confirmSignTime2).format('YYYY-MM-DD HH:mm:ss'),
                signName: nameFormat(values?.confirmSignName2, 'name'),
                mainId: id,
              },
              {
                type: '3',
                signTime: dayjs(values.confirmSignTime3).format('YYYY-MM-DD HH:mm:ss'),
                signName: nameFormat(values?.confirmSignName3, 'name'),
                mainId: id,
              },
              {
                type: '4',
                signTime: dayjs(values.confirmSignTime4).format('YYYY-MM-DD HH:mm:ss'),
                signName: nameFormat(values?.confirmSignName4, 'name'),
                mainId: id,
              },
              {
                type: '5',
                signTime: dayjs(values.confirmSignTime5).format('YYYY-MM-DD HH:mm:ss'),
                signName: nameFormat(values?.confirmSignName5, 'name'),
                mainId: id,
              },
            ];
            newValues.workFireFirstResponsiblePerson = {
              leaderName: nameFormat(values?.leaderName, 'name'),
              reviewTime: dayjs(values?.reviewTime).format('YYYY-MM-DD HH:mm:ss'),
              allowBeginTime: dayjs(values?.allowBeginTime).format('YYYY-MM-DD HH:mm:ss'),
            };
            newValues.workFlowExecute = {
              submitTime: dayjs(values?.submitTime).format('YYYY-MM-DD HH:mm:ss'),
            };
            newValues.workFireFinalConfirmList = [
              {
                type: '1',
                signTime: dayjs(values.finalSignTime1).format('YYYY-MM-DD HH:mm:ss'),
                signName: nameFormat(values?.finalSignName1, 'name'),
                mainId: id,
              },
              {
                type: '2',
                signTime: dayjs(values.finalSignTime2).format('YYYY-MM-DD HH:mm:ss'),
                signName: nameFormat(values?.finalSignName2, 'name'),
                mainId: id,
              },
            ];
          }
          if ((type === 'two' && data?.status === '18') || (type === 'one' && data?.status === '20')) {
            newValues.workFlowAudit = {
              auditResult: values.auditResult,
              auditOpinion: values.opinion,
            };
          }
          delete newValues.fireTime;
          delete newValues.allowTime;
          delete newValues.allowBeginTime;
          delete newValues.confirmTime;
          delete newValues.submitTime;
          delete newValues.workMatter;
          delete newValues.auditResult;
          delete newValues.auditOpinion;
          delete newValues.signName1;
          delete newValues.signName2;
          delete newValues.signTime1;
          delete newValues.signTime2;
          delete newValues.desc;
          delete newValues.checkResult;
          delete newValues.checkUserId;
          delete newValues.checkUserName;
          delete newValues.checkTime;
          delete newValues.confirmSignTime2;
          delete newValues.confirmSignTime3;
          delete newValues.confirmSignTime4;
          delete newValues.confirmSignTime5;
          delete newValues.confirmSignName2;
          delete newValues.confirmSignName3;
          delete newValues.confirmSignName4;
          delete newValues.confirmSignName5;
          delete newValues.leaderName;
          delete newValues.reviewTime;
          delete newValues.finalSignTime1;
          delete newValues.finalSignTime2;
          delete newValues.finalSignName1;
          delete newValues.finalSignName2;
          handleSubmit(newValues);
          setFlowPopupVisible(false);
        }}
      >
        <EndFilling data={data} type={ticketType} nextState={nextInfo?.info?.data} />
      </FlowPopup>
    </div>
  );
};

export default HotWorkPage;
