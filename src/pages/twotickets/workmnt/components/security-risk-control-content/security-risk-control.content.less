.securityRiskControl {
  width: 100%;
  font-family: PingFang SC;
  margin-bottom: 0.8rem;

  .moduleName {
    margin: 49px 37px 28px;
    font-size: 46px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 66.61px;
    color: rgba(51, 51, 51, 1);
  }

  .title {
    font-size: 46px;
    font-weight: 500;
    letter-spacing: 0px;
    line-height: 66.61px;
    margin-bottom: 20px;
  }

  .mark {
    margin-left: 90px;

    span {
      font-weight: 500;
    }
  }

  :global {
    .adm-card-header-title {
      font-size: 46px;
      font-weight: 500;
    }

    .adm-card-body {
      padding-bottom: 1.8rem;
    }

    .adm-list {
      --border-bottom: none !important;

      .adm-list-item {
        padding-left: 0;
      }

      .adm-list-item-content {
        border-top: none;
        padding-right: 0;
      }

      .adm-list-item-content-main {
        padding: 10px 0;
      }

      .adm-list-item-content-prefix {
        width: 60px;
        padding-top: 15px !important;
      }
    }

    .adm-form {
      --border-top: none;
    }

    .adm-checkbox {
      margin-right: 50px;

      .adm-checkbox-icon {
        width: 37px;
        height: 37px;
        border-radius: 0;
      }

      .adm-checkbox-content {
        font-size: 42px;
      }
    }

    .adm-list-item-disabled.adm-list-item-disabled>.adm-list-item-content>* {
      opacity: 1;
    }
  }
}
.typeCard {
  :global {
    .adm-form-item-label {
      color: rgba(17, 84, 237, 1);
    }
    .adm-checkbox-icon {
      background-color: #f5f5f5;
    }
  }
}

