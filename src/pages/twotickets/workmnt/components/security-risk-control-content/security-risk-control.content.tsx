import {
  useState,
  useEffect,
} from 'react';
import { Form, Input, Checkbox, Card, Divider, Space } from 'antd-mobile'
import styles from './security-risk-control.content.less';
import SafetyRiskControlComponent from '../safety-risk-control/safety-risk-control.component';
import { RightOutline } from 'antd-mobile-icons';
import { history } from 'umi';

interface Porps {
  data: any; // 详情对象
  executeStatus?: number; // 执行填报状态
}
const checkboxOne = [
  { label: '安装', value: '安装', checked: true },
  { label: '调试', value: '调试', checked: false },
  { label: '检查', value: '检查', checked: false },
  { label: '维护', value: '维护', checked: false },
  { label: '检修', value: '检修', checked: false },
  { label: '清扫', value: '清扫', checked: true },
];
const checkboxTwo = [
  { label: '动火作业', value: '动火作业' },
  { label: '高处作业', value: '高处作业' },
  { label: '起重作业', value: '起重作业' },
  { label: '临时用电', value: '临时用电' },
  { label: '水冲洗', value: '水冲洗' },
  { label: '化学清洗', value: '化学清洗' },
  { label: '射线深伤', value: '射线深伤' },
];
const checkboxThree = [
  { label: '有限空间', value: '有限空间' },
  { label: '土方开挖', value: '土方开挖' },
  { label: '基坑支护', value: '基坑支护' },
  { label: '脚手架搭设', value: '脚手架搭设' },
  { label: '模板安装', value: '模板安装' },
];
const checkboxFive = [
  { label: '高处坠落', value: '高处坠落' },
  { label: '物体打击', value: '物体打击' },
  { label: '火灾/爆炸', value: '火灾/爆炸' },
  { label: '灼烫', value: '灼烫' },
  { label: '坍塌', value: '坍塌' },
  { label: '起重伤害', value: '起重伤害' },
  { label: '机械伤害', value: '机械伤害' },
  { label: '中毒/窒息', value: '中毒/窒息' },
  { label: '触电', value: '触电' },
  { label: '淹溺', value: '淹溺' },
  { label: '车辆伤害', value: '车辆伤害' },
  { label: '设备损坏', value: '设备损坏' },
];

const SecurityRiskControlContent = ((props: Porps) => {
  const [other, setOther] = useState(''); // 作业类别其他 补充信息
  const [otherRisk, setOtherRisk] = useState(''); // 作业风险其他 补充信息
  const { data } = props;
  const [form] = Form.useForm();
  const [homewordRiskForm] = Form.useForm();
  const [remarksForm] = Form.useForm();

  useEffect(() => {
    if (data?.workBaseJobType) {
      //设置作业类别123
      let jobType = data.workBaseJobType?.jobType?.split(',') || [];
      let checkedArrOne = jobType?.filter((el: any) => checkboxOne.some((obj) => obj.value === el)) || [];
      let checkedArrTwo = jobType?.filter((el: any) => checkboxTwo.some((obj) => obj.value === el)) || [];
      let checkedArrThree = jobType?.filter((el: any) => checkboxThree.some((obj) => obj.value === el)) || [];
      //设置作业类别4（其他）
      let other = data.workBaseJobType?.other || '';
      form.setFieldsValue({ 
        checkboxOne: checkedArrOne || [], 
        checkboxTwo: checkedArrTwo || [], 
        checkboxThree: checkedArrThree || [], 
        other: other || '' 
      });
      setOther(other || '')

      //设置作业风险
      let jobRisk = data.workBaseJobType?.jobRisk?.split(',') || [];
      let otherRisk = data.workBaseJobType?.otherRisk || '';
      homewordRiskForm.setFieldsValue({ 
        jobRisk: jobRisk || [], 
        otherRisk: otherRisk || '' 
      });
      setOtherRisk(otherRisk || '')
      //设置备注
      let remark = data.workBaseJobType.remark || '';
      remarksForm.setFieldsValue({ remark: remark || '' });
    }
  }, [data]);
  /* 作业类别数据处理 */

  return (
    <div className={styles.securityRiskControl}>
      <div className={styles.moduleName}>安全风险控制卡</div>
      <Space direction='vertical'>
        <Card bodyClassName={styles.typeCard}>
          <div className={styles.title}>作业类别</div>
          <Form form={form} disabled>
            <Form.Item name="checkboxOne" label="(1)" layout='horizontal'>
              <Checkbox.Group>
                {checkboxOne.map((el) => {
                  return (
                    <Checkbox key={el.value} value={el.value} style={{ lineHeight: '32px' }}>
                      {el.label}
                    </Checkbox>
                  );
                })}
              </Checkbox.Group>
            </Form.Item>
            <Form.Item name="checkboxTwo" label="(2)" layout='horizontal'>
              <Checkbox.Group>
                {checkboxTwo.map((el) => {
                  return (
                    <Checkbox key={el.value} value={el.value} style={{ lineHeight: '32px' }}>
                      {el.label}
                    </Checkbox>
                  );
                })}
              </Checkbox.Group>
            </Form.Item>
            <Form.Item name="checkboxThree" label="(3)" layout='horizontal'>
              <Checkbox.Group>
                {checkboxThree.map((el) => {
                  return (
                    <Checkbox key={el.value} value={el.value} style={{ lineHeight: '32px' }}>
                      {el.label}
                    </Checkbox>
                  );
                })}
              </Checkbox.Group>
            </Form.Item>
            <Form.Item name="other" label="(4)" layout='horizontal'>
              <Checkbox key='其他' value='其他' checked={!!other} style={{ lineHeight: '32px' }}>其他</Checkbox>
            </Form.Item>
            {other ? (
              <div className={styles.mark}>
                <span>补充说明：</span>{other}
              </div>
            ) : null}
          </Form>
          <Divider />
          <div className={styles.title}>作业风险</div>
          <Form form={homewordRiskForm} disabled>
            <Form.Item name="jobRisk" label=' ' layout='horizontal'>
              <Checkbox.Group>
                {checkboxFive.map((el) => {
                  return (
                    <Checkbox key={el.value} value={el.value} style={{ lineHeight: '32px' }}>
                      {el.label}
                    </Checkbox>
                  );
                })}
              </Checkbox.Group>
            </Form.Item>
            <Form.Item name="otherRisk" label=" " layout='horizontal'>
              <Checkbox key='其他' value='其他' checked={!!otherRisk} style={{ lineHeight: '32px' }}>其他</Checkbox>
            </Form.Item>
            {otherRisk ? (
              <div className={styles.mark}>
                <span>补充说明：</span>{otherRisk}
              </div>
            ) : null}
          </Form>
        </Card>
        <Card title="过程风险预控"  extra={<RightOutline fontSize={18} color='var(--adm-color-weak)' onClick={() => history.push(`/twotickets/common/check-list/safety-risk/${data?.id}?ticketType=${data?.ticketType}`)} />}>
          <SafetyRiskControlComponent workBaseRisks={data?.workBaseRisks} />
        </Card>
        <Card>
          <Form form={remarksForm} disabled>
            <Form.Item name="remark" label='备注'>
              <Input />
            </Form.Item>
          </Form>
        </Card>
      </Space>
    </div>
  );
});

export default SecurityRiskControlContent;
