import dayjs from 'dayjs';
import {
  createText<PERSON>ield,
  createDate<PERSON><PERSON>,
  createText<PERSON>rea<PERSON>ield,
  createSelectionField,
  createDateRangeField,
  createSignature<PERSON>ield,
  createRadioField,
} from './field-builder';
import { FormFieldConfig } from './form-field-group';
import { Base } from '@ant-design/plots';
import { StrategyTypes } from '../dynamic-form/typing';

// 配合精确的类型定义：
type WorkTicketConfig = {
  pass: {
    get: (state: WorkflowState, data: any) => FormFieldConfig<any>[];
  };
  reject: {
    get: (state: WorkflowState, data: any) => FormFieldConfig<any>[];
  };
};

// ---------------------------
// 1. 核心类型定义
// ---------------------------
type WorkflowState = number | 'BASE';

type ProcessType = 'pass' | 'reject';

interface StateStrategy<T extends ProcessType> {
  baseFields: FormFieldConfig<T>[];
  specificFields: (data: any) => FormFieldConfig<T>[];
}

// ---------------------------
// 2. 策略基类抽象
// ---------------------------
abstract class BaseStrategyRegistry {
  protected static _strategies: Record<string, StateStrategy<any>> = {};
  protected static _rejectStrategies: Record<string, StateStrategy<any>> = {};
  static createField(config: {
    type: 'text' | 'date' | 'select' | 'signature' | 'dateRange' | 'textarea' | 'radio';
    name: string;
    label: string;
    options?: Record<string, any>;
  }) {
    const creators = {
      text: createTextField,
      date: createDateField,
      select: createSelectionField,
      signature: createSignatureField,
      dateRange: createDateRangeField,
      textarea: createTextAreaField,
      radio: createRadioField
    };
    return creators[config.type](config.name, config.label, {
      precision: 'minute',
      formatter: (value: string | number | Date | dayjs.Dayjs | null | undefined) => dayjs(value).format('YYYY/MM/DD HH:mm'),
      ...(config?.options || {}),
    } as any);
  }
  static getStrategy(state: WorkflowState): StateStrategy<any> {
    return this._strategies[state] || this._strategies.BASE;
  }

  static getRejectStrategy(state: WorkflowState): StateStrategy<any> {
    return this._rejectStrategies[state] || this._rejectStrategies.BASE;
  }
}

// ---------------------------
// 3. 公共配置工厂
// ---------------------------
class CommonStrategyFactory {
  static readonly createStaffFields = (data: any): FormFieldConfig<any>[] => {
    const STAFF_FIELDS = {
      动火执行人签名: {
        checkUserName: {
          strategy: 'signature',
          label: '${key}',
        },
        checkTime: {
          strategy: 'date',
          label: '${key}时间',
        },
      },
      消防监护人签字: {
        confirmSignName2: {
          strategy: 'signature',
          label: '${key}',
        },
        confirmSignTime2: {
          strategy: 'date',
          label: '${key}时间',
        },
      },
      动火工作负责人签名: {
        confirmSignName3: {
          strategy: 'signature',
          label: '${key}',
        },
        confirmSignTime4: {
          strategy: 'date',
          label: '${key}时间',
        },
      },
      企业安监负责人签名: {
        confirmSignName5: {
          strategy: 'signature',
          label: '${key}',
        },
        confirmSignTime5: {
          strategy: 'date',
          label: '${key}时间',
        },
      },
      企业主管安全生产的领导签名: {
        leaderName: {
          strategy: 'signature',
          label: '${key}',
        },
        reviewTime: {
          strategy: 'date',
          label: '领导签名时间',
        },
      },
      允许动火时间: {
        allowBeginTime: {
          strategy: 'date',
          label: '${key}',
        },
        submitTime: {
          strategy: 'date',
          label: '结束执行情况填报时间',
        },
      },
      动火执行人确认签字: {
        finalSignName1: {
          strategy: 'signature',
          label: '${key}',
        },
        finalSignTime1: {
          strategy: 'date',
          label: '动火执行人确认时间',
        },
      },
      消防监护人确认签字: {
        finalSignName2: {
          strategy: 'signature',
          label: '${key}',
        },
        finalSignTime2: {
          strategy: 'date',
          label: '消防监护人确认时间',
        },
      },
    };
    type STAFF_FIELDS_TYPE = keyof typeof STAFF_FIELDS;
    return Object.keys(STAFF_FIELDS).flatMap((key: string) => {
      const STAFF_FIELD = STAFF_FIELDS[key as STAFF_FIELDS_TYPE];
      type STAFF_FIELD_TYPE = keyof typeof STAFF_FIELD;
      return Object.keys(STAFF_FIELD).map((item: string, index: number) => {
        const fieldConfig = STAFF_FIELD[item as STAFF_FIELD_TYPE] as any;
        // 替换 label 中的 ${key}
        const label = fieldConfig.label.replace('${key}', key);
        if (fieldConfig.strategy === 'signature') {
          return this.createSignatureField(item, label, {
            fieldProps: {
              isCanCustom: true
            },
            style: {
              marginTop: index === 0 ? '1em' : '',
            },
          });
        } else {
          return this.createDateField(item, label, {
            style: {
              marginTop: index === 0 ? '1em' : '',
              '--align-items': 'center',
            },
          });
        }
      });
    });
  };
  private static createSignatureField(name: string, label: string, options?: Record<string, any>) {
    return BaseStrategyRegistry.createField({
      type: 'signature',
      name,
      label,
      options: {
        layout: 'vertical',
        required: true,
        rules: [
          {
            validator: (_: any, v: string | any[]) => {
              if (!!v?.length) {
                return Promise.resolve();
              } else {
                return Promise.reject(`请选择${label}`);
              }
            },
          },
        ],
        ...options,
      },
    });
  }

  private static createDateField(name: string, label: string, options?: Record<string, any>) {
    return BaseStrategyRegistry.createField({
      type: 'date',
      name,
      label,
      options: {
        precision: 'minute',
        formatter: (value: any) => dayjs(value).format('YYYY/MM/DD HH:mm'),
        layout: 'horizontal',
        style: { '--prefix-width': '10em' },
        required: true,
        rules: [
          {
            validator: (_: any, v: any) => {
              if (!!v) {
                return Promise.resolve();
              } else {
                return Promise.reject(`请选择${label}`);
              }
            },
          },
        ],
        ...options,
      },
    });
  }
}

// ---------------------------
// 4. 具体策略实现
// ---------------------------
class WorkTicketStrategyRegistry extends BaseStrategyRegistry {
  static {
    // 初始化公共配置
    this._strategies.BASE = this.createBaseStrategy();
    this._rejectStrategies.BASE = this.createRejectBase();
  }

  private static createBaseStrategy(): StateStrategy<any> {
    return {
      baseFields: [
        this.createField({
          type: 'textarea',
          name: 'opinion',
          label: '审核意见',
        }),
      ],
      specificFields: () => [],
    };
  }

  private static createRejectBase(): StateStrategy<any> {
    return {
      baseFields: [this.createField({ type: 'textarea', name: 'opinion', label: '审核意见' })],
      specificFields: () => [],
    };
  }

  // 动态注册状态策略
  static registerState(
    state: WorkflowState,
    config: {
      base?: FormFieldConfig<any>[];
      specific?: (data: any) => FormFieldConfig<any>[];
      reject?: FormFieldConfig<any>[];
    },
  ) {
    this._strategies[state] = {
      baseFields: config.base || [],
      specificFields: config.specific || (() => []),
    };

    if (config.reject) {
      this._rejectStrategies[state] = {
        baseFields: config.reject,
        specificFields: () => [],
      };
    }
  }
}

// ---------------------------
// 5. 策略注册
// ---------------------------
/**
 * 注册两票类型的策略
 * @param type 两票类型 hotWorkOne | hotWorkTwo
 */
export function registerWorkTicketStrategies(type: number, state: number) {
  const types = {
    hotWork: [6, 7], // 动火票6（一）7（二）
  };
  if (types.hotWork.includes(type)) {
    WorkTicketStrategyRegistry.registerState(type === 6 ? 5.3 : 5, {
      base: [],
      specific: (data) => [
        BaseStrategyRegistry.createField({
          type: 'dateRange',
          name: 'fireTime',
          label: '批准动火时间区间',
          options: {
            layout: 'vertical',
            required: true,
            rules: [
              {
                validator: (_: any, v: { startDate: any; endDate: any }) => {
                  const { startDate, endDate } = v;
                  if (!startDate || !endDate) {
                    return Promise.reject('请选择批准动火时间区间');
                  }
                  if (startDate > endDate) {
                    return Promise.reject('开始时间不能大于结束时间');
                  }
                  return Promise.resolve();
                },
              },
            ],
          },
        }),
        BaseStrategyRegistry.createField({
          type: 'textarea',
          name: 'opinion',
          label: '审核意见',
          options: { layout: 'vertical' },
        }),
      ],
    });
    if (type === 6) {
      WorkTicketStrategyRegistry.registerState(5.6, {
        base: [],
        specific: (data) => [
          BaseStrategyRegistry.createField({
            type: 'date',
            name: 'confirmTime',
            label: '确认时间',
            options: {
              layout: 'horizontal',
              style: {
                '--prefix-width': '10em',
              },
              required: true,
              rules: [
                {
                  required: true,
                  message: '请选择确认时间',
                },
              ],
            },
          }),
          BaseStrategyRegistry.createField({
            type: 'textarea',
            name: 'opinion',
            label: '审核意见',
            options: { layout: 'vertical' },
          }),
        ],
      });
    }
    WorkTicketStrategyRegistry.registerState(type === 6 ? 14 : 12, {
      base: [],
      specific: (data) => [
        BaseStrategyRegistry.createField({
          type: 'date',
          name: 'allowTime',
          label: '运行许可动火时间',
          options: {
            layout: 'horizontal',
            style: {
              '--prefix-width': '10em',
            },
            required: true,
            rules: [
              {
                required: true,
                message: '请选择确认时间',
              },
            ],
          },
        }),
        BaseStrategyRegistry.createField({
          type: 'textarea',
          name: 'opinion',
          label: '审核意见',
          options: { layout: 'vertical' },
        }),
      ],
    });
    WorkTicketStrategyRegistry.registerState(type === 6 ? 16 : 14, {
      base: [
        BaseStrategyRegistry.createField({
          type: 'text',
          name: 'checkResult',
          label: '气体测定值',
          options: {
            layout: 'horizontal',
            style: {
              '--prefix-width': '10em',
            },
            required: true,
            rules: [
              {
                required: true,
                message: '请输入气体测定值',
              },
            ],
          },
        }),
      ],
      specific: (data: any) => CommonStrategyFactory.createStaffFields(data),
    });
    WorkTicketStrategyRegistry.registerState(type === 6 ? 20 : 18, {
      base: [],
      specific: (data) => [
        BaseStrategyRegistry.createField({
          type: 'date',
          name: 'allowTime',
          label: '工作票终结时间',
          options: {
            layout: 'horizontal',
            precision: 'minute',
            formatter: (value: any) => dayjs(value).format('YYYY/MM/DD HH:mm'),
            style: { '--prefix-width': '10em' },
          },
        }),
        BaseStrategyRegistry.createField({
          type: 'textarea',
          name: 'opinion',
          label: '审核意见',
          options: { layout: 'vertical' },
        }),
      ],
    });
    WorkTicketStrategyRegistry.registerState(type === 6 ? 22 : 20, {
      base: [],
      specific: (data) => [
        BaseStrategyRegistry.createField({
          type: 'radio',
          name: 'auditResult',
          label: '审核结果',
          options: {
            layout: 'horizontal',
            options: ['合格', '不合格'],
            initialValue: '合格',
            required: true
          },
        }),
        BaseStrategyRegistry.createField({
          type: 'textarea',
          name: 'opinion',
          label: '审核意见',
          options: { layout: 'vertical' },
        }),
      ],
    });
  }
}

class ConfigComposer {
  static compose(state: WorkflowState, data: any): FormFieldConfig<any>[] {
    // 可扩展的策略注册表
    const strategy = WorkTicketStrategyRegistry.getStrategy(state);
    return [...strategy.baseFields, ...strategy.specificFields(data)];
  }
  static rejectCompose(state: WorkflowState, data: any): FormFieldConfig<any>[] {
    const strategy = WorkTicketStrategyRegistry.getRejectStrategy(data?.sub || 'BASE');
    return [...strategy.baseFields, ...strategy.specificFields(data)];
  }
}

const WORK_TICKET_CONFIG = {
  hotWork: {
    pass: {
      // 配置项
      // 组合基础配置 + 状态特有配置
      get: (state: WorkflowState, data: any) => ConfigComposer.compose(state, data),
    },
    reject: {
      // 配置项
      get: (state: WorkflowState, data: any) => ConfigComposer.rejectCompose(state, data),
    },
  },
} satisfies Record<string, WorkTicketConfig>;

type WorkTicketType = keyof typeof WORK_TICKET_CONFIG;

export class WorkTicketFactory {
  static createFormConfig(type: WorkTicketType, data: any) {
    const config = WORK_TICKET_CONFIG[type].pass.get(data?.status, data);
    // 根据审批结果选择配置
    if (data?.pass === 'true') {
      return this.resolveConfig(config, data);
    }

    // 不通过时的处理
    const rejectConfig = WORK_TICKET_CONFIG[type].reject.get('BASE', data);
    return this.resolveConfig(rejectConfig, data);
  }

  private static resolveConfig(config: FormFieldConfig<any>[], data: any) {
    return config.map((item) => ({
      ...item,
      initialValue: item.getInitialValue?.(data) ?? item?.initialValue ??  null,
    }));
  }
}
