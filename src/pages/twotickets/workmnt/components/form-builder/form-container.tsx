import { Footer } from "antd-mobile";
import Form<PERSON>ieldG<PERSON>, { FormFieldConfig } from "./form-field-group";
import { StrategyTypes } from "../dynamic-form/typing";

interface FormContainerProps<T extends StrategyTypes> {
  fields: FormFieldConfig<T>[];
  data?: any;
}

export const FormContainer = <T extends StrategyTypes>({ fields, data }: FormContainerProps<T>) => {
  return (
    <div className="form-container">
      {fields.map((field, index) => (
        <FormFieldGroup key={index} config={field} data={data} />
      ))}
      <Footer>已经到底了</Footer>
    </div>
  );
};
