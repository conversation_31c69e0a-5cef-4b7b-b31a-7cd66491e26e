import { DatePrecision } from 'antd-mobile/es/components/date-picker/date-picker-date-utils';
import { FormFieldConfig } from './form-field-group';
import { FormItemProps } from 'antd-mobile';
import { StrategyTypes } from '../dynamic-form/typing';
import dayjs from 'dayjs';
import { RefObject } from 'react';

type FieldConfig = FormItemProps & {
  name: string;
  label: string;
  options?: string[];
  layout?: 'horizontal' | 'vertical';
  required?: boolean;
  style?: React.CSSProperties;
  formatter?: (value: any) => string;
};

export const createTextField = <T extends StrategyTypes>(
  name: string,
  label: string,
  options?: Partial<FieldConfig>,
): FormFieldConfig<T> => ({
  strategy: 'input',
  name,
  label,
  ...options,
  props: {
    placeholder: `请输入${label}`,
    style: { '--text-align': 'right' },
  },
});

export const createDateField = <T extends StrategyTypes>(
  name: string,
  label: string,
  options: Partial<FieldConfig> & {
    precision: DatePrecision;
    formatter: (value: any) => string;
  },
): FormFieldConfig<T> => ({
  strategy: 'datepicker',
  name,
  label,
  trigger: 'onConfirm',
  onClick: (e, datePickerRef: RefObject<DatePickerRef>) => {
    datePickerRef.current?.open();
  },
  ...options,
  props: {
    precision: options.precision,
    children: (value: any) => <div style={{ textAlign: 'right' }}>{value ? options.formatter(value) : '请选择'}</div>,
  },
});

export const createDateRangeField = <T extends StrategyTypes>(
  name: string,
  label: string,
  options: Partial<FieldConfig>,
): FormFieldConfig<T> => ({
  strategy: 'date-range',
  name,
  label,
  ...options,
  props: {
    placeholder: '请选择',
    precision: 'minute',
    style: {
      '--text-align': 'left',
    },
    children: (value: any) =>
      value?.startDate || value?.endDate ? (
        <div
          style={{
            textAlign: 'left',
            '--prefix-width': '10em',
          }}
        >
          {value.startDate && dayjs(value.startDate).format('YYYY/MM/DD HH:mm')}~
          {value.endDate && dayjs(value.endDate).format('YYYY/MM/DD HH:mm')}
        </div>
      ) : (
        <div
          style={{
            textAlign: 'left',
            color: '#ccc',
          }}
        >
          请选择
        </div>
      ),
  },
});

export const createTextAreaField = <T extends StrategyTypes>(
  name: string,
  label: string,
  options?: Partial<FieldConfig>,
): FormFieldConfig<T> => ({
  strategy: 'textarea',
  name,
  label,
  ...options,
  props: {
    placeholder: `请输入${label}`,
  },
});

export const createSelectionField = <T extends StrategyTypes>(
  name: string,
  label: string,
  options: Partial<FieldConfig>,
): FormFieldConfig<T> => ({
  strategy: 'select',
  name,
  label,
  ...options,
  props: {
    placeholder: `请选择${label}`,
    options: options.options,
    ...(options?.style ? { style: options.style } : {}),
  },
});

export const createSignatureField = <T extends StrategyTypes>(
  name: string,
  label: string,
  options: Partial<FieldConfig>,
): FormFieldConfig<T> => ({
  strategy: 'signature',
  name,
  label,
  ...options,
  props: {
    placeholder: `请选择${label}`,
    ...(options?.style ? { style: options.style } : {}),
  },
});

// 创建单选createRadioField
export const createRadioField = <T extends StrategyTypes>(
  name: string,
  label: string,
  options: Partial<FieldConfig> & {
    options: string[];
  },
): FormFieldConfig<T> => ({
  strategy: 'radio',
  name,
  label,
  ...options,
  props: {
    placeholder: `请选择${label}`,
    options: options.options,
    ...(options?.style ? { style: options.style } : {}),
  },
});
