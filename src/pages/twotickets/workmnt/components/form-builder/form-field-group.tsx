import { FormItemProps, Rule } from 'antd/es/form';
import { StrategyProps } from '../dynamic-form/typing';
import { strategyFactory } from '../dynamic-form/strategy-factory';
import { DynamicFormItem } from '../dynamic-form/dynamic-form-item';

export interface FormFieldConfig <T extends StrategyTypes> extends FormItemProps {
  strategy: string;
  name: string;
  label: string;
  props?: Record<string, any>;
  rules?: Rule[];
  options?: any;
  getInitialValue?: (data: any) => any;
  layout?: 'horizontal' | 'vertical';
  style?: React.CSSProperties;
  strategyProps?: StrategyProps<T>;
  
};

interface FormFieldGroupProps<T extends StrategyTypes> {
  config: FormFieldConfig<T>;
  data: any;
}

const FormFieldGroup = <T extends StrategyTypes>({ config, data }: FormFieldGroupProps<T>) => {
  const strategy = strategyFactory.getStrategy(config.strategy);
  const initialValue = config.getInitialValue?.(data) ?? data?.[config.name] ?? config.initialValue;
  return (
    <div className="field-group" style={{
      background: "#fff"
    }}>
      <DynamicFormItem
        {...strategy?.defaultProps}
        {...config}
        strategyType={config.strategy}
        name={config.name}
        label={config.label}
        strategy={config.strategy}
        initialValue={initialValue}
        rules={config.rules}
        strategyProps={config.props}
      />
    </div>
  );
};


export default FormFieldGroup;