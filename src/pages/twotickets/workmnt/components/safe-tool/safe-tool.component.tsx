import { List, Space } from 'antd-mobile';
import { styled } from 'umi';
import useInterleave from '@/hooks/useInterleave';
import { ListCard } from '@/component/list-card/list-card.component';
import { OperatorStandardTool } from '@/styles/twotickets.style';

const SafeToolComponentWrapper = styled(Space)`
  width: 100%;
  font-family: PingFang SC;
  .adm-card-body {
    padding-bottom: 5vw;
  }
  .module_name {
    margin: 4.6297vw 2.9631vw 2.5vw;
    font-size: 4.2592vw;
    font-weight: 500;
    letter-spacing: 0vw;
    line-height: 6.1675vw;
    color: rgba(51, 51, 51, 1);
  }
`;


type SafeToolComponentProps = {
  data: any;
};

const SafeToolComponent = (props: SafeToolComponentProps) => {
  const { data } = props;
  const SplitPoint = (
    <div
      style={{
        display: 'inline-block',
        width: '.7408vw',
        height: '.7408vw',
        borderRadius: '.3703vw',
        margin: '0 1.1111vw',
        backgroundColor: 'rgba(102, 102, 102, 1)',
      }}
    />
  );

  return (
    <SafeToolComponentWrapper direction="vertical">
      <div className="module_name">安全工器具</div>
      <ListCard title="全部安全工器具" isMore data={data?.workBaseTools || []} maxLength={3} moreLink={`/twotickets/common/check-list/standard-operator-tool/${data?.id}?ticketType=${data?.ticketType}`}>
        {(dataList) => (
          <OperatorStandardTool
            style={{
              '--border-top': 'none',
              '--border-bottom': 'none',
            }}
          >
            {dataList?.map((item: any, index: number) => (
              <List.Item key={index}>
                <div className="tool_name">{item?.toolName}</div>
                <div className="tool_info">
                  {useInterleave(
                    [item?.typeName, item?.kind, item?.responsibilityName]?.filter((i) => i),
                    SplitPoint,
                  )}
                </div>
              </List.Item>
            ))}
          </OperatorStandardTool>
        )}
      </ListCard>
    </SafeToolComponentWrapper>
  );
};
export default SafeToolComponent;
