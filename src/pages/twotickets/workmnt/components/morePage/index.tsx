import { Popup } from 'antd-mobile';
import { useState, forwardRef, useImperativeHandle } from 'react';
import styles from './index.less';
import NavBarComponent from '@/component/navbar/nav-bar.component';
import ToTheEnd from '@/pages/twotickets/workmnt/mechanical/components/toTheEnd/index';

// import { history } from 'umi';
interface Iprops {
  // morePage: () => void;
  ref: any;
  children?: React.ReactNode;
  title?: string; //标题
  right?: React.ReactNode; //右侧按钮
}

/**
 * 更多页面
 * @param props
 * @param props.children 子组件
 * @param props.title 标题
 * @param props.right 右侧按钮
 * @returns
 */

const MorePage: React.FC<Iprops> = forwardRef((props, ref) => {
  const { children, title = '更多', right = null } = props;
  const [visibleCloseRight, setVisibleCloseRight] = useState(false);

  useImperativeHandle(ref, () => ({
    morePage: () => {
      setVisibleCloseRight(true);
      // window.history.pushState({}, '', '/new-url');
    },
  }));
  return (
    <Popup position="right" visible={visibleCloseRight}>
      <div className={styles.morePage}>
        <NavBarComponent
          defineTitle={title}
          right={right}
          onBack={() => {
            setVisibleCloseRight(false);
            // history.back();
          }}
        />
        <div style={{ paddingTop: '1rem' }}>
          {children}
          <ToTheEnd />
        </div>
      </div>
    </Popup>
  );
});
export default MorePage;
