import { useEffect, useMemo } from 'react';

import { Popup, Form, ConfigProvider } from 'antd-mobile';
import { DeleteOutline, CloseOutline } from 'antd-mobile-icons';

import { styled } from 'umi';

const PopupTitle = styled.div.attrs({ className: (props: { className: any }) => `${props.className} popup-title` as any })`
  box-sizing: border-box;
  font-family: PingFang SC;
  font-size: 1.6rem;
  font-weight: 400;
  letter-spacing: 0rem;
  line-height: 2.3167rem;
  color: rgba(0, 0, 0, 1);
  text-align: center;
  vertical-align: top;
  margin-top: 1.0667rem;
  margin-bottom: 1.0667rem;
`;

const PopupContainer = styled.div.attrs({ className: (props: { className: any }) => `${props.className} popup-container` as any })`
  flex: 1;
  box-sizing: border-box;
  width: 100%;
  position: relative;
  margin-bottom: 1.2rem;
  border-top: 0.0333rem solid rgba(230, 230, 230, 1);
  border-bottom: 0.0333rem solid rgba(230, 230, 230, 1);
  .body {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background-color: #fff;
    .adm-list-body {
      overflow-y: auto;
    }
    .adm-list,
    .adm-list-body,
    .adm-list-body-inner {
      height: 100%;
    }
    .adm-form-item-label {
      font-size: 1.6rem;
      font-weight: 500;
      letter-spacing: 0rem;
      line-height: 2.3333rem;
      color: rgba(51, 51, 51, 1);
      margin-bottom: 1.3rem;
    }
    .adm-list-item {
      padding: 0 1.6rem;
    }
    .adm-text-area {
      box-sizing: border-box;
      padding: 0.7333rem 1.4667rem;
      border-radius: 0.6667rem;
      background: rgba(245, 245, 245, 1);
      // 提示文字改小点
      .adm-text-area-element::placeholder {
        font-size: 1.2rem;
      }
    }
  }
`;

export const PopupFooter = styled.div.attrs({ className: (props: { className: any }) => `${props.className} popup-footer` as any })`
  box-sizing: border-box;
  overflow: hidden;
  border-radius: 1.2rem;
  margin: 0 1.2rem 0.8rem;
  border: 0.1333rem solid rgba(17, 84, 237, 1);
  .btn {
    display: inline-block;
    width: 50%;
    text-align: center;
    height: 4rem;
    line-height: 4rem;
    font-size: 1.3997rem;
    font-weight: 400;
    letter-spacing: 0rem;
    color: #2b488c;
    &.btn-submit {
      color: #fff;
      background: rgba(17, 84, 237, 1);
    }
  }
`;

interface Iprops {
  visible: boolean;
  setVisible: (val: boolean) => void;
  title: string;
  children: React.ReactNode;
  footer?: (handleSubmit: () => void, handleReset: () => void, handleCustom: () => void) => React.ReactNode;
  onSubmit: (values: any) => void;
  value?: any;
  isDeleteBottom?: boolean; //删除按钮底部还是顶部，默认底部
}
const FormPopup = (props: Iprops) => {
  const { visible, setVisible, title, children, footer, onSubmit, value, isDeleteBottom = false } = props;
  const [form] = Form.useForm();

  const handleReset = () => {
    form.resetFields();
  };

  const handleSubmit = () => {
    form.validateFields().then(() => {
      form.submit();
    });
  };

  const handleCustom = () => {
    form.submit();
  };

  useEffect(() => {
    if (visible) {
      if (value) {
        form.resetFields();
        form.setFieldsValue(value);
      } else {
        form.resetFields();
      }
    }
  }, [visible, value]);

  return (
    <ConfigProvider>
      <Popup
        visible={visible}
        onMaskClick={() => {
          setVisible(false);
        }}
        showCloseButton
        destroyOnClose={true}
        onClose={() => {
          setVisible(false);
        }}
        closeIcon={
          value?.id && !isDeleteBottom ? (
            <div
              onClick={() => {
                onSubmit(value?.id);
              }}
            >
              <DeleteOutline />
            </div>
          ) : (
            <CloseOutline />
          )
        }
        bodyStyle={{
          borderTopLeftRadius: '8px',
          borderTopRightRadius: '8px',
          minHeight: '90vh',
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        {visible && (
          <>
            <PopupTitle>{title}</PopupTitle>
            <PopupContainer>
              <Form
                form={form}
                layout="horizontal"
                className="body"
                onFinish={(values) => {
                  onSubmit(values);
                  setVisible(false);
                }}
              >
                {children}
              </Form>
            </PopupContainer>
            {footer ? (
              footer(handleSubmit, handleReset, handleCustom)
            ) : (
              <PopupFooter>
                {!value?.id ? (
                  <span
                    className="btn btn-reset"
                    onClick={() => {
                      setVisible(false);
                    }}
                  >
                    取消
                  </span>
                ) : isDeleteBottom ? (
                  <span
                    className="btn btn-delete"
                    style={{
                      color: 'red',
                    }}
                    onClick={() => {
                      onSubmit(value?.id);
                      setVisible(false);
                    }}
                  >
                    删除
                  </span>
                ) : (
                  <span className="btn btn-reset" onClick={handleReset}>
                    重置
                  </span>
                )}

                <span className="btn btn-submit" onClick={handleSubmit}>
                  确定
                </span>
              </PopupFooter>
            )}
          </>
        )}
      </Popup>
    </ConfigProvider>
  );
};
export default FormPopup;
