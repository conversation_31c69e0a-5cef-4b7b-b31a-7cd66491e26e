/**
 * @description 工作票执行-检修交代（请交代设备是否可运行）
 */

import React, { forwardRef, useImperativeHandle, useState, useRef, useEffect } from 'react';
import { Card, Form, Input, TextArea, Space, ActionSheet } from 'antd-mobile';
import styles from './index.less';
import { RightOutline } from 'antd-mobile-icons';
import type { Action } from 'antd-mobile/es/components/action-sheet';

interface CanRunProps {
  value?: number;
  onChange?: (value: number) => void;
  ref: any;
  options: any[];
  disabled: boolean;
}
//是否可投运
export const CanRun: React.FC<CanRunProps> = forwardRef((props, ref) => {
  const { value = '', onChange, options, disabled } = props;
  const [visible, setVisible] = useState(false);

  useImperativeHandle(ref, () => ({
    setVisible: (val: boolean) => {
      setVisible(val);
    },
  }));

  console.log(value, '======================')

  return (
    <>
      <div
        onClick={() => {
          if (!disabled) {
            setVisible(true);
          }
        }}
        className={styles.selectValue}
      >
        {options?.filter((el) => el.key === value)[0]?.text}
      </div>
      <ActionSheet
        cancelText="取消"
        visible={visible}
        actions={options}
        onClose={() => setVisible(false)}
        onAction={(action: Action) => {
          onChange(action?.key);
          setVisible(false);
        }}
      />
    </>
  );
});

interface Iprops {
  ref: any;
  current: any; //详情参数
  disabled?: boolean; //表单是否可编辑
  showOutTilte?: boolean
}
const MaintenanceIstructions: React.FC<Iprops> = forwardRef((props, ref) => {
  const { current, disabled = false, showOutTilte = false } = props;
  const [form] = Form.useForm();
  const { workFlowExecute } = current?.workTicketExecute || {};

  const canRunRef = useRef(null);

  const actions: Action[] = [
    { text: '可投运', key: 1 },
    { text: '不可投运', key: 0 },
  ];

  useEffect(() => {
    if (workFlowExecute) {
      form.setFieldsValue({
        ...workFlowExecute,
        repairOrder: workFlowExecute.repairOrder || '', // 防止null值
      });
    }
  }, [workFlowExecute, form]);

  useImperativeHandle(ref, () => ({
    ...form,
  }));

  return (
    <>
      {showOutTilte && <div className={styles.moduleName}>工作票执行</div>}
      <Card title="检修交代（请交代设备是否可运行）" className={styles.myCard}>
        <Form form={form}>
          <Form.Item name="repairOrder" label="检修交代" rules={[{ required: true }]}>
            <TextArea readOnly={disabled} className={styles.myTextArea} placeholder="请输入内容" />
          </Form.Item>
          <Form.Item
            name="canRun"
            label={
              <div className={styles.myLabel}>
                <span>设备是否可投运</span>
                {!disabled && (
                  <Space
                    onClick={() => {
                      canRunRef.current?.setVisible(true);
                    }}
                  >
                    请选择
                    <RightOutline />
                  </Space>
                )}
              </div>
            }
            rules={[{ required: true }]}
          >
            <CanRun ref={canRunRef} options={actions} disabled={disabled} />
          </Form.Item>
          {workFlowExecute?.submitHeadName && (
            <Form.Item name="submitHeadName" label="工作负责人">
              <Input readOnly={true} />
            </Form.Item>
          )}
          {workFlowExecute?.submitTime && (
            <Form.Item name="submitTime" label="递交时间">
              <Input readOnly={true} />
            </Form.Item>
          )}
        </Form>
      </Card>
    </>

  );
});
export default MaintenanceIstructions;
