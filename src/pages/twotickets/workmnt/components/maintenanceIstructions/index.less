.myTextArea {
  background-color: rgba(245, 245, 245, 1);
  border-radius: .6rem;
  padding: .6rem;
}

.myLabel {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.myInput {
  display: none;
}

.selectValue {
  font-size: 1.5333rem;
  font-weight: 700;
  line-height: 2.2rem;
  color: rgba(51, 51, 51, 1);
}

.myCard {
  margin-bottom: 0.8rem;

  :global {
    .adm-card-body {
      padding: 0;
    }
  }
}

.moduleName {
  width: 100%;
  font-family: PingFang SC;
  margin: 49px 37px 28px;
  font-size: 46px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 66.61px;
  color: rgba(51, 51, 51, 1);
}