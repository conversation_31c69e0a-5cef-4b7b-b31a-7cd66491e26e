/**
 * @description 工作票执行-工作成员变更
 */
import React, { forwardRef, useImperativeHandle, useState, useEffect, useRef, useMemo } from 'react';
import { ListCard } from '@/component/new-list-card/list-card.component';
import { EditSOutline, AddCircleOutline } from 'antd-mobile-icons';
import styles from './index.less';
import MorePage from '@/pages/twotickets/workmnt/components/morePage/index';

import styled from 'styled-components';

import FormPopup from '@/pages/twotickets/workmnt/components/form-popup/index';
// import SelecDictOptionComponent from '@/component/select-dictoptions/select-dictoptions-component';
import SelectPopup from '../select-popup/index';
import { DatePickerItem } from '@/component/dynamic-form/items/date-picker-popup/date-picker-popup.component';
import dayjs from 'dayjs';

export const ChangeDivItem = styled.div`
  font-size: 1.6rem;
  padding: 0.8333rem 0;
  .item_top {
    display: flex;
    /* align-items: center; */
    font-size: 1.4rem;
    margin-bottom: 0.6333rem;
    .item_top_left {
      flex: 1;
    }
    .item_top_right {
      width: 3rem;
      font-size: 1.5333rem;
      text-align: right;
    }
  }
  .item_bottom {
    padding-left: 2rem;
    background: rgba(245, 245, 245, 1);
    border-radius: 0.6rem;
    height: 3.4rem;
    display: flex;
    align-items: center;
    font-size: 1.2rem;
    color: rgba(153, 153, 153, 1);
    span {
      &:nth-child(1) {
        display: inline-block;
        border: 1px solid rgba(253, 146, 40, 1);
        padding: 0 0.2667rem;
        color: rgba(253, 146, 40, 1);
        margin-right: 0.8333rem;
        border-radius: 0.4rem 0.4rem 0.4rem 0;
      }
      &:nth-child(3) {
        padding: 0 0.7667rem;
      }
      &:nth-child(4) {
        margin-right: 0.7667rem;
      }
    }
  }
  .item_top_flex {
    display: flex;

    .item_top_flex_right_new {
      font-size: 1.2rem;
      display: inline-block;
      border: 1px solid rgba(17, 84, 237, 1);
      padding: 0 0.2667rem;
      color: rgba(17, 84, 237, 1);
      margin-right: 0.8333rem;
      border-radius: 0.4rem 0.4rem 0.4rem 0;
    }
    .item_top_flex_right_name {
      font-size: 1.4rem;
      color: rgba(51, 51, 51, 1);
      font-weight: 500;
    }
  }
  .item_top_time {
    span {
      font-size: 1.2rem;
      color: rgba(102, 102, 102, 1);
      &:nth-child(2) {
        padding: 0 0.7667rem;
      }
    }
  }
`;

import { Form } from 'antd-mobile';
import UserPicker from '../user-picker/user-picker.component';

export const getUserName = (val, filed: 'id' | 'name' = 'id') => {
  if (Array.isArray(val)) {
    if (typeof val[0] === 'string') {
      return filed === 'id' ? val?.join(',') : null;
    } else {
      return val?.map((el) => el?.[filed]).join(',');
    }
  } else {
    return null;
  }
};

interface Iprops {
  current: any; //详情数据
  isAddAndEdit?: boolean; //是否有新增和编辑权限(默认false)
  ref: any;
  workClass: string[]; //工作班列表数组
}

interface Item {
  addHeadId: string;
  addHeadName: string;
  leaveHeadId: string;
  leaveHeadName: string;
  addUserName: string;
  memo: string;
  leaveUserName: string;
  id: string;
  addTime: string;
  headName: string;
  leaveTime: string;
}

const WorkMemberChange: React.FC<Iprops> = forwardRef((props, ref) => {
  const { current, isAddAndEdit = false, workClass } = props;

  const [data, setData] = useState([]);
  const [visible, setVisible] = useState(false);
  const [popupValue, setPoputValue] = useState<Item>();

  const morePageRef = useRef<any>(null);
  const editFn = (record: Item) => {
    let obj = {
      ...record,
      addHeadName: record?.headName ? record?.headName : current?.headName,
      leaveHeadName: record?.headName ? record?.headName : current?.headName,
      addTime: {
        date: dayjs(record?.addTime).format('YYYY-MM-DD HH:mm:ss') || Date.now(),
        mode: 'minute',
      }, //变动时间
      leaveTime: {
        date: dayjs(record?.leaveTime).format('YYYY-MM-DD HH:mm:ss') || Date.now(),
        mode: 'minute',
      }, //变动时间
      addHeadId: record?.addHeadId?.split(',') || current?.headId?.split(','), //新增-工作负责人
      leaveHeadId: record?.leaveHeadId?.split(',') || current?.headId?.split(','), //离去-工作负责人
    };
    setPoputValue(obj);
    setVisible(true);
    //判断是新增或编辑
  };

  useImperativeHandle(ref, () => ({
    getData: () => data?.map(({ id, ...rest }) => ({ ...rest })),
    setData: (val: any[]) => setData(val),
  }));

  useEffect(() => {
    if (current) {
      setData(current?.workTicketExecute?.workFlowExecuteUserExchanges || []);
    }
  }, [current]);

  return (
    <div style={{ marginBottom: '0.8rem' }}>
      <ListCard
        title={<div>工作成员变更</div>}
        isMore={true}
        refresh={[isAddAndEdit]}
        maxLength={3}
        rightButtonFn={isAddAndEdit ? editFn : null}
        moreLink={() => {
          morePageRef.current.morePage();
        }}
        data={data || []}
        children={(item: any[]) => {
          return (
            <div className={styles.listContent}>
              {item?.map((el, index) => {
                return (
                  <ChangeDivItem key={index}>
                    <div className="item_top">
                      <div>{`${index + 1}、`}</div>
                      <div className="item_top_left">
                        <div className="item_top_flex">
                          <span className="item_top_flex_right_new">新</span>
                          <span className="item_top_flex_right_name">{el.addUserName}</span>
                        </div>
                        <div className="item_top_time">
                          <span>{el.addTime}</span>
                          <span>·</span>
                          <span>{el.addHeadName}</span>
                          <span>(负责人)</span>
                        </div>
                      </div>
                      {isAddAndEdit ? (
                        <div
                          className="item_top_right"
                          onClick={() => {
                            editFn(el);
                          }}
                        >
                          <EditSOutline />
                        </div>
                      ) : null}
                    </div>
                    <div className="item_bottom">
                      <span>离</span>
                      <span>{el.leaveUserName}</span>
                      <span>·</span>
                      <span>{el.leaveTime}</span>
                      <span>{el.leaveHeadName}(负责人)</span>
                    </div>
                  </ChangeDivItem>
                );
              })}
            </div>
          );
        }}
      />
      {/* 【列表页面写此处】 */}
      <MorePage
        ref={morePageRef}
        title="工作负责人变更"
        right={
          isAddAndEdit ? (
            <div
              className={styles.addBtn}
              onClick={() => {
                editFn();
              }}
            >
              <AddCircleOutline />
            </div>
          ) : null
        }
      >
        <div className={styles.listContent} style={{ backgroundColor: '#FFF', padding: '0 1rem' }}>
          {data?.map((el: Item, index) => {
            return (
              <ChangeDivItem key={index}>
                <div className="item_top">
                  <div>{`${index + 1}、`}</div>
                  <div className="item_top_left">
                    <div className="item_top_flex">
                      <span className="item_top_flex_right_new">新</span>
                      <span className="item_top_flex_right_name">{el.addUserName}</span>
                    </div>
                    <div className="item_top_time">
                      <span>{el.addTime}</span>
                      <span>·</span>
                      <span>{el.addHeadName}</span>
                      <span>(负责人)</span>
                    </div>
                  </div>
                  {isAddAndEdit ? (
                    <div
                      className="item_top_right"
                      onClick={() => {
                        editFn(el);
                      }}
                    >
                      <EditSOutline />
                    </div>
                  ) : null}
                </div>
                <div className="item_bottom">
                  <span>离</span>
                  <span>{el.leaveUserName}</span>
                  <span>·</span>
                  <span>{el.leaveTime}</span>
                  <span>{el.leaveHeadName}(负责人)</span>
                </div>
              </ChangeDivItem>
            );
          })}
        </div>
      </MorePage>
      {/* 【弹窗组件写此处】弹出编辑/新增 */}
      <FormPopup
        visible={visible}
        setVisible={setVisible}
        value={popupValue}
        title={popupValue?.id ? '编辑' : '新增'}
        onSubmit={(values) => {
          console.log(values, '提交表单获取的数据');
          if (typeof values === 'string') {
            let arr = data.filter((el) => el.id !== values);
            setData(arr);
            return;
          }
          let obj = {
            ...values,
            addHeadId: getUserName(values.addHeadId, 'id') || popupValue?.addHeadId,
            addHeadName: getUserName(values.addHeadId, 'name') || popupValue?.addHeadName,
            leaveHeadId: getUserName(values.leaveHeadId, 'id') || popupValue?.leaveHeadId,
            leaveHeadName: getUserName(values.leaveHeadId, 'name') || popupValue?.leaveHeadName,
            addTime: dayjs(values.addTime.date).format('YYYY-MM-DD HH:mm:ss'),
            leaveTime: dayjs(values.leaveTime.date).format('YYYY-MM-DD HH:mm:ss'),
            addUserName: getUserName(values.memo, 'name') || popupValue?.addUserName,
            memo: getUserName(values.memo, 'id') || popupValue?.memo,
          };

          /* 判断修改还是新增，并写入组件data */
          if (popupValue?.leaveUserName) {
            let arr = data?.map((el) => {
              if (el.id === popupValue?.id) {
                return { ...el, ...obj };
              } else {
                return el;
              }
            });
            setData(arr);
          } else {
            console.log(data, obj, '===========');
            let arr = [...data, { ...obj, id: Math.floor(Math.random() * (9999 - 10 + 1)) + 10 }];
            // arr.push({ ...obj, id: Math.floor(Math.random() * (9999 - 10 + 1)) + 10 });
            setData(arr);
          }
        }}
      >
        <UserPicker
          label="新增人员"
          name="memo"
          showUsers={true}
          multiple={false}
          customMode={true}
          placeholder="请选择用户"
          title="用户选择"
        />
        {/* <Form.Item label="变动时间"></Form.Item> */}
        <DatePickerItem
          name="addTime"
          label="变动时间"
          rules={[{ required: true, message: '此项必选' }]}
          format="YYYY-MM-DD HH:mm"
          datePickerConfig={{
            dateType: 'single',
            value: { date: Date.now(), mode: 'minute' },
            initialMode: 'minute',
            onChange: (value) => console.log(value),
          }}
        />
        <UserPicker label="工作负责人" name="addHeadId" showUsers={true} placeholder="请选择用户" title="用户选择" />
        <Form.Item name="leaveUserName" label="离去人员">
          {/* 工作班成员（select） */}
          <SelectPopup options={workClass} />
        </Form.Item>
        {/* <Form.Item label="变动时间"></Form.Item> */}
        <DatePickerItem
          name="leaveTime"
          label="变动时间"
          rules={[{ required: true, message: '此项必选' }]}
          datePickerConfig={{
            dateType: 'single',
            value: { date: Date.now(), mode: 'minute' },
            initialMode: 'minute',
            onChange: (value) => console.log(value),
          }}
          format="YYYY-MM-DD HH:mm"
        />
        <UserPicker label="工作负责人" name="leaveHeadId" showUsers={true} placeholder="请选择用户" title="用户选择" />
      </FormPopup>
    </div>
  );
});
export default WorkMemberChange;
