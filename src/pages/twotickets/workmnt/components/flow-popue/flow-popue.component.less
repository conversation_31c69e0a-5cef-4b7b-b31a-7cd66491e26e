
.popup{
  .body{
    flex: 1;
    box-sizing: border-box;
    width: 100%;
    position: relative;
    margin-bottom: 35.9996px;
    border-radius: 0;
    .form {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;
      :global{
        .adm-list,
        .adm-list-body,
        .adm-list-body-inner {
          height: 100%;
        }
        .adm-form-item-label {
          font-size: 4.2593vw;
          font-weight: 500;
          letter-spacing: 0vw;
          line-height: 6.4814vw;
          color: rgba(51, 51, 51, 1);
          // margin-bottom: 3.6111vw;
        }
        .adm-text-area {
          box-sizing: border-box;
          padding: 2.0369vw 4.0742vw;
          border-radius: 1.8519vw;
          background: rgba(245, 245, 245, 1);
          // 提示文字改小点
          .adm-text-area-element::placeholder {
            font-size: 3.3333vw;
          }
        }
        .adm-form-item-has-error{
          .adm-form-item-label{
            color: rgba(252, 89, 90, 1);
          }
        }
      }
    }
  }
  .footer{
    padding: 1.0667rem 1.1667rem;
    box-sizing: border-box;
    background-color: #fff;
    .btn{
      font-size: 1.3997rem;
      font-weight: 400;
      letter-spacing: 0rem;
      line-height: 1.8667rem;
      color: rgba(255, 255, 255, 1);
    }
  }
}