import React, { forwardRef, useImperativeHandle, useMemo } from 'react';
import { Button, Form, Grid, Popup, FormProps } from 'antd-mobile';
import styles from './flow-popue.component.less';
import TwoTicketsNavBar, { TwoTicketsNavBarProps } from '@/component/twotickets-navbar/twotickets-navbar.component';

// 类型定义增强
export interface FlowPopupFormMethods {
  submit: () => void;
  reset: () => void;
  validate: () => Promise<any>;
  getFieldsValue: () => any;
}

export interface FlowPopupProps<V = any> extends Partial<FormProps> {
  // 弹窗控制
  visible: boolean;
  onClose: () => void;

  // 标题和导航栏
  title?: React.ReactNode;
  navBarProps?: Partial<TwoTicketsNavBarProps>;

  // 表单配置
  initialValues?: V;
  onSubmit?: (values: V) => Promise<void> | void;
  form?: FormProps['form'];

  // 底部操作栏
  showDefaultFooter?: boolean;
  footer?: React.ReactNode | ((methods: FlowPopupFormMethods) => React.ReactNode);
  footerGridColumns?: number | [number, number];

  // 样式配置
  popupStyle?: React.CSSProperties;
  bodyStyle?: React.CSSProperties;
  formStyle?: React.CSSProperties;

  // 状态控制
  loading?: boolean;
  disableAutoClose?: boolean;
}

// 默认底部操作栏
const DefaultFooter = ({ onSubmit, onCancel, loading }: { onSubmit: () => void; onCancel: () => void; loading?: boolean }) => (
  <Grid columns={2} gap={8} className={styles.footer}>
    <Grid.Item>
      <Button
        block
        size="large"
        className={styles.btn}
        style={{
          borderRadius: '3.3333vw',
          color: 'rgba(17, 84, 237, 1)',
          '--border-color': 'rgba(17, 84, 237, 1)',
        }}
        data-testid="operate-save-btn"
        onClick={onCancel}
        disabled={loading}
      >
        取消
      </Button>
    </Grid.Item>
    <Grid.Item>
      <Button
        block
        color="primary"
        size="large"
        className={styles.btn}
        style={{
          borderRadius: '3.3333vw',
        }}
        data-testid="operate-auth-btn"
        onClick={onSubmit}
        loading={loading}
      >
        提交
      </Button>
    </Grid.Item>
  </Grid>
);

const FlowPopup = forwardRef<FlowPopupFormMethods, FlowPopupProps>((props, ref) => {
  const {
    visible,
    onClose,
    title,
    navBarProps,
    children,
    footer,
    showDefaultFooter = true,
    footerGridColumns,
    onSubmit,
    initialValues,
    form: outerForm,
    loading,
    disableAutoClose = false,
    popupStyle,
    bodyStyle,
    formStyle,
    ...formProps
  } = props;

  const [innerForm] = Form.useForm();
  const form = outerForm || innerForm;

  // 暴露表单方法
  useImperativeHandle(ref, () => ({
    submit: () => form.submit(),
    reset: () => form.resetFields(),
    validate: () => form.validateFields(),
    getFieldsValue: () => form.getFieldsValue(),
  }));

  // 处理表单提交
  const handleSubmit = async (values: any) => {
    try {

      await onSubmit?.(values);
      if (!disableAutoClose) onClose();
    } catch (error) {
      console.error('Form submit error:', error);
    }
  };

  // 合并样式
  const mergedPopupStyle = useMemo(
    () => ({
      minHeight: '100vh',
      display: 'flex',
      flexDirection: 'column',
      borderRadius: 0,
      ...popupStyle,
    }),
    [popupStyle],
  );

  const mergedBodyStyle = useMemo(
    () => ({
      flex: 1,
      overflow: 'auto',
      ...bodyStyle,
    }),
    [bodyStyle],
  );

  return (
    <Popup
      visible={visible}
      onMaskClick={onClose}
      showCloseButton={false}
      destroyOnClose
      className={styles.popup}
      bodyStyle={mergedPopupStyle}
    >
      {/* 导航栏 */}
      <TwoTicketsNavBar title={title} bgcolor="#fff" color="#000" onBack={onClose} {...navBarProps} />

      {/* 表单主体 */}
      <div className={styles.body} style={mergedBodyStyle}>
        <Form
          form={form}
          className={styles.form}
          initialValues={initialValues}
          onFinish={handleSubmit}
          style={{
            '--adm-color-background': 'rgba(245, 245, 245, 1)',
            ...formStyle,
          }}
          {...formProps}
        >
          {children}
        </Form>
      </div>

      {/* 底部操作栏 */}
      {footer && (
        <div className={styles.footer}>
          {typeof footer === 'function'
            ? footer({
                onSubmit: () => form.submit(),
                onClose,
                form,
              })
            : footer}
        </div>
      )}

      {showDefaultFooter && !footer && <DefaultFooter onSubmit={() => form.submit()} onCancel={onClose} loading={loading} />}
    </Popup>
  );
});

export default FlowPopup;
