.safetyAnalysisContent {
  width: 100%;
  font-family: <PERSON>Fang SC;
  .moduleName {
    margin: 49px 37px 28px;
    font-size: 46px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 66.61px;
    color: rgba(51, 51, 51, 1);
  }
  .title {
    font-size: 46px;
    font-weight: 500;
    letter-spacing: 0px;
    line-height: 66.61px;
    margin-bottom: 20px;
  }
  .subTitle {
    position: absolute;
    font-size: 36px;
    font-weight: 400;
    letter-spacing: 0px;
    line-height: 52.13px;
    color: rgba(17, 84, 237, 1);
  }
  .level {
    text-indent: 180px;
  }
  .moreInfo {
    font-size: 42px;
    font-weight: 400;
    letter-spacing: 0px;
    line-height: 60.82px;
    color: rgba(17, 84, 237, 1);
    text-align: center;
  }
  :global {
    .adm-list {
      --border-top: none;
    }
    .adm-ellipsis {
      color: rgba(51, 51, 51, 1);
      font-size: 36px;
      font-weight: 400;
      letter-spacing: 0px;
      line-height: 52.13px;
      a {
        color: rgba(17, 84, 237, 1);
        font-weight: 500;
      }
    }
    .adm-collapse-panel-header {
      .adm-list-item-content-arrow {
        position: absolute;
      }
      .adm-list-item-content-main {
        text-indent: 100px;
        font-weight: 500;
      }
    }
    .adm-collapse-panel-content {
      padding-left: 100px;
    }
  }
}
.riskDescription :global  .adm-ellipsis {
  text-indent: 280px !important;
}
.controlMeasure :global .adm-ellipsis {
  text-indent: 180px;
}
