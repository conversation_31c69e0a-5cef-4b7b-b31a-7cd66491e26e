import { useEffect, useRef, useState } from 'react';
import styles from './safety-risk-control.component.less';
import _ from 'lodash';
import { Collapse, Divider, Ellipsis, Space } from 'antd-mobile';
import { DownOutline, UpOutline } from 'antd-mobile-icons';
import { RISK_LEVEL_COLOR } from '@/constant';

interface Props {
  workBaseRisks: any[]; // 安全控制卡数据
}
function groupByField(arr, field) {
  return arr.reduce((acc, obj) => {
    const key = obj[field];
    if (!acc[key]) {
      acc[key] = [];
    }
    acc[key].push(obj);
    return acc;
  }, {});
}

const SafetyRiskControlComponent = (props: Props) => {
  const [dataSource, setDataSource] = useState<Record<string, any[]>>({});
  const [moreNum, setMoreNum] = useState<number>(0);
  const [processHeight, setProcessHeight] = useState<number | string>('auto');
  const processRef = useRef(null);

  const { workBaseRisks = [] } = props;

  useEffect(() => {
    if (workBaseRisks?.length) {
      const groupedByCategory = groupByField(workBaseRisks, 'process');
      setDataSource(groupedByCategory);
      const keys = Object.keys(groupedByCategory);
      if (keys?.length > 2) {
        // 设置更多数量（所有子项-前两项大类的子类）
        const num = workBaseRisks.length - groupedByCategory[keys[0]].length - groupedByCategory[keys[1]].length;
        setMoreNum(num);
        // 设置有更多数据时显示内容区域高度
        setTimeout(() => {
          const process0Top = (document.querySelector('.process0') as any)?.offsetTop;
          const process2Top = (document.querySelector('.process2') as any)?.offsetTop;
          setProcessHeight(process2Top - process0Top - 40);
        }, 1000);
      }
    }
  }, [workBaseRisks]);

  return (
    <div className={styles.safetyAnalysisContent} ref={processRef}>
      <div style={{ height: processHeight, overflow: 'hidden' }}>
        <Collapse defaultActiveKey={['1', '2']}>
          {Object.keys(dataSource)?.map((process, index) => (
            <Collapse.Panel key={process.slice(0, 1)} title={process} className={`process${index}`}>
              {dataSource[process]?.map((item, index) => (
                <div key={index}>
                  <Space direction="vertical">
                    <div className={styles.riskDescription}>
                      <div className={styles.subTitle}>风险描述及后果：</div>
                      <Ellipsis
                        direction="end"
                        rows={2}
                        content={item.riskDescription}
                        expandText={
                          <>
                            展开
                            <DownOutline />
                          </>
                        }
                        collapseText={
                          <>
                            收起
                            <UpOutline />
                          </>
                        }
                      />
                    </div>
                    <div className={styles.controlMeasure}>
                      <div className={styles.subTitle}>管控措施：</div>
                      <Ellipsis
                        direction="end"
                        rows={2}
                        content={item.controlMeasure}
                        expandText={
                          <>
                            展开
                            <DownOutline />
                          </>
                        }
                        collapseText={
                          <>
                            收起
                            <UpOutline />
                          </>
                        }
                      />
                    </div>
                    <div className={styles.riskLevel}>
                      <div className={styles.subTitle}>风险等级：</div>
                      <div className={styles.level} style={{ color: RISK_LEVEL_COLOR[item.riskLevel] }}>
                        {item.riskLevel}
                      </div>
                    </div>
                  </Space>
                  <Divider />
                </div>
              ))}
            </Collapse.Panel>
          ))}
        </Collapse>
      </div>
      {processHeight === 'auto' ? null : (
        <>
          <Divider />
          <div className={styles.moreInfo} onClick={() => setProcessHeight('auto')}>
            更多({moreNum})
          </div>
        </>
      )}
    </div>
  );
};

export default SafetyRiskControlComponent;
