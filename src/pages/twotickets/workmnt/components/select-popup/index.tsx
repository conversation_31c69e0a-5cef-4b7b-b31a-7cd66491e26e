import { RightOutline } from 'antd-mobile-icons';
import { CheckList, Popup, Empty } from 'antd-mobile';
import { CloseOutline } from 'antd-mobile-icons';
import { useEffect, useState, useMemo } from 'react';
import useDict from '@/hooks/useDict';
// import { CheckListValue } from 'antd-mobile/es/components/check-list';
import { FormInstance } from 'antd-mobile/es/components/form';
import styles from '@/component/select-dictoptions/select-dictoptions-component.less';

interface Field {
  label: string;
  value: string;
}
interface Props {
  value?: string;
  form?: FormInstance;
  onChange?: (value: string) => void;
  field?: Field;
  options: any[];
  title?: string;
}

const fieldDefault = { label: 'label', value: 'value' };
const SelectPopup: React.FC<Props> = ({ value, onChange, options, title, field = fieldDefault }) => {
  const [visible, setVisible] = useState(false);
  const [slectedOp, setSlectedOp] = useState('');

  const labelValue = useMemo(() => {
    return options?.filter((el) => el[field.value] === slectedOp)[0]?.[field.label];
  }, [slectedOp]);

  const handleChange = (value: string[]) => {
    setVisible(false);
    onChange?.(value[0]);
    setSlectedOp(value[0]);
  };

  useEffect(() => {
    if (value?.length) {
      setSlectedOp(value);
      onChange?.(value);
    }
  }, [value]);

  return (
    <div
      className={styles.selectItem}
      onClick={() => {
        setVisible(true);
      }}
    >
      <span className={slectedOp?.length ? styles.value : styles.placeholder}>{labelValue || '请选择'}</span>
      <RightOutline />
      <Popup
        visible={visible}
        getContainer={null}
        onMaskClick={() => {
          setVisible(false);
        }}
        onClose={() => {
          setVisible(false);
        }}
        bodyStyle={{ borderRadius: '8px 8px 0 0' }}
      >
        <div className={styles.popupTitle}>
          <span />
          {title || '请选择'}
          <CloseOutline
            onClick={() => {
              setVisible(false);
            }}
          />
        </div>
        <div className={styles.checkList}>
          <CheckList value={[slectedOp]} onChange={handleChange}>
            {options?.length ? (
              options?.map((item) => (
                <CheckList.Item value={item?.[field.value]} key={item?.[field.value]}>
                  {item?.[field.label]}
                </CheckList.Item>
              ))
            ) : (
              <Empty description="暂无数据" />
            )}
          </CheckList>
        </div>
      </Popup>
    </div>
  );
};

export default SelectPopup;
