import { ActionSheet, CheckList, Popup, Space, Tag } from 'antd-mobile';
import { DownOutline } from 'antd-mobile-icons';
import { Action } from 'antd-mobile/es/components/action-sheet';
import { useState } from 'react';
import { styled } from 'umi';

interface DropActionSheetProps {
  prefix?: string;
  options: Action[];
  dropType?: 'popup' | 'action-sheet';
  value: string;
  dropOptions?: any;
  onChange: (key: string | number) => void;
}

const PopupTitle = styled.div.attrs({ className: (props: { className: any }) => `${props.className} popup-title` as any })`
  box-sizing: border-box;
  font-family: PingFang SC;
  font-size: 4.4444vw;
  font-weight: 400;
  letter-spacing: 0vw;
  line-height: 6.4353vw;
  color: rgba(0, 0, 0, 1);
  text-align: center;
  vertical-align: top;
  margin-top: 2.9631vw;
  margin-bottom: 2.9631vw;
`;

const PopupContainer = styled.div.attrs({ className: (props: { className: any }) => `${props.className} popup-container` as any })`
  flex: 1;
  box-sizing: border-box;
  width: 100%;
  position: relative;
  margin-bottom: 3.3333vw;
  border-top: 0.0925vw solid rgba(230, 230, 230, 1);
  overflow-y: auto;
  .adm-list {
    border: none;
    --border-top: none;
    --border-inner: 0.0925vw solid rgba(230, 230, 230, 1);
    --border-bottom: none;
  }
  .adm-list-item-content {
    justify-content: center;
    .adm-list-item-content-main {
      flex: none;
      font-size: 4.4444vw;
      font-weight: 400;
      letter-spacing: 0vw;
      line-height: 6.4352vw;
      color: rgba(51, 51, 51, 1);
    }
  }
  .adm-check-list-item-active {
    .adm-list-item-content {
      .adm-list-item-content-main {
        flex: none;
        font-size: 4.4444vw;
        font-weight: 500;
        letter-spacing: 0vw;
        line-height: 6.4352vw;
        color: rgba(51, 51, 51, 1);
      }
    }
  }
`;

const ActionSheetWrapper = styled(ActionSheet)`
  box-sizing: border-box;
  margin: 4.537vw 3.3333vw;
  .adm-action-sheet-button-list{
    border-bottom: none;
    .adm-action-sheet-button-item-wrapper{
      border-bottom: none;
      height: 15.5556vw;
      opacity: 1;
      border-radius: 3.3333vw;
      background: rgba(255, 255, 255, 1);
      margin-bottom: 1.4815vw;
      .adm-action-sheet-button-item-name{
        font-size: 4.4444vw;
        font-weight: 400;
        letter-spacing: 0vw;
        line-height: 6.4352vw;
        color: rgba(51, 51, 51, 1);
      }
    }
  }
  .adm-action-sheet-cancel{
    margin-top: 2.963vw;
    padding-top: 0;
    height: 15.5556vw;
      opacity: 1;
      border-radius: 3.3333vw;
      background: rgba(255, 255, 255, 1);
  }
`;

/**
 * 下拉动作面板
 * @returns
 */
const DropActionSheet = (props: DropActionSheetProps) => {
  const { prefix = null, dropType = 'popup', value, options, dropOptions = {}, onChange } = props;
  const [visible, setVisible] = useState(false);

  return (
    <>
      <Space
        className="drop-action-sheet"
        onClick={() => setVisible(true)}
        block
        justify="center"
        style={{
          '--gap': '.9259vw',
        }}
      >
        {prefix}
        {options?.reduce((all, item) => Object.assign(all, { [item.key]: item.text }), {})[value]} <DownOutline />
      </Space>
      {dropType === 'popup' && (
        <Popup
          showCloseButton
          destroyOnClose
          onMaskClick={() => setVisible(false)}
          visible={visible}
          onClose={() => setVisible(false)}
          bodyStyle={{
            borderTopLeftRadius: '3.3333vw',
            borderTopRightRadius: '3.3333vw',
            height: '70vh',
            display: 'flex',
            flexDirection: 'column',
          }}
        >
          <PopupTitle>{dropOptions?.title || '请选择'}</PopupTitle>
          <PopupContainer>
            <CheckList
              value={[value]}
              extra={(active) =>
                active ? (
                  <Tag
                    style={{
                      '--background-color': 'rgba(215, 225, 250, 1)',
                      '--text-color': 'rgba(17, 84, 237, 1)',
                      '--border-color': 'rgba(215, 225, 250, 1)',
                    }}
                  >
                    当前
                  </Tag>
                ) : null
              }
              onChange={(value) => onChange(value[0])}
            >
              {options.map((item) => (
                <CheckList.Item key={item.key} value={item.key}>
                  {item.text}
                </CheckList.Item>
              ))}
            </CheckList>
          </PopupContainer>
        </Popup>
      )}
      {dropType === 'action-sheet' && (
        <ActionSheetWrapper
          cancelText="取消"
          visible={visible}
          actions={options}
          onClose={() => setVisible(false)}
          onAction={(action: Action) => {
            onChange(action?.key);
            setVisible(false);
          }}
          styles={{
            body: {
              '--adm-color-background': 'transparent',
              '--adm-color-fill-content': 'rgba(255, 255, 255, 1)',
            },
          }}
        />
      )}
    </>
  );
};

export default DropActionSheet;
