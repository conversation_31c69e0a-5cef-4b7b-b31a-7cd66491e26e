/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-12 13:43:24
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-05-23 11:28:05
 * @FilePath: /mobile-yw/src/pages/twotickets/workmnt/components/user-picker/user-picker.component.tsx
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */
import { TreePickerFormItem } from "@/component/tree-picker/tree-picker.component";
import useOrgTree from "../../hooks/useOrgTree";
import styles from "./user-picker.component.less";
import { TreeNodeType } from "@/component/checked-tree/checked-tree.component";
import { forwardRef, useRef } from "react";
import { TreePickerRef } from "@/component/tree-picker/types";

interface UserPickerProps {
  label: string;
  name: string;
  placeholder: string;
  title: string;
  value?: string[];
  showUsers?: boolean;
  onChang<PERSON>?: (value: string[]) => void;
  onConfirm?: (value: string[]) => void;
  multiple?: boolean;
  customMode?: boolean;
  // 是否对用户去重，为true时会移除重复的用户，只保留一个实例
  // 为false时会保留完整的组织层级信息，返回格式为 "组织ID_用户ID"
  deduplicateUsers?: boolean;
  wrapFormItem?: boolean;
}

/**
 * 用户选择器组件
 * @param props 组件属性
 * @param props.label 标签文本
 * @param props.name 表单项名称
 * @param props.placeholder 输入框占位符
 * @param props.title 标题
 * @param props.value 选中的用户或组织
 * @param props.showUsers 是否显示用户
 * @param props.onChange 值变化回调
 * @param props.onConfirm 确认回调
 * @param props.multiple 是否多选
 * @param props.customMode 是否自定义模式
 * @param props.deduplicateUsers 是否对用户去重，默认为true。设为false时保留组织层级信息，返回格式为"组织ID_用户ID"
 * 
 * 优化说明：
 * - 当 customMode 为 true 且 multiple 为 false 时，合并显示"已选择人员"和"自定义输入"为统一的"已选择人员"
 * - 在单选模式下，选择新的树节点或输入新的自定义内容时，会清空之前的选择，只保留最后的选择或输入
 */
const UserPicker = forwardRef((props: UserPickerProps, ref) => {
  const { label, name, placeholder, title,
    showUsers = false,
    value = [],
    onChange,
    onConfirm,
    multiple = true,
    customMode = false,
    deduplicateUsers = true,
    wrapFormItem = true,
  } = props;

  const [treeData] = useOrgTree({ mergeUsersToChildren: showUsers });
  const treePickerRef = useRef<TreePickerRef>(null);

  const handleClick = (e: React.MouseEvent) => {
    treePickerRef.current?.open();
  };
  return (
    <div onClick={handleClick}>
      {/* @ts-ignore - TreePickerFormItem类型问题 */}
      <TreePickerFormItem
        ref={treePickerRef}
        label={label}
        name={name}
        customMode={customMode}
        placeholder={placeholder}
        title={title}
        value={value}
        // 默认只显示组织
        // leafNodesOnly={true}
        // 默认多选
        // multiple={false}
        multiple={multiple}
        nodeTypeFilter="user"
        wrapFormItem={wrapFormItem}
        onChange={onChange}
        onConfirm={onConfirm}
        deduplicateUsers={deduplicateUsers}
        nodeTitleRender={(nodeData, selectedNodes) => {
          const isSelected = selectedNodes.some(item => item.id === nodeData.id);
          if (nodeData?.username) {
            return (
              <>
                <span className={styles.userTitle}>{`${nodeData.name}（${nodeData.username}）`}</span>
                {!multiple && isSelected ? <span className={styles.curssTag}>当前</span> : null}
              </>
            )
          }
          return (
            <>
              <span className={styles.orgTitle}>{nodeData.name}</span>
              {!multiple && isSelected ? <span className={styles.curssTag}>当前</span> : null}
            </>
          )
        }}
        treeData={treeData as TreeNodeType[]}
        checkStrictly={false}
      />
    </div>
  );
});

export default UserPicker;
