import React, { useEffect, useMemo } from 'react';
import { styled, history } from 'umi';
import { Ellipsis, Grid, List } from 'antd-mobile';
import { nanoid } from 'nanoid';
import StringUtils from '@/utils/str';
import { WorkBase, WorkBaseVo } from '../../types';
import { getRelationListPage } from '@/services/twotickets/workmnt/hotWork';
import { getTicketDetailPageUrl, TICKET_TYPE, TICK_TYPE_KEY, TICKET_TYPE_MAP, WORKMNT_ELECTRICAL_STATUSES_MAP } from '@/constant';
import dayjs from 'dayjs';

interface AssociatedMainTicketItemProps {
  item: WorkBase;
}

interface AssociatedMainTicketProps {
  list?: WorkBase[];
}

const MainTicketListWrapper = styled(List).attrs(() => ({
  className: `main-ticket-list-wrapper-${nanoid()}`,
}))`
  .header {
    display: flex;
    font-size: 3.8889vw;
    font-weight: 400;
    letter-spacing: 0vw;
    line-height: 5.6315vw;
    color: rgba(51, 51, 51, 1);
    margin-bottom: 0.3704vw;
    .title {
      flex: 1;
    }
    .btn {
      margin-left: 0.9259vw;
      font-size: 3.8889vw;
      font-weight: 500;
      letter-spacing: 0vw;
      line-height: 5.6315vw;
      color: rgba(17, 84, 237, 1);
    }
  }
  .describe {
    display: flex;
    align-items: center;
    font-size: 3.3333vw;
    font-weight: 400;
    letter-spacing: 0vw;
    line-height: 4.8269vw;
    color: rgba(102, 102, 102, 1);
  }
  .more {
    margin-top: 1.6667vw;
    padding: 2.2222vw 3.3333vw;
    border-radius: 1.6667vw;
    background: rgba(245, 245, 245, 1);
    &-item {
      display: flex;
      align-items: center;
      font-size: 3.3333vw;
      font-weight: 400;
      letter-spacing: 0vw;
      line-height: 4.8269vw;
      color: rgba(153, 153, 153, 1);
      &-value {
        color: rgba(51, 51, 51, 1);
      }
    }
  }
`;

/**
 * 加载关联主票-- 列表项
 * @returns
 */
export const AssociatedMainTicketItem: React.FC<AssociatedMainTicketItemProps> = (props) => {
  const { item } = props;
  const [ticketKey, type] = useMemo(() => {
    console.log('item:', item);
    const ticketKey = TICK_TYPE_KEY?.[Number(item?.ticketType) - 1];
    return [ticketKey, Object.keys(TICKET_TYPE)[Object.values(TICKET_TYPE).findIndex((v) => v?.includes(Number(item?.ticketType)))]];
  }, [item]);
  return (
    <List.Item>
      <div className="header">
        <div className="title">
          <Ellipsis content={item.workTask}>{item.workTask}</Ellipsis>{' '}
        </div>
        <div
          className="btn"
          style={{
            color: WORKMNT_ELECTRICAL_STATUSES_MAP?.[item?.ticketType]?.[item?.status]?.color,
          }}
          onClick={() => history.push(getTicketDetailPageUrl(ticketKey, type, item.id, 'view'))}
        >
          {WORKMNT_ELECTRICAL_STATUSES_MAP?.[item?.ticketType]?.[item?.status]?.label}
        </div>
      </div>
      <div
        className="describe"
        dangerouslySetInnerHTML={{
          __html: StringUtils.join([dayjs(item?.createTime).format('YYYY-MM-DD'), TICKET_TYPE_MAP?.[item?.workNum]]),
        }}
      ></div>
      <div className="more">
        <div className="more-item">
          <div className="more-item-label">地点：</div>
          <div className="more-item-value">{item?.unitName}</div>
        </div>
      </div>
    </List.Item>
  );
};

// 关联主票
const AssociatedMainTicket = (props: AssociatedMainTicketProps) => {
  const { list = [] } = props;
  return (
    <MainTicketListWrapper>
      {list.map((item, index) => (
        <AssociatedMainTicketItem item={item} key={item.id} />
      ))}
    </MainTicketListWrapper>
  );
};

export default AssociatedMainTicket;
