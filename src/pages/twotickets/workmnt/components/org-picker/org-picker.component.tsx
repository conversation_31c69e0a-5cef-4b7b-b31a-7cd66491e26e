/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-04-30 11:07:47
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-05-20 22:32:48
 * @FilePath: /mobile-yw/src/pages/twotickets/workmnt/components/org-picker/org-picker.component.tsx
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */
import { TreePickerFormItem } from "@/component/tree-picker/tree-picker.component";
import useOrgTree from "../../hooks/useOrgTree";
import styles from "./org-picker.component.less";
import { TreeNodeType } from "@/component/checked-tree/checked-tree.component";
import { forwardRef, useRef } from "react";
import { TreePickerRef } from "@/component/tree-picker/types";

interface OrgPickerProps {
  label: string;
  name: string;
  placeholder: string;
  title: string;
  value?: string[];
  showUsers?: boolean;
  onChang<PERSON>?: (value: string[]) => void;
  onConfirm?: (value: string[]) => void;
  multiple?: boolean;
  wrapFormItem?: boolean;
}

const OrgPicker = forwardRef((props: OrgPickerProps, ref) => {
  const { label, name, placeholder, title,
    showUsers = false,
    value = [],
    onChange,
    multiple = true,
    onConfirm,
    wrapFormItem = true,
  } = props;

  const [treeData] = useOrgTree({ mergeUsersToChildren: showUsers });
  const treePickerRef = useRef<TreePickerRef>(null);

  const handleClick = (e) => {
    treePickerRef.current?.open();
  };

  return (
    <div onClick={handleClick}>
      <TreePickerFormItem
        ref={treePickerRef}
        label={label}
        name={name}
        placeholder={placeholder}
        title={title}
        value={value}
        // 默认只显示组织
        // leafNodesOnly={true}
        // 默认多选
        multiple={multiple}
        wrapFormItem={wrapFormItem}
        // nodeTypeFilter="user"
        onChange={onChange}
        onConfirm={onConfirm}
        nodeTitleRender={(nodeData, selectedNodes) => {
          const isSelected = selectedNodes.some(item => item.id === nodeData.id);
          if (nodeData?.username) {
            return (
              <>
                <span className={styles.userTitle}>{`${nodeData.name}（${nodeData.username}）`}</span>
                {!multiple && isSelected ? <span className={styles.curssTag}>当前</span> : null}
              </>
            )
          }
          return (
            <>
              <span className={styles.orgTitle}>{nodeData.name}</span>
              {!multiple && isSelected ? <span className={styles.curssTag}>当前</span> : null}
            </>
          )
        }}
        treeData={treeData as TreeNodeType[]}
        checkStrictly={false}
      />
    </div>);
});

export default OrgPicker;
