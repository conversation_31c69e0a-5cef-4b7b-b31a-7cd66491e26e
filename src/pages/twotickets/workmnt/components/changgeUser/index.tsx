/**
 * @description 工作票执行-工作负责人变更
 */
import { Card, Grid } from 'antd-mobile';
import styles from './index.less';
const ChangeUser = (props) => {
  const { current } = props;
  const { workFlowExecuteHeadExchange } = current?.workTicketExecute || {};

  //数据为空默认结束
  if (!current?.workTicketExecute?.workFlowExecuteHeadExchange) {
    return null;
  }
  return (
    <Card title="工作负责人变更" className={styles.myCard}>
      {workFlowExecuteHeadExchange?.nowHeadName ? (
        <>
          <div className={styles.changValueItem}>
            <div className={styles.userAndTime}>
              <div>变更后负责人</div>
              <div>{workFlowExecuteHeadExchange?.nowHeadName}</div>
            </div>
            <div className={styles.userAndTime}>
              <div>变动时间</div>
              <div>{workFlowExecuteHeadExchange?.changeTime}</div>
            </div>
          </div>
        </>
      ) : null}

      {workFlowExecuteHeadExchange?.signName ? (
        <>
          <div className={styles.changValueItem}>
            <div className={styles.userAndTime}>
              <span className={styles.colorGrey}>工作签发人：</span>
              <span>{workFlowExecuteHeadExchange?.signName}</span>
              <span>，</span>
              <span className={styles.colorGrey}>提交时间：</span>
              <span>{workFlowExecuteHeadExchange?.signTime}</span>
            </div>
            <div className={styles.userAndTime}>
              <span className={styles.colorGrey}>工作许可人：</span>
              <span>{workFlowExecuteHeadExchange?.allowName}</span>
              <span>，</span>
              <span className={styles.colorGrey}>提交时间：</span>
              <span>{workFlowExecuteHeadExchange?.allowTime}</span>
            </div>
          </div>
        </>
      ) : null}
      {workFlowExecuteHeadExchange?.situationDescription ? (
        <>
          <div className={styles.textArea}>
            <span>变动情况说明：</span>
            <span>{workFlowExecuteHeadExchange?.situationDescription}</span>
          </div>
        </>
      ) : null}
    </Card>
  );
};
export default ChangeUser;
