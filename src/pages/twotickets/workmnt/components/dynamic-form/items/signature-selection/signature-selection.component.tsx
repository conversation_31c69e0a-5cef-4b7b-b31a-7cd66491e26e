import { ActionSheet, Button, Divider, Form, Grid, Input, Popup, Space, Tag } from 'antd-mobile';
import CustomTree from '@/component/tree-picker/components/CustomTree';
import TwoTicketsNavBar from '@/component/twotickets-navbar/twotickets-navbar.component';
import { AddCircleOutline, CheckCircleOutline, CloseCircleFill, CloseCircleOutline, SearchOutline } from 'antd-mobile-icons';
import { styled } from 'umi';
import { ReactNode, useRef, useState } from 'react';
import useOrgTree from '@/pages/twotickets/workmnt/hooks/useOrgTree';
import { convertTreeData } from '@/component/tree-picker/utils';
import { CustomTreeNode } from '@/component/tree-picker/types';
import { useTreePicker } from '@/component/tree-picker/hooks/useTreePicker';

export interface ISignatureUserPickerProps {
  placeholder: string;
  value?: string | any | string[] | any[];
  /* 是否多选 */
  multiple?: boolean;
  // 节点类型过滤器，用于限制只能选择特定类型的节点
  nodeTypeFilter?: string | string[];
  checkStrictly?: boolean;
  /* 是否显示图标 */
  showIcon?: boolean;
  /* 是否展开所有节点 */
  defaultExpandAll?: boolean;
  /* 节点标题渲染函数 */
  switcherIcon?: ReactNode | ((props: { expanded: boolean }) => ReactNode);
  /* 是否能够自定义输入用户 */
  isCanCustom?: boolean;
  /* 自定义加载 */
  render?: (value: any) => ReactNode;
  /* 节点名称统一扩展 */
  titleRender?: (nodeData: CustomTreeNode) => ReactNode;
  /*  添加新的控制节点标题渲染的prop */
  nodeTitleRender?: (nodeData: CustomTreeNode, selectNodes: CustomTreeNode[]) => ReactNode;
  onExpand?: (expandedKeys: React.Key[], info: any) => void;
  onChange: (value: any) => void;
}

const PlaceholderWrapper = styled.div`
  font-size: 4.2593vw;
  font-weight: 400;
  letter-spacing: 0vw;
  line-height: 6.1676vw;
  color: rgba(153, 153, 153, 1);
`;

const SignatureUserPickerPopup = styled(Popup)`
  .footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2.2222vw 3.3333vw 2.2222vw 3.7963vw;
    border-top: 0.0926vw solid #f0f0f0;
    background-color: #fff;
    height: 11.1111vw;

    .selectedCount {
      font-size: 4.4444vw;
      color: #666;

      .count {
        color: var(--adm-color-primary);
        font-weight: 500;
      }
    }
    .adm-button {
      width: 55.5556vw;
      font-size: 4.4444vw;
      line-height: 4.4444vw;
      padding: 2.3148vw 0;
      border-radius: 3.3333vw;
    }
  }
`;

const SignatureCustomUserInputPopup = styled(Popup)`
  .footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2.2222vw 3.3333vw 2.2222vw 3.7963vw;
    border-top: 0.0926vw solid #f0f0f0;
    background-color: #fff;
    height: 11.1111vw;

    .selectedCount {
      font-size: 4.4444vw;
      color: #666;

      .count {
        color: var(--adm-color-primary);
        font-weight: 500;
      }
    }
    .adm-button {
      width: 100%;
      font-size: 4.4444vw;
      line-height: 4.4444vw;
      padding: 2.3148vw 0;
      border-radius: 3.3333vw;
    }
  }
`;

const StaffTag = styled.div`
  padding: 1.1111vw 3.7037vw;
  border-radius: 6.4815vw;
  background: rgba(245, 245, 245, 1);
  font-size: 3.8889vw;
  font-weight: 400;
  letter-spacing: 0vw;
  line-height: 5.6315vw;
  color: rgba(51, 51, 51, 1);
`;

const StaffSelectBtn = styled.span`
  display: inline-block;
  border-radius: 7.7778vw;
  background: rgba(215, 225, 250, 1);
  font-size: 3.8889vw;
  font-weight: 400;
  letter-spacing: 0vw;
  line-height: 5.6315vw;
  color: rgba(17, 84, 237, 1);
  min-width: 16.6667vw;
  height: 7.7778vw;
  text-align: center;
  line-height: 7.7778vw;
`;

// 修改后的组件结构
const EnterInputTags = ({ value, onChange }: any) => {
  const [tempInputs, setTempInputs] = useState<Array<{id: string, text: string}>>([]);
  const inputRefs = useRef<{[key: string]: HTMLInputElement | null}>({});

  // 添加新输入框
  const handleAddInput = () => {
    const newInputId = `temp-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    setTempInputs(prev => [...prev, { id: newInputId, text: '' }]);
    
    // 自动聚焦新输入框
    setTimeout(() => {
      inputRefs.current[newInputId]?.focus();
    }, 0);
  };

  // 输入框内容变更
  const handleInputChange = (id: string, newText: string) => {
    setTempInputs(prev => 
      prev.map(input => 
        input.id === id ? { ...input, text: newText } : input
      )
    );
  };

  // 确认添加新标签
  const handleConfirm = (id: string) => {
    const input = tempInputs.find(i => i.id === id);
    if (!input?.text.trim()) return;

    onChange([
      ...(value || []),
      { 
        userId: `custom-${Date.now()}-${input.text}`,
        name: input.text 
      }
    ]);
    
    // 移除已确认的输入框
    setTempInputs(prev => prev.filter(i => i.id !== id));
  };

  // 取消添加
  const handleCancel = (id: string) => {
    setTempInputs(prev => prev.filter(i => i.id !== id));
  };

  // 回车确认
  const handleKeyDown = (e: React.KeyboardEvent, id: string) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleConfirm(id);
    }
  };

  return (
    <div style={{ width: '100%' }}>
      {/* 已有标签 */}
      <Space wrap>
        {value?.map?.((item: any) => (
          <StaffTag key={item.userId}>
            {item?.name}{' '}
             <CloseCircleFill
              onClick={() => {
                onChange(value.filter((v: any) => v.userId !== item.userId));
              }}
            />
          </StaffTag>
        ))}
      </Space>

      {/* 临时输入框容器 */}
      <div style={{ marginTop: 8 }}>
        {tempInputs.map(input => (
          <div key={input.id} style={{ marginBottom: 4 }}>
            <Grid columns={24}>
              <Grid.Item span={18}>
                <Input
                  ref={(el) => {
                    inputRefs.current[input.id] = el;
                  }}
                  value={input.text}
                  onChange={(val) => handleInputChange(input.id, val)}
                  onKeyDown={(e) => handleKeyDown(e, input.id)}
                  onBlur={(e) => handleConfirm(input.id)}
                  placeholder="请输入姓名"
                  style={{ height: 32 }}
                />
              </Grid.Item>
              <Grid.Item span={3}>
                <Button
                  size="mini"
                  color="primary"
                  shape='rounded'
                  onClick={() => handleConfirm(input.id)}
                  style={{ marginLeft: 4, height: 32, width: 32 }}
                >
                  <CheckCircleOutline />
                </Button>
              </Grid.Item>
              <Grid.Item span={3}>
                <Button
                  size="mini"
                  shape='rounded'
                  onClick={() => handleCancel(input.id)}
                  style={{ marginLeft: 4, height: 32, width: 32 }}
                >
                  <CloseCircleOutline />
                </Button>
              </Grid.Item>
            </Grid>
          </div>
        ))}
      </div>

      {/* 添加按钮 */}
      <Button
        block
        color="primary"
        fill="none"
        onClick={handleAddInput}
        style={{ marginTop: 8, height: 32 }}
      >
        + 添加新成员
      </Button>
    </div>
  );
};

const CustomUserInputPopup = ({ visible, onClose, onChange }: any) => {
  const [form] = Form.useForm();
  return (
    <SignatureCustomUserInputPopup
      visible={visible}
      onClose={onClose}
      onMaskClick={onClose}
      position="bottom"
      destroyOnClose
      bodyStyle={
        {
          'border-radius': '0',
          '--header-height': '45px',
          height: '100vh',
          background: 'rgba(245, 245, 245, 33)',
        } as any
      }
    >
      <TwoTicketsNavBar onBack={() => onClose()} title={'请选择'} bgcolor={'#fff'} color={'#000'} />
      <Form form={form} onFinish={(values) => onChange(values.users)}>
        <Form.Item name="users">
          <EnterInputTags />
        </Form.Item>
      </Form>
      <div className="footer">
        {/* @ts-ignore - 忽略类型错误，确保组件可以正常渲染 */}
        <Button
          block
          color="primary"
          onClick={() => {
            form.submit();
          }}
        >
          确认
        </Button>
      </div>
    </SignatureCustomUserInputPopup>
  );
};

const SignatureUserPicker = (props: ISignatureUserPickerProps) => {
  const {
    placeholder,
    value,
    multiple = true,
    defaultExpandAll = true,
    showIcon = false,
    switcherIcon,
    isCanCustom = false,
    render = null,
    titleRender,
    nodeTitleRender,
    onExpand,
    onChange,
  } = props;
  // 获取数据
  const [userTreeData] = useOrgTree({ mergeUsersToChildren: true });

  // 数据进行格式化
  const treeData = convertTreeData(userTreeData as any);

  // 自定义了一个useTreePicker来进行对选择的picker进行处理

  const { footerRef, visible, treeHeight, selectedNodes, handleSelect, handleConfirm, isNodeSelectable, setVisible, setSelectedNodes } =
    useTreePicker(props as any);
  // 添加输入弹框
  const [customInputVisible, setCustomInputVisible] = useState(false);
  return (
    <>
      {!render ? (
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'flex-start',
          }}
        >
          {value?.length ? (
            <Space wrap>
              {(value || [])?.map?.((item: any) => (
                <StaffTag key={item.id}>
                  {item?.name}{' '}
                  <CloseCircleFill
                    onClick={() => {
                      onChange(value?.filter?.((v: any) => v.id !== item.id));
                    }}
                  />
                </StaffTag>
              ))}
            </Space>
          ) : (
            <PlaceholderWrapper>{placeholder}</PlaceholderWrapper>
          )}
          <StaffSelectBtn
            onClick={() => {
              if (!isCanCustom) {
                setVisible(true);
              } else {
                const openAction = ActionSheet.show({
                  actions: [
                    { text: '选择人员', key: 'select' },
                    { text: '自定义输入', key: 'custom' },
                  ],
                  cancelText: '取消',
                  onAction: (action) => {
                    openAction.close();
                    if (action.key === 'select') {
                      setVisible(true);
                    } else if (action.key === 'custom') {
                      setCustomInputVisible(true);
                      // const inputValue = window.prompt('请输入人员名称:');
                      // if (inputValue) {
                      //   const newVal = Array.isArray(value)
                      //     ? [...value, { id: inputValue, name: inputValue }]
                      //     : [{ id: inputValue, name: inputValue }];
                      //   onChange(newVal);
                      // }
                    }
                  },
                });
              }
            }}
          >
            + 选择
          </StaffSelectBtn>
        </div>
      ) : (
        <div
          onClick={() => {
            setVisible(true);
          }}
        >
          {render(value)}
        </div>
      )}
      <SignatureUserPickerPopup
        visible={visible}
        onMaskClick={() => setVisible(false)}
        position="bottom"
        bodyStyle={
          {
            '--header-height': '45px',
            height: '100vh',
            background: 'rgba(245, 245, 245, 33)',
          } as any
        }
      >
        <TwoTicketsNavBar
          onBack={() => setVisible(false)}
          title={'请选择'}
          bgcolor={'#fff'}
          color={'#000'}
          right={<SearchOutline fontSize={'5vw'} />}
        />
        <CustomTree
          treeData={treeData}
          treeHeight={treeHeight}
          defaultExpandAll={defaultExpandAll}
          showIcon={showIcon}
          selectedNodes={selectedNodes}
          multiple={multiple}
          switcherIcon={switcherIcon}
          isNodeSelectable={isNodeSelectable}
          titleRender={titleRender}
          nodeTitleRender={nodeTitleRender}
          onExpand={onExpand}
          onSelect={handleSelect}
        />
        <div className="footer" ref={footerRef}>
          <div className="selectedCount">
            已选择: <span className="count">{selectedNodes.length}</span> 项
          </div>
          <Space block>
            {/* @ts-ignore - 忽略类型错误，确保组件可以正常渲染 */}
            <Button block color="primary" onClick={handleConfirm}>
              选择
            </Button>
          </Space>
        </div>
      </SignatureUserPickerPopup>
      {customInputVisible && (
        <CustomUserInputPopup
          visible={customInputVisible}
          onClose={() => {
            setCustomInputVisible(false);
          }}
          onChange={(values: any) => {
            props?.onChange([...(value || []), ...(values || [])]);
            setCustomInputVisible(false);
          }}
        />
      )}
    </>
  );
};

export default SignatureUserPicker;
