// types/form.ts
import { FormItemProps, SelectorProps } from 'antd-mobile';
import { StrategyFactory } from './strategy-factory';
import { InputProps } from 'antd';
import { ISignatureUserPickerProps } from './items/signature-selection/signature-selection.component';

declare module './strategy-factory' {
  interface StrategyTypeMap {
    text: InputProps;
    date: DatePickerProps;
    signature: ISignatureUserPickerProps;
  }
}

export type StrategyTypes = keyof StrategyTypeMap;
export type StrategyProps<T extends StrategyTypes> = StrategyTypeMap[T];