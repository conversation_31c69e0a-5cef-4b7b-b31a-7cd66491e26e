import React, { useMemo } from 'react';
import { Form, FormItemProps } from "antd-mobile";
import {  StrategyProps, StrategyTypes } from './typing.d';
import { strategyFactory } from './strategy-factory';


export interface DynamicFormItemProps<T extends StrategyTypes> extends FormItemProps {
  strategyType: T;
  strategyProps?: StrategyProps<T>;
  customValidator?: (value: any) => string | undefined;
  [key: string]: any
}
export const DynamicFormItem = <T extends StrategyTypes>({
  strategyType,
  strategyProps,
  customValidator,
  ...formItemProps
}: DynamicFormItemProps<T>) => {
  const strategyConfig = useMemo(() => {
    return strategyFactory.getStrategy(strategyType as string);
  }, [strategyType]);

  const rules = useMemo(() => {
    const baseRules = formItemProps.rules || [];
    const strategyRule = strategyConfig?.validator 
      ? [{ validator: (_: any, value: any) => strategyConfig.validator!(value) }]
      : [];
    const customRule = customValidator 
      ? [{ validator: (_: any, value: any) => customValidator(value) }]
      : [];
    
    return [...baseRules, ...strategyRule, ...customRule];
  }, [strategyConfig, customValidator]);

  if (!strategyConfig) {
    console.warn(`未注册的策略类型: ${strategyType as string}`);
    return null;
  }

  return (
    <Form.Item
      {...formItemProps}
      rules={rules as any}
    >
      {strategyFactory.createComponent(strategyType as string, {
        ...strategyProps,
        ...formItemProps.fieldProps
      })}
    </Form.Item>
  );
};