// strategy-factory.ts
import React from 'react';

type StrategyComponent<T extends object = {}> = React.ComponentType<T>;
type StrategyConfig<T extends object = {}> = {
  component: StrategyComponent<T>;
  defaultProps?: Partial<T>;
  validator?: (value: any) => string | undefined;
};

export class StrategyFactory {
  private strategies = new Map<string, StrategyConfig<any>>();

  registerStrategy<T extends object>(type: string, config: StrategyConfig<T>) {
    this.strategies.set(type, config);
  }

  getStrategy<T extends object>(type: string): StrategyConfig<T> | undefined {
    return this.strategies.get(type);
  }

  createComponent<T extends object>(type: string, props: T): React.ReactElement | null {
    const strategy = this.getStrategy<T>(type);
    if (!strategy) return null;

    const MergedComponent: any = strategy?.component;
    const mergedProps = { ...strategy.defaultProps, ...props };
    return (<MergedComponent {...mergedProps} />)
  }
}

// 初始化工厂实例
export const strategyFactory = new StrategyFactory();
