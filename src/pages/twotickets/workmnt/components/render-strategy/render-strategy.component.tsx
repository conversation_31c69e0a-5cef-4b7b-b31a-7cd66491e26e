import { Checkbox, Divider, Form, Space } from 'antd-mobile';
import dayjs from 'dayjs';
import React, { CSSProperties } from 'react';
import { useEffect, useState } from 'react';
import { styled } from 'umi';
import styles from '@/pages/twotickets/operation/list/operation-list.page.less';
import CheckInput from '@/component/ants/check-input/check-input.component';
import DynamicsCheckList from '@/component/ants/dynamics-check-list/dynamics-check-list.component';
import ImageIcon from '@/component/image-icon/image-icon.component';

type RenderType = 'default' | 'date' | 'planTime' | 'members' | 'fill_check' | 'display_render' | 'fill_check-input';

export interface FieldConfig {
  label: string | React.ReactNode;
  field: string;
  type?: RenderType;
  hideLabel?: boolean;
  hideSemicolon?: boolean;
  layout?: 'horizontal' | 'vertical';
  options?: { label: string; value: string }[] | (() => Promise<any[]>);
  styles?: Partial<Record<'item' | 'label' | 'name', CSSProperties>>;
  render?: (value: any, data?: any) => React.ReactNode;
}

type RenderStrategy = (value: any, data?: any, item?: any) => React.ReactNode;

// 获取的数据进行处理，根据字段获取返回数据，并且过滤掉为空的字段，然后根据字段获取的内容进行拼接成{data["headName"]}(负责人), {data["fireExecuteName"]}(动火执行人), {data["ownerSignerName"]}(签发人), {data["allowName"]}(许可人),{data["finalAllowUserName"]}(最终许可人)
const getUserInfoList =  (data: any) => {
  const fields = [
    { key: 'headName', label: '负责人' },
    { key: 'fireExecuteName', label: '动火执行人' },
    { key: 'ownerSignerName', label: '签发人' },
    { key: 'allowName', label: '许可人' },
    { key: 'finalAllowUserName', label: '最终许可人' },
  ];
  return fields
    .map(({ key, label }) => {
      const value = data?.[key];
      if (value) {
        return `${value}(${label})`;
      }
      return null;
    })
    .filter(Boolean)
    .join('，');
};

const renderStrategies: Record<RenderType, RenderStrategy> = {
  default: (value) => value ?? '-',
  date: (value) => (value ? dayjs(value).format('YYYY/MM/DD HH:mm:ss') : '-'),
  planTime: (_value, data) =>
    `${data?.planBeginTime ? dayjs(data.planBeginTime).format('YYYY-MM-DD HH-mm') : ''} ~ ${
      data?.planEndTime ? dayjs(data.planEndTime).format('YYYY-MM-DD HH-mm') : ''
    }`,
  members: (_value, data) => getUserInfoList(data),
  // checkbox的多选方式
  fill_check: (value, data, item) => {
    const [checkList, setCheckList] = useState([]);
    useEffect(() => {
      if (item?.options) {
        // 判断item?.options是数组还是Promise对象
        if (Array.isArray(item?.options)) {
          setCheckList(item?.options);
        } else {
          item?.options().then((res: React.SetStateAction<never[]>) => {
            setCheckList(res);
          });
        }
      }
    }, [item?.options]);
    return (
      <Form.Item 
        style={{
          padding: '0',
        }}
      name={item.field} label={!item?.hideLabel ? item.label : null} layout="horizontal">
        <Checkbox.Group disabled>
          <Space
            wrap
            style={{
              '--gap-horizontal': '1.8519vw',
              margin: '0',
            }}
          >
            {checkList.map((el: any) => {
              return (
                <Checkbox
                  key={el.value}
                  value={el.value}
                  style={{ lineHeight: '3.1746vw' }}
                  icon={(checked) =>
                    checked ? (
                      <ImageIcon width="3.1746vw" height="3.1746vw" name="checkbox-line" fit="fill" />
                    ) : (
                      <ImageIcon width="3.1746vw" height="3.1746vw" name="checkbox-blank-line" fit="fill" />
                    )
                  }
                >
                  {el.label}
                </Checkbox>
              );
            })}
          </Space>
        </Checkbox.Group>
      </Form.Item>
    );
  },
  'fill_check-input': (value, data, item) => {
    // 新增一个勾选然后有个输入框，勾选，选中的是输入框的内容
    const [checked, setChecked] = useState(false);
    const [inputValue, setInputValue] = useState(value || '');

    useEffect(() => {
      if (value) {
        setChecked(true);
        setInputValue(value);
      }
    }, [value]);

    return (
      <Form.Item layout="horizontal" name={item.field} label={!item?.hideLabel ? item.label : null}>
        {/* <DynamicsCheckList /> */}
      </Form.Item>
    );
  },
  // 自定义传递显示内容
  display_render: (val, data, item) => {
    const CustomRender = ({ value }: any) => <>{item.render(value, data)}</>;
    return (
      <Form.Item name={item.field} label={!item?.hideLabel ? item.label : null} layout="horizontal" noStyle>
        <CustomRender />
      </Form.Item>
    );
  },
};

// 渲染工厂
function renderFieldFactory(item: FieldConfig, data: any) {
  const type = item.type ?? 'default';
  const strategy = renderStrategies[type] ?? renderStrategies.default;
  return strategy(data?.[item.field], data, item);
}

interface IRenderFields {
  fields: FieldConfig[][];
  data: any;
}

const RenderFieldsWrapper = styled.div`
  .item {
    font-size: 1.2rem;
    font-weight: 400;
    letter-spacing: 0rem;
    line-height: 1.7377rem;
    color: rgba(153, 153, 153, 1);
    .name {
      font-size: 1.2rem;
      font-weight: 400;
      letter-spacing: 0rem;
      line-height: 1.7377rem;
      color: rgba(51, 51, 51, 1);
    }
    .adm-checkbox-icon {
      width: 4.4444vw;
      height: 4.4444vw;
      border-width: 0.463vw;
      border-radius: 0;
    }
    .adm-checkbox-content {
      font-size: 3.8889vw;
      font-weight: 400;
      letter-spacing: 0vw;
      line-height: 4.4444vw;
      color: rgba(0, 0, 0, 1);
      opacity: 1;
    }
    .adm-list-item-content-main{
      padding: 0;
    }
    .fire_method,
    .describe,
    .subtitle {
      font-size: 4.2593vw;
      font-weight: 500;
      letter-spacing: 0vw;
      line-height: 6.1676vw;
      color: rgba(51, 51, 51, 1);
    }
    .describe_info,
    .subtitle_info {
      font-size: 3.8889vw;
      font-weight: 400;
      letter-spacing: 0vw;
      line-height: 5.6315vw;
      color: rgba(51, 51, 51, 1);
    }
  }
`;

export default function RenderFields(props: IRenderFields) {
  const { data, fields, style = {} } = props;
  return (
    <RenderFieldsWrapper>
      {fields.map((items, index) => (
        <div key={index} style={styles || {}}>
          {items.map((item, idx) => (
            <div
              className="item"
              key={idx}
              style={{
                display: 'flex',
                flexDirection: item?.layout !== 'vertical' ? 'row' : 'column',
                ...(item?.styles?.item || {}),
              }}
            >
              <span style={item?.styles?.label || {}}>{item.label}</span>
              {!item?.hideSemicolon && '：'}
              <span className="name" style={item?.styles?.name || {}}>
                {renderFieldFactory(item, data)}
              </span>
            </div>
          ))}
          {index !== fields.length - 1 && <Divider />}
        </div>
      ))}
    </RenderFieldsWrapper>
  );
}
