/* eslint-disable @typescript-eslint/no-unused-vars */

import { useEffect, useState } from 'react';
import { useParams } from 'umi';
import useNextTask from '@/hooks/useNextTask';
import { ActionSheet, Button } from 'antd-mobile';
import styles from './action-buttons.component.less';
interface Props {
  loading?: boolean; //加载
  onClick?: (actionType: '处理' | '保存', variables?: any, title?: string) => void; //点击获取操作类型、审批参数与弹窗标题
  disabledConfig?: { [name: string]: boolean }; //禁用按钮配置   name：操作名称
  hiddenConfig?: { [name: string]: boolean }; //隐藏按钮配置   name：操作名称
  taskId?: string; //流程id
  detailData?: any; //详情数据
  showSaveBtn?: boolean; //显示保存按钮
  height?: string;
  moreBtn?: {
    //更多自定义按钮设置
    text: string;
    key: string;
    disabled: boolean;
    onClick: () => void;
  }[];
}

const ActionButtons: React.FC<Props> = ({
  moreBtn,
  height = '5.6rem',
  showSaveBtn,
  detailData,
  taskId,
  loading = false,
  disabledConfig = null,
  hiddenConfig = null,
  onClick = () => {},
}) => {
  // 获取url参数
  const { pageType } = useParams();
  const { nextTask, getNextTask } = useNextTask([]);
  const [showBtns, setShowBtns] = useState(false);
  const [actions, setActions] = useState<
    {
      text: string;
      key: string;
      disabled: boolean;
      onClick: () => void;
      prototype?: Record<string, any>
    }[]
  >([]);
  const handleClick = (item: any) => {
    // 审批参数处理
    const assignee = item?.assignee ? item.assignee.slice(2, item.assignee.length - 1) : '';
    const variables = {
      ...item?.conditionParams,
      [assignee]: assignee ? detailData?.[assignee] : undefined,
    };
    delete variables.hiddenButton;
    delete variables.confirmDesc;
    console.log('处理', variables, item.name, 'ActionButtons===onClick=========');
    onClick('处理', variables, item.name);
    setShowBtns(false);
  };

  // 获取审批按钮
  useEffect(() => {
    if (taskId) {
      getNextTask(taskId);
    }
  }, [taskId]);

  useEffect(() => {
    if (nextTask) {
      console.log(nextTask, 'nextTask==========');
      // 动态生成的按钮
      const btns = nextTask?.filter(item=>!hiddenConfig?.[item.name])?.map((item: any) => ({
        text: item.name,
        key: item.code,
        disabled: disabledConfig ? disabledConfig[item.name] : false,
        prototype: {...item}
      }));
      // 自定义的按钮
      if (moreBtn) {
        const newMoreBtn = moreBtn?.map((item) => ({
          ...item,
          onClick: () => {
            item.onClick();
            setShowBtns(false);
          },
        }));
        btns.push(...newMoreBtn);
      }
      setActions(btns);
    }
  }, [nextTask, disabledConfig, moreBtn]);

  return (
    pageType !== 'view' && (
      <div className={styles.btnBox} style={{ height }}>
        {showSaveBtn && (
          <Button key={'save'} color="primary" fill="outline" loading={loading} onClick={() => onClick('保存')}>
            保存数据
          </Button>
        )}
        {nextTask?.length && (
          <Button key={'hanle'} loading={loading} onClick={() => setShowBtns(true)} color="primary" fill="solid">
            处理
          </Button>
        )}
        <ActionSheet cancelText="取消" visible={showBtns} actions={actions} onAction={(action)=>{
            handleClick(action.prototype)
        }} onClose={() => setShowBtns(false)} />
      </div>
    )
  );
};

export default ActionButtons;
