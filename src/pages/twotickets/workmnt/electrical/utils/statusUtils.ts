/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-01-XX XX:XX:XX
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-01-XX XX:XX:XX
 * @FilePath: /mobile-yw/src/pages/twotickets/workmnt/electrical/utils/statusUtils.ts
 * @Description: 电气工作票状态管理工具类
 */

import { 
  FINAL_STATES, 
  STATUS_TRANSITION_RULES, 
  STATUS_NAME_MAP,
  SPECIAL_OPERATION_ALLOWED_STATES,
  SUB_PROCESS_TYPES
} from '../constants/status';
import { ElectricalWorkTicketData } from '../types';

/**
 * 状态工具类
 */
export class StatusUtils {
  /**
   * 检查是否为终态
   */
  static isFinalState(status: string): boolean {
    return FINAL_STATES.includes(status as any);
  }

  /**
   * 获取状态显示名称
   */
  static getStatusName(status: string): string {
    return STATUS_NAME_MAP[status] || status;
  }

  /**
   * 检查状态是否允许特殊操作
   */
  static isSpecialOperationAllowed(status: string): boolean {
    const statusNum = parseInt(status);
    return SPECIAL_OPERATION_ALLOWED_STATES.includes(statusNum);
  }

  /**
   * 获取下一个可能的状态
   */
  static getNextPossibleStates(currentStatus: string): string[] {
    const rules = STATUS_TRANSITION_RULES[currentStatus];
    if (!rules) return [];
    
    return [rules.approve, rules.reject, rules.back].filter(Boolean);
  }

  /**
   * 验证状态转换是否合法
   */
  static isValidTransition(fromStatus: string, toStatus: string, operation: 'approve' | 'reject' | 'back'): boolean {
    const rules = STATUS_TRANSITION_RULES[fromStatus];
    if (!rules) return false;
    
    return rules[operation] === toStatus;
  }

  /**
   * 格式化状态转换日志
   */
  static formatStatusTransitionLog(fromStatus: string, toStatus: string): string {
    const fromName = this.getStatusName(fromStatus);
    const toName = this.getStatusName(toStatus);
    return `${fromStatus}(${fromName}) -> ${toStatus}(${toName})`;
  }

  /**
   * 检查是否为子流程状态
   */
  static isSubProcessStatus(status: string): boolean {
    return status.includes('[') && status.includes(']');
  }

  /**
   * 解析子流程状态
   */
  static parseSubProcessStatus(status: string): { type: string; memo?: string; isReject?: boolean } | null {
    const match = status.match(/\[(\w+)-?(\d+)?\](_reject)?/);
    if (!match) return null;

    return {
      type: match[1],
      memo: match[2],
      isReject: !!match[3]
    };
  }

  /**
   * 生成子流程状态字符串
   */
  static generateSubProcessStatus(type: string, memo?: string, isReject?: boolean): string {
    let status = `[${type}`;
    if (memo) {
      status += `-${memo}`;
    }
    status += ']';
    if (isReject) {
      status += '_reject';
    }
    return status;
  }

  /**
   * 获取工作票类型相关的状态配置
   */
  static getTicketTypeConfig(ticketType: string): { allowedStates: string[]; requiredFields: string[] } {
    switch (ticketType) {
      case '1': // 电气一种工作票
        return {
          allowedStates: ['1', '2', '3', '4', '5', '6', '7', '8', '13', '14', '15', '16', '17', '18'],
          requiredFields: ['workTask', 'workLocation', 'shouldOff', 'shouldConnect', 'shouldBlock']
        };
      case '2': // 电气二种工作票
        return {
          allowedStates: ['1', '2', '3', '4', '5', '6', '7', '8', '18'],
          requiredFields: ['workTask', 'workLocation', 'workCondition', 'precaution']
        };
      default:
        return {
          allowedStates: [],
          requiredFields: []
        };
    }
  }

  /**
   * 验证工作票数据完整性
   */
  static validateTicketData(data: ElectricalWorkTicketData): { valid: boolean; missingFields: string[] } {
    const config = this.getTicketTypeConfig(data.ticketType || '1');
    const missingFields: string[] = [];

    config.requiredFields.forEach(field => {
      if (!data[field]) {
        missingFields.push(field);
      }
    });

    return {
      valid: missingFields.length === 0,
      missingFields
    };
  }

  /**
   * 获取状态对应的操作按钮配置
   */
  static getStatusActions(status: string): Array<{ label: string; type: string; disabled?: boolean }> {
    const actions = [];
    const statusNum = parseInt(status);

    // 终态不显示操作按钮
    if (this.isFinalState(status)) {
      return actions;
    }

    // 基础操作按钮
    switch (status) {
      case '2': // 工作票签发
        actions.push({ label: '签发', type: 'approve' });
        actions.push({ label: '退回', type: 'back' });
        break;
      case '3': // 业主签发人确认
        actions.push({ label: '确认', type: 'approve' });
        actions.push({ label: '退回', type: 'back' });
        break;
      case '4': // 工作负责人确认
        actions.push({ label: '确认', type: 'approve' });
        break;
      case '5': // 值班负责人签字确认
        actions.push({ label: '确认', type: 'approve' });
        actions.push({ label: '退回', type: 'back' });
        break;
      case '6': // 值长审核
        actions.push({ label: '批准', type: 'approve' });
        actions.push({ label: '驳回', type: 'reject' });
        break;
      case '7': // 工作票许可审批
        actions.push({ label: '许可', type: 'approve' });
        actions.push({ label: '驳回', type: 'reject' });
        break;
      default:
        if (statusNum >= 9 && statusNum <= 12) {
          // 执行阶段，显示特殊操作
          actions.push({ label: '延期申请', type: 'delay' });
          actions.push({ label: '负责人变更', type: 'change' });
          actions.push({ label: '工作票交回', type: 'break' });
          actions.push({ label: '试运行申请', type: 'tryRun' });
        }
        break;
    }

    return actions;
  }
}

/**
 * 状态验证器类
 */
export class StatusValidator {
  /**
   * 验证状态转换的前置条件
   */
  static validatePreConditions(
    data: ElectricalWorkTicketData, 
    fromStatus: string, 
    toStatus: string
  ): { valid: boolean; message?: string } {
    // 检查数据完整性
    const dataValidation = StatusUtils.validateTicketData(data);
    if (!dataValidation.valid) {
      return {
        valid: false,
        message: `数据不完整，缺少字段: ${dataValidation.missingFields.join(', ')}`
      };
    }

    // 检查状态转换合法性
    if (StatusUtils.isFinalState(fromStatus)) {
      return {
        valid: false,
        message: `当前状态 ${StatusUtils.getStatusName(fromStatus)} 是终态，无法转换`
      };
    }

    return { valid: true };
  }

  /**
   * 验证操作权限
   */
  static validateOperationPermission(
    currentStatus: string,
    operation: string,
    userRole?: string
  ): { valid: boolean; message?: string } {
    // 根据用户角色和当前状态验证操作权限
    // 这里可以根据实际业务需求扩展权限验证逻辑
    
    return { valid: true };
  }
} 