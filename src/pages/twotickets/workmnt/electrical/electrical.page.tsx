import { createElement, useRef, useState } from 'react';

import _ from 'lodash';
import dayjs from 'dayjs';
import { history } from 'umi';
import { Tabs } from 'antd-mobile';

import { SHOW_TIME_FORMAT } from '@/utils/constant';
import { ProcessCard } from '@/component/process-card/process-card.component';
import TwoTicketsNavBar from '@/component/twotickets-navbar/twotickets-navbar.component';
import { useTabScrollSync } from '@/hooks/useTabScrollSync';

import useDetail from './hooks/useDetail';
import { useSubmitWorkOrder } from './hooks/useSubmitWorkOrder';
import { useStatusHandler } from './hooks/useStatusHandler';
import { useOptimizedReviewPopup } from './hooks/useOptimizedReviewPopup';
import ReviewPopup from './components/review-popup/review-popup.component';
import ActionButtons from '../components/action-buttons/action-buttons.component';
import SafeToolComponent from '../components/safe-tool/safe-tool.component';
import WorkOrderExecution from './components/work-order-execution/work-order-execution.component';
import { BasicInfoComponent } from './components/basic-info.component';
import SafetyMeasureComponent from './components/safety-measure/safety-measure.component';
import { OperationHeaderBtn } from '../../operation/detail/components/operation-btn/operation-header-btn.component';
import SecurityRiskControlContent from '../components/security-risk-control-content/security-risk-control.content';
import SecurityTicketContent, { SecurityTicketContentRef } from './components/security-ticket-content/security-ticket-content.component';

import styles from './electrical.page.less';

// import { TreePickerRef } from '@/component/tree-picker/types';
// import OrgPicker from '../components/org-picker/org-picker.component';
// import UserPicker from '../components/user-picker/user-picker.component';

const TAB_HEIGHT = 42;

const ElectricalPage = () => {
  const [activeKey, setActiveKey] = useState('1');
  const { data, loading, title, pageType, type, ticketType } = useDetail('electrical');

  const safetyMeasureRef = useRef<any>(null);
  const securityTicketContentRef = useRef<SecurityTicketContentRef>(null);
  const workOrderExecutionRef = useRef<any>(null);

  //#region Hooks 初始化
  // 使用新的 hook
  const {
    onSubmit
  } = useSubmitWorkOrder({
    data,
    type: type || '',
    safetyMeasureRef,
    securityTicketContentRef,
    workOrderExecutionRef,
  });

  // 提取状态处理逻辑
  const {
    visible,
    setVisible,
    variables,
    activeName,
    status,
    handleActionClick
  } = useStatusHandler(data, onSubmit, {
    safetyMeasureRef,
    securityTicketContentRef,
    workOrderExecutionRef,
  });
  //#endregion

  //#region 基本信息配置
  const basicList = [
    { type: 'title', value: "workTask" },
    { label: '设备信息', value: "deviceName" },

    // 分割线
    { type: 'divider' },

    { label: '单位', value: "unitName" },
    { label: '成员', value: "member", render: (_text: string, record: any) => `${record?.member || ''}${record?.member ? `（参加工作人数共${record?.peopleNumber || 0}人）` : ''}` },
    { label: '班组', value: "teamsId" },

    // 分割线
    { type: 'divider' },

    {
      label: '计划时间', value: "planBeginTime", render: () => {
        return `${data?.planBeginTime ?
          dayjs(data.planBeginTime).format(SHOW_TIME_FORMAT) :
          ''} ~ ${data?.planEndTime ?
            dayjs(data.planEndTime).format(SHOW_TIME_FORMAT) : ''}`
      }
    },
    ...(ticketType === '2' ? [
      { label: '工作地点及或地段', value: "workLocation" },
      { label: '工作条件', value: "workCondition" },
      { label: '注意事项（安全措施）', value: "precaution" },
    ] : [
      { label: '工作地点及设备双重名称', value: "workLocation" },
    ]),
    { label: '创建时间', value: "createTime" },
  ]
  //#endregion

  //#region Tab页签配置
  const TAB_ITEMS: { key: string; title: string; content: (data: any) => unknown }[] = [
    {
      key: '1',
      title: '基本信息',
      content: (data: any) => <BasicInfoComponent data={data} list={basicList} />,
    },
    {
      key: '3',
      title: '安全风险控制卡',
      content: (data: any) => <SecurityRiskControlContent data={data} />,
    },
    ...(ticketType === '1' ? [{
      key: '4',
      title: '安全措施',
      content: (data: any) => createElement(SafetyMeasureComponent as any, {
        ref: safetyMeasureRef,
        data,
      }),
    }] : []),
    {
      key: '5',
      title: '安全措施票',
      content: (data: any) => createElement(SecurityTicketContent as any, {
        ref: securityTicketContentRef,
        data,
      }),
    },
    {
      key: '6',
      title: '安全工器具',
      content: (data: any) => <SafeToolComponent data={data} />,
    },
  ];

  if (Number(data?.status) > 8) {
    TAB_ITEMS.splice(1, 0, {
      key: '2',
      title: '工作票执行',
      content: (data: any) => createElement(WorkOrderExecution, {
        data,
        ref: workOrderExecutionRef,
        ticketType, // 使用常量
        actionType: pageType,
      }),
    });
  }
  //#endregion

  //#region 滚动处理逻辑 - 使用自定义hook
  const { scrollContainerRef, handleTabChange } = useTabScrollSync({
    tabItems: TAB_ITEMS,
    activeKey,
    setActiveKey,
    tabHeight: TAB_HEIGHT,
  });
  //#endregion

  return (
    <div className={styles.container} style={{ paddingTop: `${window.top?.localStorage?.topHeight ?? 0}px` }}>
      <TwoTicketsNavBar title={data?.taskName ? data?.taskName : title} subTitle={data?.taskName ? title : ''} bgcolor={'#fff'} color={'#000'} right={<OperationHeaderBtn data={data} />} />

      {/* 主体内容区域 */}
      <div className={styles.body} ref={scrollContainerRef}>
        <div className={styles.content}>
          <ProcessCard
            id={data?.id}
            records={_.cloneDeep(data?.workflowApprovalRecordList || []).reverse()}
            onProcess={function (_id: number): void {
              history.push(`/twotickets/common/check-list/process-viewer/${data?.id}?ticketType=${ticketType}`);
            }}
            taskUserName={data?.taskUserName}
          />
        </div>

        <div className={styles.tabsContainer}>
          <Tabs
            activeKey={activeKey}
            onChange={handleTabChange}
          >
            {TAB_ITEMS.map((item) => (
              <Tabs.Tab title={item.title} key={item.key} />
            ))}
          </Tabs>
        </div>

        <div className={styles.tabsContent}>
          {TAB_ITEMS.map((item: any) => (
            <div key={item.key} id={`anchor-${item.key}`}>
              {item.content(data)}
            </div>
          ))}
        </div>
        <div className={styles.bottom}>已经到底了</div>
      </div>

      {/* 操作按钮区域 */}
      {pageType === "edit" && data?.curTask && <div className={styles.operation}>
        {/* 操作按钮 */}

        <ActionButtons
          onClick={handleActionClick}
          showSaveBtn={![4, 5, 6, 8, 10, 11, 12, 13, 14, 15, 16, 17].includes(Number(data?.status))}
          taskId={data?.taskId}
          disabledConfig={{
            "工作延期": !!data?.workTicketExecute?.workFlowExecuteDelay,
            "工作负责人变更": !!data?.workTicketExecute?.workFlowExecuteHeadExchange,
            "检修设备试运行申请": !!data?.workTicketExecute?.workFlowTryRecover,
            "检修设备试运行后恢复工作申请": !!data?.workTicketExecute?.workFlowTryRunSafe,
          }}
          loading={loading}
          detailData={data}
        />

      </div>}

      {/* 审核弹窗 */}
      {/* 使用优化的 ReviewPopup */}
      {(() => {
        const optimizedPopup = useOptimizedReviewPopup({
          visible,
          setVisible,
          variables,
          activeName,
          status,
          detailData: data,
          onSubmit
        });

        // 只在需要时渲染 ReviewPopup
        if (!optimizedPopup.shouldRender) {
          return null;
        }

        return (
          // @ts-ignore
          <ReviewPopup {...optimizedPopup.reviewPopupProps} />
        );
      })()}

    </div>)
};
export default ElectricalPage;
