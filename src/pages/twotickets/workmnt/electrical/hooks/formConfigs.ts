/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-21 14:17:54
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-05-28 17:11:30
 * @FilePath: /mobile-yw/src/pages/twotickets/workmnt/electrical/hooks/formConfigs.ts
 * @Description: 具体表单配置定义
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */

import { FormConfig, FormItemConfig, StatusFormList } from './types';
import { 
  createOpinionForm,
  createTimeAndOpinionForm,
  createRangeTimeAndOpinionForm,
  createChangeFormConfig,
  createWorkHeadUserPickerConfig,
  createOrgPickerConfig,
  createGroupPickerConfig,
  createExecutionStatusConfig,
} from './configFactory';
import { DEFAULT_RULES, OPINION_DEFAULTS } from './constants';
import { FinalExplanationValue } from '../components/final-explanation/final-explanation.component';

/**
 * 提取常用的意见表单配置为辅助函数
 */
const getOpinionTextareaConfig = (): FormConfig => {
  return [createOpinionForm(OPINION_DEFAULTS.auditLabel)];
};

/**
 * 检修设备试运行申请配置
 */
export const createTryRunFormConfig = (): FormConfig => [
  [
    ...createOpinionForm("工作地点", "workLocation"),
    ...createOpinionForm("工作内容", "workTask"),
    createWorkHeadUserPickerConfig(),
    createOrgPickerConfig(),
    createGroupPickerConfig(),
  ],
  [
    ...createOpinionForm("热控工作票编号", "thermalControlCode"),
    createWorkHeadUserPickerConfig('工作负责人', 'thermalControlHead'),
  ],
  [
    ...createOpinionForm("电气工作票编号", "electricalCode"),
    createWorkHeadUserPickerConfig('工作负责人', 'electricalName'),
  ],
  [
    ...createOpinionForm("机械工作票编号", "machineCode"),
    createWorkHeadUserPickerConfig('工作负责人', 'machineName'),
  ],
  [
    ...createOpinionForm("其他", "otherCode"),
    createWorkHeadUserPickerConfig('工作负责人', 'otherName'),
  ],
  [
    createExecutionStatusConfig(),
  ]
];

/**
 * 检修设备试运行后恢复工作申请配置
 */
export const createTryRunRepairFormConfig = (): FormConfig => [
  [
    ...createOpinionForm("工作地点", "workLocation"),
    ...createOpinionForm("工作内容", "workTask"),
    createWorkHeadUserPickerConfig(),
    createOrgPickerConfig(),
    createGroupPickerConfig(),
  ],
  [
    createExecutionStatusConfig('拆除安全措施', 'workFlowTryRunSafe'),
  ]
];

/**
 * Form15 审核配置
 */
export const createForm15ApproveConfig = (): FormConfig => [[
  {
    strategyType: 'datePicker',
    label: '结票时间',
    name: 'endTime',
    required: true,
  },
  {
    strategyType: 'finalExplanation',
    label: '终结说明',
    name: 'endDescribe',
    required: true,
    rules: [
      { 
        validator: (_: any, value: FinalExplanationValue) => {
          if (!value || !value.groundWireCode || !value.groundWireNum || !value.knifeSwitch || !value.knifeSwitchNum) {
            return Promise.reject('请填写终结说明的所有输入项');
          }
          return Promise.resolve();
        }
      }
    ],
  },
  {
    strategyType: 'userPicker',
    label: '指定专责负责人',
    name: 'appointHeadName',
    layout: 'horizontal',
    required: true,
    rules: DEFAULT_RULES.required('请选择指定专责负责人', 'array'),
    strategyProps: {
      placeholder: '请选择',
      multiple: false,
    },
  },
  ...createOpinionForm("地点及具体工作", "workMatter"),
  ...createOpinionForm("其他事项", "appointWork"),
]];

/**
 * Form17 审核配置
 */
export const createForm17ApproveConfig = (): FormConfig => [[
  {
    strategyType: 'radioGroup',
    label: '审核结果',
    name: 'auditResult',
    childElementPosition: 'right',
    layout: 'horizontal',
    required: true,
    rules: DEFAULT_RULES.required('请选择审核结果', 'string'),
  },
  ...createOpinionForm(OPINION_DEFAULTS.auditLabel)
]];

/**
 * Form10 Approve3 配置
 */
export const createForm10Approve3Config = (): FormConfig => [[
  {
    strategyType: 'datePicker',
    label: '有效期延长至',
    required: true,
    name: 'newEndTime',
    rules: DEFAULT_RULES.required('请选择有效期延长至', 'date'),
  },
  ...createOpinionForm(OPINION_DEFAULTS.auditLabel)
]];

/**
 * 试运行修复11表单配置
 */
export const createTryRunRepair11FormConfig = (): FormConfig => [
  [
    ...createOpinionForm("工作地点", "workLocation"),
    ...createOpinionForm("工作内容", "workTask"),
    createWorkHeadUserPickerConfig(),
    createOrgPickerConfig(),
    createGroupPickerConfig('班组', 'teamsName'),
  ],
  [
    createExecutionStatusConfig('拆除安全措施', 'workFlowTryRunSafe'),
  ],
  [
    {
      strategyType: 'datePicker',
      label: '恢复时间',
      name: 'tryTime',
      layout: 'horizontal',
      strategyProps: {},
    },
    ...createOpinionForm(OPINION_DEFAULTS.auditLabel, "opinion")
  ]
];

/**
 * 试运行11表单配置
 */
export const createTryRun11FormConfig = (): FormConfig => [
  [
    ...createOpinionForm("工作地点", "workLocation"),
    ...createOpinionForm("工作内容", "workTask"),
    createWorkHeadUserPickerConfig(),
    createOrgPickerConfig(),
    createGroupPickerConfig(),
  ],
  [
    ...createOpinionForm("热控工作票编号", "thermalControlCode"),
    createWorkHeadUserPickerConfig('工作负责人', 'thermalControlHead'),
  ],
  [
    ...createOpinionForm("电气工作票编号", "electricalCode"),
    createWorkHeadUserPickerConfig('工作负责人', 'electricalName'),
  ],
  [
    ...createOpinionForm("机械工作票编号", "machineCode"),
    createWorkHeadUserPickerConfig('工作负责人', 'machineName'),
  ],
  [
    ...createOpinionForm("其他", "otherCode"),
    createWorkHeadUserPickerConfig('工作负责人', 'otherName'),
  ],
  [
    createExecutionStatusConfig('拆除安全措施', 'workFlowTryRunSafe'),
  ]
];

/**
 * 创建主流程表单配置
 */
const createMainProcessForms = (): Record<string, FormConfig | FormItemConfig[]> => {
  const opinionTextareaConfig = getOpinionTextareaConfig();
  
  return {
    "2_approve": opinionTextareaConfig, // 工作票签发
    "3_approve": opinionTextareaConfig, // 业主签发人确认
    "4_approve": [], // 工作负责人确认
    "5_approve": createTimeAndOpinionForm('收到工作票时间'), // 值班负责人签字确认
    "6_approve": createRangeTimeAndOpinionForm('检修作业批准时间'), // 值长审核
    "7_approve": createTimeAndOpinionForm('许可时间'), // 工作票许可审批
    "8": [], // 工作负责人接收确认
    "9_approve": opinionTextareaConfig, // 工作票执行情况填报
    "10_approve": opinionTextareaConfig, // 工作票执行情况填报
    "11_approve": opinionTextareaConfig, // 工作票执行情况填报
    "12_approve": opinionTextareaConfig, // 工作票执行情况填报
    "13_approve": opinionTextareaConfig, // 工作票执行确认审核（工作许可人）
    "14": [],
    "15_approve": createForm15ApproveConfig(), // 工作票执行确认审核（工作负责人）
    "16_approve": opinionTextareaConfig, // 可能遗漏的状态
    "17_approve": createForm17ApproveConfig(), // 工作票执行确认审核（工作负责人）
    "18_approve": opinionTextareaConfig, // 可能遗漏的状态
  };
};

/**
 * 创建延期流程表单配置
 */
const createDelayProcessForms = (): Record<string, FormConfig | FormItemConfig[]> => {
  const opinionTextareaConfig = getOpinionTextareaConfig();
  
  return {
    "delay": [createOpinionForm('延期理由')], // 工作延期
    "[delay-2]": opinionTextareaConfig, // 工作延期 通过不通过
    "[delay-3]": createForm10Approve3Config(), // 工作延期 
    "[delay-3]_reject": opinionTextareaConfig, // 工作延期 通过不通过
    "[delay-4]": [createOpinionForm('延期理由')], // 该禁用
  };
};

/**
 * 创建工作负责人变更流程表单配置
 */
const createChangeProcessForms = (): Record<string, FormConfig | FormItemConfig[]> => {
  const opinionTextareaConfig = getOpinionTextareaConfig();
  
  return {
    "change": createChangeFormConfig(), // 工作负责人变更
    "[change-2]": opinionTextareaConfig,
    "[change-3]": opinionTextareaConfig,
    "[change-4]": createChangeFormConfig(),
  };
};

/**
 * 创建工作票交回流程表单配置
 */
const createTicketReturnForms = (): Record<string, FormConfig | FormItemConfig[]> => {
  const opinionTextareaConfig = getOpinionTextareaConfig();
  
  return {
    "break": [createOpinionForm('备注', 'remark')], // 工作票交回
    "[ticketReturn-2]": [createOpinionForm('备注', 'remark')],
    "[ticketReturn-1]": opinionTextareaConfig,
    "[ticketReturn-3]": opinionTextareaConfig,
    "ticketReturn": opinionTextareaConfig,
  };
};

/**
 * 创建检修设备试运行相关流程表单配置
 */
const createTryRunProcessForms = (): Record<string, FormConfig | FormItemConfig[]> => {
  const opinionTextareaConfig = getOpinionTextareaConfig();
  
  return {
    "[tryRun-1]": opinionTextareaConfig,
    "[tryRun-2]": createTryRunFormConfig(), // 检修设备试运行申请
    "[tryRun-3]": opinionTextareaConfig,
    
    "[tryRunRepair-1]": opinionTextareaConfig,
    "[tryRunRepair-2]": createTryRunRepairFormConfig(), // 检修设备试运行后恢复工作申请
    "[tryRunRepair-3]": opinionTextareaConfig,
  };
};

/**
 * 生成基础状态表单列表
 */
export const createBaseStatusFormList = (): StatusFormList => {
  // 组合所有模块化配置
  const config: StatusFormList = {
    ...createMainProcessForms(),
    ...createDelayProcessForms(),
    ...createChangeProcessForms(),
    ...createTicketReturnForms(),
    ...createTryRunProcessForms(),
  };
  
  return config;
}; 