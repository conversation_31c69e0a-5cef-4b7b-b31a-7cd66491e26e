import dayjs from 'dayjs';
import { useRef } from 'react';
import { history } from 'umi';
import { Toast } from 'antd-mobile';
import type { ToastHandler } from 'antd-mobile/es/components/toast';
import { delayAudit, ticketReturn, tryRunAudit } from '@/services/twotickets/workmnt/electrical';
import { headerExchangeAudit, updateWorkBase } from '@/services/twotickets/workmnt/workmnt';
import { SHOW_TIME_FORMAT } from '@/utils/constant';

// 生成显示文本
const generateDisplayText = (
  groundWireCode: string,
  groundWireNum: string,
  knifeSwitch: string,
  knifeSwitchNum: string,
  endTime: string
) => {
  return `1.全部工作于 ${endTime} 结束，设备及安全措施已恢复至开工前状态，工作人员全部撤离，材料工具已清理完毕。
  2.临时遮拦、标示牌已拆除，常设遮栏已恢复。未拆除或未拉开的接地线编号${groundWireCode}等共（${groundWireNum}）组，接地刀闸（小车）${knifeSwitch}共（${knifeSwitchNum}）副（台），已汇报值班负责人。`;
};

const otherUserName = 'header';

interface UseSubmitWorkOrderProps {
  data: any;
  type: string;
  safetyMeasureRef: React.RefObject<any>;
  securityTicketContentRef: React.RefObject<any>;
  workOrderExecutionRef: React.RefObject<any>;
}

export const useSubmitWorkOrder = ({
  data,
  type,
  safetyMeasureRef,
  securityTicketContentRef,
  workOrderExecutionRef,
}: UseSubmitWorkOrderProps) => {
  const toastHandler = useRef<ToastHandler>();

  // 主提交函数
  const onSubmit = async (values: any, { extra, variables: vari, code = '' }: any, activeName: string) => {
    console.log("🚀 ~ onSubmit:", { values, extra, vari, code, activeName })
    for (const key in values) {
      if (values[key] instanceof Date) {
        values[key] = dayjs(values[key]).format('YYYY-MM-DD HH:mm:ss');
      }
    }

    const { sub = '' } = vari || {};

    // 延期
    if (sub === 'delay') {
      return onDelayFetch(values, { extra, variables: vari }, activeName);
    }

    // 负责人变更
    if (sub === 'change') {
      return onExChangeFetch(values, { extra, variables: vari }, activeName);
    }

    // 工作票交回
    if (sub === 'break') {
      return onTicketReturnFetch(values, { extra, variables: vari }, activeName);
    }

    // 检修设备试运行申请
    if (sub === 'tryRun') {
      return onTryRunFetch(values, { extra, variables: vari }, activeName);
    }

    // 检修设备试运行后恢复工作申请
    if (sub === 'tryRunRepair') {
      return onTryRunRepairFetch(values, { extra, variables: vari }, activeName);
    }

    toastHandler.current = Toast.show({
      duration: 0,
      position: 'top',
      icon: 'loading',
      content: '加载中…',
      maskClickable: false,
    });

    if (vari?.pass === 'true') {
      vari[otherUserName] = data?.['headUserName'] || '';
    }

    const isAudit = 1;
    const payload = {
      ...Object.fromEntries(
        Object.entries(data ?? {}).filter(([key, value]) => value !== undefined && value !== null),
      ),
      isAudit,
      behavior: isAudit === 1 && activeName === "通过" ? 'true' : 'false',
      ...values,
      variables: {
        ...vari,
      },
    };

    // 准备中的数据
    if (payload.status === "1") {
      payload.behavior = payload?.behavior ? "true" : "false";
    }

    // 工作负责人签字确认
    if (payload.status === "4") {
      payload.behavior = "true";
      payload.opinion = payload.opinion;   // 确认意见
    }

    // 值班负责人确认
    if (payload?.status === "5") {
      payload.workFlowConfirm = {
        accessTime: dayjs(payload.accessTime).format('YYYY-MM-DD HH:mm:ss')
      }
      delete payload.accessTime;
    }

    // 值长批准
    if (payload?.variables?.pass === 'true' && (payload.status === "6")) {
      // 处理批准作业时间
      payload.workFlowRatify = {
        approveBeginTime: payload?.approveTime?.[0] ? dayjs(payload?.approveTime?.[0]).format('YYYY-MM-DD HH:mm:ss') : '',
        approveEndTime: payload?.approveTime?.[1] ? dayjs(payload?.approveTime?.[1]).format('YYYY-MM-DD HH:mm:ss') : '',
        opinion: payload?.opinion,
      }
      delete payload.approveTime;
    }

    // 基本信息许可人审核
    if (payload.status === "7") {    // 值长批准
      payload.workFlowLicense = {
        allowBeginTime: payload?.allowBeginTime ? dayjs(payload.allowBeginTime).format('YYYY-MM-DD HH:mm:ss') : '',
        opinion: payload?.reviewData,
      }
      delete payload.allowBeginTime;
    }

    // 工作人负责人确认
    if (payload.status === "8") {
      payload.behavior = true;
      payload.opinion = payload.opinion;   // 确认意见
    }

    if (payload.status === "9") {    // 执行情况填报
      payload.behavior = true;
      payload.opinion = payload.opinion;   // 确认意见
    }

    if (payload.status === "15") {    // 工作票终结
      // 如果需要生成完整文本
      const endDescribeText = values.endDescribe ? generateDisplayText(
        values.endDescribe.groundWireCode,
        values.endDescribe.groundWireNum,
        values.endDescribe.knifeSwitch,
        values.endDescribe.knifeSwitchNum,
        dayjs(values.endTime).format('YYYY年MM月DD日HH时mm分')
      ) : null;

      payload.workFlowEnd = {
        ...values,

        endTime: dayjs(values.endTime).format('YYYY-MM-DD HH:mm:ss'),
        headName: values?.headName?.[0]?.name ?? '',
        headId: values?.headName?.[0].id ?? '',

        appointHeadName: values?.appointHeadName?.[0]?.name ?? '',
        appointHeadId: values?.appointHeadName?.[0]?.id ?? '',

        groundWireCode: values?.endDescribe?.groundWireCode ?? '',
        groundWireNum: values?.endDescribe?.groundWireNum ?? '',
        knifeSwitch: values?.endDescribe?.knifeSwitch ?? '',
        knifeSwitchNum: values?.endDescribe?.knifeSwitchNum ?? '',

        endDescribe: endDescribeText, // 如果需要传递完整文本
      };
      payload.behavior = "true";
      delete payload.endDescribe;
      delete payload.appointHeadName;
      delete payload.groundWireCode;
      delete payload.groundWireNum;
      delete payload.knifeSwitch;
      delete payload.knifeSwitchNum;
      delete payload.endTime;
    }
    if (payload?.status === "9" && isAudit === 1) {
      const status = data?.status ? Number(data?.status) : 0; // 当前流程状态
      const { planEndTime, workFlowConfirm, workFlowRatify, workFlowLicense, workFlowExecuteConfirm, workFlowEnd } = payload;
      workOrderExecutionRef.current?.form?.validateFields?.()?.then(() => {
        const planEndTimeD = dayjs(planEndTime);
        const accessTimeD = dayjs(workFlowConfirm?.accessTime);
        const approveEndTimeD = dayjs(workFlowRatify?.approveEndTime);
        const approveTuneD = dayjs(workFlowRatify?.approveTune);
        const allowBeginTimeD = dayjs(workFlowLicense?.allowBeginTime);
        const endTimeD = dayjs(workFlowEnd?.endTimeD);
        const shiftSupervisorTimeD = dayjs(workFlowExecuteConfirm?.shiftSupervisorTime);

        let errorMessage = '';
        if (status === 5 && (approveEndTimeD.isBefore(accessTimeD) || approveEndTimeD.isAfter(planEndTimeD))) {
          // 值长审批：批准工作结束时间默认是计划结束日期（修改时要求不能早于确认时间，晚于计划结束日期，之前页面设计时写的默认当前时间，需要修改一下； 备注：一般批准工作结束时间就是计划结束日期 ）
          errorMessage = '批准工作结束时间不能早于运行值班负责人确认时间，晚于计划结束时间';
        } else if (status === 6 && (allowBeginTimeD.isBefore(approveTuneD) || allowBeginTimeD.isAfter(approveEndTimeD))) {
          // 许可人审批：许可开始工作时间默认是当前时间（修改时要求：不能早于值长批准时间，晚于批准工作结束时间）
          errorMessage = '许可开始工作时间不能早于值长批准时间，晚于批准工作结束时间';
        } else if (status === 12 && (endTimeD.isBefore(shiftSupervisorTimeD) || allowBeginTimeD.isAfter(approveEndTimeD))) {
          // 许可人终结：工作结束时间默认是当前时间 （修改时要求：不能早于工作票执行确认时间，晚于批准作业结束时间）
          errorMessage = '许可开始工作时间不能早于值长批准时间，晚于批准工作结束时间';
        }
        if (errorMessage) {
          Toast.show({
            icon: 'fail',
            content: errorMessage,
          });
          return;
        }
      })
    }
    if (payload?.status === "17") {
      payload.workFlowAudit = {
        auditResult: payload.auditResult,
        auditOpinion: payload.opinion,
      }
    }
    // 工作票执行成员变更表
    if (payload && ['9', '10', '11', '12'].includes(payload.status)) {
      const _executeData = {
        ...payload.workTicketExecute,
        workFlowExecuteUserExchanges: workOrderExecutionRef?.current?.getWorkTicketExecute()?.workFlowExecuteUserExchanges,
        workFlowExecute: workOrderExecutionRef?.current?.getWorkTicketExecute().workFlowExecute,
        workExecuteRecordList: workOrderExecutionRef?.current?.getWorkTicketExecute().workExecuteRecordList,
      }
      payload.workTicketExecute = _executeData;
    }

    // 获取安全措施组件的数据并合并到payload
    const safetyMeasureData = safetyMeasureRef.current?.getValue();
    if (safetyMeasureData) {
      payload.workElectricalSafeMeasure = safetyMeasureData;
    }

    // 获取工作票执行组件的数据并合并到payload
    const maintenanceIstructionsData = workOrderExecutionRef.current?.getMaintenanceIstructionsRef()?.getFieldsValue() || {};
    if (data?.status === "9") {
      const { canRun, repairOrder } = maintenanceIstructionsData;
      payload.maintenanceIstructions = maintenanceIstructionsData;
      if (!repairOrder) {
        Toast.clear();
        Toast.show({
          icon: 'fail',
          content: '检修交代不能为空',
        });
        return;
      }
      if (!canRun) {
        Toast.clear();
        Toast.show({
          icon: 'fail',
          content: '请选择设备是否可投运',
        });
        return;
      }
      payload.workFlowExecute = {
        ...payload.workFlowExecute,
        canRun,
        repairOrder,
      }
    }

    payload.headUserName = data?.['headUserName'] || '';

    try {
      const res = await updateWorkBase(payload);
      setTimeout(() => {
        Toast.clear()
        if (res?.code === '1') {
          Toast.show({
            icon: 'success',
            content: '已处理',
          });
          history.push(`/twotickets/workmnt/electrical/${type}`);
        } else {
          Toast.show({
            icon: 'fail',
            content: res?.message || '提交失败',
          });
        }
      }, 600);
      return true;
    } catch (error) {
      Toast.show({
        icon: 'fail',
        content: '提交失败',
      });
      return false;
    }
  };

  // 延期
  const onDelayFetch = async (values: any, { extra, variables: conditionParams }: any, activeName: string) => {

    const workFlowExecuteDelay = data.workTicketExecute.workFlowExecuteDelay || {};
    let memoValue = 1;
    if (conditionParams?.pass === 'true') {
      memoValue = Number(workFlowExecuteDelay.memo) + 1;
    } else if (conditionParams.memo) {
      memoValue = Number(conditionParams.memo);
    }
    // const memo = (conditionParams?.pass === 'true' ? (Number(workFlowExecuteDelay.memo) + 1) : conditionParams.memo ? Number(conditionParams.memo) : 1).toString();
    const memo = memoValue.toString();
    const payload = {
      ...workFlowExecuteDelay,
      newEndTime: dayjs(values.newEndTime).format('YYYY-MM-DD HH:mm:ss'),
      ...values,
      variables: conditionParams,
      ticketType: data?.ticketType,
      mainId: data?.id,
      memo,
    }
    if (payload.memo != '4') {
      payload.newEndTime = workFlowExecuteDelay?.newEndTime || null;
    }
    try {
      toastHandler.current = Toast.show({
        duration: 0,
        position: 'top',
        icon: 'loading',
        content: '加载中…',
        maskClickable: false,
      })
      const res = await delayAudit(payload)
      if (res?.code === '1') {
        Toast.clear();
        Toast.show({
          icon: 'success',
          content: '已处理',
        });
        history.push(`/twotickets/workmnt/electrical/${type}`);
        return true;
      }
      return false;
    } catch (error) {
      Toast.clear();
      Toast.show({
        icon: 'fail',
        content: '提交失败',
      });
      return false;
    }
  };

  // 负责人变更
  const onExChangeFetch = async (values: any, { extra, variables: conditionParams }: any, activeName: string) => {
    const approveEndTimeD = dayjs(data?.planEndTime);
    const planBeginTimeD = dayjs(data?.planBeginTime);
    const changeTimeD = dayjs(values?.changeTime);

    console.log("🚀 ~ onExChangeFetch:", { values, data, activeName })

    if (data?.planEndTime && data?.planBeginTime && data?.changeTime) {
      if (changeTimeD.isBefore(planBeginTimeD) || changeTimeD.isAfter(approveEndTimeD)) {
        Toast.show({
          icon: 'fail',
          content: '变更时间不能早于计划开始日期，不能晚于批准工作结束日期',
        });
        return false;
      }
    }

    const nowHeadId = values?.nowHeadId?.[0].id;
    const nowHeadName = values?.nowHeadId?.[0].name;

    const workFlowExecuteHeadExchange = data?.workTicketExecute?.workFlowExecuteHeadExchange;

    const payload = {
      nowHeadId,
      nowHeadName,
      ...workFlowExecuteHeadExchange,
      ...values,
      changeTime: dayjs(values?.changeTime).format('YYYY-MM-DD HH:mm:ss'),
      ticketType: data?.ticketType,
      mainId: data?.id,
      isAudit: "true",
      variables: conditionParams || {},
      memo: conditionParams?.memo || "2",
    }
    try {
      const res = await headerExchangeAudit(payload)
      if (res?.code === '1') {
        Toast.clear();
        Toast.show({
          icon: 'success',
          content: '已处理',
        });
        history.push(`/twotickets/workmnt/electrical/${type}`);
        return true;
      }
      return false;
    } catch (error) {
      Toast.clear();
      Toast.show({
        icon: 'fail',
        content: '提交失败',
      });
      return false;
    }
  };

  // 工作票交回
  const onTicketReturnFetch = async (values: any, { extra, variables: conditionParams }: any, activeName: string) => {
    const workFlowReturns = data?.workTicketExecute?.workFlowReturns ?? [];
    let params: any = {};

    const isIssuance = workFlowReturns.length && workFlowReturns[0].memo === '3' ? true : false
    if (isIssuance) {   // 工作票发放的参数
      params = {
        ...workFlowReturns[0],
        grantTime: dayjs(values.submitTime).format('YYYY-MM-DD HH:mm:ss'),
        grantRemark: values.remark,
        memo: 4,
        mainId: Number(data.id),
        ticketType: data.ticketType,
      }
    } else {
      params = {
        ...values,
        submitTime: dayjs(values.submitTime).format('YYYY-MM-DD HH:mm:ss'),
        mainId: Number(data.id),
        ticketType: data.ticketType,
      }
    }
    params.variables = conditionParams;
    try {
      const res = await ticketReturn(params)
      if (res?.code === '1') {
        Toast.clear();
        Toast.show({
          icon: 'success',
          content: '已处理',
        });
        history.push(`/twotickets/workmnt/electrical/${type}`);
        return true;
      }
      return false;
    } catch (error) {
      Toast.clear();
      Toast.show({
        icon: 'fail',
        content: '提交失败',
      });
      return false;
    }
  };

  // 检修设备试运行申请
  const onTryRunFetch = async (values: any, { extra, variables: conditionParams }: any, activeName: string) => {
    console.log("🚀 ~ onTryRunFetch:", { values, conditionParams, extra, activeName, data, type, workOrderExecutionRef })
    const payload = {
      ...values,

      teamsNmae: values?.teamsNmae?.map?.((item: any) => item?.label || item)?.join?.(','),
      workFlowTryRunSafe: values?.workFlowTryRunSafe?.length ? values?.workFlowTryRunSafe : null,
      orgId: Number(values.orgId),
      ticketType: '1',
      type: type === 'trialRun' ? 1 : 2, // 类型 1-试运行 2-恢复工作
      tryTime: dayjs(values.tryTime).format('YYYY-MM-DD HH:mm:ss'),
      behavior: '1',
      mainId: Number(data.id),
      isAudit: 1,
      processInstanceId: data.processInstanceId,
      variables: conditionParams,
      memo: conditionParams?.memo || "2",
    }
    if (Array.isArray(payload?.headId)) {
      payload.headId = payload?.headId[0].id || '';
    }
    if (Array.isArray(payload?.headName)) {
      payload.headName = payload?.headName[0].name || '';
    }
    if (Array.isArray(payload?.electricalName)) {
      payload.electricalName = payload?.electricalName[0].id || '';
    }
    if (Array.isArray(payload?.thermalControlHead)) {
      payload.thermalControlHead = payload?.thermalControlHead[0].id || '';
    }
    if (Array.isArray(payload?.thermalControlCode)) {
      payload.thermalControlCode = payload?.thermalControlCode[0].id || '';
    }
    if (Array.isArray(payload?.machineName)) {
      payload.machineName = payload?.machineName[0].id || '';
    }
    if (Array.isArray(payload?.electricalName)) {
      payload.electricalName = payload?.electricalName[0].id || '';
    }

    if (Array.isArray(payload?.otherName)) {
      payload.otherName = payload?.otherName[0].id || '';
    }
    try {
      const res = await tryRunAudit(payload)
      if (res?.code === '1') {
        Toast.clear();
        Toast.show({
          icon: 'success',
          content: '已处理',
        });
        history.push(`/twotickets/workmnt/electrical/${type}`);
        return true;
      }
      return false;
    } catch (error) {
      Toast.clear();
      Toast.show({
        icon: 'fail',
        content: '提交失败',
      });
      return false;
    }
  };

  // 检修设备试运行后恢复工作申请
  const onTryRunRepairFetch = async (values: any, { extra, variables: conditionParams }: any, activeName: string) => {
    const payload = {
      ...values,
      teamsNmae: values?.teamsNmae?.map?.((item: any) => item)?.join?.(','),
      workFlowTryRunSafe: values?.workFlowTryRunSafe?.length ? values?.workFlowTryRunSafe : null,
      orgId: values.orgId?.map?.((item: any) => item?.id)?.join?.(',') || Number(values.orgId),
      ticketType: '1',
      type: type === 'trialRun' ? 1 : 2, // 类型 1-试运行 2-恢复工作
      tryTime: dayjs(values.tryTime).format('YYYY-MM-DD HH:mm:ss'),
      behavior: '1',
      mainId: Number(data.id),
      isAudit: 1,
      processInstanceId: data.processInstanceId,
      variables: conditionParams,
      memo: conditionParams?.memo || "2",
    }
    if (Array.isArray(payload?.headId)) {
      payload.headId = payload?.headId[0].id || '';
    }
    if (Array.isArray(payload?.headName)) {
      payload.headName = payload?.headName[0].name || '';
    }
    if (Array.isArray(payload?.electricalName)) {
      payload.electricalName = payload?.electricalName[0].id || '';
    }
    if (Array.isArray(payload?.thermalControlHead)) {
      payload.thermalControlHead = payload?.thermalControlHead[0].id || '';
    }
    if (Array.isArray(payload?.thermalControlCode)) {
      payload.thermalControlCode = payload?.thermalControlCode[0].id || '';
    }
    if (Array.isArray(payload?.machineName)) {
      payload.machineName = payload?.machineName[0].id || '';
    }
    if (Array.isArray(payload?.electricalName)) {
      payload.electricalName = payload?.electricalName[0].id || '';
    }
    if (Array.isArray(payload?.otherName)) {
      payload.otherName = payload?.otherName[0].id || '';
    }

    console.log("🚀 ~ onTryRunRepairFetch ~ payload:", payload)
    try {
      const res = await tryRunAudit(payload)
      if (res?.code === '1') {
        Toast.clear();
        Toast.show({
          icon: 'success',
          content: '已处理',
        });
        history.push(`/twotickets/workmnt/electrical/${type}`);
        return true;
      }
      return false;
    } catch (error) {
      Toast.clear();
      Toast.show({
        icon: 'fail',
        content: '提交失败',
      });
      return false;
    }
  };

  return {
    onSubmit,
    onDelayFetch,
    onExChangeFetch,
    onTicketReturnFetch,
    onTryRunFetch,
    onTryRunRepairFetch,
  };
}; 