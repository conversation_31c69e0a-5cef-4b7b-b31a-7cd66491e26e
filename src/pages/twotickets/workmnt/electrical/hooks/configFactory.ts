/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-21 14:17:54
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-05-28 14:18:53
 * @FilePath: /mobile-yw/src/pages/twotickets/workmnt/electrical/hooks/configFactory.ts
 * @Description: 表单配置工厂函数
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */

import { RefObject } from 'react';
import { DatePickerRef } from 'antd-mobile';
import dayjs from 'dayjs';
import { 
  FormItemConfig, 
  PickerConfig, 
  UserPickerConfig 
} from './types';
import { 
  DEFAULT_STYLES, 
  PLACEHOLDERS, 
  DEFAULT_RULES, 
  LAYOUTS,
  OPINION_DEFAULTS 
} from './constants';

/**
 * 创建审核意见表单配置
 */
export const createOpinionForm = (
  pickerLabel: string, 
  name: string = OPINION_DEFAULTS.defaultName, 
  placeholder: string = OPINION_DEFAULTS.defaultPlaceholder
): FormItemConfig[] => [
  {
    strategyType: 'textarea',
    label: pickerLabel,
    name,
    placeholder,
    strategyProps: {
      placeholder: OPINION_DEFAULTS.defaultPlaceholder,
    },
  }
];

/**
 * 创建时间 + 审核意见表单配置
 */
export const createTimeAndOpinionForm = (pickerLabel: string): FormItemConfig[][] => [[
  {
    strategyType: 'datePicker',
    required: true,
    rules: DEFAULT_RULES.required('请选择时间', 'date'),
    label: pickerLabel,
    name: 'accessTime',
  },
  ...createOpinionForm(OPINION_DEFAULTS.auditLabel)
]];

/**
 * 创建时间范围 + 审核意见表单配置
 */
export const createRangeTimeAndOpinionForm = (pickerLabel: string): FormItemConfig[][] => [[
  {
    strategyType: 'datePickerRange',
    required: true,
    rules: DEFAULT_RULES.required('请选择时间', 'array'),
    label: pickerLabel,
    name: 'approveTime',
  },
  ...createOpinionForm(OPINION_DEFAULTS.auditLabel)
]];

/**
 * 创建日期选择器配置
 */
export const createDatePickerConfig = (): any => ({
  trigger: 'onConfirm',
  required: true,
  rules: DEFAULT_RULES.required('请选择时间', 'date'),
  strategyProps: {
    placeholder: PLACEHOLDERS.select,
    precision: 'minute',
    style: {
      '--text-align': 'right',
    },
    children: (value: string | number | Date | dayjs.Dayjs | null | undefined) => (
      value ? dayjs(value).format('YYYY/MM/DD HH:mm') : PLACEHOLDERS.select
    ),
  },
  onClick: (e: any, datePickerRef: RefObject<DatePickerRef>) => datePickerRef.current?.open(),
  style: DEFAULT_STYLES.prefixWidth,
});

/**
 * 创建选择器基础配置
 */
export const createPickerConfig = (): PickerConfig => ({
  trigger: 'onConfirm',
  required: true,
  rules: DEFAULT_RULES.required('请选择', 'array'),
  style: DEFAULT_STYLES.prefixWidth,
  onClick: (e: any, datePickerRef: RefObject<DatePickerRef>) => {
    datePickerRef.current?.open();
    console.log(e, datePickerRef, 'e, datePickerRef');
  },
});

/**
 * 创建用户选择器基础配置
 */
export const createUserPickerConfig = (): UserPickerConfig => ({
  required: true,
  rules: DEFAULT_RULES.required('请选择用户', 'array'),
  style: DEFAULT_STYLES.prefixWidth,
  strategyProps: {
    placeholder: PLACEHOLDERS.select,
    layout: LAYOUTS.horizontal,
  },
});

/**
 * 创建针对特定场景的用户选择器配置
 */
export const createCustomUserPickerConfig = (message: string): UserPickerConfig => ({
  ...createUserPickerConfig(),
  rules: DEFAULT_RULES.required(message, 'array'),
});

/**
 * 创建工作负责人用户选择器配置
 */
export const createWorkHeadUserPickerConfig = (label: string = '工作负责人', name: string = 'headId'): FormItemConfig => ({
  strategyType: 'userPicker',
  label,
  name,
  layout: LAYOUTS.horizontal,
  ...createCustomUserPickerConfig(`请选择${label}`),
  strategyProps: {
    multiple: false,
    customMode: false,
    layout: LAYOUTS.horizontal,
    placeholder: PLACEHOLDERS.select,
  },
});

/**
 * 创建部门选择器配置
 */
export const createOrgPickerConfig = (label: string = '部门', name: string = 'orgId'): FormItemConfig => ({
  strategyType: 'orgPicker',
  label,
  name,
  layout: LAYOUTS.horizontal,
  ...createPickerConfig(),
  strategyProps: {
    placeholder: PLACEHOLDERS.select,
  },
});

/**
 * 创建班组选择器配置
 */
export const createGroupPickerConfig = (label: string = '班组', name: string = 'teamsName'): FormItemConfig => ({
  strategyType: 'groupPicker',
  label,
  name,
  layout: LAYOUTS.horizontal,
  arrow: true,
  strategyProps: {
    placeholder: PLACEHOLDERS.select,
  },
  style: DEFAULT_STYLES.prefixWidth,
});

/**
 * 创建执行状态配置
 */
export const createExecutionStatusConfig = (
  title: string = '拆除安全措施',
  name: string = 'workSafeMeasureTickets'
): FormItemConfig => ({
  strategyType: 'executionStatus',
  label: undefined,
  name,
  layout: LAYOUTS.horizontal,
  strategyProps: {
    title,
    placeholder: PLACEHOLDERS.select,
    mode: "actionSheet",
  },
  style: DEFAULT_STYLES.prefixWidth,
});

/**
 * 创建变更表单配置
 */
export const createChangeFormConfig = (): FormItemConfig[][] => [[
  createWorkHeadUserPickerConfig('变更后负责人', 'nowHeadId'),
  {
    strategyType: 'datePicker',
    label: '变更时间',
    name: 'changeTime',
    required: true,
    rules: DEFAULT_RULES.required('请选择变更时间', 'date'),
  },
  ...createOpinionForm("变动情况说明", "situationDescription")
]]; 