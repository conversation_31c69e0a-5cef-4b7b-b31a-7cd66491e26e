/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-21 14:17:54
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-05-28 14:18:53
 * @FilePath: /mobile-yw/src/pages/twotickets/workmnt/electrical/hooks/constants.ts
 * @Description: 表单策略常量定义
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */

// 默认样式配置
export const DEFAULT_STYLES = {
  prefixWidth: { '--prefix-width': '10em' },
} as const;

// 默认占位符
export const PLACEHOLDERS = {
  select: '请选择',
  confirm: '确认本工作上述各项内容',
} as const;

// 默认验证规则
export const DEFAULT_RULES = {
  required: (message: string, type: string = 'string') => [
    { required: true, message, type, warningOnly: false }
  ],
} as const;

// 单选选项
export const RADIO_OPTIONS = {
  qualified: [
    { label: '合格', value: '合格' },
    { label: '不合格', value: '不合格' },
  ],
} as const;

// 布局配置
export const LAYOUTS = {
  horizontal: 'horizontal',
  vertical: 'vertical',
} as const;

// 状态相关常量
export const STATUS_RANGES = {
  cancelStatusStart: 2,
  cancelStatusEnd: 18,
  backStatusStart: 2,
  backStatusEnd: 18,
  rejectStatusStart: 2,
  rejectStatusEnd: 18,
} as const;

// 审核意见相关
export const OPINION_DEFAULTS = {
  defaultPlaceholder: '确认本工作上述各项内容',
  defaultName: 'opinion',
  auditLabel: '审核意见',
} as const; 