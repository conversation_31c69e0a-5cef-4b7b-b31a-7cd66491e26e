/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-21 14:17:54
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-05-28 14:18:53
 * @FilePath: /mobile-yw/src/pages/twotickets/workmnt/electrical/hooks/strategyRegistry.ts
 * @Description: 策略组件注册
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */

import { DatePicker, Input, TextArea } from 'antd-mobile';
import SignatureUserPicker from '../../components/dynamic-form/items/signature-selection/signature-selection.component';
import { strategyFactory } from '../../components/dynamic-form/strategy-factory';
import OrgPicker from '../../components/org-picker/org-picker.component';
import UserPicker from '../../components/user-picker/user-picker.component';
import GroupPicker from '../components/group-picker';
import ExecutionStatus from '../components/execution-status';
import FinalExplanation from '../components/final-explanation';
import RadioGroup from '../components/radio-group';
import TimeRangePicker from '../components/time-range-picker/time-range-picker.component';
import { RADIO_OPTIONS } from './constants';

/**
 * 注册所有策略组件
 */
export const registerAllStrategies = (): void => {
  // 输入框
  strategyFactory.registerStrategy('input', {
    component: Input,
  });

  // 单选选择器组
  strategyFactory.registerStrategy('radioGroup', {
    component: RadioGroup,
    defaultProps: {
      formItemProps: true,
      direction: 'horizontal',
      options: RADIO_OPTIONS.qualified,
    }
  });

  // 签名选择器
  strategyFactory.registerStrategy('signature', {
    component: SignatureUserPicker,
  });

  // 部门选择器
  strategyFactory.registerStrategy('orgPicker', {
    component: OrgPicker,
    defaultProps: {
      wrapFormItem: false,
      showUsers: false,
      multiple: false,
    }
  });

  // 用户选择器
  strategyFactory.registerStrategy('userPicker', {
    component: UserPicker,
    defaultProps: {
      wrapFormItem: false,
      showUsers: true,
      deduplicateUsers: true,
    }
  });

  // 单选选择器时间
  strategyFactory.registerStrategy('datePicker', {
    component: DatePicker,
  });

  // 终结说明
  strategyFactory.registerStrategy('finalExplanation', {
    component: FinalExplanation,
    defaultProps: {
      wrapFormItem: false,
    },
    valueProp: 'value',
    trigger: 'onChange',
  });

  // 区间选择器时间
  strategyFactory.registerStrategy('datePickerRange', {
    component: TimeRangePicker,
    defaultProps: {
      initialMode: 'second',
      isHideHeader: true,
    },
  });

  // 多行文本
  strategyFactory.registerStrategy('textarea', {
    component: TextArea,
  });

  // 执行情况
  strategyFactory.registerStrategy('executionStatus', {
    component: ExecutionStatus,
  });

  // 班组选择器
  strategyFactory.registerStrategy('groupPicker', {
    component: GroupPicker,
    defaultProps: {
      multiple: true,
      wrapFormItem: false,
    }
  });
}; 