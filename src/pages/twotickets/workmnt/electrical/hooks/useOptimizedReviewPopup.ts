import { useMemo, useCallback } from 'react';

interface UseOptimizedReviewPopupProps {
  visible: boolean;
  setVisible: (visible: boolean) => void;
  variables: any;
  activeName: string;
  status: string;
  detailData: any;
  onSubmit: (values: any, variables: any, activeName: string) => void;
}

/**
 * 优化的 ReviewPopup hook
 * 通过稳定的引用和缓存来减少不必要的重新渲染
 */
export const useOptimizedReviewPopup = ({
  visible,
  setVisible,
  variables,
  activeName,
  status,
  detailData,
  onSubmit
}: UseOptimizedReviewPopupProps) => {
  
  // 使用 useMemo 创建稳定的 props 对象
  const reviewPopupProps = useMemo(() => ({
    visible,
    setVisible,
    variables,
    activeName,
    status,
    detailData,
    onSubmit
  }), [visible, setVisible, variables, activeName, status, detailData, onSubmit]);

  // 使用 useCallback 创建稳定的关闭处理函数
  const handleClose = useCallback(() => {
    setVisible(false);
  }, [setVisible]);

  // 检查是否应该渲染组件
  const shouldRender = useMemo(() => {
    return visible || status; // 当可见或有状态时才渲染
  }, [visible, status]);

  return {
    reviewPopupProps,
    handleClose,
    shouldRender
  };
}; 