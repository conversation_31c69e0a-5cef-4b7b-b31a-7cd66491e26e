/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-21 14:17:54
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-05-28 18:02:05
 * @FilePath: /mobile-yw/src/pages/twotickets/workmnt/electrical/hooks/useRegisterStrategey_old.ts
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */
import { DatePicker, DatePickerRef, Input, TextArea } from 'antd-mobile';
import SignatureUserPicker from '../../components/dynamic-form/items/signature-selection/signature-selection.component';
import { strategyFactory } from '../../components/dynamic-form/strategy-factory';
import OrgPicker from '../../components/org-picker/org-picker.component';
import UserPicker from '../../components/user-picker/user-picker.component';
import GroupPicker from '../components/group-picker';
import ExecutionStatus from '../components/execution-status';
import FinalExplanation from '../components/final-explanation';
import RadioGroup from '../components/radio-group';
import TimeRangePicker from '../components/time-range-picker/time-range-picker.component';
import { RefObject } from 'react';
import dayjs from 'dayjs';


const useRegisterStrategy = () => {

  // 输入框
  strategyFactory.registerStrategy('input', {
    component: Input,
  });

  // 单选选择器组
  strategyFactory.registerStrategy('radioGroup', {
    component: RadioGroup,
    defaultProps: {
      formItemProps: true,
      direction: 'horizontal',
      options: [
        { label: '合格', value: '合格' },
        { label: '不合格', value: '不合格' },
      ]
    }
  });

  // 签名选择器
  strategyFactory.registerStrategy('signature', {
    component: SignatureUserPicker,
  });

  // 部门选择器
  strategyFactory.registerStrategy('orgPicker', {
    component: OrgPicker,
    defaultProps: {
      wrapFormItem: false,
      showUsers: false,
      multiple: false,
    }
  });

  // 用户选择器
  strategyFactory.registerStrategy('userPicker', {
    component: UserPicker,
    defaultProps: {
      wrapFormItem: false,
      showUsers: true,
      deduplicateUsers: true,
    }
  });

  // 单选选择器时间
  strategyFactory.registerStrategy('datePicker', {
    component: DatePicker,
  });

  // 终结说明
  strategyFactory.registerStrategy('finalExplanation', {
    component: FinalExplanation,
  });

  // 区间选择器时间
  strategyFactory.registerStrategy('datePickerRange', {
    component: TimeRangePicker,
    defaultProps: {
      initialMode: 'second',
      isHideHeader: true,
    },
  });

  // 多行文本
  strategyFactory.registerStrategy('textarea', {
    component: TextArea,
  });

  // 执行情况
  strategyFactory.registerStrategy('executionStatus', {
    component: ExecutionStatus,
  });

  // 班组选择器
  strategyFactory.registerStrategy('groupPicker', {
    component: GroupPicker,
    defaultProps: {
      multiple: true,
      wrapFormItem: false,
    }
  });

  // 单独一个的审核意见
  const opinionForm = (pickerLabel: string, name: string = 'opinion', placeholder: string = '确认本工作上述各项内容') => [
    {
      strategyType: 'textarea',
      label: pickerLabel,
      name,
      placeholder,
      strategyProps: {
        placeholder: '确认本工作上述各项内容',
      },
    }
  ];

  // 时间 + 审核意见
  const timeAndOpinionForm = (pickerLabel: string) => [[
    {
      strategyType: 'datePicker',
      required: true,
      rules: [{ required: true, message: '请选择时间', type: 'date', warningOnly: false }],
      label: pickerLabel,
      name: 'accessTime',
    },
    ...opinionForm("审核意见")
  ]];

  // 时间范围 + 审核意见
  const rangeTimeAndOpinionForm = (pickerLabel: string) => [[
    {
      strategyType: 'datePickerRange',
      required: true,
      rules: [{ required: true, message: '请选择时间', type: 'array', warningOnly: false }],
      label: pickerLabel,
      name: 'approveTime',
    },
    ...opinionForm("审核意见")
  ]];

  // 时间选择器配置
  const datePickerOtherConfig: any = {
    trigger: 'onConfirm',
    required: true,
    rules: [{ required: true, message: '请选择时间', type: 'date', warningOnly: false }],
    strategyProps: {
      placeholder: '请选择',
      precision: 'minute',
      style: {
        '--text-align': 'right',
      },
      children: (value: string | number | Date | dayjs.Dayjs | null | undefined) => (
        value ? dayjs(value).format('YYYY/MM/DD HH:mm') : '请选择'
      ),
    },
    onClick: (e: any, datePickerRef: RefObject<DatePickerRef>) => datePickerRef.current?.open(),
    style: { '--prefix-width': '10em' },
  }

  // 选择器配置
  const pickerOtherConfig = {
    trigger: 'onConfirm',
    required: true,
    rules: [{ required: true, message: '请选择', type: 'array', warningOnly: false }],
    style: { '--prefix-width': '10em' },
    onClick: (e: any, datePickerRef: RefObject<DatePickerRef>) => {
      datePickerRef.current?.open()
      console.log(e, datePickerRef, 'e, datePickerRef');
    },
  }

  // 用户选择器配置
  const userPickerConfig = {
    required: true,
    rules: [{ required: true, message: '请选择用户', type: 'array', warningOnly: false }],
    style: { '--prefix-width': '10em' },
    strategyProps: {
      placeholder: '请选择',
      layout: 'horizontal',
    },
  }

  // 针对不同场景的用户选择器配置
  const createUserPickerConfig = (message: string) => ({
    ...userPickerConfig,
    rules: [{ required: true, message, type: 'array', warningOnly: false }],
  });

  // 变更后负责人 + 变更时间 + 变动情况说明
  const changeFormConfit = [[
    {
      strategyType: 'userPicker',
      label: '变更后负责人',
      name: 'nowHeadId',
      ...createUserPickerConfig('请选择变更后负责人'),
      strategyProps: {
        multiple: false,
        customMode: false,
        layout: 'horizontal',
        placeholder: '请选择',
      },
    },
    {
      strategyType: 'datePicker',
      label: '变更时间',
      name: 'changeTime',
      required: true,
      rules: [{ required: true, message: '请选择变更时间', type: 'date', warningOnly: false }],
    },
    ...opinionForm("变动情况说明", "situationDescription")
  ]];

  // 检修设备试运行申请
  const tryRunFormConfig = [[
    ...opinionForm("工作地点", "workLocation"),
    ...opinionForm("工作内容", "workTask"),
    {
      strategyType: 'userPicker',
      label: '工作负责人',
      name: 'headId',
      layout: 'horizontal',
      ...createUserPickerConfig('请选择工作负责人'),
    },
    {
      strategyType: 'orgPicker',
      label: '部门',
      name: 'orgId',
      layout: 'horizontal',
      ...pickerOtherConfig,
      strategyProps: {
        placeholder: '请选择',
      },
    },
    {
      strategyType: 'groupPicker',
      label: '班组',
      name: 'teamsNmae',
      layout: 'horizontal',
      arrow: true,
      strategyProps: {
        placeholder: '请选择',
      },
      style: { '--prefix-width': '10em' },
    }], [
    ...opinionForm("热控工作票编号", "thermalControlCode"),
    {
      strategyType: 'userPicker',
      label: '工作负责人',
      name: 'thermalControlHead',
      layout: 'horizontal',
      ...createUserPickerConfig('请选择工作负责人'),
    }], [
    ...opinionForm("电气工作票编号", "electricalCode"),
    {
      strategyType: 'userPicker',
      label: '工作负责人',
      name: 'electricalName',
      layout: 'horizontal',
      ...createUserPickerConfig('请选择工作负责人'),
    }], [
    ...opinionForm("机械工作票编号", "machineCode"),
    {
      strategyType: 'userPicker',
      label: '工作负责人',
      name: 'machineName',
      layout: 'horizontal',
      ...createUserPickerConfig('请选择工作负责人'),
    }], [
    ...opinionForm("其他", "otherCode"),
    {
      strategyType: 'userPicker',
      label: '工作负责人',
      name: 'otherName',
      layout: 'horizontal',
      ...createUserPickerConfig('请选择工作负责人'),
    }], [{
      strategyType: 'executionStatus',
      label: undefined,
      name: 'workSafeMeasureTickets',
      layout: 'horizontal',
      strategyProps: {
        title: '拆除安全措施',
        placeholder: '请选择',
        mode: "actionSheet",
      },
      style: { '--prefix-width': '10em' },
    }]];

  // 检修设备试运行申请
  const tryRunRepairFormConfig = [[
    ...opinionForm("工作地点", "workLocation"),
    ...opinionForm("工作内容", "workTask"),
    {
      strategyType: 'userPicker',
      label: '工作负责人',
      name: 'headId',
      layout: 'horizontal',
      ...createUserPickerConfig('请选择工作负责人'),
    },
    {
      strategyType: 'orgPicker',
      label: '部门',
      name: 'orgId',
      layout: 'horizontal',
      ...pickerOtherConfig,
      strategyProps: {
        placeholder: '请选择',
      },
    },
    {
      strategyType: 'groupPicker',
      label: '班组',
      name: 'teamsNmae',
      layout: 'horizontal',
      arrow: true,
      strategyProps: {
        placeholder: '请选择',
      },
      style: { '--prefix-width': '10em' },
    }], [{
      strategyType: 'executionStatus',
      label: undefined,
      name: 'workFlowTryRunSafe',
      layout: 'horizontal',
      strategyProps: {
        title: '拆除安全措施',
        placeholder: '请选择',
        mode: "actionSheet",
      },
      style: { '--prefix-width': '10em' },
    }]];

  const form15ApproveConfig = [[
    {
      strategyType: 'datePicker',
      label: '结票时间',
      name: 'endTime',
      required: true,
    },
    {
      strategyType: 'finalExplanation',
      label: '终结说明',
      name: 'endDescribe',
      required: true,
    },
    {
      strategyType: 'userPicker',
      label: '指定专责负责人',
      name: 'appointHeadName',
      layout: 'horizontal',
      ...createUserPickerConfig('请选择指定专责负责人'),
      strategyProps: {
        placeholder: '请选择',
        multiple: false,
      },
    },
    ...opinionForm("地点及具体工作", "workMatter"),
    ...opinionForm("其他事项", "appointWork"),
  ]];

  const form17ApproveConfig = [[
    {
      strategyType: 'radioGroup',
      label: '审核结果',
      name: 'auditResult',
      childElementPosition: 'right',
      layout: 'horizontal',
      required: true,
      rules: [{ required: true, message: '请选择审核结果', type: 'string', warningOnly: false }],
    },
    ...opinionForm("审核意见")
  ]];

  // 独一个审核意见框
  const opinionTextareaConfig = [opinionForm('审核意见')];

  const form10Approve3Config = [[
    {
      strategyType: 'datePicker',
      label: '有效期延长至',
      required: true,
      name: 'newEndTime',
      rules: [{ required: true, message: '请选择有效期延长至', type: 'date', warningOnly: false }],
    },
    ...opinionForm("审核意见")
  ]];

  const tryRunRepair11FormConfig = [
    [
      ...opinionForm("工作地点", "workLocation"),
      ...opinionForm("工作内容", "workTask"),
      {
        strategyType: 'userPicker',
        label: '工作负责人',
        layout: "horizontal",
        name: 'headId',
        ...createUserPickerConfig('请选择工作负责人'),
      },
      {
        strategyType: "orgPicker",
        label: "部门",
        name: "orgId",
        layout: "horizontal",
        ...pickerOtherConfig,
        strategyProps: {
          placeholder: '请选择',
        },
      },

      {
        strategyType: 'groupPicker',
        label: '班组',
        name: 'teamsName',
        layout: 'horizontal',
        arrow: true,
        strategyProps: {
          placeholder: '请选择',
        },
        style: { '--prefix-width': '10em' },
      }
    ],
    [
      {
        strategyType: 'executionStatus',
        label: undefined,
        name: 'workFlowTryRunSafe',
        layout: 'horizontal',
        strategyProps: {
          title: '拆除安全措施',
          placeholder: '请选择',
          mode: "actionSheet",
        },
        style: { '--prefix-width': '10em' },
      }
    ],
    [
      {
        strategyType: 'datePicker',
        label: '恢复时间',
        name: 'tryTime',
        layout: 'horizontal',
        strategyProps: {
        },
      },
      ...opinionForm("审核意见", "opinion")
    ]
  ];

  const tryRun11FormConfig = [
    [
      ...opinionForm("工作地点", "workLocation"),
      ...opinionForm("工作内容", "workTask"),

      {
        strategyType: 'userPicker',
        label: '工作负责人',
        layout: "horizontal",
        name: 'headId',
        ...createUserPickerConfig('请选择工作负责人'),
      },
      {
        strategyType: "orgPicker",
        label: "部门",
        name: "orgId",
        layout: "horizontal",
        ...pickerOtherConfig,
        strategyProps: {
          placeholder: '请选择',
        },
      },
      {
        strategyType: 'groupPicker',
        label: '班组',
        name: 'teamsNmae',
        layout: 'horizontal',
        arrow: true,
        strategyProps: {
          placeholder: '请选择',
        },
        style: { '--prefix-width': '10em' },
      }
    ],
    [
      ...opinionForm("热控工作票编号", "thermalControlCode"),

      {
        strategyType: 'userPicker',
        name: 'thermalControlHead',
        label: '工作负责人',
        layout: "horizontal",
        ...createUserPickerConfig('请选择工作负责人'),
      },
    ],
    [
      ...opinionForm("电气工作票编号", "electricalCode"),
      {
        strategyType: 'userPicker',
        name: 'electricalName',
        label: '工作负责人',
        layout: "horizontal",
        ...createUserPickerConfig('请选择工作负责人'),
      },
    ],
    [
      ...opinionForm("机械工作票编号", "machineCode"),
      {
        strategyType: 'userPicker',
        name: 'machineName',
        label: '工作负责人',
        layout: "horizontal",
        ...createUserPickerConfig('请选择工作负责人'),
      },
    ],
    [
      ...opinionForm("其他", "otherCode"),
      {
        strategyType: 'userPicker',
        name: 'otherName',
        label: '工作负责人',
        layout: "horizontal",
        ...createUserPickerConfig('请选择工作负责人'),
      },
    ], [{
      strategyType: 'executionStatus',
      label: undefined,
      name: 'workFlowTryRunSafe',
      layout: 'horizontal',
      strategyProps: {
        title: '拆除安全措施',
        placeholder: '请选择',
        mode: "actionSheet",
      },
      style: { '--prefix-width': '10em' },
    }]
  ];

  const statusFormList: any = {
    "2_approve": opinionTextareaConfig, // 工作票签发
    "3_approve": opinionTextareaConfig, // 业主签发人确认
    "4_approve": [], // 工作负责人确认
    "5_approve": timeAndOpinionForm('收到工作票时间'), // 值班负责人签字确认
    "6_approve": rangeTimeAndOpinionForm('检修作业批准时间'), // 值长审核
    "7_approve": timeAndOpinionForm('许可时间'), // 工作票许可审批
    "8": [], // 工作负责人接收确认

    // 子流程 【第 1 轮】
    "delay": [opinionForm('延期理由')], // 工作延期
    "change": changeFormConfit, // 工作负责人变更
    "break": [opinionForm('备注', 'remark')], // 工作票交回
    "[tryRun-2]": tryRunFormConfig, // 检修设备试运行申请
    "[tryRunRepair-2]": tryRunRepairFormConfig, // 检修设备试运行后恢复工作申请


    // 延期第二步
    "[delay-2]": opinionTextareaConfig, // 工作延期 通过不通过
    // 第三步
    "[delay-3]": form10Approve3Config, // 工作延期 
    "[delay-3]_reject": opinionTextareaConfig, // 工作延期 通过不通过

    // 子流程 【第 2 轮】
    "[delay-4]": [opinionForm('延期理由')], // 该禁用



    // 工作负责人变更 第二步
    "[change-2]": opinionTextareaConfig,

    // 工作负责人变更 第三步
    "[change-3]": opinionTextareaConfig,

    // 工作负责人变更 第二轮
    "[change-4]": changeFormConfit,

    // 工作票交回
    "[ticketReturn-2]": [opinionForm('备注', 'remark')],
    "[ticketReturn-1]": opinionTextareaConfig,
    "[ticketReturn-3]": opinionTextareaConfig,
    "ticketReturn": opinionTextareaConfig,

    // 检修设备试运行申请
    "[tryRun-1]": opinionTextareaConfig,
    "[tryRun-3]": opinionTextareaConfig,

    // 检修设备试运行后恢复工作申请
    "[tryRunRepair-1]": opinionTextareaConfig,
    "[tryRunRepair-3]": opinionTextareaConfig,

    "9_approve": opinionTextareaConfig, // 工作票执行情况填报 - 终结
    "10_approve": opinionTextareaConfig, // 工作票执行情况填报 - 终结
    "11_approve": opinionTextareaConfig, // 工作票执行情况填报 - 终结
    "12_approve": opinionTextareaConfig, // 工作票执行情况填报 - 终结

    "13_approve": opinionTextareaConfig, // 工作票执行确认审核（工作许可人）
    "14": [],
    "15_approve": form15ApproveConfig, // 工作票执行确认审核（工作负责人）
    "17_approve": form17ApproveConfig, // 工作票执行确认审核（工作负责人）
  }

  for (let i = 2; i <= 18; i++) {
    statusFormList[`${i}_cancel`] = [opinionForm('作废原因')];
    statusFormList[`${i}_back`] = [opinionForm('退回原因')];
    statusFormList[`${i}_reject`] = [opinionForm('驳回原因')];
  }

  return {
    statusFormList,
    datePickerOtherConfig
  }
};

export default useRegisterStrategy;