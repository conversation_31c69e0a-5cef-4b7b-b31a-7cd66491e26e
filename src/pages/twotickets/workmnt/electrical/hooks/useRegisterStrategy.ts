/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-21 14:17:54
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-05-28 17:59:29
 * @FilePath: /mobile-yw/src/pages/twotickets/workmnt/electrical/hooks/useRegisterStrategy.ts
 * @Description: 表单策略注册和配置管理
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */

import { useMemo } from 'react';
import { registerAllStrategies } from './strategyRegistry';
import { createDatePickerConfig } from './configFactory';
import { createBaseStatusFormList } from './formConfigs';
import { StatusFormList, FormConfig } from './types';
import { STATUS_RANGES } from './constants';
import { createOpinionForm } from './configFactory';
import { STATUS_TRANSITION_RULES } from '../constants/status';

/**
 * 操作类型定义
 */
type OperationType = 'cancel' | 'back' | 'reject';

/**
 * 获取状态配置项
 * @param operationType 操作类型
 * @returns 对应的表单配置
 */
const getStatusFormConfig = (operationType: OperationType): FormConfig => {
  const labelMap: Record<OperationType, string> = {
    cancel: '作废原因',
    back: '退回原因',
    reject: '驳回原因'
  };
  
  return [createOpinionForm(labelMap[operationType])];
};

/**
 * 生成状态相关的配置（取消、退回、驳回）
 */
const generateStatusConfigs = (statusFormList: StatusFormList): void => {
  // 获取状态范围
  const { cancelStatusStart, cancelStatusEnd, backStatusStart, backStatusEnd, rejectStatusStart, rejectStatusEnd } = STATUS_RANGES;
  
  // 定义操作类型对应的范围
  const operationRanges = {
    cancel: { start: cancelStatusStart, end: cancelStatusEnd },
    back: { start: backStatusStart, end: backStatusEnd },
    reject: { start: rejectStatusStart, end: rejectStatusEnd }
  };
  
  // 遍历所有状态键
  Object.keys(STATUS_TRANSITION_RULES).forEach(statusKey => {
    const statusNumber = parseInt(statusKey, 10);
    
    // 为每个操作类型生成对应配置
    Object.entries(operationRanges).forEach(([operation, range]) => {
      const { start, end } = range;
      
      // 如果状态在范围内，生成对应配置
      if (statusNumber >= start && statusNumber <= end) {
        const opType = operation as OperationType;
        const statusRules = STATUS_TRANSITION_RULES[statusKey];
        
        // 确保存在对应的操作
        if ((opType === 'cancel' && statusRules.cancel) || 
            (opType === 'back' && statusRules.back) || 
            (opType === 'reject' && statusRules.approve)) {
          
          if (opType === 'reject') {
            // 对于驳回操作，使用approve值但替换为reject
            const rejectKey = statusRules.approve.replace('approve', 'reject');
            statusFormList[rejectKey] = getStatusFormConfig(opType);
          } else {
            // 对于其他操作，直接使用对应的键
            const operationKey = statusRules[opType];
            if (operationKey) {
              statusFormList[operationKey] = getStatusFormConfig(opType);
            }
          }
        }
      }
    });
  });
};

/**
 * 表单策略注册和配置管理 Hook
 */
const useRegisterStrategy = () => {
  // 使用 useMemo 避免重复计算
  const { statusFormList, datePickerOtherConfig } = useMemo(() => {
    // 注册所有策略组件
    registerAllStrategies();
    
    // 创建基础状态表单列表
    const statusFormList = createBaseStatusFormList();
    
    // 生成状态相关配置
    generateStatusConfigs(statusFormList);
    
    // 创建日期选择器配置
    const datePickerOtherConfig = createDatePickerConfig();

    return {
      statusFormList,
      datePickerOtherConfig
    };
  }, []);

  return {
    statusFormList,
    datePickerOtherConfig
  };
};

export default useRegisterStrategy;