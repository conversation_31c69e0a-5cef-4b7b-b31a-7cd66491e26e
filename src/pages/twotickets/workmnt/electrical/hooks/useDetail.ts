/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-07 15:01:52
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-05-21 16:48:10
 * @FilePath: /mobile-yw/src/pages/twotickets/workmnt/electrical/hooks/useDetail.ts
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */

import { useParams, history } from 'umi';
import { getTicketTypeAndTitle } from '@/constant';
import { useRequest, useThrottleFn } from 'ahooks';
import { getWorkBaseDetail } from '@/services/twotickets/workmnt/workmnt';

type WorkmntType = 'electrical' | 'hydraulic' | 'mechanical' | 'hotWork';

const TAB_HEIGHT = 42;

const useDetail = (workmntType: WorkmntType) => {
  const { type, id, pageType } = useParams();


  const { title, ticketType } = getTicketTypeAndTitle(workmntType, type);

  const getDetail = () => {
    return getWorkBaseDetail({
      id,
      ticketType
    }).then((res) => res.data)
  }

  const { data, loading } = useRequest(getDetail)

  return { data, loading, ticketType, title, pageType, type }
}

export default useDetail;