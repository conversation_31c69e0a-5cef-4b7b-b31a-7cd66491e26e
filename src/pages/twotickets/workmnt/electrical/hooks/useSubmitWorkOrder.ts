import dayjs from 'dayjs';
import { useRef } from 'react';
import { history } from 'umi';
import { Toast } from 'antd-mobile';
import type { ToastHandler } from 'antd-mobile/es/components/toast';
import { delayAudit, ticketReturn, tryRunAudit } from '@/services/twotickets/workmnt/electrical';
import { headerExchangeAudit, updateWorkBase } from '@/services/twotickets/workmnt/workmnt';
import { SHOW_TIME_FORMAT } from '@/utils/constant';

// ==================== 类型定义 ====================
interface SubmitContext {
  extra: any;
  variables: any;
  code?: string;
}

interface UserSelection {
  id: string;
  name: string;
}

interface FinalExplanationData {
  groundWireCode: string;
  groundWireNum: string;
  knifeSwitch: string;
  knifeSwitchNum: string;
}

interface UseSubmitWorkOrderProps {
  data: any;
  type: string;
  safetyMeasureRef: React.RefObject<any>;
  securityTicketContentRef: React.RefObject<any>;
  workOrderExecutionRef: React.RefObject<any>;
}

// ==================== 常量定义 ====================
const STATUS_CONSTANTS = {
  PREPARING: '1',
  WORK_HEAD_CONFIRM: '4',
  DUTY_HEAD_CONFIRM: '5',
  DUTY_MANAGER_APPROVE: '6',
  PERMIT_APPROVE: '7',
  WORK_HEAD_RECEIVE: '8',
  EXECUTION_REPORT: '9',
  EXECUTION_REPORT_2: '10',
  EXECUTION_REPORT_3: '11',
  EXECUTION_REPORT_4: '12',
  WORK_TERMINATION: '15',
  AUDIT_RESULT: '17',
} as const;

const SUB_PROCESS_TYPES = {
  DELAY: 'delay',
  CHANGE: 'change',
  BREAK: 'break',
  TRY_RUN: 'tryRun',
  TRY_RUN_REPAIR: 'tryRunRepair',
} as const;

const OTHER_USER_NAME = 'header';
const TOAST_DELAY = 600;

// ==================== 工具函数 ====================

/**
 * 生成终结说明显示文本
 */
const generateDisplayText = (
  data: FinalExplanationData,
  endTime: string
): string => {
  const { groundWireCode, groundWireNum, knifeSwitch, knifeSwitchNum } = data;
  return `1.全部工作于 ${endTime} 结束，设备及安全措施已恢复至开工前状态，工作人员全部撤离，材料工具已清理完毕。
  2.临时遮拦、标示牌已拆除，常设遮栏已恢复。未拆除或未拉开的接地线编号${groundWireCode}等共（${groundWireNum}）组，接地刀闸（小车）${knifeSwitch}共（${knifeSwitchNum}）副（台），已汇报值班负责人。`;
};

/**
 * 格式化日期时间
 */
const formatDateTime = (date: Date | string): string => {
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss');
};

/**
 * 格式化中文日期时间
 */
const formatChineseDateTime = (date: Date | string): string => {
  return dayjs(date).format('YYYY年MM月DD日HH时mm分');
};

/**
 * 提取数组中的用户信息
 */
const extractUserInfo = (userArray: UserSelection[] | any, field: 'id' | 'name' = 'id'): string => {
  if (Array.isArray(userArray) && userArray.length > 0) {
    return userArray[0][field] || '';
  }
  return '';
};

/**
 * 处理数组字段到字符串的转换
 */
const normalizeArrayFields = (payload: any): void => {
  const arrayFields = [
    'headId', 'headName', 'electricalName', 'thermalControlHead',
    'thermalControlCode', 'machineName', 'otherName'
  ];

  arrayFields.forEach(field => {
    if (Array.isArray(payload[field])) {
      const fieldType = field.includes('Name') ? 'name' : 'id';
      payload[field] = extractUserInfo(payload[field], fieldType as 'id' | 'name');
    }
  });
};

/**
 * 格式化表单中的日期字段
 */
const formatDateFields = (values: any): any => {
  const formatted = { ...values };
  for (const key in formatted) {
    if (formatted[key] instanceof Date) {
      formatted[key] = formatDateTime(formatted[key]);
    }
  }
  return formatted;
};

/**
 * 显示加载Toast
 */
const showLoadingToast = (): ToastHandler => {
  return Toast.show({
    duration: 0,
    position: 'top',
    icon: 'loading',
    content: '加载中…',
    maskClickable: false,
  });
};

/**
 * 显示结果Toast并跳转
 */
const showResultAndRedirect = (success: boolean, message: string, redirectPath: string): void => {
  setTimeout(() => {
    Toast.clear();
    Toast.show({
      icon: success ? 'success' : 'fail',
      content: message,
    });
    if (success) {
      history.push(redirectPath);
    }
  }, TOAST_DELAY);
};

// ==================== 状态处理器 ====================

/**
 * 状态处理器类
 */
class StatusProcessor {
  private static instance: StatusProcessor;

  static getInstance(): StatusProcessor {
    if (!StatusProcessor.instance) {
      StatusProcessor.instance = new StatusProcessor();
    }
    return StatusProcessor.instance;
  }

  /**
   * 处理准备中状态
   */
  processPreparing(payload: any): void {
    // 如果是保存操作（isAudit为0），behavior 应该为 false
    if (payload.isAudit === 0) {
      payload.behavior = false;
    } else {
      payload.behavior = payload?.behavior ? "true" : "false";
    }
  }

  /**
   * 处理工作负责人确认状态
   */
  processWorkHeadConfirm(payload: any): void {
    payload.behavior = "true";
    payload.opinion = payload.opinion;
  }

  /**
   * 处理值班负责人确认状态
   */
  processDutyHeadConfirm(payload: any): void {
    payload.workFlowConfirm = {
      accessTime: formatDateTime(payload.accessTime)
    };
    delete payload.accessTime;
  }

  /**
   * 处理值长批准状态
   */
  processDutyManagerApprove(payload: any): void {
    if (payload?.variables?.pass === 'true') {
      payload.workFlowRatify = {
        approveBeginTime: payload?.approveTime?.[0] ? formatDateTime(payload.approveTime[0]) : '',
        approveEndTime: payload?.approveTime?.[1] ? formatDateTime(payload.approveTime[1]) : '',
        opinion: payload?.opinion,
      };
      delete payload.approveTime;
    }
  }

  /**
   * 处理许可审核状态
   */
  processPermitApprove(payload: any): void {
    payload.workFlowLicense = {
      allowBeginTime: payload?.allowBeginTime ? formatDateTime(payload.allowBeginTime) : '',
      opinion: payload?.reviewData,
    };
    delete payload.allowBeginTime;
  }

  /**
   * 处理工作负责人接收状态
   */
  processWorkHeadReceive(payload: any): void {
    payload.behavior = true;
    payload.opinion = payload.opinion;
  }

  /**
   * 处理执行情况填报状态
   */
  processExecutionReport(payload: any): void {
    payload.behavior = true;
    payload.opinion = payload.opinion;
  }

  /**
   * 处理工作票终结状态
   */
  processWorkTermination(payload: any, values: any): void {
    const endDescribeText = values.endDescribe ? generateDisplayText(
      values.endDescribe,
      formatChineseDateTime(values.endTime)
    ) : null;

    payload.workFlowEnd = {
      ...values,
      endTime: formatDateTime(values.endTime),
      headName: extractUserInfo(values?.headName, 'name'),
      headId: extractUserInfo(values?.headName, 'id'),
      appointHeadName: extractUserInfo(values?.appointHeadName, 'name'),
      appointHeadId: extractUserInfo(values?.appointHeadName, 'id'),
      groundWireCode: values?.endDescribe?.groundWireCode ?? '',
      groundWireNum: values?.endDescribe?.groundWireNum ?? '',
      knifeSwitch: values?.endDescribe?.knifeSwitch ?? '',
      knifeSwitchNum: values?.endDescribe?.knifeSwitchNum ?? '',
      endDescribe: endDescribeText,
    };

    payload.behavior = "true";
    this.cleanupTerminationFields(payload);
  }

  /**
   * 处理审核结果状态
   */
  processAuditResult(payload: any): void {
    payload.workFlowAudit = {
      auditResult: payload.auditResult,
      auditOpinion: payload.opinion,
    };
  }

  /**
   * 清理终结状态的多余字段
   */
  private cleanupTerminationFields(payload: any): void {
    const fieldsToDelete = [
      'endDescribe', 'appointHeadName', 'groundWireCode',
      'groundWireNum', 'knifeSwitch', 'knifeSwitchNum', 'endTime'
    ];
    fieldsToDelete.forEach(field => delete payload[field]);
  }

  /**
   * 根据状态处理payload
   */
  processPayloadByStatus(payload: any, values: any): void {
    switch (payload.status) {
      case STATUS_CONSTANTS.PREPARING:
        this.processPreparing(payload);
        break;
      case STATUS_CONSTANTS.WORK_HEAD_CONFIRM:
        this.processWorkHeadConfirm(payload);
        break;
      case STATUS_CONSTANTS.DUTY_HEAD_CONFIRM:
        this.processDutyHeadConfirm(payload);
        break;
      case STATUS_CONSTANTS.DUTY_MANAGER_APPROVE:
        this.processDutyManagerApprove(payload);
        break;
      case STATUS_CONSTANTS.PERMIT_APPROVE:
        this.processPermitApprove(payload);
        break;
      case STATUS_CONSTANTS.WORK_HEAD_RECEIVE:
        this.processWorkHeadReceive(payload);
        break;
      case STATUS_CONSTANTS.EXECUTION_REPORT:
        this.processExecutionReport(payload);
        break;
      case STATUS_CONSTANTS.WORK_TERMINATION:
        this.processWorkTermination(payload, values);
        break;
      case STATUS_CONSTANTS.AUDIT_RESULT:
        this.processAuditResult(payload);
        break;
      default:
        break;
    }
  }
}

// ==================== 主Hook ====================

export const useSubmitWorkOrder = ({
  data,
  type,
  safetyMeasureRef,
  securityTicketContentRef,
  workOrderExecutionRef,
}: UseSubmitWorkOrderProps) => {
  const toastHandler = useRef<ToastHandler>();
  const statusProcessor = StatusProcessor.getInstance();

  /**
   * 路由处理器
   */
  const handleSubProcess = async (
    sub: string,
    values: any,
    context: SubmitContext,
    activeName: string
  ): Promise<boolean> => {
    const handlers = {
      [SUB_PROCESS_TYPES.DELAY]: () => onDelayFetch(values, context, activeName),
      [SUB_PROCESS_TYPES.CHANGE]: () => onExChangeFetch(values, context, activeName),
      [SUB_PROCESS_TYPES.BREAK]: () => onTicketReturnFetch(values, context, activeName),
      [SUB_PROCESS_TYPES.TRY_RUN]: () => onTryRunFetch(values, context, activeName),
      [SUB_PROCESS_TYPES.TRY_RUN_REPAIR]: () => onTryRunRepairFetch(values, context, activeName),
    };

    const handler = handlers[sub as keyof typeof handlers];
    return handler ? await handler() : false;
  };

  /**
   * 构建基础payload
   */
  const buildBasePayload = (values: any, context: SubmitContext, activeName: string) => {
    const { variables: vari } = context;

    // 判断是否是保存操作
    const isSaveOperation = activeName === "保存";
    const isAudit = isSaveOperation ? 0 : 1;

    if (vari?.pass === 'true' && !isSaveOperation) {
      vari[OTHER_USER_NAME] = data?.['headUserName'] || '';
    }

    return {
      ...Object.fromEntries(
        Object.entries(data ?? {}).filter(([key, value]) => value !== undefined && value !== null),
      ),
      isAudit,
      behavior: isSaveOperation ? false : (isAudit === 1 && activeName === "通过" ? 'true' : 'false'),
      ...values,
      variables: { ...vari },
      headUserName: data?.['headUserName'] || '',
    };
  };

  /**
   * 处理工作票执行数据
   */
  const processWorkTicketExecuteData = (payload: any): void => {
    const executionStatuses = [
      STATUS_CONSTANTS.EXECUTION_REPORT,
      STATUS_CONSTANTS.EXECUTION_REPORT_2,
      STATUS_CONSTANTS.EXECUTION_REPORT_3,
      STATUS_CONSTANTS.EXECUTION_REPORT_4,
    ];

    if (payload && executionStatuses.includes(payload.status)) {
      // 获取执行数据
      const workTicketExecuteData = workOrderExecutionRef?.current?.getWorkTicketExecute() || {};
      
      const executeData = {
        ...payload.workTicketExecute,
        // 工作成员变更数据，保留id字段用于更新
        workFlowExecuteUserExchanges: workTicketExecuteData?.workFlowExecuteUserExchanges?.map((item: any, index: number) => ({
          ...item,
          mainId: data?.id,
          id: data?.workTicketExecute?.workFlowExecuteUserExchanges?.[index]?.id || null,
        })),
        // 工作票执行表数据
        workFlowExecute: {
          mainId: data?.id,
          ...data?.workTicketExecute?.workFlowExecute, // 保留原有数据
          ...workTicketExecuteData?.workFlowExecute, // 合并新数据
        },
        // 工作执行记录列表，包含检修允许试运行(type=1)和检修工作恢复(type=2)的数据
        workExecuteRecordList: (workTicketExecuteData?.workExecuteRecordList || []).map((item: any) => {
          // 查找原来记录中是否存在相同类型和相似内容的记录用于更新
          const existingRecord = data?.workTicketExecute?.workExecuteRecordList?.find((record: any) => 
            record.type === item.type && 
            (record.id === item.id || 
             (record.allowTime === item.allowTime && record.allowName === item.allowName))
          );
          
          return {
            ...item,
            mainId: data?.id, // 确保mainId存在
            id: item.id || existingRecord?.id || null, // 优先使用item自己的id，其次用匹配到的记录id
            type: item.type || (item.id ? null : (item.allowTime ? 1 : 2)), // 确保type字段存在，如果没有则根据内容判断默认值
          };
        }),
      };
      payload.workTicketExecute = executeData;
    }
  };

  /**
   * 处理安全措施数据
   */
  const processSafetyMeasureData = (payload: any): void => {
    const safetyMeasureData = safetyMeasureRef.current?.getValue();
    if (safetyMeasureData) {
      payload.workElectricalSafeMeasure = safetyMeasureData;
    }
  };

  /**
   * 处理维护指令数据
   */
  const processMaintenanceInstructionsData = (payload: any): boolean => {
    const maintenanceInstructionsData = workOrderExecutionRef.current?.getMaintenanceIstructionsRef()?.getFieldsValue() || {};

    if (data?.status === STATUS_CONSTANTS.EXECUTION_REPORT) {
      const { canRun, repairOrder } = maintenanceInstructionsData;
      payload.maintenanceIstructions = maintenanceInstructionsData;

      if (!repairOrder) {
        Toast.clear();
        Toast.show({ icon: 'fail', content: '检修交代不能为空' });
        return false;
      }

      if (!canRun) {
        Toast.clear();
        Toast.show({ icon: 'fail', content: '请选择设备是否可投运' });
        return false;
      }

      payload.workFlowExecute = {
        ...payload.workFlowExecute,
        canRun,
        repairOrder,
      };
    }

    return true;
  };

  // ==================== 主提交函数 ====================

  /**
   * 主提交函数
   */
  const onSubmit = async (values: any, context: SubmitContext, activeName: string): Promise<boolean> => {
    console.log("🚀 ~ onSubmit:", { values, context, activeName });

    const formattedValues = formatDateFields(values);
    const { variables } = context;
    const { sub = '' } = variables || {};
    const isSaveOperation = activeName === "保存";

    // 处理子流程
    if (sub) {
      return await handleSubProcess(sub, formattedValues, context, activeName);
    }

    toastHandler.current = showLoadingToast();

    try {
      // 构建基础payload
      const payload = buildBasePayload(formattedValues, context, activeName);

      // 根据状态处理payload（保存操作也需要基本的状态处理）
      statusProcessor.processPayloadByStatus(payload, formattedValues);

      // 处理工作票执行数据
      processWorkTicketExecuteData(payload);

      // 处理安全措施数据
      processSafetyMeasureData(payload);

      // 处理维护指令数据（保存操作可能不需要严格验证）
      if (!isSaveOperation) {
        const isValid = processMaintenanceInstructionsData(payload);
        if (!isValid) return false;
      } else {
        // 保存操作时也处理维护指令数据，但不进行严格验证
        const maintenanceInstructionsData = workOrderExecutionRef.current?.getMaintenanceIstructionsRef()?.getFieldsValue() || {};
        if (data?.status === STATUS_CONSTANTS.EXECUTION_REPORT) {
          payload.maintenanceIstructions = maintenanceInstructionsData;
          if (maintenanceInstructionsData.canRun || maintenanceInstructionsData.repairOrder) {
            payload.workFlowExecute = {
              ...payload.workFlowExecute,
              canRun: maintenanceInstructionsData.canRun,
              repairOrder: maintenanceInstructionsData.repairOrder,
            };
          }
        }
      }

      // 发送请求
      const res = await updateWorkBase(payload);
      const redirectPath = `/twotickets/workmnt/electrical/${type}`;

      showResultAndRedirect(
        res?.code === '1',
        res?.code === '1' ? (isSaveOperation ? '保存成功' : '已处理') : res?.message || '提交失败',
        redirectPath
      );

      return res?.code === '1';
    } catch (error) {
      Toast.show({ icon: 'fail', content: isSaveOperation ? '保存失败' : '提交失败' });
      return false;
    }
  };

  // ==================== 子流程处理函数 ====================

  /**
   * 延期处理
   */
  const onDelayFetch = async (values: any, context: SubmitContext, activeName: string): Promise<boolean> => {
    const { variables: conditionParams } = context;
    const workFlowExecuteDelay = data.workTicketExecute.workFlowExecuteDelay || {};

    let memoValue = 1;
    if (conditionParams?.pass === 'true') {
      memoValue = Number(workFlowExecuteDelay.memo) + 1;
    } else if (conditionParams.memo) {
      memoValue = Number(conditionParams.memo);
    }

    const payload = {
      ...workFlowExecuteDelay,
      newEndTime: formatDateTime(values.newEndTime),
      ...values,
      variables: conditionParams,
      ticketType: data?.ticketType,
      mainId: data?.id,
      memo: memoValue.toString(),
    };

    if (payload.memo !== '4') {
      payload.newEndTime = workFlowExecuteDelay?.newEndTime || null;
    }

    try {
      toastHandler.current = showLoadingToast();
      const res = await delayAudit(payload);
      const redirectPath = `/twotickets/workmnt/electrical/${type}`;

      showResultAndRedirect(
        res?.code === '1',
        res?.code === '1' ? '已处理' : '提交失败',
        redirectPath
      );

      return res?.code === '1';
    } catch (error) {
      Toast.clear();
      Toast.show({ icon: 'fail', content: '提交失败' });
      return false;
    }
  };

  /**
   * 负责人变更处理
   */
  const onExChangeFetch = async (values: any, context: SubmitContext, activeName: string): Promise<boolean> => {
    const { variables: conditionParams } = context;
    const approveEndTimeD = dayjs(data?.planEndTime);
    const planBeginTimeD = dayjs(data?.planBeginTime);
    const changeTimeD = dayjs(values?.changeTime);

    if (data?.planEndTime && data?.planBeginTime && values?.changeTime) {
      if (changeTimeD.isBefore(planBeginTimeD) || changeTimeD.isAfter(approveEndTimeD)) {
        Toast.show({
          icon: 'fail',
          content: '变更时间不能早于计划开始日期，不能晚于批准工作结束日期',
        });
        return false;
      }
    }

    const payload = {
      nowHeadId: extractUserInfo(values?.nowHeadId, 'id'),
      nowHeadName: extractUserInfo(values?.nowHeadId, 'name'),
      ...data?.workTicketExecute?.workFlowExecuteHeadExchange,
      ...values,
      changeTime: formatDateTime(values?.changeTime),
      ticketType: data?.ticketType,
      mainId: data?.id,
      isAudit: "true",
      variables: conditionParams || {},
      memo: conditionParams?.memo || "2",
    };

    try {
      const res = await headerExchangeAudit(payload);
      const redirectPath = `/twotickets/workmnt/electrical/${type}`;

      showResultAndRedirect(
        res?.code === '1',
        res?.code === '1' ? '已处理' : '提交失败',
        redirectPath
      );

      return res?.code === '1';
    } catch (error) {
      Toast.clear();
      Toast.show({ icon: 'fail', content: '提交失败' });
      return false;
    }
  };

  /**
   * 工作票交回处理
   */
  const onTicketReturnFetch = async (values: any, context: SubmitContext, activeName: string): Promise<boolean> => {
    const { variables: conditionParams } = context;
    const workFlowReturns = data?.workTicketExecute?.workFlowReturns ?? [];

    const isIssuance = workFlowReturns.length && workFlowReturns[0].memo === '3';

    let params: any = {};

    if (isIssuance) {
      params = {
        ...workFlowReturns[0],
        grantTime: formatDateTime(values.submitTime),
        grantRemark: values.remark,
        memo: 4,
        mainId: Number(data.id),
        ticketType: data.ticketType,
      };
    } else {
      params = {
        ...values,
        submitTime: formatDateTime(values.submitTime),
        mainId: Number(data.id),
        ticketType: data.ticketType,
      };
    }

    params.variables = conditionParams;

    try {
      const res = await ticketReturn(params);
      const redirectPath = `/twotickets/workmnt/electrical/${type}`;

      showResultAndRedirect(
        res?.code === '1',
        res?.code === '1' ? '已处理' : '提交失败',
        redirectPath
      );

      return res?.code === '1';
    } catch (error) {
      Toast.clear();
      Toast.show({ icon: 'fail', content: '提交失败' });
      return false;
    }
  };

  /**
   * 构建试运行基础payload
   */
  const buildTryRunBasePayload = (values: any, conditionParams: any): any => {
    return {
      ...values,
      teamsNmae: values?.teamsNmae?.map?.((item: any) => item?.label || item)?.join?.(','),
      workFlowTryRunSafe: values?.workFlowTryRunSafe?.length ? values?.workFlowTryRunSafe : null,
      orgId: Number(values.orgId),
      ticketType: '1',
      type: type === 'trialRun' ? 1 : 2,
      tryTime: formatDateTime(values.tryTime),
      behavior: '1',
      mainId: Number(data.id),
      isAudit: 1,
      processInstanceId: data.processInstanceId,
      variables: conditionParams,
      memo: conditionParams?.memo || "2",
    };
  };

  /**
   * 检修设备试运行申请处理
   */
  const onTryRunFetch = async (values: any, context: SubmitContext, activeName: string): Promise<boolean> => {
    const { variables: conditionParams } = context;
    const payload = buildTryRunBasePayload(values, conditionParams);

    normalizeArrayFields(payload);

    try {
      const res = await tryRunAudit(payload);
      const redirectPath = `/twotickets/workmnt/electrical/${type}`;

      showResultAndRedirect(
        res?.code === '1',
        res?.code === '1' ? '已处理' : '提交失败',
        redirectPath
      );

      return res?.code === '1';
    } catch (error) {
      Toast.clear();
      Toast.show({ icon: 'fail', content: '提交失败' });
      return false;
    }
  };

  /**
   * 检修设备试运行后恢复工作申请处理
   */
  const onTryRunRepairFetch = async (values: any, context: SubmitContext, activeName: string): Promise<boolean> => {
    const { variables: conditionParams } = context;
    const payload = {
      ...values,
      teamsNmae: values?.teamsNmae?.map?.((item: any) => item)?.join?.(','),
      workFlowTryRunSafe: values?.workFlowTryRunSafe?.length ? values?.workFlowTryRunSafe : null,
      orgId: values.orgId?.map?.((item: any) => item?.id)?.join?.(',') || Number(values.orgId),
      ticketType: '1',
      type: type === 'trialRun' ? 1 : 2,
      tryTime: formatDateTime(values.tryTime),
      behavior: '1',
      mainId: Number(data.id),
      isAudit: 1,
      processInstanceId: data.processInstanceId,
      variables: conditionParams,
      memo: conditionParams?.memo || "2",
    };

    normalizeArrayFields(payload);

    try {
      const res = await tryRunAudit(payload);
      const redirectPath = `/twotickets/workmnt/electrical/${type}`;

      showResultAndRedirect(
        res?.code === '1',
        res?.code === '1' ? '已处理' : '提交失败',
        redirectPath
      );

      return res?.code === '1';
    } catch (error) {
      Toast.clear();
      Toast.show({ icon: 'fail', content: '提交失败' });
      return false;
    }
  };

  return {
    onSubmit,
    onDelayFetch,
    onExChangeFetch,
    onTicketReturnFetch,
    onTryRunFetch,
    onTryRunRepairFetch,
  };
}; 