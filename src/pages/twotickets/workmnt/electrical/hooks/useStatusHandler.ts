/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-01-XX XX:XX:XX
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-06-03 11:34:46
 * @FilePath: /mobile-yw/src/pages/twotickets/workmnt/electrical/hooks/useStatusHandler.ts
 * @Description: 状态处理逻辑hook - 优化版本
 */

import { useState, useCallback } from 'react';
import { Toast } from 'antd-mobile';
import {
  ElectricalWorkTicketData,
  StatusContext
} from '../types';
import {
  FINAL_STATES,
  STATUS_TRANSITION_RULES,
  SPECIAL_OPERATION_ALLOWED_STATES,
  SUB_PROCESS_TYPES,
  VALID_DELAY_MEMOS,
  OPERATION_TYPES,
  STATUS_NAME_MAP
} from '../constants/status';

interface StatusHandlerReturn {
  visible: boolean;
  setVisible: (visible: boolean) => void;
  variables: any;
  activeName: string;
  status: string;
  handleActionClick: (label: "处理" | "保存", next: any, name: string | undefined) => void;
}

interface EditableDataRefs {
  safetyMeasureRef?: React.RefObject<any>;
  securityTicketContentRef?: React.RefObject<any>;
  workOrderExecutionRef?: React.RefObject<any>;
}

/**
 * 状态处理hook
 * 处理电气工作票的各种状态转换逻辑
 */
export const useStatusHandler = (
  data: ElectricalWorkTicketData | null,
  onSubmit: Function,
  editableDataRefs?: EditableDataRefs
): StatusHandlerReturn => {
  const [visible, setVisible] = useState(false);
  const [variables, setVariables] = useState<any>({});
  const [activeName, setActiveName] = useState<string>('');
  const [status, setStatus] = useState<string>('');

  /**
   * 验证状态转换是否合法
   */
  const validateStatusTransition = useCallback((
    fromStatus: string,
    toStatus: string,
    context: StatusContext
  ): boolean => {
    
    // 临时跳过验证（仅用于调试）
    console.warn('临时跳过状态转换验证', { fromStatus, toStatus, context });
    return true;

    console.log("🚀 ~ validateStatusTransition ~ 验证参数:", { 
      fromStatus, 
      toStatus, 
      context,
      fromStatusName: STATUS_NAME_MAP[fromStatus],
      toStatusName: STATUS_NAME_MAP[toStatus]
    });

    // 基本验证：终态不能转换
    if (FINAL_STATES.includes(fromStatus as any)) {
      console.warn(`状态转换验证失败: ${fromStatus}(${STATUS_NAME_MAP[fromStatus]}) 是终态，不能再转换`);
      return false;
    }

    // 验证状态转换规则
    const rules = STATUS_TRANSITION_RULES[fromStatus];
    console.log("🚀 ~ validateStatusTransition ~ 转换规则:", { 
      fromStatus, 
      rules, 
      hasRules: !!rules,
      pass: context.pass
    });

    if (rules && context.pass) {
      const expectedStatus = rules[context.pass === 'true' ? OPERATION_TYPES.APPROVE : OPERATION_TYPES.REJECT];
      console.log("🚀 ~ validateStatusTransition ~ 期望状态:", { 
        expectedStatus, 
        actualStatus: toStatus,
        operation: context.pass === 'true' ? 'APPROVE' : 'REJECT'
      });

      if (expectedStatus && toStatus !== expectedStatus) {
        console.warn(`状态转换验证失败: ${fromStatus}(${STATUS_NAME_MAP[fromStatus]}) -> ${toStatus}(${STATUS_NAME_MAP[toStatus]}) 不符合规则，期望: ${expectedStatus}(${STATUS_NAME_MAP[expectedStatus]})`);
        console.warn(`详细信息:`, {
          fromStatus,
          toStatus,
          expectedStatus,
          context,
          rules,
          operation: context.pass === 'true' ? 'APPROVE' : 'REJECT'
        });
        return false;
      }
    }

    console.log("✅ ~ validateStatusTransition ~ 状态转换验证通过");
    return true;
  }, []);

  /**
   * 生成基础状态
   */
  const generateBaseStatus = useCallback((
    currentStatus: string,
    pass: string,
    sub?: string
  ): string => {
    // 输入验证
    if (!currentStatus) {
      console.warn('generateBaseStatus: currentStatus is empty');
      Toast.show({ icon: 'fail', content: '当前状态为空，无法处理' });
      return '';
    }

    // 终态状态检查
    if (FINAL_STATES.includes(currentStatus as any)) {
      const statusName = STATUS_NAME_MAP[currentStatus] || currentStatus;
      const message = `状态 ${statusName} 是终态，不应该有进一步操作`;
      console.warn(`generateBaseStatus: ${message}`);
      Toast.show({ icon: 'fail', content: message });
      return currentStatus;
    }

    // 状态生成逻辑
    if (pass === 'false' && sub) {
      return `${currentStatus}_${sub}`;
    } else if (pass === 'false' && !sub) {
      return `${currentStatus}_reject`;
    } else if (pass === 'true') {
      return `${currentStatus}_approve`;
    }

    return currentStatus;
  }, []);

  /**
   * 处理延期状态
   */
  const handleDelayStatus = useCallback((context: StatusContext, pass?: string): string => {
    const delayMemo = data?.workTicketExecute?.workFlowExecuteDelay?.memo;

    if (!delayMemo) {
      return SUB_PROCESS_TYPES.DELAY;
    }

    // 验证memo值的有效性
    if (!VALID_DELAY_MEMOS.includes(delayMemo)) {
      console.warn(`handleDelayStatus: 无效的延期memo值: ${delayMemo}`);
      return SUB_PROCESS_TYPES.DELAY;
    }

    let status = `[delay-${delayMemo}]`;

    // 特殊处理memo为3且拒绝的情况
    if (delayMemo === '3' && pass === 'false') {
      status = `[delay-${delayMemo}]_reject`;
    }

    return status;
  }, [data]);

  /**
   * 处理负责人变更状态
   */
  const handleChangeStatus = useCallback((context: StatusContext): string => {
    const changeMemo = data?.workTicketExecute?.workFlowExecuteHeadExchange?.memo;
    return changeMemo ? `[change-${changeMemo}]` : SUB_PROCESS_TYPES.CHANGE;
  }, [data]);

  /**
   * 处理工作票交回状态
   */
  const handleBreakStatus = useCallback((context: StatusContext): string => {
    const ticketMemo = context?.variables?.memo || '';
    return ticketMemo ? `[ticketReturn-${ticketMemo}]` : 'ticketReturn';
  }, []);

  /**
   * 处理试运行申请状态
   */
  const handleTryRunStatus = useCallback((context: StatusContext): string => {
    const tryRunMemo = context?.variables?.memo || '';
    return tryRunMemo ? `[tryRun-${tryRunMemo}]` : SUB_PROCESS_TYPES.TRY_RUN;
  }, []);

  /**
   * 处理试运行后恢复工作申请状态
   */
  const handleTryRunRepairStatus = useCallback((context: StatusContext): string => {
    const tryRunRepairMemo = context?.variables?.memo || '';
    return tryRunRepairMemo ? `[tryRunRepair-${tryRunRepairMemo}]` : SUB_PROCESS_TYPES.TRY_RUN_REPAIR;
  }, []);

  /**
   * 应用状态修饰符 - 优化版本
   */
  const applyStatusModifiers = useCallback((
    baseStatus: string,
    context: StatusContext
  ): string => {
    if (!baseStatus || !data) return baseStatus;

    const { sub, pass } = context;
    console.log("🚀 ~ sub, pass:", { sub, pass, baseStatus })

    try {
      switch (sub) {
        case SUB_PROCESS_TYPES.DELAY:
          return handleDelayStatus(context, pass);
        case SUB_PROCESS_TYPES.CHANGE:
          return handleChangeStatus(context);
        case SUB_PROCESS_TYPES.BREAK:
          return handleBreakStatus(context);
        case SUB_PROCESS_TYPES.TRY_RUN:
          return handleTryRunStatus(context);
        case SUB_PROCESS_TYPES.TRY_RUN_REPAIR:
          return handleTryRunRepairStatus(context);
        default:
          // 保持原状态
          console.log("🚀 ~ baseStatus:保持原状态", baseStatus)
          return baseStatus;
      }
    } catch (error) {
      console.error('应用状态修饰符时发生错误:', error);
      Toast.show({ icon: 'fail', content: '状态处理出错，请重试' });
      return baseStatus;
    }
  }, [data, handleDelayStatus, handleChangeStatus, handleBreakStatus, handleTryRunStatus, handleTryRunRepairStatus]);

  /**
   * 验证操作权限
   */
  const validateOperationPermission = useCallback((
    currentStatus: string,
    operation: string
  ): boolean => {
    // 根据当前状态和操作类型验证权限
    const statusNum = parseInt(currentStatus);

    // 基础权限验证
    if (FINAL_STATES.includes(currentStatus as any)) {
      return false;
    }

    // 特定操作的权限验证
    const specialOperations = [
      SUB_PROCESS_TYPES.DELAY,
      SUB_PROCESS_TYPES.CHANGE,
      SUB_PROCESS_TYPES.BREAK,
      SUB_PROCESS_TYPES.TRY_RUN,
      SUB_PROCESS_TYPES.TRY_RUN_REPAIR
    ];

    if (specialOperations.includes(operation as any)) {
      // 只有在特定状态下才能执行这些操作
      return SPECIAL_OPERATION_ALLOWED_STATES.includes(statusNum);
    }

    return true;
  }, []);

  /**
   * 收集可编辑组件的数据
   */
  const collectEditableData = useCallback(() => {
    const editableData: any = {};

    try {
      // 收集安全措施数据
      if (editableDataRefs?.safetyMeasureRef?.current) {
        const safetyData = editableDataRefs.safetyMeasureRef.current.getData?.();
        if (safetyData) {
          editableData.safetyMeasure = safetyData;
        }
      }

      // 收集安全措施票数据
      if (editableDataRefs?.securityTicketContentRef?.current) {
        const securityData = editableDataRefs.securityTicketContentRef.current.getData?.();
        if (securityData) {
          editableData.securityTicketContent = securityData;
        }
      }

      // 收集工作票执行数据
      if (editableDataRefs?.workOrderExecutionRef?.current) {
        const workOrderData = editableDataRefs.workOrderExecutionRef.current.getData?.();
        if (workOrderData) {
          editableData.workOrderExecution = workOrderData;
        }
      }

      return editableData;
    } catch (error) {
      console.error('收集可编辑数据时发生错误:', error);
      return {};
    }
  }, [editableDataRefs]);

  /**
   * 处理操作点击事件 - 优化版本
   */
  const handleActionClick = useCallback((
    label: "处理" | "保存",
    next: any,
    name: string | undefined
  ) => {
    console.log('handleActionClick:', { label, next, name });

    // 对于保存操作，不需要验证 next 参数
    if (label === "保存") {
      if (!data?.status) {
        console.error('handleActionClick: 数据状态为空');
        Toast.show({ icon: 'fail', content: '工作票状态为空' });
        return;
      }

      try {
        // 收集可编辑数据
        const editableData = collectEditableData();
        console.log('收集到的可编辑数据:', editableData);

        // 直接提交保存
        onSubmit(
          { 
            opinion: "",
            ...editableData // 混入可编辑数据
          },
          { 
            extra: false, 
            variables: { 
              pass: 'true',  // 保存操作默认为通过
              sub: '',
              code: ''
            } 
          },
          label
        );
        return;
      } catch (error) {
        console.error('保存操作处理失败:', error);
        Toast.show({ icon: 'fail', content: '保存失败，请重试' });
        return;
      }
    }

    // 严格的参数验证（仅对非保存操作）
    if (!next) {
      console.error('handleActionClick: next参数为空');
      Toast.show({ icon: 'fail', content: '操作参数为空' });
      return;
    }

    if (!data?.status) {
      console.error('handleActionClick: 数据状态为空');
      Toast.show({ icon: 'fail', content: '工作票状态为空' });
      return;
    }

    const extraVariables = next?.extra || false;
    const sub = next?.variables?.sub || '';
    const code = next?.variables?.code || next?.code || '';
    const pass = next?.variables?.pass || '';

    // 验证操作权限
    if (sub && !validateOperationPermission(data.status, sub)) {
      const statusName = STATUS_NAME_MAP[data.status] || data.status;
      Toast.show({
        icon: 'fail',
        content: `当前状态(${statusName})下不允许此操作`
      });
      return;
    }

    console.log("状态处理参数:", { pass, sub, code, extraVariables });

    try {
      // 生成基础状态
      const baseStatus = generateBaseStatus(data.status, pass, sub);
      if (!baseStatus) return; // 如果基础状态生成失败，直接返回

      // 应用状态修饰符
      const finalStatus = applyStatusModifiers(baseStatus, {
        sub,
        code,
        pass,
        variables: next?.variables
      });

      // 验证状态转换
      if (!validateStatusTransition(data.status, finalStatus, { sub, code, pass })) {
        Toast.show({ icon: 'fail', content: '状态转换不合法' });
        return;
      }

      const fromStatusName = STATUS_NAME_MAP[data.status] || data.status;
      const toStatusName = STATUS_NAME_MAP[finalStatus] || finalStatus;
      console.log('状态转换:', {
        from: `${data.status}(${fromStatusName})`,
        to: `${finalStatus}(${toStatusName})`
      });

      setStatus(finalStatus);

      if (extraVariables) {
        // 需要弹窗处理
        setVisible(true);
        setVariables(next);
        setActiveName(name || '');
      } else {
        // 直接提交，收集可编辑数据
        const editableData = collectEditableData();
        onSubmit(
          { 
            opinion: "",
            ...editableData // 混入可编辑数据
          },
          { extra: extraVariables, variables: next?.variables },
          label || ''
        );
      }
    } catch (error) {
      console.error('处理操作点击时发生错误:', error);
      Toast.show({ icon: 'fail', content: '操作处理失败，请重试' });
    }
  }, [
    data,
    collectEditableData,
    generateBaseStatus,
    applyStatusModifiers,
    validateStatusTransition,
    validateOperationPermission,
    onSubmit
  ]);

  return {
    visible,
    setVisible,
    variables,
    activeName,
    status,
    handleActionClick
  };
}; 