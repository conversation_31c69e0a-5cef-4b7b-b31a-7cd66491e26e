/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-21 14:17:54
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-05-28 14:18:53
 * @FilePath: /mobile-yw/src/pages/twotickets/workmnt/electrical/hooks/types.ts
 * @Description: 表单策略相关类型定义
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */

export interface FormItemConfig {
  strategyType: string;
  label?: string;
  name?: string;
  placeholder?: string;
  required?: boolean;
  rules?: any[];
  layout?: string;
  trigger?: string;
  style?: Record<string, any>;
  strategyProps?: Record<string, any>;
  onClick?: (e: any, ref: any) => void;
  childElementPosition?: string;
  arrow?: boolean;
}

export interface PickerConfig {
  trigger: string;
  required: boolean;
  rules: any[];
  style: Record<string, any>;
  onClick?: (e: any, ref: any) => void;
}

export interface UserPickerConfig {
  required: boolean;
  rules: any[];
  style: Record<string, any>;
  strategyProps: {
    placeholder: string;
    layout: string;
    multiple?: boolean;
    customMode?: boolean;
  };
}

export type FormConfig = FormItemConfig[][];
export type StatusFormList = Record<string, FormConfig | FormItemConfig[]>; 