# useRegisterStrategy.ts 文件优化方案

## 优化前的问题

1. **代码过长**: 原文件 615 行，职责过于集中
2. **重复代码多**: 大量重复的配置对象和验证规则
3. **硬编码**: 存在大量魔法数字和魔法字符串
4. **耦合度高**: 策略注册和表单配置混在一起
5. **缺乏类型定义**: 配置对象没有明确的类型约束
6. **可维护性差**: 新增或修改配置需要在巨大的文件中查找

## 优化方案

### 1. 模块化拆分

将原来的单一文件拆分为多个职责明确的模块：

- `types.ts` - 类型定义
- `constants.ts` - 常量定义  
- `strategyRegistry.ts` - 策略组件注册
- `configFactory.ts` - 配置工厂函数
- `formConfigs.ts` - 具体表单配置
- `useRegisterStrategy.ts` - 主 Hook（优化后）

### 2. 类型安全

```typescript
// 新增类型定义，提供完整的类型约束
export interface FormItemConfig {
  strategyType: string;
  label?: string;
  name?: string;
  required?: boolean;
  rules?: any[];
  // ... 其他属性
}

export type FormConfig = FormItemConfig[][];
export type StatusFormList = Record<string, FormConfig | FormItemConfig[]>;
```

### 3. 常量提取

```typescript
// 将重复使用的配置提取为常量
export const DEFAULT_STYLES = {
  prefixWidth: { '--prefix-width': '10em' },
} as const;

export const PLACEHOLDERS = {
  select: '请选择',
  confirm: '确认本工作上述各项内容',
} as const;
```

### 4. 工厂模式

```typescript
// 使用工厂函数生成重复的配置
export const createOpinionForm = (
  pickerLabel: string, 
  name: string = 'opinion', 
  placeholder: string = '确认本工作上述各项内容'
): FormItemConfig[] => [...];

export const createWorkHeadUserPickerConfig = (
  label: string = '工作负责人', 
  name: string = 'headId'
): FormItemConfig => {...};
```

### 5. 配置集中管理

```typescript
// 将复杂的表单配置抽象为函数
export const createTryRunFormConfig = (): FormConfig => [...];
export const createForm15ApproveConfig = (): FormConfig => [...];
```

### 6. 性能优化

```typescript
// 使用 useMemo 避免重复计算
const { statusFormList, datePickerOtherConfig } = useMemo(() => {
  // 注册策略和生成配置
}, []);
```

## 优化效果

### 代码量对比
- **优化前**: 615 行单一文件
- **优化后**: 
  - 主文件: 55 行 (-91%)
  - 总计: 约 400 行分布在 6 个文件中

### 主要优势

1. **职责分离**: 每个文件都有明确的职责
2. **可维护性**: 修改配置时可以快速定位到相关文件
3. **可复用性**: 工厂函数可以在其他地方复用
4. **类型安全**: 完整的 TypeScript 类型支持
5. **代码清晰**: 通过函数名和注释可以快速理解配置用途
6. **性能提升**: useMemo 避免了不必要的重复计算
7. **扩展性**: 新增配置项更加简单

### 文件结构

```
hooks/
├── types.ts                    # 类型定义
├── constants.ts               # 常量定义
├── strategyRegistry.ts        # 组件策略注册
├── configFactory.ts           # 配置工厂函数
├── formConfigs.ts            # 具体表单配置
└── useRegisterStrategy.ts     # 主 Hook（优化后）
```

### 使用方式

优化后的使用方式保持不变：

```typescript
const { statusFormList, datePickerOtherConfig } = useRegisterStrategy();
```

## 后续优化建议

1. **配置文件化**: 可以考虑将部分静态配置移到 JSON 文件中
2. **国际化支持**: 将硬编码的中文文本提取到国际化文件中
3. **单元测试**: 为工厂函数和配置生成逻辑添加单元测试
4. **文档完善**: 为每个配置项添加详细的使用说明

这个重构方案在保持功能完整性的同时，大幅提升了代码的可维护性、可读性和可扩展性。 