# 用户选择器组件优化总结

## 优化目标
当 `customMode` 为 `true` 且 `multiple` 为 `false` 时：
1. 合并显示"已选择人员"和"自定义输入"为统一的"已选择人员"
2. 在单选模式下，选择新的树节点或输入新的自定义内容时，只保留最后的选择或输入

## 修改的文件

### 1. `src/component/tree-picker/components/CustomMode.tsx`
- **修改内容**：
  - 添加 `multiple` 属性支持
  - 修改 `renderSelectedItems` 方法，当 `multiple` 为 `false` 时合并显示逻辑
  - 在单选模式下，将"已选择人员"和"自定义输入"合并为统一的"已选择人员"显示
  - 修复 TypeScript 类型错误

### 2. `src/component/tree-picker/types.ts`
- **修改内容**：
  - 在 `CustomModeProps` 接口中添加 `multiple?: boolean` 属性

### 3. `src/component/tree-picker/tree-picker.component.tsx`
- **修改内容**：
  - 将 `multiple` 属性传递给 `CustomMode` 组件

### 4. `src/component/tree-picker/hooks/useTreePicker.ts`
- **修改内容**：
  - 修改 `handleSelect` 方法：在 `customMode` 为 `true` 且 `multiple` 为 `false` 时，选择新的树节点时清空自定义节点
  - 修改 `handleCustomInputChange` 方法：
    - 在 `customMode` 为 `true` 且 `multiple` 为 `false` 时，输入新的自定义内容时清空已选择的树节点
    - 在单选模式下，只保留最后一个自定义输入节点

### 5. `src/pages/twotickets/workmnt/components/user-picker/user-picker.component.tsx`
- **修改内容**：
  - 更新组件注释，添加优化说明

## 功能说明

### 多选模式 (`multiple: true`)
- 保持原有行为不变
- 分别显示"已选择人员"和"自定义输入"两个区域
- 可以同时选择多个树节点和输入多个自定义项

### 单选模式 (`multiple: false`)
- **自定义模式关闭时**：保持原有单选行为
- **自定义模式开启时**：
  - 合并显示为统一的"已选择人员"区域
  - 选择新的树节点时，自动清空之前的自定义输入
  - 输入新的自定义内容时，自动清空之前选择的树节点
  - 确保任何时候只保留最后一次的选择或输入

## 使用示例

```tsx
// 单选 + 自定义模式
<UserPicker
  label="负责人"
  name="responsible"
  placeholder="请选择负责人"
  title="选择负责人"
  multiple={false}
  customMode={true}
  onChange={(value) => console.log('选中的值:', value)}
/>
```

在这种配置下：
- 界面只显示一个"已选择人员"区域
- 用户可以通过树选择器选择人员，或者手动输入人员名称
- 每次新的选择或输入都会替换之前的选择
- 确保始终只有一个选中项

## 测试验证
- ✅ 构建成功，无编译错误
- ✅ TypeScript 类型检查通过
- ✅ 功能逻辑符合需求规范 