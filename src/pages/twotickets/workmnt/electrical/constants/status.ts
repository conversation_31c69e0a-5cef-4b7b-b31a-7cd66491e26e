/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-01-XX XX:XX:XX
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-05-28 17:57:39
 * @FilePath: /mobile-yw/src/pages/twotickets/workmnt/electrical/constants/status.ts
 * @Description: 电气工作票状态管理常量
 */

import { FormStatus } from '../types';

/**
 * 终态状态列表
 */
export const FINAL_STATES = [
  FormStatus.COMPLETED,   // '18' - 已审查（终态）
  FormStatus.CANCELLED,   // '19' - 已作废（终态）
  FormStatus.TERMINATED   // '20' - 已取消（终态）
];

/**
 * 状态转换规则映射
 * 定义每个状态下的合法转换目标
 */
type TransitionRule = {
  approve: string;
  reject?: string;
  back: string;
  cancel?: string;
};

/**
 * 生成状态转换规则
 * @param maxStatus 最大状态序号
 * @returns 状态转换规则映射
 */
const generateTransitionRules = (maxStatus: number): Record<string, TransitionRule> => {
  const rules: Record<string, TransitionRule> = {};
  
  for (let i = 0; i <= maxStatus; i++) {
    rules[`${i}`] = {
      approve: `${i}_approve`,
      back: `${i}_back`,
      cancel: `${i}_cancel`,
      reject: `${i}_reject`,
    };
  }
  
  return rules;
};

/**
 * 状态转换规则映射
 * 定义每个状态下的合法转换目标
 */
export const STATUS_TRANSITION_RULES = generateTransitionRules(20);

/**
 * 允许特殊操作的状态列表
 */
export const SPECIAL_OPERATION_ALLOWED_STATES = [9, 10, 11, 12];

/**
 * 状态名称映射（用于显示）
 */
export const STATUS_NAME_MAP: Record<string, string> = {
  '1': '准备中',
  '2': '工作票签发',
  '3': '业主签发人确认',
  '4': '工作负责人确认',
  '5': '值班负责人签字确认',
  '6': '值长审核',
  '7': '工作票许可审批',
  '8': '工作负责人接收确认',
  '9': '工作票执行情况填报',
  '10': '工作票执行情况填报',
  '11': '工作票执行情况填报',
  '12': '工作票执行情况填报',
  '13': '工作票执行确认审核（工作许可人）',
  '14': '待值长确认',
  '15': '工作票执行确认审核（工作负责人）',
  '16': '待工作负责人确认',
  '17': '工作票执行确认审核',
  '18': '已审查（终态）',
  '19': '已作废（终态）',
  '20': '已取消（终态）'
};

/**
 * 子流程类型定义
 */
export const SUB_PROCESS_TYPES = {
  DELAY: 'delay',              // 延期
  CHANGE: 'change',            // 负责人变更
  BREAK: 'break',              // 工作票交回
  TRY_RUN: 'tryRun',          // 试运行申请
  TRY_RUN_REPAIR: 'tryRunRepair' // 试运行后恢复工作申请
} as const;

/**
 * 有效的延期memo值
 */
export const VALID_DELAY_MEMOS = ['1', '2', '3'];

/**
 * 操作类型定义
 */
export const OPERATION_TYPES = {
  APPROVE: 'approve',
  REJECT: 'reject',
  BACK: 'back',
  CANCEL: 'cancel',
  SAVE: 'save'
} as const; 