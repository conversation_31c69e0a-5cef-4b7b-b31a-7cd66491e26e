import dayjs from 'dayjs';
import { useThrottleFn } from 'ahooks';
import { useParams, history } from 'umi';
import { Button, Form, Input, Tabs, Toast } from 'antd-mobile';
import { createElement, useCallback, useEffect, useRef, useState } from 'react';
import { ProcessCard } from '@/component/process-card/process-card.component';
import type { ToastHandler } from 'antd-mobile/es/components/toast';
import TwoTicketsNavBar from '@/component/twotickets-navbar/twotickets-navbar.component';
import { SHOW_TIME_FORMAT } from '@/utils/constant';
import styles from './electrical.page.less';
import useDetail from './hooks/useDetail';
import ReviewPopup from './components/review-popup/review-popup.component';
import WorkOrderExecution from './components/work-order-execution/work-order-execution.component';
import { BasicInfoComponent } from './components/basic-info.component';
import SafetyMeasureComponent from './components/safety-measure/safety-measure.component';
import SecurityTicketContent, { SecurityTicketContentRef } from './components/security-ticket-content/security-ticket-content.component';
import ActionButtons from '../components/action-buttons/action-buttons.component';
import SafeToolComponent from '../components/safe-tool/safe-tool.component';
import SecurityRiskControlContent from '../components/security-risk-control-content/security-risk-control.content';
import { OperationHeaderBtn } from '../../operation/detail/components/operation-btn/operation-header-btn.component';
import { useSubmitWorkOrder } from './hooks/useSubmitWorkOrder';
// import { TreePickerRef } from '@/component/tree-picker/types';
// import OrgPicker from '../components/org-picker/org-picker.component';
// import UserPicker from '../components/user-picker/user-picker.component';


const TAB_HEIGHT = 42;
const otherUserName = 'header';

const ElectricalPage = () => {

  const [status, setStatus] = useState<string>('');
  const [activeKey, setActiveKey] = useState('1');
  const { data, loading, title, pageType, type, ticketType } = useDetail('electrical');

  const safetyMeasureRef = useRef<any>(null);
  const securityTicketContentRef = useRef<SecurityTicketContentRef>(null);
  const workOrderExecutionRef = useRef<any>(null);

  // 使用新的 hook
  const {
    onSubmit
  } = useSubmitWorkOrder({
    data,
    type: type || '', // 确保 type 不为 undefined
    safetyMeasureRef,
    securityTicketContentRef,
    workOrderExecutionRef,
  });

  const basicList = [
    { type: 'title', value: "workTask" },
    { label: '设备信息', value: "deviceName" },

    // 分割线
    { type: 'divider' },

    { label: '单位', value: "unitName" },
    { label: '成员', value: "member", render: (text: string, record: any) => `${record?.member || ''}${record?.member ? `（参加工作人数共${record?.peopleNumber || 0}人）` : ''}` },
    { label: '班组', value: "workTask" },

    // 分割线
    { type: 'divider' },

    {
      label: '计划时间', value: "planBeginTime", render: () => {
        return `${data?.planBeginTime ?
          dayjs(data.planBeginTime).format(SHOW_TIME_FORMAT) :
          ''} ~ ${data?.planEndTime ?
            dayjs(data.planEndTime).format(SHOW_TIME_FORMAT) : ''}`
      }
    },
    ...(ticketType === '2' ? [
      { label: '工作地点及或地段', value: "workLocation" },
      { label: '工作条件', value: "workCondition" },
      { label: '注意事项（安全措施）', value: "precaution" },
    ] : [
      { label: '工作地点及设备双重名称', value: "workLocation" },
    ]),
    { label: '创建时间', value: "createTime" },
  ]

  const TAB_ITEMS: { key: string; title: string; content: (data: any) => unknown }[] = [
    {
      key: '1',
      title: '基本信息',
      content: (data: any) => <BasicInfoComponent data={data} list={basicList} />,
    },
    {
      key: '3',
      title: '安全风险控制卡',
      content: (data: any) => <SecurityRiskControlContent data={data} />,
    },
    ...(ticketType === '1' ? [{
      key: '4',
      title: '安全措施',
      content: (data: any) => createElement(SafetyMeasureComponent as any, {
        ref: safetyMeasureRef,
        data,
      }),
    }] : []),
    {
      key: '5',
      title: '安全措施票',
      content: (data: any) => createElement(SecurityTicketContent as any, {
        ref: securityTicketContentRef,
        data,
      }),
    },
    {
      key: '6',
      title: '安全工器具',
      content: (data: any) => <SafeToolComponent data={data} />,
    },
  ];

  if (pageType === "edit" && Number(data?.status) > 8) {
    TAB_ITEMS.splice(1, 0, {
      key: '2',
      title: '工作票执行',
      content: (data: any) => createElement(WorkOrderExecution, {
        data,
        ref: workOrderExecutionRef,
        ticketType, // 使用常量
        actionType: pageType,
      }),
    });
  }

  const { run: handleScroll } = useThrottleFn(
    () => {
      let currentKey = TAB_ITEMS[0].key;
      for (const item of TAB_ITEMS) {
        const element = document.getElementById(`anchor-${item.key}`);
        if (!element) continue;
        const rect = element.getBoundingClientRect();
        if (rect.top <= TAB_HEIGHT) {
          currentKey = item.key;
        } else {
          break;
        }
      }
      setActiveKey(currentKey);
    },
    {
      leading: true,
      trailing: true,
      wait: 100,
    },
  );

  useEffect(() => {
    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  const [visible, setVisible] = useState(false);
  const [variables, setVariables] = useState<any>({});
  const [activeName, setActiveName] = useState<string>('');

  const handleActionClick = (label: "处理" | "保存", next: any, name: string | undefined) => {
    console.log(label, next, name);
    const extraVariables = next?.extra || false;
    const sub = next?.variables?.sub || '';
    const code = next?.variables?.code || next?.code || '';
    const pass = next?.variables?.pass || '';
    console.log("🚀 ~ ElectricalPage ~ pass:", { pass, sub, code, extraVariables })
    let status = '';
    if (pass === 'false' && sub) {
      status = `${data?.status}_${sub}`;
    } else if (pass === 'false' && !sub) {
      status = `${data?.status}_reject`;
    } else if (pass === 'true') {
      status = `${data?.status}_approve`;
    }

    // 延期审核
    if (sub === 'delay') {
      const memo = data?.workTicketExecute?.workFlowExecuteDelay?.memo;
      status = memo ? `[delay-${memo}]` : 'delay';
      if (memo === "3") {
        if (pass === 'false') {
          status = `[delay-${memo}]_reject`;
        }
      }
    }

    // 负责人变动审核
    if (sub === 'change') {
      const memo = data?.workTicketExecute?.workFlowExecuteHeadExchange?.memo;
      status = memo ? `[change-${memo}]` : 'change';
    }
    // 工作票交回
    if (sub === 'break') {
      const memo = next?.variables?.memo || '';
      status = memo ? `[ticketReturn-${memo}]` : 'ticketReturn';
    }

    // 检修设备试运行申请
    if (sub === 'tryRun') {
      const memo = next?.variables?.memo || '';
      console.log("🚀 ~ handleActionClick ~ memo:", memo)
      status = memo ? `[tryRun-${memo}]` : 'tryRun';
    }

    // 检修设备试运行后恢复工作申请
    if (sub === 'tryRunRepair') {
      const memo = next?.variables?.memo || '';
      status = memo ? `[tryRunRepair-${memo}]` : 'tryRunRepair';
    }
    console.log(status, 'status');
    setStatus(status);
    if (extraVariables) {
      setVisible(true);
      setVariables(next);
      setActiveName(name || '');
    } else {
      // 执行提交
      onSubmit({ opinion: "" }, { extra: extraVariables, variables: next?.variables }, label || '');
    }
  };

  return (
    <div className={styles.container}>
      <TwoTicketsNavBar title={data?.taskName ? data?.taskName : title} subTitle={data?.taskName ? title : ''} bgcolor={'#fff'} color={'#000'} right={<OperationHeaderBtn data={data} />} />

      <div className={styles.body}>
        <div className={styles.content}>
          <ProcessCard
            id={data?.id}
            records={(data?.workflowApprovalRecordList || [])}
            onProcess={function (id: number): void {
              history.push(`/twotickets/common/check-list/process-viewer/${data?.id}?ticketType=${ticketType}`);
            }}
            taskUserName={data?.taskUserName}
          />
        </div>

        <div className={styles.tabsContainer}>
          <Tabs
            activeKey={activeKey}
            onChange={(key) => {
              setActiveKey(key)
              document.getElementById(`anchor-${key}`)?.scrollIntoView();
              window.scrollTo({
                top: window.scrollY - TAB_HEIGHT,
              });
            }}
          >
            {TAB_ITEMS.map((item) => (
              <Tabs.Tab title={item.title} key={item.key} />
            ))}
          </Tabs>
        </div>

        <div className={styles.tabsContent}>
          {TAB_ITEMS.map((item: any) => (
            <div key={item.key} id={`anchor-${item.key}`}>
              {item.content(data)}
            </div>
          ))}
        </div>
        <div className={styles.bottom}>已经到底了</div>
      </div>
      {pageType === "edit" && data?.curTask && <div className={styles.operation}>
        {/* 操作按钮 */}

        <ActionButtons
          onClick={handleActionClick}
          showSaveBtn={![5, 6, 8, 10, 11, 12, 13, 14, 15, 16, 17].includes(Number(data?.status))}
          taskId={data?.taskId}
          disabledConfig={{
            // "工作延期": !!data?.workTicketExecute?.workFlowExecuteDelay,
            // "工作负责人变更": !!data?.workTicketExecute?.workFlowExecuteHeadExchange,
            // "检修设备试运行申请": !!data?.workTicketExecute?.workFlowTryRecover,
            // "检修设备试运行后恢复工作申请": !!data?.workTicketExecute?.workFlowTryRunSafe,
          }}
          loading={loading}
          detailData={data}
        />

      </div>}

      <ReviewPopup
        visible={visible}
        setVisible={setVisible}
        variables={variables}
        status={status}
        detailData={data}
        activeName={activeName}
        onSubmit={onSubmit}
      />

    </div>)
};
export default ElectricalPage;
