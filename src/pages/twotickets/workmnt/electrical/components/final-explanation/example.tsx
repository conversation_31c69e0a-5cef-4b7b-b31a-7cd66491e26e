import React, { useState } from 'react';
import { Form, Button, Toast } from 'antd-mobile';
import FinalExplanation, { FinalExplanationValue } from './index';

const ExamplePage: React.FC = () => {
  const [form] = Form.useForm();

  // 初始值
  const [finalExplanationData, setFinalExplanationData] = useState<FinalExplanationValue>({
    groundWireCode: 'GW-001',
    groundWireNum: '3',
    knifeSwitch: 'KS-001',
    knifeSwitchNum: '2',
  });

  // 处理终结说明变更
  const handleFinalExplanationChange = (value: FinalExplanationValue) => {
    setFinalExplanationData(value);
    console.log('终结说明数据:', value);
  };

  // 提交表单
  const handleSubmit = () => {
    console.log('终结说明数据:', finalExplanationData);
    Toast.show({
      content: '提交成功',
      position: 'bottom',
    });
  };

  return (
    <div style={{ padding: '16px' }}>
      <h2>终结说明示例</h2>

      <Form
        form={form}
        onFinish={handleSubmit}
        layout="vertical"
        footer={
          <Button block color="primary" size="large" onClick={handleSubmit}>
            提交
          </Button>
        }
      >
        <FinalExplanation
          title="终结说明"
          value={finalExplanationData}
          onChange={handleFinalExplanationChange}
        />
      </Form>
    </div>
  );
};

export default ExamplePage; 