.final-explanation-wrapper {

  .content {
    font-size: 42px;
    line-height: 60.82px;
    color: rgba(102, 102, 102, 1);
  }

  .inline-input {
    display: inline-block;
    width: auto;
    margin: 0 0.5rem;
  }

  .input {
    border-radius: 18px;
    background: rgba(245, 245, 245, 1);
    padding: 18px 35px;
    outline: none;
    font-size: 42px;
    line-height: 46px;
    border: none;
    width: 400px;
    height: 70px;
    color: rgba(51, 51, 51, 1);
    margin-top: 24px;

    &::placeholder {
      color: #999;
    }

    &:focus {
      border-bottom-color: rgba(17, 84, 237, 1);
    }
    
    &.required {
      border: 1px solid #eee;
      
      &:focus {
        border-color: rgba(17, 84, 237, 1);
      }
      
      &:invalid {
        border-color: #ff4d4f;
      }
    }
  }
  
  .required-mark {
    color: #ff4d4f;
    margin-right: 4px;
    font-size: 42px;
    vertical-align: middle;
  }
}