/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-21 10:36:23
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-05-21 11:01:16
 * @FilePath: /mobile-yw/src/pages/twotickets/workmnt/electrical/components/final-explanation/final-explanation.component.tsx
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */
import React, { ChangeEvent, forwardRef, useImperativeHandle, useEffect, useState, useMemo } from 'react';
import styles from './final-explanation.component.less';

// 组件属性定义
export interface FinalExplanationValue {
  groundWireCode: string;
  groundWireNum: string;
  knifeSwitch: string;
  knifeSwitchNum: string;
}

export interface FinalExplanationProps {
  title?: string;
  value?: FinalExplanationValue;
  onChange?: (value: FinalExplanationValue) => void;
}

/**
 * 终结说明组件
 * 支持antd-mobile表单集成的自定义表单控件
 */
const FinalExplanation = forwardRef<any, FinalExplanationProps>((props, ref) => {
  const { 
    title = '终结说明', 
    value: propsValue,
    onChange 
  } = props;
  
  // 内部状态，确保受控和非受控模式都能正常工作
  const [internalValue, setInternalValue] = useState<FinalExplanationValue>({
    groundWireCode: '',
    groundWireNum: '',
    knifeSwitch: '',
    knifeSwitchNum: '',
  });
  
  // 合并外部传入的值和内部状态
  const value = useMemo(() => {
    return propsValue || internalValue;
  }, [propsValue, internalValue]);
  
  // 暴露给Form的ref接口
  useImperativeHandle(ref, () => ({
    getValue: () => value,
    setValue: (val: FinalExplanationValue) => {
      setInternalValue(val);
    },
  }));
  
  // 同步外部值到内部状态
  useEffect(() => {
    if (propsValue) {
      setInternalValue(propsValue);
    }
  }, [propsValue]);
  
  // 处理输入变更
  const handleInputChange = (key: keyof FinalExplanationValue, e: ChangeEvent<HTMLInputElement>) => {
    const newValue = {
      ...value,
      [key]: e.target.value,
    };
    
    setInternalValue(newValue);
    
    if (onChange) {
      onChange(newValue);
    }
  };
  
  return (
    <div className={styles['final-explanation-wrapper']}>
      <div className={styles.content}>
        <span className={styles['required-mark']}>*</span> 临时遮拦、标示牌已拆除，常设遮栏已恢复。未拆除或未拉开的接地线编号
        <span className={styles['inline-input']}>
          <input
            type="text"
            value={value.groundWireCode}
            onChange={(e: ChangeEvent<HTMLInputElement>) => handleInputChange('groundWireCode', e)}
            placeholder="请输入（必填）"
            className={`${styles.input} ${styles.required}`}
          />
        </span>
        等工
        <span className={styles['inline-input']}>
          <input
            type="text"
            value={value.groundWireNum}
            onChange={(e: ChangeEvent<HTMLInputElement>) => handleInputChange('groundWireNum', e)}
            placeholder="请输入（必填）"
            className={`${styles.input} ${styles.required}`}
          />
        </span>
        组，接地刀闸（小车）
        <span className={styles['inline-input']}>
          <input
            type="text"
            value={value.knifeSwitch}
            onChange={(e: ChangeEvent<HTMLInputElement>) => handleInputChange('knifeSwitch', e)}
            placeholder="请输入（必填）"
            className={`${styles.input} ${styles.required}`}
          />
        </span>
        共
        <span className={styles['inline-input']}>
          <input
            type="text"
            value={value.knifeSwitchNum}
            onChange={(e: ChangeEvent<HTMLInputElement>) => handleInputChange('knifeSwitchNum', e)}
            placeholder="请输入（必填）"
            className={`${styles.input} ${styles.required}`}
          />
        </span>
        副（台），已汇报值班负责人。
      </div>
    </div>
  );
});

export default FinalExplanation; 