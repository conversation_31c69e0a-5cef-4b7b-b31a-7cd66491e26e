import React, { useEffect, useImperativeHandle, useState, useMemo, useCallback } from 'react';
import { Form, Card, Ellipsis, TextArea } from 'antd-mobile';
import { useParams } from 'umi';
import styles from './safety-measure.component.less';
import { DownOutline, UpOutline } from 'antd-mobile-icons';

interface Props {
  data?: any;
}

const dutyStatus = [4, 5]; // 值班负责人，值长填写
const licenseStatus = 6; // 许可人填写

const SafetyMeasureComponent = React.forwardRef<any, Props>((props: Props, ref) => {
  const [form] = Form.useForm();
  const [form2] = Form.useForm();
  const [form3] = Form.useForm();
  const { data } = props;

  // 获取url参数
  const params = useParams<any>();
  const pageType = params?.pageType;

  const [status, setStatus] = useState(data?.status ? Number(data.status) : 0);

  useEffect(() => {
    if (data?.status) {
      setStatus(Number(data.status));
    }
  }, [data?.status]);

  // 计算只读状态
  const dutyReadonly = pageType === 'view' || !(dutyStatus.includes(status));
  const licenseReadonly = pageType === 'view' || status !== licenseStatus;

  // 提取展开/收起按钮组件
  const createExpandButton = useCallback((text: string, IconComponent: React.ComponentType) => 
    React.createElement(React.Fragment, null, text, React.createElement(IconComponent)), []);

  const expandText = useMemo(() => createExpandButton('展开', DownOutline), [createExpandButton]);
  const collapseText = useMemo(() => createExpandButton('收起', UpOutline), [createExpandButton]);

  // 提取创建 Ellipsis 组件的函数
  const createEllipsis = useCallback((content: string) => 
    React.createElement(Ellipsis, {
      direction: 'end' as const,
      rows: 3,
      content: content ?? '无',
      expandText,
      collapseText
    }), [expandText, collapseText]);

  // 提取创建 TextArea 组件的函数
  const createTextArea = useCallback((rows?: number, placeholder = '请输入') => 
    React.createElement(TextArea, { rows, placeholder }), []);

  // 提取表单项渲染函数
  const renderFormItem = useCallback((item: any, isReadonly: boolean, dataField?: string) => {
    const content = dataField 
      ? data?.workElectricalSafeMeasure?.[dataField] 
      : data?.workElectricalSafeMeasure?.[item.name];
    
    return React.createElement(Form.Item, { key: item.name, ...item }, 
      isReadonly ? createEllipsis(content) : createTextArea(item.rows || 5)
    );
  }, [data?.workElectricalSafeMeasure, createEllipsis, createTextArea]);

  // 优化后的表单配置生成函数
  const createSafetyMeasureConfig = useCallback(() => {
    const baseDisabled = pageType === 'view' || (status > 1 && status !== 3);
    
    return [
      {
        label: '应拉的断路器（开关）、隔离开关（刀闸），应解除的继电保护连接片等（包括写前已断开、取下、解除的，注明编号）',
        name: 'shouldOff',
        disabled: baseDisabled,
      },
      {
        label: '应装接地线、应合接地闸（注明确实地点、名称及接地线编号）',
        name: 'shouldConnect',
        disabled: baseDisabled,
      },
      {
        label: '应设遮拦、应挂标识牌及防止二次回路误碰等措施',
        name: 'shouldBlock',
        disabled: baseDisabled,
      },
    ];
  }, [pageType, status]);

  // 生成许可人表单配置
  const createLicenseSafetyMeasureConfig = useCallback(() => {
    const safetyMeasure = createSafetyMeasureConfig();
    const requiredRule = { required: status >= licenseStatus && pageType !== 'view', message: '此项必填' };
    
    return [
      safetyMeasure[0],
      {
        label: '已断开的断路器（开关）、隔离开关（刀闸），已取下的熔断器，已解除的继电保护连接片等',
        name: 'shouldOffExecute',
        rules: [requiredRule],
        disabled: licenseReadonly,
      },
      {
        label: '执行人',
        name: 'shouldOffUser',
        rules: [requiredRule],
        disabled: licenseReadonly,
      },
      safetyMeasure[1],
      {
        label: '已应装接地线、应合接地刀闸（注明确实地点、名称及接地线编号）',
        name: 'shouldConnectExecute',
        rules: [requiredRule],
        disabled: licenseReadonly,
      },
      {
        label: '执行人',
        name: 'shouldConnectUser',
        rules: [requiredRule],
        disabled: licenseReadonly,
      },
      safetyMeasure[2],
      {
        label: '已应设遮拦、因挂标识牌及防止二次回路误碰等措施',
        name: 'shouldBlockExecute',
        rules: [requiredRule],
        disabled: licenseReadonly,
      },
      {
        label: '执行人',
        name: 'shouldBlockUser',
        rules: [requiredRule],
        disabled: licenseReadonly,
      },
    ];
  }, [createSafetyMeasureConfig, status, licenseStatus, pageType, licenseReadonly]);

  // 计算表单数据列表
  const dataList = useMemo(() => 
    status >= 7 ? createLicenseSafetyMeasureConfig() : createSafetyMeasureConfig(), 
    [status, createLicenseSafetyMeasureConfig, createSafetyMeasureConfig]
  );

  // 提取合并表单值的函数
  const getMergedFormValues = useCallback(() => {
    const baseValues = form.getFieldsValue();
    const form2Values = status >= 2 ? form2.getFieldsValue() : {};
    const form3Values = status >= 7 ? form3.getFieldsValue() : {};
    
    return data?.workElectricalSafeMeasure 
      ? { ...data.workElectricalSafeMeasure, ...baseValues, ...form2Values, ...form3Values }
      : { ...baseValues, ...form2Values, ...form3Values };
  }, [form, form2, form3, status, data?.workElectricalSafeMeasure]);

  // 通用表单创建函数
  const createFormWithItems = useCallback((formInstance: any, children: React.ReactNode) =>
    React.createElement(Form, { form: formInstance }, children), []);

  // 通用卡片渲染函数
  const renderCard = useCallback((title: string, content: React.ReactNode, style?: React.CSSProperties) =>
    React.createElement(Card, { title, style }, content), []);

  /* 暴露的提交事件及元素 */
  useImperativeHandle(ref, () => ({
    form,
    getValue: getMergedFormValues,
  }), [getMergedFormValues]);

  useEffect(() => {
    if (data?.workElectricalSafeMeasure) {
      form.setFieldsValue(data.workElectricalSafeMeasure);
      if (status >= 2) {
        form2.setFieldsValue({ matter: data.workElectricalSafeMeasure.matter });
      }
      if (status >= 7) {
        form3.setFieldsValue({ additionalMeasure: data.workElectricalSafeMeasure.additionalMeasure });
      }
    }
  }, [data?.workElectricalSafeMeasure, status, form, form2, form3]);

  return (
    <div className={styles.container}>
      <div className={styles.moduleName}>安全措施</div>
      
      {/* 主要安全技术措施表单 */}
      {renderCard('安全技术措施',
        createFormWithItems(form,
          dataList?.map((item: any, index: number) => (
            <div key={item.name + index}>
              {renderFormItem(item, item.disabled || pageType === 'view')}
            </div>
          ))
        )
      )}

      {/* 工作地点保留带电部分表单 */}
      {status >= 2 && renderCard('',
        createFormWithItems(form2,
          React.createElement(Form.Item, {
            label: '工作地点保留带电部分或注意事项',
            name: 'matter',
            layout: 'vertical'
          },
            (pageType === 'view' || status > 3) 
              ? createEllipsis(data?.workElectricalSafeMeasure?.matter)
              : createTextArea(5)
          )
        ),
        { marginTop: '0.8rem' }
      )}

      {/* 补充安全措施表单 */}
      {status >= 7 && renderCard('',
        createFormWithItems(form3,
          React.createElement(Form.Item, {
            label: '补充工作地点保留带电部分和安全措施',
            name: 'additionalMeasure',
            layout: 'vertical'
          },
            (pageType === 'view' || status !== 7)
              ? createEllipsis(data?.workElectricalSafeMeasure?.additionalMeasure)
              : createTextArea()
          )
        ),
        { marginTop: '0.8rem' }
      )}
    </div>
  );
});

export default SafetyMeasureComponent;
