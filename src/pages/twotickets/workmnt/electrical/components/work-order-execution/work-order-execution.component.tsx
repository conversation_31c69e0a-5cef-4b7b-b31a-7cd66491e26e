/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-12 15:37:43
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-06-03 15:58:24
 * @FilePath: /mobile-yw/src/pages/twotickets/workmnt/electrical/components/work-order-execution/work-order-execution.component.tsx
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */
import { useRef, useState, forwardRef, useImperativeHandle, useMemo } from "react";
import { useParams } from 'umi';
import ChangeUser from "../../../components/changgeUser";
import MaintenanceIstructions from "../../../components/maintenanceIstructions";
import WorkMemberChange from "../../../components/workMemberChange";
import WorkTicketExtension from "../../../mechanical/tab-content/safety-analysis-voucher/components/workTicketExtension";
import Maintenance from "../../../mechanical/tab-content/safety-analysis-voucher/components/maintenance";
import MaintenanceRestore from "../../../mechanical/tab-content/safety-analysis-voucher/components/maintenanceRestore";
import WorkFlowReturns from "../../../mechanical/tab-content/safety-analysis-voucher/components/workFlowReturns";
import styles from './work-order-execution.component.less';

interface WorkOrderExecutionProps {
  data: any;
  ticketType?: string;
  actionType?: string; // 可选的操作类型，如果不传则从URL参数获取
}

/**
 * @description 电气工作票执行组件，基于机械工作票逻辑优化，包含完整的权限控制和条件渲染
 */
const WorkOrderExecution = forwardRef<any, WorkOrderExecutionProps>((props, ref) => {
  const { data, ticketType = '1', actionType: propActionType } = props; // 电气工作票默认ticketType为'1'
  const current = data;

  const { type, id, actionType: urlActionType } = useParams<{ type: string; id: string; actionType: string }>();

  // 优先使用props传入的actionType，否则使用URL参数
  const actionType = propActionType || urlActionType;

  const workMemberChangeRef = useRef<any>(null);
  const maintenanceRef = useRef<any>(null);
  const maintenanceRestoreRef = useRef<any>(null);
  const maintenanceIstructionsRef = useRef<any>(null);

  // 是否为查看模式
  const isViewMode = actionType === 'view';

  // 判断下方整体内容是否展示 - 在查看模式下，如果有工作票执行数据就显示
  const isContentShow = useMemo(() => {
    if (isViewMode) {
      // 查看模式：只要有工作票执行数据就显示
      return !!current?.workTicketExecute;
    }
    
    // 编辑模式：原有逻辑
    if (actionType === 'edit') {
      return [8, 9, 10, 11, 12, 13, 14, 15, 16, 17].includes(Number(current?.status));
    }
    
    return false;
  }, [current?.status, current?.workTicketExecute, actionType, isViewMode]);

  // 判断检修相关内容是否展示 - 在查看模式下，如果有检修相关数据就显示
  const isMaintenanceShow = useMemo(() => {
    if (isViewMode) {
      // 查看模式：如果有检修相关的执行记录数据就显示
      const hasMaintenanceData = current?.workTicketExecute?.workExecuteRecordList?.some(
        (record: any) => record.type === 1 || record.type === 2
      );
      return hasMaintenanceData;
    }
    
    // 编辑模式：原有逻辑
    if (actionType === 'edit') {
      return [8, 9, 10, 11, 12, 13, 14, 15].includes(Number(current?.status));
    }
    
    return false;
  }, [current?.status, current?.workTicketExecute?.workExecuteRecordList, actionType, isViewMode]);

  // 判断工作成员变更是否显示 - 在查看模式下，如果有成员变更数据就显示
  const isMemberChangeShow = useMemo(() => {
    if (isViewMode) {
      // 查看模式：如果有成员变更数据就显示
      return !!(current?.workTicketExecute?.workFlowExecuteUserExchanges?.length > 0);
    }
    
    // 编辑模式：原有逻辑，需要满足状态和权限条件
    return isContentShow;
  }, [current?.workTicketExecute?.workFlowExecuteUserExchanges, isViewMode, isContentShow]);

  // 判断工作成员变更新增或编辑权限
  const isMemberChange = useMemo(() => {
    if (isViewMode) return false; // 查看模式禁止编辑
    
    if (actionType === 'edit') {
      // 电气工作票在状态9时允许成员变更
      return current?.status === '9';
    }
    
    return false;
  }, [current?.status, actionType, isViewMode]);

  // 判断检修操作的编辑权限
  const isMaintenanceEditable = useMemo(() => {
    if (isViewMode) return false; // 查看模式禁止编辑
    if (actionType !== 'edit') return false;
    
    // 电气工作票在状态9时允许编辑检修内容
    return current?.status === '9';
  }, [current?.status, actionType, isViewMode]);

  // 获取工作班成员 - 优化数据处理逻辑
  const workClass = useMemo(() => {
    if (!current?.member) return [];

    const members = current.member.split(',') || [];
    return members.map((member: string) => ({
      value: member.trim(),
      label: member.trim()
    })).filter((item: { value: string; label: string }) => item.value); // 过滤空值
  }, [current?.member]);

  // 检修交代是否显示 - 在查看模式下，如果有检修交代数据就显示
  const isMaintenanceInstructionsShow = useMemo(() => {
    if (isViewMode) {
      // 查看模式：如果有检修交代相关数据就显示
      return !!(current?.workTicketExecute?.workFlowExecute?.repairOrder || 
                current?.workTicketExecute?.workFlowExecute?.canRun !== undefined);
    }
    
    // 编辑模式：原有逻辑
    return isContentShow;
  }, [current?.workTicketExecute?.workFlowExecute, isViewMode, isContentShow]);

  // 检修交代是否禁用 - 基于状态判断
  const isMaintenanceInstructionsDisabled = useMemo(() => {
    if (isViewMode) return true; // 查看模式禁用编辑
    
    const status = Number(current?.status);
    // 状态13及以上时禁用编辑
    return status >= 13;
  }, [current?.status, isViewMode]);

  useImperativeHandle(ref, () => ({
    ...maintenanceIstructionsRef?.current,

    // 保持与原有接口的兼容性
    getWorkMemberChangeRef: () => workMemberChangeRef.current,
    getMaintenanceRef: () => maintenanceRef.current,
    getMaintenanceRestoreRef: () => maintenanceRestoreRef.current,
    getMaintenanceIstructionsRef: () => maintenanceIstructionsRef.current,

    // 新增：完整的工作票执行数据获取方法（兼容机械工作票逻辑）
    getWorkTicketExecute: () => {
      const maintenanceInstructionsData = maintenanceIstructionsRef?.current?.getFieldsValue();

      // 获取检修允许试运行数据(type=1)
      const maintenanceData = maintenanceRef?.current?.getData() || [];
      // 获取检修工作恢复数据(type=2)
      const restoreData = maintenanceRestoreRef?.current?.getData() || [];

      return {
        workFlowExecute: {
          id: current?.workTicketExecute?.workFlowExecute?.id,
          mainId: current?.workTicketExecute?.workFlowExecute?.mainId,
          ...(maintenanceInstructionsData ? maintenanceInstructionsData : current?.workTicketExecute?.workFlowExecute),
        },
        workFlowExecuteUserExchanges: workMemberChangeRef?.current?.getData() || [],
        // 合并两个类型的数据，注意：各组件内部已经设置了正确的type值
        workExecuteRecordList: [
          ...maintenanceData,  // type=1 由组件内部处理
          ...restoreData       // type=2 由组件内部处理
        ]
      };
    },

    // 新增：获取所有表单数据的方法
    getAllFormData: () => ({
      maintenanceInstructions: maintenanceIstructionsRef?.current?.getFieldsValue() || {},
      workMemberChanges: workMemberChangeRef?.current?.getData() || [],
      maintenanceRecords: maintenanceRef?.current?.getData() || [],
      maintenanceRestoreRecords: maintenanceRestoreRef?.current?.getData() || [],
    }),
  }));

  return (
    <>
      <div className={styles.moduleName}>工作票执行</div>

      {/* 【工作负责人变更】- 条件渲染：有数据就显示 */}
      {current?.workTicketExecute?.workFlowExecuteHeadExchange?.memo && (
        <ChangeUser current={current} />
      )}

      {/* 【工作票延期】- 条件渲染：有数据就显示 */}
      {current?.workTicketExecute?.workFlowExecuteDelay?.memo && (
        <WorkTicketExtension current={current} />
      )}

      {/* 【每日开工和收工记录】- 条件渲染：有数据就显示 */}
      {current?.workTicketExecute?.workFlowReturns?.length > 0 && (
        <WorkFlowReturns current={current} />
      )}

      {/* 主要执行内容 - 基于权限和数据控制显示 */}
      {isContentShow && (
        <>
          {/* 【工作成员变更】- 根据数据和权限控制显示 */}
          {isMemberChangeShow && (
            <WorkMemberChange
              ref={workMemberChangeRef}
              current={current}
              isAddAndEdit={isMemberChange}
              workClass={workClass}
            />
          )}

          {/* 检修相关内容 - 基于状态和数据控制显示 */}
          {isMaintenanceShow && (
            <>
              {/* 【检修允许试运行】- 组件内部会过滤type=1的数据 */}
              <Maintenance
                ref={maintenanceRef}
                current={current}
                isAddAndEdit={isMaintenanceEditable}
              />

              {/* 【检修工作恢复】- 组件内部会过滤type=2的数据 */}
              <MaintenanceRestore
                ref={maintenanceRestoreRef}
                current={current}
                isAddAndEdit={isMaintenanceEditable}
              />
            </>
          )}

          {/* 【检修交代】- 根据数据控制显示，根据状态控制是否可编辑 */}
          {isMaintenanceInstructionsShow && (
            <MaintenanceIstructions
              ref={maintenanceIstructionsRef}
              disabled={isMaintenanceInstructionsDisabled}
              current={current}
            />
          )}
        </>
      )}
    </>
  );
});

export default WorkOrderExecution;

