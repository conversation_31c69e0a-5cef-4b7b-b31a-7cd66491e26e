import React from 'react';
import { <PERSON>, Grid, Divider, Space, List, Ellipsis } from 'antd-mobile';
import { styled } from 'umi';
import useInterleave from '@/hooks/useInterleave';
import ImageView from '@/component/image-view/image-view.component';
import { ListCard } from '@/component/list-card/list-card.component';
import { OperationFileList } from '@/styles/twotickets.style';
import { ReviewRecordComponent } from '@/pages/twotickets/common/[type]/components/review-record/review-record.component';
import { AttachmentItemRow } from '@/pages/twotickets/common/[type]/components/attachment-content/attachment-content.component';

const BasicInfoComponentWrapper = styled(Space)`
  width: 100%;
  font-family: PingFang SC;
  .adm-card-body {
    padding-bottom: 1.8rem;
  }
  .title {
    font-size: 1.6rem;
    font-weight: 500;
    letter-spacing: 0rem;
    line-height: 2.3333rem;
    color: rgba(56, 56, 56, 1);
    text-align: left;
  }
  .item {
    font-size: 1.2rem;
    font-weight: 400;
    letter-spacing: 0rem;
    line-height: 1.7377rem;
    color: rgba(153, 153, 153, 1);
    .name {
      font-size: 1.2rem;
      font-weight: 400;
      letter-spacing: 0rem;
      line-height: 1.7377rem;
      color: rgba(51, 51, 51, 1);
    }
  }
  .remark {
    font-size: 1.5333rem;
    font-weight: 400;
    letter-spacing: 0rem;
    line-height: 2.2203rem;
    color: rgba(51, 51, 51, 1);
  }
`;

type BasicInfoComponentProps = {
  data: any;
  list: any;
};

const RecordFileds = [
  {
    title: '工作票签发',
    name: 'workFlowSign',
    status: 2,
    fileds: [
      {
        title: '工作签发人',
        name: 'signName',
      },
      {
        title: '签发日期',
        name: 'signTime',
        type: 'date',
      },
      {
        title: '工作票业主签发人',
        name: 'ownerSignName',
      },
      {
        title: '签发日期',
        name: 'ownerSignTime',
        type: 'date',
      },
    ],
  },
  {
    title: '工作票确认',
    name: 'workFlowConfirm',
    status: 4,
    fileds: [
      {
        title: '工作负责人',
        name: 'headName',
      },
      {
        title: '提交工作票时间',
        name: 'submitTime',
        type: 'date',
      },
      {
        title: '值班负责人',
        name: 'dutyName',
      },
      {
        title: '收到工作票时间',
        name: 'accessTime',
        type: 'date',
      },
    ],
  },
  {
    title: '值长批准',
    name: 'workFlowRatify',
    status: 6,
    fileds: [
      {
        title: '批准工作开始时间',
        name: 'approveBeginTime',
        type: 'date',
      },
      {
        title: '批准工作结束时间',
        name: 'approveEndTime',
        type: 'date',
      },
      {
        title: '值长',
        name: 'shiftSupervisorName',
      },
      {
        title: '批准时间',
        name: 'approveTune',
        type: 'date',
      },
    ],
  },
  {
    title: '工作票许可',
    name: 'workFlowLicense',
    status: 7,
    fileds: [
      {
        title: '许可开始工作时间',
        name: 'allowBeginTime',
        type: 'date',
      },
      {
        title: '工作许可人',
        name: 'allowName',
      },
      {
        title: '工作负责人',
        name: 'headName',
      },
      {
        title: '提交时间',
        name: 'submitTime',
        type: 'date',
      },
      {
        title: '审核意见',
        name: 'opinion',
      },
    ],
  },
  {
    title: '工作票执行确认',
    name: 'workFlowExecuteConfirm',
    status: 13,
    fileds: [
      {
        title: '工作许可人',
        name: 'allowName',
      },
      {
        title: '确认时间',
        name: 'allowTime',
        type: 'date',
      },
      {
        title: '值长',
        name: 'shiftSupervisorName',
      },
      {
        title: '接收时间',
        name: 'shiftSupervisorTime',
        type: 'date',
      },
    ],
  },
  {
    title: '工作票终结',
    name: 'workFlowEnd',
    status: 16,
    fileds: [
      {
        title: '终结说明',
        name: 'endDescribe',
        type: 'textArea',
      },
      {
        title: '工作负责人',
        name: 'headName',
      },
      {
        title: '提交时间',
        name: 'endTime',
        type: 'date',
      },
      {
        title: '工作许可人',
        name: 'allowName',
      },
      {
        title: '提交时间',
        name: 'allowTime',
        type: 'date',
      },
      {
        title: '备注',
        name: 'remark',
        type: 'textArea',
      },
      {
        title: '备注',
        name: 'remark',
        type: 'textArea',
      },
      {
        title: '指定专责监护人',
        name: 'appointHeadName',
      },
      {
        title: '负责监护（地点及具体工作）',
        name: 'workMatter',
        type: 'textArea',
      },
      {
        title: '其他事项',
        name: 'appointWork',
        type: 'textArea',
      },
    ],
  },
  {
    title: '审核',
    name: 'workFlowAudit',
    status: 17,
    fileds: [
      {
        title: '审核人',
        name: 'auditName',
      },
      {
        title: '审核日期',
        name: 'auditTime',
        type: 'date',
      },
      {
        title: '审核意见',
        name: 'auditOpinion',
        type: 'textArea',
      },
      {
        title: '审核结论',
        name: 'auditResult',
        type: 'textArea',
      },
    ],
  },
];

export const BasicInfoComponent = (props: BasicInfoComponentProps) => {
  const { data, list } = props;
  const SplitPoint = (
    <div
      style={{
        display: 'inline-block',
        width: '.2667rem',
        height: '.2667rem',
        borderRadius: '.1333rem',
        margin: '0 .4rem',
        backgroundColor: 'rgba(102, 102, 102, 1)',
      }}
    />
  );

  return (
    <BasicInfoComponentWrapper direction="vertical">
      <Card>
        {list.map((item: any, index: number) => (
          <div key={item?.type + item?.value + item?.label + index}>
            {/* 加粗标题 */}
            {item?.type === 'title' ? (
              <div className="title">{item?.render ? item?.render(data?.[item?.value], data) : data?.[item?.value] || ''}</div>
            ) : null}

            {/* 分隔线 */}
            {item?.type === 'divider' ? <Divider /> : null}

            {/* 正文项 */}
            {!item?.type || item?.type === 'item' ? (
              <div className="item">
                {item?.label}：
                <span className="name">{item?.render ? item?.render(data?.[item?.value], data) : data?.[item?.value] || ''}</span>
              </div>
            ) : null}
          </div>
        ))}
        <Divider />
        <div className="remark">
          <div className="title">备注</div>
          <div className="content">{data?.remark}</div>
        </div>
      </Card>

      {data?.imgList?.length ? (
        <Card title="图片">{data?.imgList?.length ? ImageView({ imgList: data?.imgList?.map((item: any) => item.id) }) : null}</Card>
      ):null}

      {data?.files?.length ? (
        <ListCard
          title="附件"
          data={data?.files || []}
          moreLink={`/twotickets/common/check-list/attachment-content/${data.id}?ticketType=${data?.ticketType}&dataKey=files`}
          isMore
          maxLength={5}
        >
          {(dataList) => (
            <OperationFileList
              style={{
                '--border-top': 'none',
                '--border-bottom': 'none',
              }}
            >
              {dataList?.map((item: any, index: number) => (
                <AttachmentItemRow key={item.id} item={item as any} />
              ))}
            </OperationFileList>
          )}
        </ListCard>
      ):null}

      {data?.status && Number(data.status) > 2 ? (
        <ListCard
          title="流程记录"
          data={RecordFileds}
          moreLink={`/twotickets/common/check-list/review-record/${data?.id}?ticketType=${data?.ticketType}`}
          isMore
          maxLength={5}
        >
          {(dataList) => <ReviewRecordComponent recordFileds={dataList} data={data} />}
        </ListCard>
      ):null}
    </BasicInfoComponentWrapper>
  );
};
