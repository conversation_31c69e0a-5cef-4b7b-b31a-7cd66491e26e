.timeRangePickerWrapper {
  .timeSelector {
    display: flex;
    align-items: center;
    height: 8.8889vw;
    padding: 0 3.7037vw;
    background-color: #fff;
    border-radius: 1.8519vw;
    font-size: 3.7037vw;
    color: #333;

    &.placeholder {
      color: #999;
    }

    .timeRangeText {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.popupContainer {
  background-color: #fff;
  border-top-left-radius: 3.7037vw;
  border-top-right-radius: 3.7037vw;
  overflow: hidden;
}

.popupHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 12.963vw;
  padding: 0 0 0 6.6296vw;
  border-bottom: 0.2778vw solid #F5F5F5;

  .cancelBtn {
    height: 12.963vw;
    width: 12.963vw;
    text-align: center;
    line-height: 12.963vw;
    font-size: 3.7037vw;
    color: #999;
  }

  .title {
    padding-left: 12.963vw;
    text-align: center;
    flex: 1;
    font-size: 4.6296vw;
    font-weight: 500;
    color: #333;
  }
}

.popupContent {
  max-height: 100vw;
  overflow-y: auto;
}

.confirmBtn {
  width: 91.6667vw;
  height: 11.1111vw;
  line-height: 11.1111vw;
  border-radius: 5.5556vw;
  font-size: 3.8889vw;
  font-weight: 400;
  color: #fff;
  background-color: #1154ED;
  text-align: center;
  margin: 5.5556vw auto;

  &.disabled {
    background-color: #ccc;
    color: #fff;
  }
}

.errorTip {
  color: #E41412;
  font-size: 3.3333vw;
  text-align: center;
  margin-top: 2.7778vw;
}

.timeRangeHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4vw;

  .modeTypes {
    display: flex;
    gap: 2vw;

    .typeItem {
      display: inline-block;
      height: 6.6667vw;
      font-size: 3.3333vw;
      font-weight: 400;
      line-height: 6.6667vw;
      color: rgba(102, 102, 102, 1);
      text-align: center;
      padding: 0 2.6042vw;

      &.active {
        color: rgba(17, 84, 237, 1);
        border-radius: 3.3333vw;
        border: 0.3703vw solid rgba(17, 84, 237, 1);
      }
    }
  }

  .resetBtn {
    font-size: 3.8889vw;
    font-weight: 400;
    line-height: 5.3703vw;
    color: rgba(228, 20, 18, 1);
  }
}