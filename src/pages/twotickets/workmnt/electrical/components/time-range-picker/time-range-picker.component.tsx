import React, { useState, useEffect } from 'react';
import { FilterDatePicker } from '../../../../../../component/filter-datepicker/filter-datepicker.component';
import { styled } from 'umi';
import { Popup } from 'antd-mobile';
import dayjs from 'dayjs';
import styles from './time-range-picker.component.less';
import { CloseOutline } from 'antd-mobile-icons';

// 使用样式模块

export type TimeRangePickerProps = {
  /**
   * 初始模式
   */
  initialMode?: 'year' | 'month' | 'day' | 'hour' | 'minute' | 'second';
  /**
   * 当前值
   */
  value?: [number | null, number | null];
  /**
   * 自定义时间类型选项
   */
  dateTypes?: Array<{
    label: string;
    value: 'year' | 'month' | 'day' | 'hour' | 'minute' | 'second';
  }>;
  /**
   * 值改变回调
   */
  onChange?: (value: [number | null, number | null]) => void;
  /**
   * 隐藏头部
   */
  isHideHeader?: boolean;
  /**
   * 是否开启验证（检查开始和结束时间是否都已填写）
   */
  enableValidation?: boolean;
  /**
   * 自定义错误提示文本
   */
  errorText?: string;
  /**
   * 自定义日期格式，默认会根据mode自动选择
   * 例如：'YYYY-MM-DD HH:mm:ss'
   */
  format?: string;
  /**
   * 自定义分隔符，用于连接开始和结束时间
   * 默认为 ' - '
   */
  separator?: string;
};

const TimeRangePicker: React.FC<TimeRangePickerProps> = (props) => {
  const {
    initialMode = 'day',
    value,
    dateTypes = [
      { label: '年', value: 'year' },
      { label: '月', value: 'month' },
      { label: '日', value: 'day' },
      { label: '时', value: 'hour' },
      { label: '分', value: 'minute' }
    ],
    onChange,
    isHideHeader = false,
    enableValidation = false,
    errorText = '请选择开始时间和结束时间',
    format,
    separator = ' - '
  } = props;

  // 控制弹出层显示
  const [visible, setVisible] = useState(false);
  // 显示验证错误
  const [showError, setShowError] = useState(false);
  // 临时存储的时间范围，用于在确认前保存选择的值
  const [tempTimeRange, setTempTimeRange] = useState<{
    startDate: number | null;
    endDate: number | null;
    mode: 'year' | 'month' | 'day' | 'hour' | 'minute' | 'second';
  }>({
    startDate: value?.[0] || null,
    endDate: value?.[1] || null,
    mode: initialMode
  });

  // 实际应用的时间范围
  const [timeRange, setTimeRange] = useState<{
    startDate: number | null;
    endDate: number | null;
    mode: 'year' | 'month' | 'day' | 'hour' | 'minute' | 'second';
  }>({
    startDate: value?.[0] || null,
    endDate: value?.[1] || null,
    mode: initialMode
  });

  useEffect(() => {
    if (value) {
      setTimeRange(prev => ({
        ...prev,
        startDate: value[0] || null,
        endDate: value[1] || null
      }));
      setTempTimeRange(prev => ({
        ...prev,
        startDate: value[0] || null,
        endDate: value[1] || null
      }));
    }
  }, [value]);

  // 临时时间范围变化处理函数
  const handleTempTimeRangeChange = (value: {
    startDate: number | null;
    endDate: number | null;
    mode: 'year' | 'month' | 'day' | 'hour' | 'minute' | 'second';
  }) => {
    // 有开始和结束时间时，自动排序，保证开始时间小于结束时间
    if (value.startDate && value.endDate) {
      // 如果开始时间大于结束时间，则交换
      if (value.startDate > value.endDate) {
        setTempTimeRange({
          ...value,
          startDate: value.endDate,
          endDate: value.startDate
        });
        return;
      }
    }

    // 重置错误状态
    if (showError) {
      setShowError(false);
    }

    setTempTimeRange(value);
  };

  // 确认选择的时间范围
  const handleConfirm = () => {
    // 如果开启了验证，并且开始或结束时间有一个未设置，则显示错误提示
    if (enableValidation && (!tempTimeRange.startDate || !tempTimeRange.endDate)) {
      setShowError(true);
      return;
    }

    // 无需再格式化时间为字符串
    // const formattedStartDate = formatSingleTime(tempTimeRange.startDate, tempTimeRange.mode);
    // const formattedEndDate = formatSingleTime(tempTimeRange.endDate, tempTimeRange.mode);

    setTimeRange(tempTimeRange);
    // 返回数组格式的时间戳
    onChange?.([tempTimeRange.startDate, tempTimeRange.endDate]);
    setVisible(false);
    setShowError(false);
  };

  // 取消选择
  const handleCancel = () => {
    // 恢复为之前的值
    setTempTimeRange(timeRange);
    setVisible(false);
    setShowError(false);
  };

  const handleCustomHeader = ({
    dateTypeOptions,
    onReset,
    onModeChange
  }: {
    dateTypeOptions: Array<{ label: string; value: string }>;
    onReset: () => void;
    onModeChange: (mode: 'year' | 'month' | 'day' | 'hour' | 'minute' | 'second') => void;
  }) => {
    return (
      <div className={styles.timeRangeHeader}>
        <div className={styles.modeTypes}>
          {dateTypes.map(item => (
            <span
              key={item.value}
              className={`${styles.typeItem} ${timeRange.mode === item.value ? styles.active : ''}`}
              onClick={() => onModeChange(item.value)}
            >
              {item.label}
            </span>
          ))}
        </div>
        <div className={styles.resetBtn} onClick={onReset}>
          重置
        </div>
      </div>
    );
  };

  /**
   * 获取适合当前模式的日期格式
   */
  const getFormatByMode = (mode: 'year' | 'month' | 'day' | 'hour' | 'minute' | 'second'): string => {
    if (format) {
      return format;
    }

    switch (mode) {
      case 'year':
        return 'YYYY';
      case 'month':
        return 'YYYY-MM';
      case 'day':
        return 'YYYY-MM-DD';
      case 'hour':
        return 'YYYY-MM-DD HH';
      case 'minute':
        return 'YYYY-MM-DD HH:mm';
      case 'second':
        return 'YYYY-MM-DD HH:mm:ss';
      default:
        return 'YYYY-MM-DD';
    }
  };

  /**
   * 格式化单个时间（仅用于显示）
   */
  const formatSingleTime = (timestamp: number | null, mode: 'year' | 'month' | 'day' | 'hour' | 'minute' | 'second'): string => {
    if (!timestamp) return '';
    const dateFormat = getFormatByMode(mode);
    return dayjs(timestamp).format(dateFormat);
  };

  // 格式化显示时间范围文本
  const formatTimeRangeText = () => {
    if (!timeRange.startDate && !timeRange.endDate) {
      return '请选择';
    }

    const startStr = formatSingleTime(timeRange.startDate, timeRange.mode);
    const endStr = formatSingleTime(timeRange.endDate, timeRange.mode);

    return `${startStr}${separator}${endStr}`;
  };

  return (
    <div className={styles.timeRangePickerWrapper}>
      {/* 显示选择器 */}
      <div
        className={`${styles.timeSelector} ${!timeRange.startDate && !timeRange.endDate ? styles.placeholder : ''}`}
        onClick={() => setVisible(true)}
      >
        <span className={styles.timeRangeText}>{formatTimeRangeText()}</span>
      </div>

      {/* 弹出层 */}
      <Popup
        visible={visible}
        onMaskClick={handleCancel}
        bodyStyle={{ borderTopLeftRadius: '12px', borderTopRightRadius: '12px' }}
      >
        <div className={styles.popupContainer}>
          <div className={styles.popupHeader}>
            <div className={styles.title}>选择时间</div>
            <div className={styles.cancelBtn} onClick={handleCancel}><CloseOutline /></div>
          </div>

          <div className={styles.popupContent}>
            <FilterDatePicker
              initialMode={initialMode}
              initValues={{
                startDate: value?.[0] || null,
                endDate: value?.[1] || null
              }}
              isHideHeader={isHideHeader}
              value={tempTimeRange}
              onChange={handleTempTimeRangeChange}
              headerRender={isHideHeader ? undefined : handleCustomHeader}
            />
          </div>

          {/* 显示错误提示 */}
          {showError && (
            <div className={styles.errorTip}>{errorText}</div>
          )}

          {/* 确认按钮放在picker下方 */}
          <div
            className={`${styles.confirmBtn} ${enableValidation && (!tempTimeRange.startDate || !tempTimeRange.endDate) ? styles.disabled : ''}`}
            onClick={handleConfirm}
          >
            确定
          </div>
        </div>
      </Popup>
    </div>
  );
};

export default TimeRangePicker;