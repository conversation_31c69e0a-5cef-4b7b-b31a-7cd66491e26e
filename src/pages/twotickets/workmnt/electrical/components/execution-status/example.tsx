import React, { useState } from 'react';
import { Form, Button, Toast } from 'antd-mobile';
import ExecutionStatus, { ExecutionItem, ExecutionMode } from './index';

const ExamplePage: React.FC = () => {
  const [form] = Form.useForm();
  
  // 模拟数据
  const [executionData, setExecutionData] = useState<ExecutionItem[]>([
    { id: '1', content: '检查设备外观是否完好，无破损', executed: false },
    { id: '2', content: '检查电气连接是否牢固，无松动', executed: false },
    { id: '3', content: '检查接地线是否连接正确，无断裂', executed: false },
    { id: '4', content: '检查绝缘电阻是否符合要求，无老化现象', executed: false },
    { id: '5', content: '检查保护装置是否动作灵敏，无卡滞', executed: false },
    { id: '6', content: '检查设备运行温度是否正常，无过热现象。这是一段很长的文本，用来测试超出两行时的展开功能，看看效果如何，是否符合预期要求。', executed: false },
  ]);
  
  // 执行模式
  const [mode, setMode] = useState<ExecutionMode>('checkbox');
  
  // 处理执行情况变更
  const handleExecutionChange = (newData: ExecutionItem[]) => {
    setExecutionData(newData);
  };
  
  // 切换模式
  const toggleMode = () => {
    setMode(mode === 'checkbox' ? 'actionSheet' : 'checkbox');
  };
  
  // 提交表单
  const handleSubmit = () => {
    console.log('执行情况数据:', executionData);
    Toast.show({
      content: '提交成功',
      position: 'bottom',
    });
  };
  
  return (
    <div style={{ padding: '16px' }}>
      <Button 
        onClick={toggleMode} 
        style={{ marginBottom: '16px' }}
      >
        切换模式: {mode === 'checkbox' ? 'Checkbox' : 'ActionSheet'}
      </Button>
      
      <Form form={form} onFinish={handleSubmit}>
        <Form.Item name="executionStatus" label="执行情况">
          <ExecutionStatus 
            title="安全检查执行情况" 
            value={executionData} 
            onChange={handleExecutionChange}
            maxDisplayRows={3}
            mode={mode}
          />
        </Form.Item>
        
        <Button block type="submit" color="primary" style={{ marginTop: '20px' }}>
          提交
        </Button>
      </Form>
    </div>
  );
};

export default ExamplePage;