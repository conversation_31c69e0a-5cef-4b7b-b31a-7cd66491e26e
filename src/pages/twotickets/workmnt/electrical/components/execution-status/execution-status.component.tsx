import React, { useRef, useState } from 'react';
import { Card, Space, Ellipsis, ActionSheet } from 'antd-mobile';
import { DownOutline, UpOutline } from 'antd-mobile-icons';
import { styled } from 'umi';
import styles from './execution-status.component.less';
import CustomCheckbox from './custom-checkbox';
import type { ActionSheetShowHandler } from 'antd-mobile/es/components/action-sheet';

// 组件样式
const ExecutionStatusWrapper = styled.div`
  width: 100%;
  font-family: PingFang SC;
  
  .title {
    font-size: 1.6rem;
    font-weight: 500;
    letter-spacing: 0rem;
    line-height: 2.3333rem;
    color: rgba(56, 56, 56, 1);
    text-align: left;
    margin-bottom: 1rem;
  }
  
  .table-header {
    display: grid;
    grid-template-columns: 3fr 1fr;
    padding: .7667rem 0;
    background-color: #f5f5f5;
    border-radius: 0.5rem;
    margin-bottom: 0.5rem;
    
    .header-item {
      text-align: center;
      font-size: 1.4rem;
      font-weight: 500;
      color: rgba(17, 84, 237, 1);

      &:first-child {
        padding: 0 1.1667rem;
        text-align: left;
      }
    }
  }
  
  .table-row {
    display: grid;
    grid-template-columns: 3fr 1fr;
    padding: 0.8rem 0;
    border-bottom: 1px solid #f0f0f0;
    
    .content-cell {
      padding: 1.2333rem 1.6667rem ;
      font-size: 1.3rem;
      line-height: 1.8rem;
    }
    
    .checkbox-cell {
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  
  .footer {
    padding: 1rem 0;
    text-align: center;
    color: #1154ED;
    font-size: 1.4rem;
    
    .count {
      margin-left: 0.5rem;
    }
  }
`;

// 执行项类型定义
export interface ExecutionItem {
  id: string;
  content: string;
  executed: boolean;
}

// 执行模式类型
export type ExecutionMode = 'checkbox' | 'actionSheet';

// 组件属性定义
export interface ExecutionStatusProps {
  title?: string;
  value: ExecutionItem[];
  onChange?: (value: ExecutionItem[]) => void;
  maxDisplayRows?: number;
  mode?: ExecutionMode;
}

/**
 * 执行情况组件
 * @param props 
 * @returns 
 */
const ExecutionStatus: React.FC<ExecutionStatusProps> = (props) => {
  const { 
    title = '执行情况', 
    value = [], 
    onChange,
    maxDisplayRows = 3,
    mode = 'checkbox'
  } = props;
  console.log("🚀 ~ props:", props)
  
  const [expanded, setExpanded] = useState(false);
  
  // 处理执行状态变更
  const handleExecutionChange = (id: string, executed: boolean) => {
    if (!onChange) return;
    
    const newData = value.map((item: ExecutionItem) => {
      if (item.id === id) {
        return { ...item, executed };
      }
      return item;
    });
    
    onChange(newData);
  };
  
  // 显示的数据
  const displayData = expanded ? value : value.slice(0, maxDisplayRows);
  
  // 是否需要显示展开/收起
  const showExpand = value.length > maxDisplayRows;
  
  const handler = useRef<ActionSheetShowHandler>()
  return (
    <ExecutionStatusWrapper className={styles['execution-status-wrapper']}>
      {title && <div className="title">{title}</div>}
      <Card>
        <div className="table-header">
          <div className="header-item">需恢复的安全措施</div>
          <div className="header-item">执行情况</div>
        </div>
        
        {displayData.map((item: ExecutionItem) => (
          <div key={item.id} className="table-row">
            <div className="content-cell">
              <Ellipsis
                direction="end"
                rows={2}
                content={item.content}
                expandText="展开"
                collapseText="收起"
              />
            </div>
            <div 
              className="checkbox-cell"
              onClick={() => {
                if (mode === 'actionSheet') {
                  // ActionSheet 模式
                  handler.current = ActionSheet.show({
                    actions: [
                      { 
                        text: (
                          <span>
                            已执行 {item.executed && <span className={styles.currentTag}>当前</span>}
                          </span>
                        ), 
                        key: 'executed' 
                      },
                      { 
                        text: (
                          <span>
                            未执行 {!item.executed && <span className={styles.currentTag}>当前</span>}
                          </span>
                        ), 
                        key: 'unexecuted' 
                      },
                    ],
                    cancelText: '取消',
                    onAction: (action) => {
                      if (action.key === 'executed') {
                        handleExecutionChange(item.id, true);
                      } else if (action.key === 'unexecuted') {
                        handleExecutionChange(item.id, false);
                      }
                      handler.current?.close()
                    },
                  });
                }
              }}
            >
              {mode === 'checkbox' ? (
                <CustomCheckbox
                  checked={item.executed}
                  onChange={(checked) => handleExecutionChange(item.id, checked)}
                />
              ) : (
                <div className={styles.statusText}>
                  {item.executed ? '已执行' : '未执行'} <DownOutline />
                </div>
              )}
            </div>
          </div>
        ))}
        
        {showExpand && (
          <div 
            className="footer"
            onClick={() => setExpanded(!expanded)}
          >
            <Space>
              {!expanded ? (
                <>
                  更多
                  <span className="count">（{value.length}）</span>
                  <DownOutline />
                </>
              ) : (
                <>
                  收起
                  <UpOutline />
                </>
              )}
            </Space>
          </div>
        )}
      </Card>
    </ExecutionStatusWrapper>
  );
};

export default ExecutionStatus;