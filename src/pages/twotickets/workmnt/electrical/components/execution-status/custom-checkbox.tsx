import React from 'react';
import { styled } from 'umi';

interface CustomCheckboxProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
}

interface CheckboxWrapperProps {
  $checked: boolean;
}

const CheckboxWrapper = styled.div<CheckboxWrapperProps>`
  width: 20px;
  height: 20px;
  border: 1px solid #ccc;
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background-color: ${props => props.$checked ? '#1154ED' : '#fff'};
  
  .checkmark {
    color: white;
    font-size: 14px;
    line-height: 1;
  }
`;

const CustomCheckbox: React.FC<CustomCheckboxProps> = ({ checked, onChange }) => {
  return (
    <CheckboxWrapper 
      $checked={checked} 
      onClick={() => onChange(!checked)}
    >
      {checked && <span className="checkmark">✓</span>}
    </CheckboxWrapper>
  );
};

export default CustomCheckbox; 