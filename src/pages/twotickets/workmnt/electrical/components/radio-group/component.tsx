import React, { useEffect, useState } from 'react';
import { Radio, Space, Form } from 'antd-mobile';
import styles from './index.less';

export interface RadioOption {
  value: string | number;
  label: string;
  disabled?: boolean;
  [key: string]: any;
}

export interface RadioGroupProps {
  value?: string | number | (string | number)[];  // 选中的值，单选为单个值，多选为数组
  onChange?: (value: string | number | (string | number)[]) => void;  // 选中回调
  options: RadioOption[];  // 选项数据
  multiple?: boolean;  // 是否多选，默认为false
  disabled?: boolean;  // 是否禁用整个组件
  readonly?: boolean;  // 是否只读
  formItemProps?: any;  // Form.Item的属性
  direction?: 'horizontal' | 'vertical';  // 排列方向
  gap?: number;  // 间距
  labelFormat?: (label: string, option: RadioOption) => React.ReactNode;  // 格式化label
  valueFormat?: (value: string | number) => string | number;  // 格式化value
}

const RadioGroup: React.FC<RadioGroupProps> = ({
  value,
  onChange,
  options = [],
  multiple = false,
  disabled = false,
  readonly = false,
  direction = 'vertical',
  gap = 24,
  labelFormat,
  valueFormat,
  formItemProps,
}) => {
  // 处理选中值，转换为数组便于处理
  const [selectedValues, setSelectedValues] = useState<(string | number)[]>(
    multiple 
      ? (Array.isArray(value) ? value as (string | number)[] : value ? [value as string | number] : []) 
      : (value ? [value as string | number] : [])
  );

  // 根据props更新内部状态
  useEffect(() => {
    if (multiple) {
      setSelectedValues(
        Array.isArray(value) 
          ? value as (string | number)[] 
          : value ? [value as string | number] : []
      );
    } else {
      setSelectedValues(value ? [value as string | number] : []);
    }
  }, [value, multiple]);

  // 处理选中/取消选中
  const handleChange = (val: string | number) => {
    if (disabled || readonly) return;

    let newValues: (string | number)[];
    
    // 格式化值（如果有格式化函数）
    const formattedVal = valueFormat ? valueFormat(val) : val;

    if (multiple) {
      // 多选模式
      if (selectedValues.includes(formattedVal)) {
        // 如果已经选中，则取消选中
        newValues = selectedValues.filter(v => v !== formattedVal);
      } else {
        // 如果未选中，则添加到选中列表
        newValues = [...selectedValues, formattedVal];
      }
    } else {
      // 单选模式
      newValues = [formattedVal];
    }

    // 更新内部状态
    setSelectedValues(newValues);
    
    // 触发外部onChange
    if (onChange) {
      onChange(multiple ? newValues : newValues[0]);
    }
  };

  // 检查选项是否被选中
  const isChecked = (val: string | number) => {
    const formattedVal = valueFormat ? valueFormat(val) : val;
    return selectedValues.includes(formattedVal);
  };

  // 渲染Radio选项
  const renderRadioOptions = () => {
    return options.map((option) => {
      const { value: optionValue, label, disabled: optionDisabled, ...restProps } = option;
      const isOptionDisabled = disabled || optionDisabled;
      const labelNode = labelFormat ? labelFormat(label, option) : label;

      return (
        <div 
          key={optionValue} 
          className={`${styles.radioItem} ${isOptionDisabled ? styles.disabled : ''} ${readonly ? styles.readonly : ''}`}
        >
          <Radio
            value={optionValue}
            checked={isChecked(optionValue)}
            disabled={isOptionDisabled}
            onChange={() => handleChange(optionValue)}
            style={{ 
              pointerEvents: readonly ? 'none' : 'auto',
              opacity: isOptionDisabled || readonly ? 0.6 : 1
            }}
            {...restProps}
          >
            {labelNode}
          </Radio>
        </div>
      );
    });
  };

  // 如果需要作为表单项使用
  if (formItemProps) {
    return (
      <Form.Item {...formItemProps}>
        <Space direction={direction} style={{ width: '100%', '--gap': `${gap}px` }} block>
          {renderRadioOptions()}
        </Space>
      </Form.Item>
    );
  }

  // 普通使用模式
  return (
    <Space direction={direction} style={{ width: '100%', '--gap': `${gap}px` }} block>
      {renderRadioOptions()}
    </Space>
  );
};

export default RadioGroup; 