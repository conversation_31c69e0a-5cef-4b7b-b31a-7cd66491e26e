import React, { useState } from 'react';
import { Form, Button } from 'antd-mobile';
import RadioGroup from './index';
import type { RadioOption } from './index';

// 单选选项
const singleOptions: RadioOption[] = [
  { value: '1', label: '选项一' },
  { value: '2', label: '选项二' },
  { value: '3', label: '选项三', disabled: true }
];

// 多选选项
const multipleOptions: RadioOption[] = [
  { value: 'a', label: '多选A' },
  { value: 'b', label: '多选B' },
  { value: 'c', label: '多选C', disabled: true }
];

// 自定义格式化函数
const customLabelFormat = (label: string, option: RadioOption) => {
  return (
    <div style={{ display: 'flex', flexDirection: 'column' }}>
      <span>{label}</span>
      <span style={{ fontSize: '12px', color: '#999' }}>
        {option.description || '暂无描述'}
      </span>
    </div>
  );
};

// 带描述的选项
const optionsWithDesc: RadioOption[] = [
  { value: 'x', label: '选项X', description: '这是选项X的描述' },
  { value: 'y', label: '选项Y', description: '这是选项Y的描述' },
  { value: 'z', label: '选项Z', description: '这是选项Z的描述' }
];

const RadioGroupDemo: React.FC = () => {
  // 单选状态
  const [singleValue, setSingleValue] = useState<string | number>('1');
  // 多选状态
  const [multipleValue, setMultipleValue] = useState<(string | number)[]>(['a', 'b']);
  // 表单实例
  const [form] = Form.useForm();

  const onFinish = (values: any) => {
    console.log('表单提交的值:', values);
  };

  return (
    <div style={{ padding: '16px' }}>
      <h2>RadioGroup组件示例</h2>
      
      <h3>1. 基础单选</h3>
      <RadioGroup 
        options={singleOptions} 
        value={singleValue} 
        onChange={(val) => {
          console.log('单选值变化:', val);
          setSingleValue(val as string | number);
        }} 
      />

      <h3>2. 水平排列</h3>
      <RadioGroup 
        options={singleOptions} 
        value={singleValue} 
        onChange={(val) => setSingleValue(val as string | number)} 
        direction="horizontal"
      />

      <h3>3. 多选模式</h3>
      <RadioGroup 
        options={multipleOptions} 
        value={multipleValue} 
        onChange={(val) => {
          console.log('多选值变化:', val);
          setMultipleValue(val as (string | number)[]);
        }} 
        multiple 
      />

      <h3>4. 禁用状态</h3>
      <RadioGroup 
        options={singleOptions} 
        value={singleValue} 
        disabled 
      />

      <h3>5. 只读状态</h3>
      <RadioGroup 
        options={singleOptions} 
        value={singleValue} 
        readonly 
      />

      <h3>6. 自定义格式化</h3>
      <RadioGroup 
        options={optionsWithDesc} 
        value="x" 
        labelFormat={customLabelFormat} 
      />

      <h3>7. 表单中使用</h3>
      <div>
        <RadioGroup 
          options={singleOptions} 
          formItemProps={{
            name: 'radioValue',
            label: '选择项目',
            rules: [{ required: true, message: '请选择一个选项' }],
          }} 
        />
        <div style={{ marginTop: '16px' }}>
          <button 
            style={{ 
              width: '100%', 
              background: '#1677ff', 
              color: 'white',
              padding: '8px',
              border: 'none',
              borderRadius: '4px'
            }}
            onClick={() => {
              console.log('表单数据:', form.getFieldsValue());
            }}
          >
            提交
          </button>
        </div>
      </div>
    </div>
  );
};

export default RadioGroupDemo; 