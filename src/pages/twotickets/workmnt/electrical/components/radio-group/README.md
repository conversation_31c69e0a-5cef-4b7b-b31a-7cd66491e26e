# RadioGroup 组件

一个功能强大的单选/多选表单组件。

## 功能特点

- 支持单选和多选模式
- 支持自定义格式化 label 和 value
- 支持禁用(disabled)和只读(readonly)模式 
- 支持水平和垂直排列
- 可以作为独立组件使用，也可以集成到表单中使用

## 安装

无需单独安装，该组件是项目内部组件。

## 使用示例

```jsx
import { RadioGroup } from '@/components/radio-group';

// 单选模式
const SingleSelect = () => {
  const [value, setValue] = useState('1');
  const options = [
    { value: '1', label: '选项一' },
    { value: '2', label: '选项二' },
    { value: '3', label: '选项三', disabled: true }
  ];

  return (
    <RadioGroup
      options={options}
      value={value}
      onChange={(val) => setValue(val)}
    />
  );
};

// 多选模式
const MultipleSelect = () => {
  const [values, setValues] = useState(['a', 'b']);
  const options = [
    { value: 'a', label: '选项A' },
    { value: 'b', label: '选项B' },
    { value: 'c', label: '选项C' }
  ];

  return (
    <RadioGroup
      options={options}
      value={values}
      onChange={(val) => setValues(val)}
      multiple
    />
  );
};

// 在表单中使用
const FormExample = () => {
  const [form] = Form.useForm();

  const onFinish = (values) => {
    console.log(values);
  };

  return (
    <Form form={form} onFinish={onFinish}>
      <RadioGroup
        options={options}
        formItemProps={{
          name: 'radioValue',
          label: '选择项目',
          rules: [{ required: true, message: '请选择一个选项' }]
        }}
      />
      <Button type="submit">提交</Button>
    </Form>
  );
};
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| value | 当前选中的值，单选为单个值，多选为数组 | `string \| number \| (string \| number)[]` | - |
| onChange | 选中值改变的回调函数 | `(value: string \| number \| (string \| number)[]) => void` | - |
| options | 选项数据 | `RadioOption[]` | `[]` |
| multiple | 是否多选 | `boolean` | `false` |
| disabled | 是否禁用整个组件 | `boolean` | `false` |
| readonly | 是否只读 | `boolean` | `false` |
| direction | 排列方向 | `'horizontal' \| 'vertical'` | `'vertical'` |
| gap | 选项间距 | `number` | `8` |
| labelFormat | 自定义格式化label的函数 | `(label: string, option: RadioOption) => React.ReactNode` | - |
| valueFormat | 自定义格式化value的函数 | `(value: string \| number) => string \| number` | - |
| formItemProps | Form.Item的属性，当在表单中使用时设置 | `object` | - |

### RadioOption 类型

```ts
interface RadioOption {
  value: string | number;  // 选项值
  label: string;           // 选项标签
  disabled?: boolean;      // 是否禁用单个选项
  [key: string]: any;      // 其他自定义属性
}
```

## 注意事项

1. 在多选模式下，`value`需要传入数组类型，`onChange`回调也会返回数组
2. 在表单中使用时，需要传入`formItemProps`属性
3. 当设置`disabled`为`true`时，整个组件会被禁用
4. 当设置`readonly`为`true`时，组件可见但不可操作 