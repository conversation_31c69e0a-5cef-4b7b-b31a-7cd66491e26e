import React, { forwardRef, useImperative<PERSON>andle, useState, useMemo } from 'react';
import { Form, Popup, Button, Radio, Space } from 'antd-mobile';
import { CloseOutline, RightOutline, CloseCircleFill, CheckCircleFill, CheckCircleOutline, CheckOutline } from 'antd-mobile-icons';
import useDict from '@/hooks/useDict';
import styles from './group-picker.component.less';

// 自定义复选框组件
const CustomCheckbox: React.FC<{
  checked: boolean;
  onChange: (checked: boolean) => void;
  children?: React.ReactNode;
}> = ({ checked, onChange, children }) => {
  return (
    <div className={styles.customCheckbox} onClick={() => onChange(!checked)}>
      <div className={`${styles.checkbox} ${checked ? styles.checked : ''}`}>
        {checked && <span className={styles.checkmark}><CheckOutline /></span>}
      </div>
      <div className={styles.checkboxLabel}>{children}</div>
    </div>
  );
};

export interface GroupItem {
  value: string | number;
  label: string;
  [key: string]: any;
}

export interface GroupPickerProps {
  /**
   * 表单项标签
   */
  label?: string;
  /**
   * 表单项名称
   */
  name?: string;
  /**
   * 是否包裹Form.Item
   * @default true
   */
  wrapFormItem?: boolean;
  /**
   * Form.Item的其他属性
   */
  formItemProps?: Record<string, any>;
  /**
   * 占位符文本
   */
  placeholder?: string;
  /**
   * 标题文本
   */
  title?: string;
  /**
   * 确认按钮文本
   */
  confirmText?: string;
  /**
   * 是否支持多选
   * @default false
   */
  multiple?: boolean;
  /**
   * 自定义渲染函数
   */
  render?: (value: GroupItem[]) => React.ReactNode;
  /**
   * 值改变回调
   */
  onChange?: (value: string | GroupItem[]) => void;
  /**
   * 当前值
   */
  value?: string | GroupItem[];
}

export interface GroupPickerRef {
  /**
   * 打开选择器
   */
  open: () => void;
  /**
   * 关闭选择器
   */
  close: () => void;
}

const GroupPicker = forwardRef<GroupPickerRef, GroupPickerProps>((props, ref) => {
  const {
    placeholder = '请选择',
    title = '选择班组',
    confirmText = '确认选择',
    onChange,
    value,
    multiple = false,
    render
  } = props;

  console.log("🚀 ~ GroupPicker ~ props:", value)
  // 控制弹出层显示
  const [visible, setVisible] = useState(false);
  
  // 获取班组字典数据
  const { dictOptData } = useDict('maintenance-group');
  
  // 处理多选和单选的值转换
  const [selectedItems, setSelectedItems] = useState<GroupItem[]>([]);
  const [tempSelectedItems, setTempSelectedItems] = useState<GroupItem[]>([]);
  
  // 初始化选中项
  useMemo(() => {
    if (!value) {
      setSelectedItems([]);
      setTempSelectedItems([]);
      return;
    }
    
    if (typeof value === 'string') {
      // 单选模式，值是字符串
      // 尝试通过value匹配
      let item = dictOptData.find(item => item.value === value);
      
      // 如果通过value没找到，尝试通过label匹配
      if (!item) {
        item = dictOptData.find(item => item.label === value);
      }
      
      if (item) {
        setSelectedItems([item]);
        setTempSelectedItems([item]);
      }
    } else if (Array.isArray(value)) {
      // 多选模式，值是数组
      // 检查每个项是否为对象格式
      const processedItems: GroupItem[] = [];
      
      value.forEach(val => {
        if (typeof val === 'object' && val !== null) {
          // 已经是对象格式，检查是否有value和label属性
          if ('value' in val && 'label' in val) {
            processedItems.push(val as GroupItem);
          }
        } else if (typeof val === 'string' || typeof val === 'number') {
          // 是字符串或数字，尝试从字典中匹配
          let item = dictOptData.find(item => item.value === val);
          
          // 如果通过value没找到，尝试通过label匹配
          if (!item) {
            item = dictOptData.find(item => item.label === val);
          }
          
          if (item) {
            processedItems.push(item);
          }
        }
      });
      
      setSelectedItems(processedItems);
      setTempSelectedItems(processedItems);
    }
  }, [value, dictOptData]);

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    open: () => setVisible(true),
    close: () => setVisible(false)
  }));

  // 处理确认选择
  const handleConfirm = () => {
    if (multiple) {
      // 多选模式，返回选中项数组
      onChange?.(tempSelectedItems);
    } else {
      // 单选模式，返回选中项的值
      const selectedValue = tempSelectedItems.length > 0 ? String(tempSelectedItems[0].value) : '';
      onChange?.(selectedValue);
    }
    setSelectedItems(tempSelectedItems);
    setVisible(false);
  };

  // 处理取消选择
  const handleCancel = () => {
    setTempSelectedItems([...selectedItems]);
    setVisible(false);
  };
  
  // 处理单选项选择
  const handleSingleSelect = (val: string | number) => {
    const item = dictOptData.find(item => item.value === val);
    if (item) {
      setTempSelectedItems([item as GroupItem]);
    }
  };
  
  // 处理多选项选择
  const handleMultipleSelect = (val: string | number, checked: boolean) => {
    const item = dictOptData.find(item => item.value === val);
    if (!item) return;
    
    if (checked) {
      // 添加选中项
      setTempSelectedItems(prev => [...prev, item as GroupItem]);
    } else {
      // 移除选中项
      setTempSelectedItems(prev => prev.filter(i => i.value !== val));
    }
  };
  
  // 处理移除选中项
  const handleRemoveItem = (itemToRemove: GroupItem) => {
    const newItems = selectedItems.filter(item => item.value !== itemToRemove.value);
    setSelectedItems(newItems);
    setTempSelectedItems(newItems);
    
    if (multiple) {
      onChange?.(newItems);
    } else {
      onChange?.('');
    }
  };
  
  // 渲染选择器显示内容
  const renderSelector = () => {
    // 如果提供了自定义渲染函数
    if (render) {
      return (
        <div onClick={() => setVisible(true)}>
          {render(selectedItems)}
        </div>
      );
    }
    
    // 多选模式且有选中项
    if (multiple && selectedItems.length > 0) {
      return (
        <div 
          className={styles.groupSelector}
          onClick={() => setVisible(true)}
        >
          <div className={styles.multipleSelector}>
            <Space wrap>
              {selectedItems.map(item => (
                <div key={item.value} className={styles.groupTag}>
                  {item.label} 
                  <CloseCircleFill 
                    className={styles.removeIcon}
                    onClick={(e: React.MouseEvent) => {
                      e.stopPropagation();
                      handleRemoveItem(item);
                    }} 
                  />
                </div>
              ))}
            </Space>
            {/* <div className={styles.selectBtn} onClick={(e: React.MouseEvent) => {
              e.stopPropagation();
              setVisible(true);
            }}>
              + 选择
            </div> */}
          </div>
        </div>
      );
    }
    
    // 单选模式或无选中项
    const displayText = selectedItems.length > 0 
      ? selectedItems[0].label 
      : placeholder;
      
    return (
      <div 
        className={styles.groupSelector}
        onClick={() => setVisible(true)}
      >
        <span className={selectedItems.length > 0 ? styles.value : styles.placeholder}>
          {displayText}
        </span>
      </div>
    );
  };

  return (
    <>
      {/* 显示选择器 */}
      {renderSelector()}
      
      {/* 弹出层 */}
      <Popup
        visible={visible}
        onMaskClick={handleCancel}
        bodyStyle={{ borderTopLeftRadius: '12px', borderTopRightRadius: '12px' }}
      >
        <div className={styles.popupContainer}>
          <div className={styles.popupHeader}>
            <div className={styles.title}>{title}</div>
            <div className={styles.closeBtn} onClick={handleCancel}>
              <CloseOutline />
            </div>
          </div>
          
          <div className={styles.groupList}>
            {multiple ? (
              // 多选模式
              <div>
                {dictOptData.map(item => {
                  const isChecked = tempSelectedItems.some(i => i.value === item.value);
                  return (
                    <div key={item.value} className={styles.groupItem}>
                      <CustomCheckbox
                        checked={isChecked}
                        onChange={(checked) => handleMultipleSelect(item.value, checked)}
                      >
                        {item.label}
                      </CustomCheckbox>
                    </div>
                  );
                })}
              </div>
            ) : (
              // 单选模式
              <Radio.Group 
                value={tempSelectedItems.length > 0 ? tempSelectedItems[0].value : undefined} 
                onChange={val => handleSingleSelect(val as string)}
              >
                {dictOptData.map(item => (
                  <div key={item.value} className={styles.groupItem}>
                    <Radio value={item.value}>{item.label}</Radio>
                  </div>
                ))}
              </Radio.Group>
            )}
          </div>
          
          <div className={styles.footer}>
            <div className={styles.selectedCount}>
              已选择: <span className={styles.count}>{tempSelectedItems.length}</span> 项
            </div>
            <div className={styles.confirmBtn} onClick={handleConfirm}>
              {confirmText}
            </div>
          </div>
        </div>
      </Popup>
    </>
  );
});

// Form组件适配器
export const GroupPickerFormItem = forwardRef<GroupPickerRef, GroupPickerProps>((props, ref) => {
  const { label, name, wrapFormItem = true, formItemProps = {}, ...restProps } = props;

  // 如果不需要包裹Form.Item，直接返回GroupPicker
  if (!wrapFormItem) {
    // @ts-ignore - 忽略类型错误，确保组件可以正常渲染
    return <GroupPicker ref={ref} {...restProps} />;
  }

  // 确保使用Form.Item时必须提供name属性
  if (!name) {
    console.warn('GroupPickerFormItem: name is required when wrapFormItem is true');
  }

  return (
    <Form.Item
      label={label}
      name={name}
      trigger="onChange"
      arrow={false}
      onClick={(e, pickerRef) => {
        if (pickerRef && 'current' in pickerRef) {
          (pickerRef as React.RefObject<GroupPickerRef>).current?.open();
        }
      }}
      {...formItemProps}
    >
      {/* @ts-ignore - 忽略类型错误，确保组件可以正常渲染 */}
      <GroupPicker ref={ref} {...restProps} />
    </Form.Item>
  );
});

export default GroupPicker; 