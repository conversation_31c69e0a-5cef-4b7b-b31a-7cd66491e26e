import React, { useRef, useState } from 'react';
import { Form, Button } from 'antd-mobile';
import GroupPicker, { GroupPickerFormItem, GroupPickerRef, GroupItem } from './index';

const GroupPickerDemo = () => {
  const [form] = Form.useForm();
  const groupPickerRef = useRef<GroupPickerRef>(null);
  const [selectedGroups, setSelectedGroups] = useState<GroupItem[]>([]);

  const onFinish = (values: any) => {
    console.log('表单提交值:', values);
  };

  return (
    <div style={{ padding: '20px' }}>
      <h2>班组选择器示例</h2>
      
      <Form
        form={form}
        onFinish={onFinish}
        layout="vertical"
        footer={
          <Button block type="submit" color="primary" size="large">
            提交
          </Button>
        }
      >
        {/* 方式一：单选模式 */}
        <GroupPickerFormItem
          label="选择班组(单选)"
          name="group1"
          placeholder="请选择班组"
          title="请选择班组"
        />
        
        {/* 方式二：多选模式 */}
        <GroupPickerFormItem
          label="选择班组(多选)"
          name="group2"
          multiple={true}
          placeholder="请选择班组"
          title="请选择多个班组"
        />
        
        {/* 方式三：不使用表单，直接使用多选 */}
        <div style={{ marginTop: '20px' }}>
          <h3>非表单用法(多选)</h3>
          <GroupPicker
            ref={groupPickerRef}
            wrapFormItem={false}
            multiple={true}
            placeholder="点击选择班组"
            title="班组列表"
            value={selectedGroups}
            onChange={(value) => {
              console.log('选择的班组:', value);
              setSelectedGroups(value as GroupItem[]);
            }}
          />
          <Button 
            style={{ marginTop: '10px' }}
            onClick={() => groupPickerRef.current?.open()}
          >
            打开选择器
          </Button>
        </div>
      </Form>
    </div>
  );
};

export default GroupPickerDemo; 