.groupSelector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 8.8889vw;
  padding: 0 3.7037vw;
  background-color: #fff;
  border-radius: 1.8519vw;
  font-size: 3.7037vw;
  
  .placeholder {
    color: #999;
  }
  
  .value {
    color: #333;
  }
  
  .multipleSelector {
    display: flex;
    flex: 1;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: flex-start;
    padding: 1.8519vw 0;
  }
}

// 自定义复选框样式
.customCheckbox {
  display: flex;
  align-items: center;
  cursor: pointer;
  
  .checkbox {
    width: 5.5556vw;
    height: 5.5556vw;
    border: 0.2778vw solid #ccc;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 2.7778vw;
    
    &.checked {
      background-color: #1154ED;
      border-color: #1154ED;
    }
    
    .checkmark {
      color: white;
      font-size: 3.7037vw;
      line-height: 1;
    }
  }
  
  .checkboxLabel {
    font-size: 3.7037vw;
    color: #333;
  }
}

.groupTag {
  padding: 1.1111vw 3.7037vw;
  border-radius: 6.4815vw;
  background: rgba(245, 245, 245, 1);
  font-size: 3.8889vw;
  font-weight: 400;
  line-height: 5.6315vw;
  color: rgba(51, 51, 51, 1);
  margin-bottom: 1.8519vw;
  display: flex;
  align-items: center;
  
  .removeIcon {
    margin-left: 1.8519vw;
    font-size: 3.7037vw;
    color: #999;
  }
}

.selectBtn {
  display: inline-block;
  border-radius: 7.7778vw;
  background: rgba(215, 225, 250, 1);
  font-size: 3.8889vw;
  font-weight: 400;
  letter-spacing: 0vw;
  line-height: 5.6315vw;
  color: rgba(17, 84, 237, 1);
  min-width: 16.6667vw;
  height: 7.7778vw;
  text-align: center;
  line-height: 7.7778vw;
}

.popupContainer {
  background-color: #fff;
  border-top-left-radius: 3.7037vw;
  border-top-right-radius: 3.7037vw;
  overflow: hidden;
  padding-bottom: 5.5556vw;
}

.popupHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 12.963vw;
  padding: 0 4.6296vw;
  border-bottom: 0.2778vw solid #F5F5F5;
  
  .title {
    flex: 1;
    text-align: center;
    font-size: 4.6296vw;
    font-weight: 500;
    color: #333;
  }
  
  .closeBtn {
    font-size: 5vw;
    color: #999;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.groupList {
  max-height: 70vh;
  overflow-y: auto;
  padding: 0 4.6296vw;
  
  .groupItem {
    padding: 3.7037vw 0;
    border-bottom: 0.2778vw solid #F5F5F5;
    
    :global(.adm-radio) {
      --icon-size: 5.5556vw;
      --font-size: 3.7037vw;
      --gap: 2.7778vw;
    }
  }
}

.footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2.2222vw 3.3333vw;
  border-top: 0.0926vw solid #f0f0f0;
  background-color: #fff;
  
  .selectedCount {
    font-size: 4.4444vw;
    color: #666;

    .count {
      color: #1154ED;
      font-weight: 500;
    }
  }
}

.confirmBtn {
  width: 55.5556vw;
  height: 11.1111vw;
  line-height: 11.1111vw;
  border-radius: 5.5556vw;
  font-size: 3.8889vw;
  font-weight: 400;
  color: #fff;
  background-color: #1154ED;
  text-align: center;
} 