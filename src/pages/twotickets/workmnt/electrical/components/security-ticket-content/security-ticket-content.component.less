.container {
  width: 100%;
  font-family: <PERSON>Fang SC;

  .moduleName {
    margin: 49px 37px 28px;
    font-size: 46px;
    font-weight: 500;
    line-height: 66.61px;
    color: rgba(51, 51, 51, 1);
  }

  :global {
    .adm-card-header-title {
      font-size: 46px;
      font-weight: 500;
    }

    .ant-form-item-label>label {
      font-size: 42px;
      font-weight: 500;
      line-height: 60.82px;
      color: rgba(51, 51, 51, 1);
    }

    .adm-text-area-element {
      padding: 25px 30px;
      font-size: 42px;
      font-weight: 400;
      line-height: 60.82px;
      border-radius: 18px;
      background-color: rgba(245, 245, 245, 1);

      &:disabled {
        -webkit-text-fill-color: rgba(51, 51, 51, 1);
      }
    }

    .adm-ellipsis {
      font-size: 42px;
      font-weight: 400;
      line-height: 60.82px;
      color: rgba(51, 51, 51, 1);

      a {
        color: rgba(17, 84, 237, 1);
        font-weight: 500;
      }
    }


    .adm-card-header-title {
      font-size: 46px;
      font-weight: 500;
    }

    .adm-card-body {
      padding-bottom: 1.8rem;
    }

    .adm-list {
      --border-bottom: none !important;

      .adm-list-item {
        padding-left: 0;
      }

      .adm-list-item-content {
        border-top: none;
        padding-right: 0;
      }

      .adm-list-item-content-main {
        padding: 10px 0;
      }

      .adm-list-item-content-prefix {
        width: 60px;
        padding-top: 15px !important;
      }
    }

    .adm-form {
      --border-top: none;
    }

    .adm-checkbox {
      margin-right: 50px;

      .adm-checkbox-icon {
        width: 37px;
        height: 37px;
        border-radius: 0;
      }

      .adm-checkbox-content {
        font-size: 42px;
      }
    }

    .adm-list-item-disabled.adm-list-item-disabled>.adm-list-item-content>* {
      opacity: 1;
    }
  }
}

.card {
  :global {
    .ant-form-item .ant-form-item-label>label {
      height: auto;
    }
  }
}

.note {
  font-size: 36px;
  font-weight: 400;
  line-height: 52.13px;
  color: rgba(153, 153, 153, 1);
}

.ticketsList {
  margin-top: 36px;

  .ticketsItem {
    margin-top: 20px;

    .ticketsItemTitle {
      font-size: 42px;
      font-weight: 500;
      line-height: 60.82px;
      color: rgba(51, 51, 51, 1);
    }

    .ticketsItemContent {
      font-size: 42px;
    }
  }
}