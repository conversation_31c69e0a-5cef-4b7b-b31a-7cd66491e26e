import { Form, FormInstance } from 'antd';
import React, { useEffect, useImperativeHandle } from 'react';
import { Card, Checkbox, Divider, Ellipsis, Space, TextArea } from 'antd-mobile';
import { useParams } from 'umi';
import styles from './security-ticket-content.component.less';
import { DownOutline, UpOutline } from 'antd-mobile-icons';


interface Props {
  data?: any;
}


// 暴露出去的 ref 类型
export interface SecurityTicketContentRef {
  form: FormInstance;
  getValue: () => any;
}

const SecurityTicketContent = React.forwardRef<SecurityTicketContentRef, Props>((props, ref) => {
  const [form] = Form.useForm();
  const [form2] = Form.useForm();
  const { data } = props;
  // 获取url参数
  const { pageType } = useParams();


  /* 暴露的提交事件及元素 */
  useImperativeHandle(ref, () => ({
    form,
    getValue: () => {
      return data?.workIrrigationSafeMeasure ? { ...data.workIrrigationSafeMeasure, ...form.getFieldsValue() } : form.getFieldsValue();
    },
  }));

  useEffect(() => {
    if (data?.workIrrigationSafeMeasure) {
      form.setFieldsValue(data.workIrrigationSafeMeasure);
    }
  }, [data]);


  return (

    <div className={styles.container}>
      <div className={styles.moduleName}>安全措施票</div>
      <Card>
        <Form form={form}>
          <Form.Item label='被测试设备及保护名称' name='testDevice' layout='horizontal'>
            <Ellipsis
              direction='end'
              rows={3}
              content={data?.testDevice ?? '无'}
              expandText={
                <>
                  展开
                  <DownOutline />
                </>
              }
              collapseText={
                <>
                  收起
                  <UpOutline />
                </>
              }
            />
          </Form.Item>
          <Form.Item label='工作条件' name='smWorkCondition' layout='horizontal'>
            <Ellipsis
              direction='end'
              rows={3}
              content={data?.smWorkCondition ?? '无'}
              expandText={
                <>
                  展开
                  <DownOutline />
                </>
              }
              collapseText={
                <>
                  收起
                  <UpOutline />
                </>
              }
            />
          </Form.Item>
        </Form>
      </Card>
      <div style={{ height: '.8rem' }}></div>
      <Card className={styles.card}>
        <Form form={form2}>
          <Form.Item label={(
            <div>
              <div>安全措施</div>
              <div className={styles.note}>包括应打开及恢复连接片（压板）、直流线、交流线、信号线、联锁线和联锁开关等，按工作顺序填写安全措施。已执行，在执行栏打√，已恢复，在恢复栏打√</div>
            </div>
          )} name='shouldMeasure' layout='horizontal'>
            <div className={styles.ticketsList}>
              <Checkbox.Group>
                {
                  data?.workSafeMeasureTickets?.map((item: any, index: number) => (
                    <div className={styles.ticketsItem}>
                      <div className={styles.ticketsItemTitle}>{item?.shouldMeasure || '无'}</div>
                      <div className={styles.ticketsItemContent}>
                        <Space>
                          <Checkbox checked={item?.execute}>
                            执行
                          </Checkbox>
                          <Checkbox checked={item?.restore}>
                            恢复
                          </Checkbox>
                        </Space>
                      </div>
                    </div>
                  ))
                }
              </Checkbox.Group>
            </div>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
});

export default SecurityTicketContent;

