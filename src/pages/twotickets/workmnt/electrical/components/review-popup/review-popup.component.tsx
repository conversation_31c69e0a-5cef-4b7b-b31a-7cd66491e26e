/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-19 15:51:39
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-05-29 18:28:07
 * @FilePath: /mobile-yw/src/pages/twotickets/workmnt/electrical/components/review-popup/review-popup.component.tsx
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */
import { DynamicFormItem } from '../../../components/dynamic-form/dynamic-form-item';
import FlowPopup from '../../../components/flow-popue/flow-popue.component';
import { FooterWrapper } from '@/styles/twotickets.style';
import useRegisterStrategy from '../../hooks/useRegisterStrategy';
import ElectricalReviewPopupItemWrapper from './review-popup.styles';
import { Toast } from 'antd-mobile';
import dayjs from 'dayjs';
import { useRef, useMemo, useCallback, memo } from 'react';

interface IReviewPopupProps {
  visible: boolean;
  setVisible: (visible: boolean) => void;
  variables: any;
  activeName: string;
  onSubmit: (values: any, variables: any, activeName: string) => void;
  status?: string;
  detailData?: any;
}

// 验证表单数据
const validateFormData = (values: any, status: string | undefined, data: any) => {
  // 如果不是电气一种工作票，跳过部分验证
  const ticketType = data?.ticketType;
  if (ticketType !== '1') {
    return { valid: true };
  }

  // 基本信息验证 (通用验证)
  if (!values.unitId && status?.includes('1_approve')) {
    return { valid: false, message: '单位不能为空' };
  }

  if (!values.workTask && status?.includes('1_approve')) {
    return { valid: false, message: '工作任务不能为空' };
  }

  if (!values.workLocation && status?.includes('1_approve')) {
    return { valid: false, message: '工作地点及设备双重名称不能为空' };
  }

  if (!values.planBeginTime && !values.planEndTime && status?.includes('1_approve')) {
    return { valid: false, message: '计划时间不能为空' };
  }

  // 安全措施基础数据验证
  if (status?.includes('1_approve')) {
    if (!values.shouldOff) {
      return { valid: false, message: '断开设备验证失败' };
    }
    if (!values.shouldConnect) {
      return { valid: false, message: '连接设备验证失败' };
    }
    if (!values.shouldBlock) {
      return { valid: false, message: '遮拦设备验证失败' };
    }
  }

  // 状态为7（值长批准）时的验证
  if (status?.includes('7_') || data?.status === '7') {
    if (!values.shouldOffExecute) {
      return { valid: false, message: '断开设备执行情况不能为空' };
    }
    if (!values.shouldOffUser) {
      return { valid: false, message: '断开设备执行人不能为空' };
    }
    if (!values.shouldConnectExecute) {
      return { valid: false, message: '连接设备执行情况不能为空' };
    }
    if (!values.shouldConnectUser) {
      return { valid: false, message: '连接设备执行人不能为空' };
    }
    if (!values.shouldBlockExecute) {
      return { valid: false, message: '遮拦设备执行情况不能为空' };
    }
    if (!values.shouldBlockUser) {
      return { valid: false, message: '遮拦设备执行人不能为空' };
    }
  }

  // 延期审核验证
  if (status?.includes('delay') && values.memo === '4') {
    const approveEndTime = dayjs(data?.workFlowRatify?.approveEndTime);
    const newEndTime = dayjs(values.newEndTime);
    if (newEndTime.isBefore(approveEndTime) || newEndTime.isSame(approveEndTime)) {
      return { valid: false, message: '有效期延长到时间必须晚于批准工作结束时间' };
    }
  }

  // 状态为9（执行情况填报）时的验证
  if ((status?.includes('9_') || data?.status === '9') && values.isAudit === 1) {
    if (!values.canRun) {
      return { valid: false, message: '请选择设备是否可投运' };
    }
    if (!values.repairOrder) {
      return { valid: false, message: '检修交代不能为空' };
    }
  }

  return { valid: true };
};

const ReviewPopup = memo((props: IReviewPopupProps) => {
  // 注册策略
  const { statusFormList, datePickerOtherConfig } = useRegisterStrategy();
  const formRef = useRef<any>(null);
  const { visible, setVisible, variables, activeName, onSubmit, status, detailData: data } = props;

  // 使用 useMemo 缓存 schemasList，避免不必要的重新计算
  const schemasList = useMemo(() => {
    if (!status || !statusFormList?.[status]) {
      return [];
    }
    
    return statusFormList[status].map((item: any) => 
      item?.map((ele: any) => ({
        layout: "vertical",
        ...ele,
        ...(ele.strategyType === 'datePicker' ? datePickerOtherConfig : {}),
      }))
    );
  }, [status, statusFormList, datePickerOtherConfig]);

  // 使用 useMemo 缓存计算当前月的最后一天
  const lastDayOfMonth = useMemo(() => {
    const currentDate = new Date();
    return new Date(
      currentDate.getFullYear(),
      currentDate.getMonth() + 1,
      0,
      0, 0, 0
    );
  }, []);

  // 使用 useMemo 缓存初始值
  const initialValues = useMemo(() => ({
    /**
     * state->7 accessTime 默认是当天
     */
    accessTime: new Date(),

    /**
     * state->11 auditResult 默认是合格
     */
    auditResult: '合格',

    /**
     * state->6 approveTime 默认是当前时间到本月最后一天的0点0分
     */
    approveTime: data?.status === "6" ? [new Date().getTime(), lastDayOfMonth.getTime()] : undefined,

    workLocation: data?.workLocation ?? '',
    workTask: data?.workTask ?? '',
    electricalName: data?.headId,
    teamsNmae: typeof data?.teamsId === 'string' ? data?.teamsId?.split?.(",") : [],
    orgId: data?.unitId,

    thermalControlHead: data?.headId,
    machineName: data?.headId,
    otherName: data?.headId,

    workFlowTryRunSafe: data?.workSafeMeasureTickets?.map?.((item: any) => ({
      id: item.id,
      content: item.shouldMeasure,
      mainId: item.mainId,
      execute: item.execute,
    })),
  }), [data, lastDayOfMonth]);

  // 使用 useCallback 缓存提交处理函数
  const handleSubmit = useCallback(async (values: any) => {
    console.log(values, variables, activeName, 'FlowPopup===onSubmit=========');

    // 表单验证
    try {
      const validateResult = await formRef.current.validate();
      console.log(validateResult, 'validateResult');
    } catch (error: any) {
      console.log(error, 'error');
      Toast.show({
        icon: 'fail',
        content: error?.message || '表单验证失败',
      });
      return;
    }

    // 验证通过后调用提交函数
    onSubmit(values, variables, activeName);
  }, [variables, activeName, onSubmit]);

  // 如果不可见，直接返回 null 避免渲染
  if (!visible) {
    return null;
  }

  console.log(schemasList, 'schemasList');
  console.log('当前状态： 【', status, '】 数据状态： 【', data?.status, '】');

  return (
    <FlowPopup
      title={activeName}
      visible={visible}
      ref={formRef}
      onClose={() => setVisible(false)}
      initialValues={initialValues}
      onSubmit={handleSubmit}
    >
      <div
        className="content"
        style={{
          height: '100%',
          overflowY: 'auto',
          '--adm-color-background': '#fff',
        }}
      >
        {schemasList?.map((item: any, index: number) => (
          <ElectricalReviewPopupItemWrapper key={index}>
            {item.map((v: any, i: number) => (
              <DynamicFormItem key={`${index}-${i}`} {...v} />
            ))}
          </ElectricalReviewPopupItemWrapper>
        ))}
        <FooterWrapper>已经到底了</FooterWrapper>
      </div>
    </FlowPopup>
  );
}, (prevProps, nextProps) => {
  // 自定义比较函数，只有在关键 props 变化时才重新渲染
  return (
    prevProps.visible === nextProps.visible &&
    prevProps.status === nextProps.status &&
    prevProps.activeName === nextProps.activeName &&
    prevProps.variables === nextProps.variables &&
    prevProps.detailData?.status === nextProps.detailData?.status &&
    prevProps.detailData?.id === nextProps.detailData?.id
  );
});

ReviewPopup.displayName = 'ReviewPopup';

export default ReviewPopup;
