/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-21 14:24:44
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-05-21 14:25:18
 * @FilePath: /mobile-yw/src/pages/twotickets/workmnt/electrical/components/review-popup/review-popup.styles.ts
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */

import { styled } from 'styled-components';
import { nanoid } from 'nanoid';

const ElectricalReviewPopupItemWrapper = styled.div.attrs(() => ({
  className: `main-electrical-review-popup-wrapper-${nanoid()}`,
}))`
  background-color: #fff;
  margin-top: 2.2222vw;

  /* border-radius: 1.2rem;
  background-color: #fff;
  margin: .8rem 1.5rem;
  overflow: hidden;
  padding: 1.2rem;
  */
  .adm-list-item:nth-child(1) .adm-list-item-content {
    border-top: none;
  } 

  .adm-list-item-content-prefix > .adm-form-item-label {
    font-size: 4.2593vw;
    font-weight: 400;
    letter-spacing: 0vw;
    line-height: 6.1676vw;
    color: rgba(252, 89, 90, 1);
    margin-bottom: 0 !important;
  }
  .adm-form-item-child-inner {
    font-size: 4.2593vw;
    font-weight: 400;
    letter-spacing: 0vw;
    line-height: 6.1676vw;
    color: rgba(51, 51, 51, 1);
  }
`;

export default ElectricalReviewPopupItemWrapper