# 电气工作票模块架构说明

## 📁 目录结构

```
electrical/
├── components/           # 组件目录
│   ├── basic-info/       # 基本信息组件
│   ├── final-explanation/ # 终结说明组件
│   ├── review-popup/     # 审核弹窗组件
│   ├── safety-measure/   # 安全措施组件
│   └── ...
├── constants/            # 常量定义
│   └── status.ts         # 状态相关常量
├── hooks/                # 自定义Hook
│   ├── configFactory.ts  # 表单配置工厂
│   ├── constants.ts      # Hook常量
│   ├── formConfigs.ts    # 表单配置定义
│   ├── strategyRegistry.ts # 策略注册
│   ├── types.ts          # Hook类型定义
│   ├── useDetail.ts      # 详情数据Hook
│   ├── useRegisterStrategy.ts # 策略注册Hook
│   ├── useStatusHandler.ts # 状态处理Hook
│   └── useSubmitWorkOrder.ts # 提交工单Hook
├── types/                # 类型定义
│   └── index.ts
├── utils/                # 工具类
│   └── statusUtils.ts    # 状态工具类
├── services/             # 服务
│   └── api.ts            # API服务
├── docs/                 # 文档
│   └── ...
├── electrical.page.tsx   # 主页面
├── electrical.page.less  # 样式文件
└── README.md             # 本文档
```

## 🔧 核心功能与优化

### 1. 模块化状态管理

#### 关键文件
- **`constants/status.ts`** - 统一管理状态常量，采用函数式生成状态转换规则
- **`utils/statusUtils.ts`** - 状态操作工具类
- **`hooks/useStatusHandler.ts`** - 状态处理逻辑hook
- **`hooks/useRegisterStrategy.ts`** - 表单策略注册和配置管理

#### 主要特点
- ✅ 状态转换规则动态生成，提高可维护性
- ✅ 提取状态处理逻辑到独立hook
- ✅ 统一状态常量管理
- ✅ 增强状态转换验证
- ✅ 改进错误处理和用户反馈

### 2. 模块化表单配置

#### 关键文件
- **`hooks/formConfigs.ts`** - 表单配置定义，按流程类型模块化组织
- **`hooks/configFactory.ts`** - 表单配置工厂函数
- **`hooks/strategyRegistry.ts`** - 策略组件注册

#### 主要特点
- ✅ 按流程类型模块化组织配置
- ✅ 使用工厂函数创建常用配置项
- ✅ 提高代码可复用性和可维护性
- ✅ 减少重复代码，增强代码组织结构

### 3. 类型安全增强

#### 类型定义
```typescript
// 主要类型示例
interface StatusFormList {
  [key: string]: FormConfig | FormItemConfig[];
}

type FormConfig = FormItemConfig[][];

interface FormItemConfig {
  strategyType: string;
  label: string;
  name: string;
  // 其他配置项...
}

// 状态转换规则类型
type TransitionRule = {
  approve: string;
  reject?: string;
  back: string;
  cancel?: string;
};
```

### 4. 高效状态生成机制

状态转换规则使用函数动态生成，减少硬编码：

```typescript
// 生成状态转换规则
const generateTransitionRules = (maxStatus: number): Record<string, TransitionRule> => {
  const rules: Record<string, TransitionRule> = {};
  
  for (let i = 0; i <= maxStatus; i++) {
    rules[`${i}`] = {
      approve: `${i}_approve`,
      back: `${i}_back`,
      cancel: `${i}_cancel`,
      reject: `${i}_reject`,
    };
  }
  
  return rules;
};

// 使用方式
export const STATUS_TRANSITION_RULES = generateTransitionRules(20);
```

## 🎯 使用指南

### 状态处理

```typescript
// 使用状态处理hook
const {
  visible,
  setVisible,
  variables,
  activeName,
  status,
  handleActionClick
} = useStatusHandler(data, onSubmit);

// 状态工具类使用
import { StatusUtils } from './utils/statusUtils';

// 检查是否为终态
const isFinal = StatusUtils.isFinalState('18');

// 获取状态名称
const statusName = StatusUtils.getStatusName('2');
```

### 表单配置使用

```typescript
// 使用表单策略注册Hook
const { statusFormList, datePickerOtherConfig } = useRegisterStrategy();

// 访问特定状态的表单配置
const formConfig = statusFormList['2_approve'];
```

### 常量使用

```typescript
import { 
  FINAL_STATES,
  STATUS_TRANSITION_RULES,
  SUB_PROCESS_TYPES,
  OPERATION_TYPES
} from './constants/status';

// 检查终态
if (FINAL_STATES.includes(currentStatus)) {
  // 处理终态逻辑
}

// 使用操作类型
if (operation === OPERATION_TYPES.APPROVE) {
  // 处理审批逻辑
}
```

## 📊 架构优势

### 模块化设计
- 🔸 按功能领域划分代码
- 🔸 高内聚低耦合
- 🔸 易于扩展和维护

### 类型安全
- 🔸 全面的TypeScript类型定义
- 🔸 减少运行时错误
- 🔸 提升开发体验

### 表单配置模块化
- 🔸 按流程类型组织配置
- 🔸 减少重复代码
- 🔸 提高可维护性

### 状态管理优化
- 🔸 动态生成状态规则
- 🔸 统一状态处理逻辑
- 🔸 简化状态转换验证

## 🚀 开发指南

### 添加新状态
1. 修改`constants/status.ts`中的`generateTransitionRules`参数
2. 在`STATUS_NAME_MAP`中添加状态名称
3. 更新相关处理逻辑

### 添加新表单配置
1. 在`hooks/formConfigs.ts`中添加新的配置生成函数
2. 在相应的流程模块中添加配置项
3. 更新`createBaseStatusFormList`函数包含新配置

### 添加新子流程
1. 在`SUB_PROCESS_TYPES`中定义类型
2. 在`formConfigs.ts`中添加相应的配置
3. 更新相关处理逻辑

## 📝 最佳实践

1. **保持一致性** - 遵循现有的命名和组织规范
2. **类型优先** - 添加新功能前先定义类型
3. **模块化思维** - 将相关功能组织在一起
4. **避免硬编码** - 使用常量和动态生成函数
5. **注重可维护性** - 编写清晰的注释和文档

## 🤝 贡献指南

1. 遵循现有代码规范和组织结构
2. 添加完整的类型定义
3. 保持模块化组织结构
4. 更新相关文档 