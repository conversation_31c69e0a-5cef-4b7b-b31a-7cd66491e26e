/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-01-XX XX:XX:XX
 * @LastEditors: <PERSON><PERSON> <EMAIL>  
 * @LastEditTime: 2025-01-XX XX:XX:XX
 * @FilePath: /mobile-yw/src/pages/twotickets/workmnt/electrical/types/index.ts
 * @Description: 电气工作票类型定义
 */

/**
 * 电气工作票详情数据接口
 */
export interface ElectricalWorkTicketData {
  id?: string;
  taskId?: string;
  taskName?: string;
  status?: string;
  ticketType?: string;
  workTask?: string;
  workLocation?: string;
  deviceName?: string;
  unitName?: string;
  unitId?: string;
  member?: string;
  peopleNumber?: number;
  planBeginTime?: string | Date;
  planEndTime?: string | Date;
  createTime?: string | Date;
  workCondition?: string;
  precaution?: string;
  headId?: string;
  teamsId?: string;
  curTask?: any;
  workflowApprovalRecordList?: WorkflowApprovalRecord[];
  workTicketExecute?: WorkTicketExecute;
  workSafeMeasureTickets?: WorkSafeMeasureTicket[];
  [key: string]: any;
}

/**
 * 工作流审批记录
 */
export interface WorkflowApprovalRecord {
  id: string;
  status: string;
  createTime: string;
  [key: string]: any;
}

/**
 * 工作票执行信息
 */
export interface WorkTicketExecute {
  workFlowExecuteDelay?: {
    memo?: string;
    [key: string]: any;
  };
  workFlowExecuteHeadExchange?: {
    memo?: string;
    [key: string]: any;
  };
  [key: string]: any;
}

/**
 * 安全措施票
 */
export interface WorkSafeMeasureTicket {
  id: string;
  shouldMeasure: string;
  mainId: string;
  execute?: string;
  [key: string]: any;
}

/**
 * 操作按钮点击事件参数
 */
export interface ActionClickParams {
  label: "处理" | "保存";
  next: {
    extra?: boolean;
    variables?: {
      sub?: string;
      code?: string;
      pass?: string;
      memo?: string;
      [key: string]: any;
    };
    code?: string;
    [key: string]: any;
  };
  name?: string;
}

/**
 * 状态处理上下文
 */
export interface StatusContext {
  sub?: string;
  code?: string;
  pass?: string;
  memo?: string;
  variables?: Record<string, any>;
  [key: string]: any;
}

/**
 * Tab项配置
 */
export interface TabItem {
  key: string;
  title: string;
  content: (data: ElectricalWorkTicketData) => React.ReactNode;
}

/**
 * 基本信息列表项
 */
export interface BasicInfoItem {
  type?: 'title' | 'divider';
  label?: string;
  value?: string;
  render?: (text: string, record: ElectricalWorkTicketData) => React.ReactNode;
}

/**
 * 表单验证结果
 */
export interface ValidationResult {
  valid: boolean;
  message?: string;
}

/**
 * 表单状态枚举
 */
export enum FormStatus {
  PREPARING = '1',
  SIGN = '2',
  OWNER_CONFIRM = '3',
  HEAD_CONFIRM = '4',
  DUTY_CONFIRM = '5',
  SUPERVISOR_APPROVE = '6',
  LICENSE = '7',
  HEAD_RECEIVE = '8',
  // ... 其他状态
  EXECUTED_CONFIRM = '13',
  WAITING_SUPERVISOR = '14',
  END_CONFIRM = '15',
  WAITING_HEAD_CONFIRM = '16',
  AUDIT = '17',
  COMPLETED = '18',
  CANCELLED = '19',
  TERMINATED = '20'
}

/**
 * 工作票类型枚举
 */
export enum TicketType {
  ELECTRICAL_FIRST = '1',  // 电气一种工作票
  ELECTRICAL_SECOND = '2'  // 电气二种工作票
} 