import React, { useEffect, useRef, useState } from 'react';
import { getMechanicalTicketAPI, getDeviceType } from '../../services';
import { useMatch, useSearchParams } from 'react-router-dom';
import useHeightScroll from '@/hooks/useHeightScroll';
// import TitleComponent from '@/pages/twotickets/workmnt/mechanical/components/title-component';
import TitleComponent from '@/component/twotickets-navbar/twotickets-navbar.component';
import style from '../../two/two.page.less';
import { List, Dropdown, Radio, Space, SearchBar } from 'antd-mobile';
import styles from './index.less';
import ToTheEnd from '@/pages/twotickets/workmnt/mechanical/components/toTheEnd/index';

const handleTreeSelect = (data: any): any[] => {
  if (!data) return [];
  return Object.entries(data).map(([value, label]) => {
    return { label: label, value: label };
  });
};

const SafetyToolList: React.FC = () => {
  const [queryParams] = useSearchParams();
  const id = queryParams.get('id'); //页面id
  const ticketType = queryParams.get('ticketType'); //票类型

  const parentRef = useRef<HTMLDivElement>(null);
  const preChildRef = useRef<HTMLDivElement>(null);
  const nextChildRef = useRef<HTMLDivElement>(null);
  const { height } = useHeightScroll(parentRef.current?.offsetHeight, preChildRef.current?.offsetHeight, nextChildRef);

  const [workBaseTools, setWorkBaseTools] = useState<any>([]);
  const [searchText, setSearchText] = useState('');
  const [sort, setSort] = useState('');
  const dropDownRef = useRef<any>(null);
  const [electricalDevice, setElectricalDevice] = useState<any[]>([]);

  useEffect(() => {
    getDeviceType().then((res) => {
      setElectricalDevice([
        {
          label: '全部',
          value: '',
          title: '全部',
        },
        ...(handleTreeSelect(res?.data) || []),
      ]);
    });
  }, []);

  useEffect(() => {
    getMechanicalTicketAPI(id, { ticketType })
      .then((res) => {
        if (res.code === '1') {
          let arr = [];
          if (!!searchText && !sort) {
            arr = res.data?.workBaseTools.filter((item) => item.toolName.search(searchText) !== -1);
            setWorkBaseTools(arr);
            return;
          }
          if (!!sort && !searchText) {
            arr = res.data?.workBaseTools.filter((item) => item.type === sort);
            setWorkBaseTools(arr);
            return;
          }
          if (!!sort && !!searchText) {
            arr = res.data?.workBaseTools.filter((item) => item.type === sort && item.toolName === searchText);
            setWorkBaseTools(arr);
            return;
          }
          setWorkBaseTools(res.data?.workBaseTools);
        }
      })
      .catch((err) => {
        console.log(err, 'err');
      });
  }, [searchText, sort]);

  return (
    <div ref={parentRef} className={style.operationStatistics}>
      <div ref={preChildRef} className={style.pageTop} style={{ background: '#fff' }}>
        {TitleComponent({ title: '安全工器具', color: 'black' })}
      </div>
      <div ref={nextChildRef}>
        <div className={styles.search}>
          <div className={styles.myDropdown}>
            <Dropdown ref={dropDownRef}>
              <Dropdown.Item key="sorter" title="排序">
                <div style={{ padding: 12 }}>
                  <Radio.Group
                    defaultValue=""
                    onChange={(value: string) => {
                      setSort(value);
                      dropDownRef.current?.close();
                    }}
                  >
                    <Space direction="vertical" block>
                      {electricalDevice?.map((el) => {
                        return (
                          <Radio key={el.value} block value={el.value}>
                            {el.label}
                          </Radio>
                        );
                      })}
                    </Space>
                  </Radio.Group>
                </div>
              </Dropdown.Item>
            </Dropdown>
          </div>
          <div className={styles.right}>
            <SearchBar
              onChange={(value) => {
                setSearchText(value);
              }}
              onFocus={() => {
                dropDownRef.current?.close();
              }}
              placeholder="请输入内容"
            />
          </div>
        </div>
        <List>
          {workBaseTools?.map((el) => (
            <List.Item key={el?.id}>
              <div className={styles.toolName}>
                {el?.toolName}[{el?.model}]
              </div>
              <div className={styles.itemBottom}>
                {el?.toolCode} · {el?.type} · {el?.responsibility}
              </div>
            </List.Item>
          ))}
        </List>
        <ToTheEnd />
      </div>
    </div>
  );
};
export default SafetyToolList;
