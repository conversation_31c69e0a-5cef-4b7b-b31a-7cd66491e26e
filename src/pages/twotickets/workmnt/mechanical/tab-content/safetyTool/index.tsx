import React from 'react';
import { ListCard } from '@/component/new-list-card/list-card.component';
import { List, Empty } from 'antd-mobile';
import styles from './index.less';

interface Iprops {
  current: any; //详情数据
}

interface ToolType {
  id: string;
  toolName: string;
  model: string;
  toolCode: string;
  type: string;
  responsibility: string;
}
const SafatyToole: React.FC<Iprops> = (props) => {
  const { current } = props;
  return (
    <ListCard
      title={<div className={styles.title}>安全工器具</div>}
      isExpandAndMore={false}
      isMore={true}
      maxLength={3}
      moreLink={`/twotickets/safatyToolList?id=${current?.id}&ticketType=${current?.ticketType}`}
      data={current?.workBaseTools || []}
      children={(item: ToolType[]) => {
        if (current?.workBaseTools?.length === 0) {
          return <Empty description="暂无数据" />;
        }
        return (
          <List>
            {item?.map((el) => (
              <List.Item key={el?.id}>
                <div className={styles.toolName}>
                  {el?.toolName}[{el?.model}]
                </div>
                <div className={styles.itemBottom}>
                  {el?.toolCode} · {el?.type} · {el?.responsibility}
                </div>
              </List.Item>
            ))}
          </List>
        );
      }}
    />
  );
};
export default SafatyToole;
