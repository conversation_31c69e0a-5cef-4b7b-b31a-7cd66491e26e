/**
 * @description 工作票执行-工作负责人变更
 */
import { Card } from 'antd-mobile';
import styles from './index.less';
import { useParams } from 'umi';
import { getTicketTypeAndTitle } from '@/constant';

interface Iprops {
  current: any;
}
const ChangeUser = (props: Iprops) => {
  const { current } = props;
  const { workFlowExecuteDelay } = current?.workTicketExecute || {};

  const { type } = useParams();
  const { ticketType } = getTicketTypeAndTitle('mechanical', type);

  //数据为空默认结束
  if (!current?.workTicketExecute?.workFlowExecuteDelay) {
    return null;
  }

  return (
    <Card title="工作票延期" className={styles.myCard}>
      {workFlowExecuteDelay?.newEndTime ? (
        <>
          <div className={styles.changValueItem}>
            <div className={styles.userAndTime}>
              <div>有效期延长到：</div>
              <div>{workFlowExecuteDelay?.newEndTime}</div>
            </div>
          </div>
        </>
      ) : null}

      <div className={styles.changValueItem}>
        {workFlowExecuteDelay?.headName && (
          <div className={styles.userAndTime}>
            <span className={styles.colorGrey}>工作负责人：</span>
            <span>{workFlowExecuteDelay?.headName}</span>
            <span>，</span>
            <span className={styles.colorGrey}>提交申请时间：</span>
            <span>{workFlowExecuteDelay?.headTime}</span>
          </div>
        )}
        {workFlowExecuteDelay?.shiftSupervisorName && ['3', '8'].includes(ticketType) && (
          <div className={styles.userAndTime}>
            <span className={styles.colorGrey}>值长：</span>
            <span>{workFlowExecuteDelay?.shiftSupervisorName}</span>
            <span>，</span>
            <span className={styles.colorGrey}>提交时间：</span>
            <span>{workFlowExecuteDelay?.shiftSupervisorTime}</span>
          </div>
        )}
        {workFlowExecuteDelay?.signName && ['3', '8'].includes(ticketType) && (
          <div className={styles.userAndTime}>
            <span className={styles.colorGrey}>值班负责人：</span>
            <span>{workFlowExecuteDelay?.signName}</span>
            <span>，</span>
            <span className={styles.colorGrey}>提交时间：</span>
            <span>{workFlowExecuteDelay?.siginTime}</span>
          </div>
        )}

        {/* 工作签发人需核对字段 */}
        {workFlowExecuteDelay?.signName && !['3', '8'].includes(ticketType) && (
          <div className={styles.userAndTime}>
            <span className={styles.colorGrey}>工作签发人：</span>
            <span>{workFlowExecuteDelay?.signName}</span>
            <span>，</span>
            <span className={styles.colorGrey}>签发时间：</span>
            <span>{workFlowExecuteDelay?.siginTime}</span>
          </div>
        )}
        {workFlowExecuteDelay?.allowName && (
          <div className={styles.userAndTime}>
            <span className={styles.colorGrey}>工作许可人：</span>
            <span>{workFlowExecuteDelay?.allowName}</span>
            <span>，</span>
            <span className={styles.colorGrey}>提交时间：</span>
            <span>{workFlowExecuteDelay?.allowTime}</span>
          </div>
        )}
      </div>
    </Card>
  );
};
export default ChangeUser;
