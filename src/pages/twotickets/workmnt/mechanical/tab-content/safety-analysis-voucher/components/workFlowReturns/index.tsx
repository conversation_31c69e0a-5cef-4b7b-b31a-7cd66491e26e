import { ListCard } from '@/component/new-list-card/list-card.component';
import { Steps } from 'antd-mobile';
import styles from './index.less';

const { Step } = Steps;

const WorkFlowReturns = (props) => {
  const { current } = props;
  // return <>每日开工和收工记录</>;
  return (
    <ListCard
      title={<div>每日开工和收工记录</div>}
      isMore={true}
      maxLength={2}
      // moreLink={`/twotickets/safatyToolList?id=${current?.id}&ticketType=${current?.ticketType}`}
      data={current?.workTicketExecute?.workFlowReturns || []}
      style={{
        marginBottom: '0.8rem',
      }}
      isExpandAndMore={true}
      children={(item: any[]) => {
        return (
          <Steps
            direction="vertical"
            current={0}
            style={{
              '--title-font-size': '17px',
              '--description-font-size': '15px',
              '--indicator-margin-right': '12px',
              '--icon-size': '22px',
            }}
          >
            {item.map((el, index) => (
              <Step
                key={index}
                title={<div>{el.type === 0 ? '开工' : '收工'}</div>}
                description={
                  <div>
                    {el.headName && (
                      <div className={styles.userInfoDiv}>
                        <span>工作负责人：</span>
                        <span>{el.headName}</span>
                        <span>，</span>
                        <span>提交时间：</span>
                        <span>{el.submitTime}</span>
                      </div>
                    )}
                    {el.allowName && (
                      <div className={styles.userInfoDiv}>
                        <span>工作许可人：</span>
                        <span>{el.allowName}</span>
                        <span>，</span>
                        <span>提交时间：</span>
                        <span>{el.createTime}</span>
                      </div>
                    )}
                    {el.remark && <div className={styles.notes}>备注：{el.remark}</div>}
                  </div>
                }
              />
            ))}
          </Steps>
        );
      }}
    />
  );
};
export default WorkFlowReturns;
