.changValueItem {
  padding: .8333rem 0;
  border-bottom: solid var(--adm-card-header-border-width, 0.5px) var(--adm-card-header-border-color, var(--adm-color-border));
}

.userAndTime {
  // display: flex;
  font-size: 1.4rem;
  align-items: center;
  line-height: 2.0333rem;

  div {
    &:nth-child(1) {
      width: 10rem;
      color: rgba(153, 153, 153, 1);
    }

    &:nth-child(2) {
      flex: 1;
      color: rgba(51, 51, 51, 1);
    }
  }
}

.colorGrey {
  color: rgba(153, 153, 153, 1);
}

.textArea {
  font-size: 1.2rem;
  line-height: 1.7333rem;
  padding: .8333rem 0;

  span {
    &:nth-child(1) {
      color: rgba(17, 84, 237, 1);
    }
  }
}

.myCard {
  margin-bottom: 0.8rem;

  :global {
    .adm-card-body {
      padding: 0;
    }
  }
}