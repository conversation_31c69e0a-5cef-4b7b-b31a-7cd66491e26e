import React, { forwardRef, useImperativeHandle, useState, useRef, useEffect } from 'react';
import { ListCard } from '@/component/new-list-card/list-card.component';
import { Form } from 'antd-mobile';
import { EditSOutline, AddCircleOutline } from 'antd-mobile-icons';
import MorePage from '@/pages/twotickets/workmnt/components/morePage/index';
import FormPopup from '@/pages/twotickets/workmnt/components/form-popup/index';
import { DatePickerItem } from '@/component/dynamic-form/items/date-picker-popup/date-picker-popup.component';
import styles from './index.less';
import UserPicker from '@/pages/twotickets/workmnt/components/user-picker/user-picker.component';
import dayjs from 'dayjs';
import { getUserName } from '@/pages/twotickets/workmnt/components/workMemberChange/index';

import styled from 'styled-components';

export const ChangeDivItem = styled.div`
  font-size: 1.6rem;
  padding: 0.8333rem 0;
  .item_top {
    display: flex;
    /* align-items: center; */
    font-size: 1.4rem;
    margin-bottom: 0.6333rem;
    .item_top_left {
      flex: 1;
    }
    .item_top_right {
      width: 3rem;
      font-size: 1.5333rem;
      text-align: right;
      display: flex;
      align-items: center;
    }
  }
  .item_bottom {
    padding-left: 2rem;
    background: rgba(245, 245, 245, 1);
    border-radius: 0.6rem;
    height: 3.4rem;
    display: flex;
    align-items: center;
    font-size: 1.2rem;
    color: rgba(153, 153, 153, 1);
    span {
      &:nth-child(1) {
        display: inline-block;
        border: 1px solid rgba(253, 146, 40, 1);
        padding: 0 0.2667rem;
        color: rgba(253, 146, 40, 1);
        margin-right: 0.8333rem;
        border-radius: 0.4rem 0.4rem 0.4rem 0;
      }
      &:nth-child(3) {
        padding: 0 0.7667rem;
      }
      &:nth-child(4) {
        margin-right: 0.7667rem;
      }
    }
  }
  .item_top_flex {
    display: flex;

    .item_top_flex_right_new {
      font-size: 1.2rem;
      display: inline-block;
      border: 1px solid rgba(17, 84, 237, 1);
      padding: 0 0.2667rem;
      color: rgba(17, 84, 237, 1);
      margin-right: 0.8333rem;
      border-radius: 0.4rem 0.4rem 0.4rem 0;
    }
    .item_top_flex_right_name {
      font-size: 1.4rem;
      color: rgba(51, 51, 51, 1);
      font-weight: 500;
    }
  }
  .item_top_time {
    span {
      font-size: 1.2rem;
      color: rgba(102, 102, 102, 1);
      &:nth-child(2) {
        padding: 0 0.7667rem;
      }
    }
  }
`;

interface Iprops {
  current: any; //详情数据
  ref: any;
  isAddAndEdit?: boolean; //是否有新增和编辑权限(默认false)
}

const Maintenance: React.FC<Iprops> = forwardRef((props, ref) => {
  const { current, isAddAndEdit = false } = props;
  // const { workTicketExecute } = current;

  const morePageRef = useRef(null);

  const [data, setData] = useState([]);
  const [visible, setVisible] = useState(false);
  const [popupValue, setPoputValue] = useState(null);

  const editFn = (record = null) => {
    let obj = {
      allowName: current?.workFlowLicense?.allowName,
      headName: current?.headName,
      ...record,
      allowTime: {
        date: dayjs(record?.allowTime).format('YYYY-MM-DD HH:mm:ss') || Date.now(),
        mode: 'year',
      },
      allowId: record?.allowId?.split(',') || current?.workFlowLicense?.allowId?.split(','),
      headId: record?.headId?.split(',') || current?.headId?.split(','),
    };
    setPoputValue(obj);
    setVisible(true);
    //判断是新增或编辑
  };

  useImperativeHandle(ref, () => ({
    getData: () => data?.map(({ id, ...rest }) => ({ ...rest, type: 1 })),
    setData: (val) => setData(val),
  }));

  useEffect(() => {
    if (current) {
      setData(current?.workTicketExecute?.workExecuteRecordList?.filter((el) => el.type === 1) || []);
    }
  }, [current]);
  return (
    <div style={{ marginBottom: '0.8rem' }}>
      <ListCard
        title={<div>检修允许试运行</div>}
        isMore={true}
        maxLength={3}
        rightButtonFn={isAddAndEdit ? editFn : null}
        moreLink={() => {
          morePageRef.current.morePage();
        }}
        data={data}
        isExpandAndMore={true}
        children={(item: any[]) => {
          return (
            <div className={styles.listContent}>
              {item?.map((el, index) => {
                return (
                  <ChangeDivItem key={index}>
                    <div className="item_top">
                      <div>{`${index + 1}、`}</div>
                      <div className="item_top_left">
                        <div className="item_top_flex">
                          <span>允许运行时间：{el.allowTime}</span>
                        </div>
                        <div className="item_top_time">
                          <span>{el.allowName}</span>
                          <span>(工作许可人)</span>
                          <span>，</span>
                          <span>{el.headName}</span>
                          <span>(工作负责人)</span>
                        </div>
                      </div>
                      {isAddAndEdit ? (
                        <div
                          className="item_top_right"
                          onClick={() => {
                            editFn(el);
                          }}
                        >
                          <EditSOutline />
                        </div>
                      ) : null}
                    </div>
                  </ChangeDivItem>
                );
              })}
            </div>
          );
        }}
      />
      <MorePage
        ref={morePageRef}
        title="检修允许试运行"
        right={
          isAddAndEdit ? (
            <div
              className={styles.addBtn}
              onClick={() => {
                editFn();
              }}
            >
              <AddCircleOutline />
            </div>
          ) : null
        }
      >
        <div className={styles.listContent} style={{ backgroundColor: '#FFF', padding: '0 1rem' }}>
          {data?.map((el, index) => {
            return (
              <ChangeDivItem key={index}>
                <div className="item_top">
                  <div>{`${index + 1}、`}</div>
                  <div className="item_top_left">
                    <div className="item_top_flex">
                      <span>允许运行时间：{el.allowTime}</span>
                    </div>
                    <div className="item_top_time">
                      <span>{el.allowName}</span>
                      <span>(工作许可人)</span>
                      <span>，</span>
                      <span>{el.headName}</span>
                      <span>(工作负责人)</span>
                    </div>
                  </div>
                  {isAddAndEdit ? (
                    <div
                      className="item_top_right"
                      onClick={() => {
                        editFn(el);
                      }}
                    >
                      <EditSOutline />
                    </div>
                  ) : null}
                </div>
              </ChangeDivItem>
            );
          })}
        </div>
      </MorePage>
      <FormPopup
        visible={visible}
        setVisible={setVisible}
        value={popupValue}
        title={popupValue ? '编辑' : '新增'}
        onSubmit={(values) => {
          if (typeof values === 'string') {
            let arr = data.filter((el) => el.id !== values);
            setData(arr);
            return;
          }
          /* 判断修改还是新增，并写入组件data */
          let obj = {
            ...values,
            allowTime: dayjs(values.allowTime.date).format('YYYY-MM-DD HH:mm:ss'),
            allowId: getUserName(values.allowId, 'id') || popupValue?.allowId,
            allowName: getUserName(values.allowId, 'name') || popupValue?.allowName,
            headId: getUserName(values.headId, 'id') || popupValue?.headId,
            headName: getUserName(values.headId, 'name') || popupValue?.headName,
          };
          if (popupValue?.id) {
            let arr = data?.map((el) => {
              if (el.id === popupValue?.id) {
                return { ...el, ...obj };
              } else {
                return el;
              }
            });
            setData(arr);
          } else {
            let arr = [...data, { ...obj, id: Math.floor(Math.random() * (9999 - 10 + 1)) + 10 }];
            setData(arr);
          }
        }}
      >
        <DatePickerItem
          name="allowTime"
          label="允许运行时间"
          rules={[{ required: true, message: '此项必选' }]}
          datePickerConfig={{
            dateType: 'single',
            value: { date: Date.now(), mode: 'minute' },
            initialMode: 'minute',
            onChange: (value) => console.log(value),
          }}
        />
        <UserPicker label="工作许可人" name="allowId" showUsers={true} placeholder="请选择用户" title="用户选择" />
        <UserPicker label="工作负责人" name="headId" showUsers={true} placeholder="请选择用户" title="用户选择" />
      </FormPopup>
    </div>
  );
});
export default Maintenance;
