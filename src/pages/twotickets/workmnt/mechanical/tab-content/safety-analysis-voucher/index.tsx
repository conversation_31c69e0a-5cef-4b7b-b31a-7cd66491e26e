import { useRef, useState, forwardRef, useImperativeHandle, useMemo } from 'react';
import { Footer } from 'antd-mobile';
import ChangeUser from '@/pages/twotickets/workmnt/components/changgeUser/index';
import WorkMemberChange from '@/pages/twotickets/workmnt/components/workMemberChange/index';
import Maintenance from './components/maintenance/index';
import MaintenanceRestore from './components/maintenanceRestore/index';
import MaintenanceIstructions from '@/pages/twotickets/workmnt/components/maintenanceIstructions/index';
import WorkTicketExtension from './components/workTicketExtension/index';
import WorkFlowReturns from './components/workFlowReturns/index';
import { useParams, history } from 'umi';

interface Iprops {
  current: any;
  ref: any;
  ticketType?: string;
}

/**
 * @description 此工作票执行组件未封装权限，各票无法直接import使用，可以import下方的各模块小组件使用
 */
const SafetyAnalysisVoucher: React.FC<Iprops> = forwardRef((props, ref) => {
  const { current, ticketType } = props;

  const { type, id, actionType } = useParams();

  const maintenanceRestoreRef = useRef<any>(null);
  const maintenanceRef = useRef<any>(null);
  const maintenanceIstructionsRef = useRef<any>(null);
  const workMemberChangeRef = useRef<any>(null);

  // const [isAddAndEdit, setIsAddAndEdit] = useState(true);

  //判断下方整体内容是否展示
  const isContentShow = useMemo(() => {
    let mark = false;
    if (actionType === 'edit') {
      // actionType==='edit'&&current?.headId===userInfo?.id
      if ([8, 9, 10, 11].includes(Number(current?.status)) && ticketType === '3') {
        mark = true;
      } else if ([7, 8, 9, 10].includes(Number(current?.status)) && ticketType === '8') {
        mark = true;
      } else {
        mark = false;
      }
    } else {
      mark = false;
    }
    return mark;
  }, [current]);

  //判断下方机械二票不展示的内容
  const isContentTwoShow = useMemo(() => {
    let mark = false;
    if (actionType === 'edit') {
      mark = ticketType === '3' ? true : false;
    } else {
      mark = false;
    }
    return mark;
  }, [current]);

  //判断下方工作成员变更新增或编辑权限
  const isMemberChange = useMemo(() => {
    let mark = false;
    if (actionType === 'edit') {
      mark = (ticketType === '3' && current?.status === '8') || (ticketType === '8' && current?.status === '7') ? true : false;
    } else {
      mark = false;
    }
    return mark;
  }, [current]);

  //获取工作班成员
  const workClass = useMemo(() => {
    const memo = current?.member?.split(',') || [];
    const member = current?.member?.split(',') || [];

    return memo?.map((el: string[], index: number) => ({ value: el, label: member[index] }));
  }, [current]);

  useImperativeHandle(ref, () => ({
    ...maintenanceIstructionsRef?.current,
    getWorkTicketExecute: () => {
      let obj = maintenanceIstructionsRef?.current?.getFieldsValue();
      return {
        workFlowExecute: {
          id: current?.workTicketExecute?.workFlowExecute?.id,
          mainId: current?.workTicketExecute?.workFlowExecute?.mainId,
          ...(obj ? obj : current?.workTicketExecute?.workFlowExecute),
        },
        workFlowExecuteUserExchanges: workMemberChangeRef?.current?.getData(),
        workExecuteRecordList: maintenanceRef?.current
          ?.getData()
          .concat(maintenanceRestoreRef?.current?.getData())
          .map(({ id, ...rest }: any) => rest),
      };
    },
  }));
  return (
    <div style={{ paddingTop: '0.8rem' }}>
      {/* 【工作负责人变更】 */}
      {current?.workTicketExecute?.workFlowExecuteHeadExchange?.memo && <ChangeUser current={current} />}
      {/* 【工作票延期】 */}
      {current?.workTicketExecute?.workFlowExecuteDelay?.memo && <WorkTicketExtension current={current} />}
      {/* 【每日开工和收工记录】 */}
      {!!current?.workTicketExecute?.workFlowReturns?.length && <WorkFlowReturns current={current} />}
      {isContentShow && (
        <>
          {/* 【工作成员变更 */}
          <WorkMemberChange ref={workMemberChangeRef} current={current} isAddAndEdit={isMemberChange} workClass={workClass} />
          {isContentTwoShow && (
            <>
              {/* 【检修允许试运行】 */}
              <Maintenance
                ref={maintenanceRef}
                current={current}
                isAddAndEdit={current?.status === '8' && actionType === 'edit' ? true : false}
              />
              {/* 【检修工作恢复】 */}
              <MaintenanceRestore
                ref={maintenanceRestoreRef}
                current={current}
                isAddAndEdit={current?.status === '8' && actionType === 'edit' ? true : false}
              />
            </>
          )}

          {/* 【检修交代】 */}
          <MaintenanceIstructions ref={maintenanceIstructionsRef} disabled={false} current={current} />
        </>
      )}
    </div>
  );
});
export default SafetyAnalysisVoucher;
