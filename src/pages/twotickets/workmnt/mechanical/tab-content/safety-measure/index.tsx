import React, { useRef, forwardRef, useImperativeHandle, useState, useEffect, useMemo } from 'react';
import { ListCard } from '@/component/new-list-card/list-card.component';
import MorePage from '@/pages/twotickets/workmnt/components/morePage/index';
import { AddCircleOutline, CheckOutline, EditSOutline } from 'antd-mobile-icons';
import styles from './index.less';
import { Ellipsis, CheckList, Form, TextArea } from 'antd-mobile';
import { useParams } from 'umi';
import FormPopup from '@/pages/twotickets/workmnt/components/form-popup/index';

export interface SafetyMeasureItem {
  // allowId: string;
  // allowName: string;
  // allowTime: string;
  // headId: string;
  // id: string;
  // mainId: string;
  // memo: any;
  // type: number;
  execute: string | undefined;
  id: string;
  mainId: string;
  memo?: any;
  num: number;
  restore: any;
  shouldMeasure: string;
  type: number;
}

export interface SafetyMeasureProps {
  listData: SafetyMeasureItem[];
  OK: (val: any[]) => void;
  isHeaderShow?: boolean;
  isCheckboxShow: boolean;
  defaultValue?: string[];
  disabled: boolean;
  tableTitle: string;
  isAddAndEdit: boolean;
  editFn: (val: any) => void;
}
/**
 * 安全措施内部模块封装
 * @param props
 * @param props.listData 数据列表
 * @param props.OK 选中数据回显
 * @param props.isHeaderShow 是否显示顶部的header部分（默认true）
 * @param props.isCheckboxShow 是否显示执行情况复选框（默认false）
 * @param props.defaultValue 默认选中的数据
 * @param props.isAddAndEdit 是否有新增及编辑权限
 * @returns
 */
const SafetyMeasure = (props: SafetyMeasureProps) => {
  const {
    listData,
    OK,
    isHeaderShow = true,
    isCheckboxShow = false,
    defaultValue,
    disabled = false,
    tableTitle = '',
    isAddAndEdit,
    editFn,
  } = props;
  return (
    <div className={styles.list}>
      {isHeaderShow && (
        <div className={styles.listHeader}>
          <div>{tableTitle}</div>
          {isCheckboxShow && <div>执行情况</div>}
        </div>
      )}
      <CheckList
        multiple
        value={defaultValue}
        disabled={disabled}
        extra={(active) => {
          return active ? (
            <div className={styles.activeIcon}>
              <CheckOutline />
            </div>
          ) : (
            <div className={styles.activeIcon}></div>
          );
        }}
        onChange={(arr) => {
          const idSet = new Set(arr);
          const updatedArray1 = listData?.map((item) => (idSet.has(item.id) ? { ...item, memo: true } : { ...item, memo: null }));
          OK?.(updatedArray1);
        }}
      >
        {listData?.map((item, index) => {
          return (
            <div className={styles.listContent} key={index}>
              <div className={styles.listContent_index}>{item.num}、</div>
              <div className={styles.listContent_text}>
                <Ellipsis direction="end" content={item.shouldMeasure} rows={2} expandText="展开" collapseText="收起" />
              </div>
              {isCheckboxShow && (
                <div className={styles.listContent_checkbox}>
                  <CheckList.Item value={item.id}></CheckList.Item>
                </div>
              )}
              {isAddAndEdit && (
                <div
                  className={styles.listContent_checkbox}
                  onClick={() => {
                    editFn(item);
                  }}
                >
                  <EditSOutline />
                </div>
              )}
            </div>
          );
        })}
      </CheckList>
    </div>
  );
};

interface Iprops {
  data?: any; //详情数据
  isAddAndEdit?: boolean; //是否有新增及编辑权限（默认false）
  ticketType: string; //哪一个票
}

interface EnmuType {
  data: SafetyMeasureItem[];
  type: number;
  title: string;
  tableTitle: string;
  hidden: boolean;
  disabled: boolean;
  checkboxShow: boolean;
  isAddAndEdit: boolean;
  memo?: any;
}
const SafetyMeasureComponent: React.FC<Iprops> = forwardRef((props, ref) => {
  const { isAddAndEdit = false, data, ticketType } = props;

  const { type, id, actionType } = useParams();

  const morePageRef = useRef<any>(null);
  const [expandType, setExpandType] = useState<EnmuType>(); //展开类型
  const [dataList, setDataList] = useState<SafetyMeasureItem[]>([]); //默认的值
  const [visible, setVisible] = useState(false);
  const [popupValue, setPoputValue] = useState<SafetyMeasureItem | null>(null);

  //安全分析单循环的数组枚举
  const enmuType: EnmuType[] = useMemo(() => {
    const disabled =
      actionType === 'edit' && ((data?.status === '6' && ticketType === '3') || (data?.status === '5' && ticketType === '8'))
        ? false
        : true;
    return [
      {
        data: dataList?.filter((el) => el.type === 1),
        type: 1,
        title: ticketType === '8' ? '安全措施及注意事项' : ticketType === '3' ? '检修提出执行的安全措施：' : '',
        tableTitle: ticketType === '8' ? '安全措施及注意事项' : ticketType === '3' ? '检修提出执行的安全措施（jxZX）' : '',
        hidden: true, //是否有这个(true显示)
        disabled, //复选框是否禁用
        checkboxShow: (ticketType === '3' && Number(data?.status) >= 6) || (ticketType === '8' && Number(data?.status) >= 5) ? true : false, //是否展示复选框这一列
        // isAddAndEdit: actionType === 'edit' && Number(data?.status) <= 3 ? true : false, //是否显示新增和编辑按钮
        isAddAndEdit: false, //是否显示新增和编辑按钮
      },
      {
        data: dataList?.filter((el) => el.type === 2),
        type: 2,
        title: '检修提出自理的安全措施',
        tableTitle: '检修提出自理的安全措施（jxZL）',
        hidden: ticketType === '3' ? true : false, //是否有这个
        disabled,
        checkboxShow: Number(data?.status) >= 6 ? true : false,
        // isAddAndEdit: actionType === 'edit' && Number(data?.status) <= 3 ? true : false,
        isAddAndEdit: false,
      },
      {
        data: dataList?.filter((el) => el.type === 3),
        type: 3,
        title: ticketType === '8' ? '运行值班人员补充的安全措施及注意事项' : ticketType === '3' ? '运行补充运行执行的安全措施' : '',
        tableTitle: '运行补充运行执行的安全措施（BCYXZX）',
        hidden: Number(data?.status) >= 4 ? true : false, //是否有这个
        disabled,
        checkboxShow: (ticketType === '3' && Number(data?.status) >= 6) || (ticketType === '8' && Number(data?.status) >= 5) ? true : false,
        isAddAndEdit:
          actionType === 'edit' &&
          ((['4', '5'].includes(data?.status) && ticketType === '3') || (data?.status === '4' && ticketType === '8'))
            ? true
            : false,
      },
      {
        data: dataList?.filter((el) => el.type === 4),
        type: 4,
        title: '运行补充检修自理的安全措施',
        tableTitle: '运行补充检修自理的安全措施（BCJXZL）',
        hidden: Number(data?.status) >= 4 && ticketType === '3' ? true : false, //是否有这个
        disabled,
        checkboxShow: Number(data?.status) >= 6 ? true : false,
        isAddAndEdit: actionType === 'edit' && ['4', '5'].includes(data?.status) ? true : false,
      },
    ];
  }, [dataList]);

  const editFn = (record = null) => {
    setPoputValue(record);
    setVisible(true);
    //判断是新增或编辑
  };

  useImperativeHandle(ref, () => ({
    getData: () =>
      dataList.map((el) => {
        const { id, ...rest } = el;
        return { ...rest, memo: typeof rest?.memo === 'boolean' ? (rest.memo ? '1' : '0') : rest?.memo || null };
      }),
    setData: (val: any[]) => setDataList(val),
  }));

  useEffect(() => {
    setDataList(
      data?.workSafeMeasureTickets?.map((el: SafetyMeasureItem) => ({ ...el, memo: el?.memo ? (el.memo === '1' ? true : false) : null })) ||
        [],
    );
  }, [data]);

  return (
    <div>
      {enmuType?.map((el, index) => {
        return el.hidden ? (
          <div key={index} style={{ marginBottom: '8px' }}>
            <ListCard
              title={<div>{el.title}</div>}
              isMore={true}
              refresh={[isAddAndEdit]}
              maxLength={3}
              rightButtonFn={
                el.isAddAndEdit
                  ? () => {
                      editFn();
                      setExpandType(el);
                    }
                  : (null as any)
              }
              moreLink={() => {
                morePageRef.current.morePage();
                setExpandType(el);
              }}
              data={el.data}
              children={(item: SafetyMeasureItem[]) => {
                let checkedS = dataList?.filter((item) => item.type === el.type && item.memo === true).map((item) => item.id);
                return (
                  <SafetyMeasure
                    listData={item}
                    isCheckboxShow={el.checkboxShow}
                    defaultValue={checkedS}
                    disabled={el.disabled}
                    tableTitle={el.tableTitle}
                    isAddAndEdit={el.isAddAndEdit}
                    editFn={editFn}
                    OK={(arr) => {
                      const array2Map = new Map(arr.map((item) => [item.id, item]));
                      const updatedArray1 = dataList.map((item1) => array2Map.get(item1.id) || item1);
                      setDataList(updatedArray1);
                    }}
                  />
                );
              }}
            />
          </div>
        ) : null;
      })}
      {/* <ListCard
              title={<div>检修提出执行的安全措施</div>}
              isMore={true}
              refresh={[isAddAndEdit]}
              maxLength={3}
              // rightButtonFn={isAddAndEdit ? editFn : null}
              moreLink={() => {
                morePageRef.current.morePage();
                setExpandType(1);
              }}
              data={dataList}
              children={(item: any[]) => {
                let checkedS = dataList?.filter((item) => item.type === 1 && item.memo === true).map((item) => item.id);
                return (
                  <SafetyMeasure
                    listData={item}
                    isCheckboxShow={true}
                    defaultValue={checkedS}
                    OK={(arr) => {
                      const array2Map = new Map(arr.map((item) => [item.id, item]));
                      const updatedArray1 = dataList.map((item1) => array2Map.get(item1.id) || item1);
                      setDataList(updatedArray1);
                    }}
                  />
                );
              }}
            /> */}
      <MorePage
        ref={morePageRef}
        title={expandType?.title}
        right={
          expandType?.isAddAndEdit ? (
            <div
              className={styles.addBtn}
              onClick={() => {
                editFn();
              }}
            >
              <AddCircleOutline />
            </div>
          ) : null
        }
      >
        <div className={styles.listContent} style={{ backgroundColor: '#FFF', padding: '0 1rem' }}>
          {expandType && (
            <SafetyMeasure
              // listData={expandType?.data}
              listData={dataList?.filter((item) => item.type === expandType?.type)}
              defaultValue={dataList?.filter((item) => item.type === expandType?.type && item.memo === true).map((item) => item.id)}
              isCheckboxShow={expandType?.checkboxShow}
              disabled={expandType?.disabled}
              tableTitle={expandType?.tableTitle}
              isAddAndEdit={expandType?.isAddAndEdit}
              editFn={editFn}
              OK={(arr) => {
                const array2Map = new Map(arr.map((item) => [item.id, item]));
                const updatedArray1 = dataList.map((item1) => array2Map.get(item1.id) || item1);
                setDataList(updatedArray1);
              }}
            />
          )}
          {/* <SafetyMeasure
            listData={dataList.filter((item) => item.type === expandType)}
            defaultValue={dataList?.filter((item) => item.type === expandType && item.memo === true).map((item) => item.id)}
            isCheckboxShow={true}
            OK={(arr) => {
              const array2Map = new Map(arr.map((item) => [item.id, item]));
              const updatedArray1 = dataList.map((item1) => array2Map.get(item1.id) || item1);
              setDataList(updatedArray1);
            }}
          /> */}
        </div>
      </MorePage>
      {/* 【弹窗组件写此处】弹出编辑/新增 */}
      <FormPopup
        visible={visible}
        isDeleteBottom={true}
        setVisible={setVisible}
        title={popupValue ? '编辑' : '新增'}
        value={popupValue}
        onSubmit={(values: SafetyMeasureItem) => {
          if (typeof values === 'string') {
            let arr = dataList.filter((el) => el.id !== values);
            setDataList(arr);
            return;
          }
          if (popupValue) {
            //popupValue不为false则编辑
            let arr = dataList.map((el) => {
              if (el.id === popupValue.id) {
                return { ...el, ...values };
              } else {
                return el;
              }
            });
            setDataList(arr);
          } else {
            let obj = {
              ...values,
              memo: null,
              type: values.type ?? expandType?.type,
              num: values.num ?? (expandType && expandType.data ? expandType.data.length + 1 : 1),
            };
            setDataList([...dataList, obj]);
          }
        }}
      >
        {/* <Form.Item name="num" label="序号">
          <Input type="number" placeholder="请输入序号" clearable />
        </Form.Item> */}
        <Form.Item name="shouldMeasure" label={expandType?.tableTitle} layout="vertical" rules={[{ required: true, message: '请填写' }]}>
          <TextArea placeholder={`请输入${expandType?.tableTitle}`} maxLength={5000} rows={2} showCount />
        </Form.Item>
      </FormPopup>
    </div>
  );
});
export default SafetyMeasureComponent;
