.addBtn {
  font-size: 1.6rem;
}

.listcontent>div {
  &:nth-last-child(n+2) {
    border-bottom: 1px solid rgb(238, 238, 238);
  }
}

/* 下方SafetyMeasure组件 */
.listContent {
  display: flex;
  font-size: 1.4rem;
  padding: 1.5rem 0;
}

.listContent_index {}

.listContent_text {
  flex: 1;
}

.listContent_checkbox {
  width: 5.6rem;
  display: flex;
  align-items: center;
  justify-content: center;

  :global {
    .adm-list-item-disabled.adm-list-item-disabled>.adm-list-item-content>.adm-list-item-content-extra>.adm-check-list-item-extra>* {
      border: 1px solid rgb(166, 166, 166) !important;
      background-color: rgba(166, 166, 166, 0.2) !important;
    }

    .adm-list-item {
      padding: 0;
    }

    a.adm-list-item:active:not(.adm-list-item-disabled) {
      background-color: transparent;
    }
  }
}

.list {
  width: 100%;
}

.listHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: rgba(245, 245, 245, 1);
  padding: .7667rem;
  border-radius: .3333rem;
  font-size: 1.4rem;
  color: rgba(17, 84, 237, 1);
  margin-bottom: 1.1667rem;
}

.activeIcon {
  //选中与不选中的框
  border: 1px solid rgba(17, 84, 237, 1);
  width: 1.2333rem;
  height: 1.2333rem;
  box-sizing: border-box;
  font-size: 1rem;
}