import React, { useEffect, useRef, useState } from 'react';
import TitleComponent from '@/pages/twotickets/workmnt/mechanical/components/title-component';
import styles from './two.page.less';
import useHeightScroll from '@/hooks/useHeightScroll';
import { getMechanicalTicketList } from '../services';
import { history } from 'umi';
import { usePagination, useInfiniteScroll, useRequest } from 'ahooks';
import { InfiniteScroll } from 'antd-mobile';
import TodoCard from '../components/todoCard';
import HeaderButtons from '../components/headerButton';
import { getLastNonEmpty } from '@/utils/utils';

const PAGE_SIZE = 10;
const TwoPage: React.FC = () => {
  const parentRef = useRef<HTMLDivElement>(null);
  const preChildRef = useRef<HTMLDivElement>(null);
  const nextChildRef = useRef<HTMLDivElement>(null);
  const { height } = useHeightScroll(parentRef.current?.offsetHeight, preChildRef.current?.offsetHeight, nextChildRef);

  // const [dataList, setDataList] = useState<any[]>([]);

  // useEffect(() => {
  //   getMechanicalTicketList({ ticketType: '3' })
  //     .then((res) => {
  //       console.log(res);
  //       if (res.code === '1') {
  //         setDataList(res.data.list);
  //       }
  //     })
  //     .catch((err) => {
  //       console.log(err, 'err');
  //     });
  // }, []);
  // 使用ahooks的usePagination实现react-virtualized-list滚动分页加载功能
  // 状态管理，用于控制顶部按钮的选中状态和筛选按钮的显示隐藏
  const [activeTab, setActiveTab] = useState('全部');
  const [filterValues, setFilterValues] = useState({});
  // 分页配置
  const {
    data: dataList,
    loading,
    loadMore,
    loadingMore,
    reload: handleReload,
  } = useInfiniteScroll(
    (d) => {
      const page = d ? Math.ceil(d.list.length / PAGE_SIZE) + 1 : 1;
      // 从 filterValues 中解构出需要特殊处理的字段，其它属性放到 otherFilters 中
      const { unitIds, state, examineResult, ...otherFilters } = filterValues || ({} as any);

      // 定义需要按照“取最后有效值”逻辑处理的字段映射
      // key：原参数名称，value：最终 params 中的键名
      const mapping = { unitIds: 'unitId', state: 'state', examineResult: 'examineResult' };

      const params = {
        ticketType: '8',
      };
      if (activeTab !== '全部') {
        params['isCurTask'] = activeTab === '已办';
      }
      // 统一处理 mapping 中字段：过滤数组、获取最后一项（非空），存在则赋值到 params
      Object.entries(mapping).forEach(([key, paramKey]) => {
        const value = getLastNonEmpty(filterValues[key] || []);
        if (value !== null) {
          params[paramKey] = value;
        }
      });

      // 处理其余其他字段：当值为 null 或空字符串时，直接赋值到 params
      Object.entries(otherFilters).forEach(([key, value]) => {
        if (value === null || value === '') {
          params[key] = value;
        }
      });
      return getMechanicalTicketList({ pageNum: page, pageSize: PAGE_SIZE, ...params }).then((res) => ({
        total: res?.data?.total ? Number(res?.data.total) : 0,
        list: res?.data?.list || [],
      }));
    },
    {
      threshold: 1000,
    },
  );

  const hasMore = dataList && dataList.list.length < dataList.total && !loadingMore && !loading;

  // 筛选按钮的回调
  const handleFilter = (values) => {
    setFilterValues(values);
    handleReload();
  };

  return (
    <div ref={parentRef} className={styles.operationStatistics}>
      <div ref={preChildRef} className={styles.pageTop}>
        {TitleComponent({ title: '机械二种工作票', subTitle: '机械二种工作票', right: <div></div> })}
      </div>
      <div ref={nextChildRef}>
        <HeaderButtons
          activeTab={activeTab}
          setActiveTab={(tabValue) => {
            setActiveTab(tabValue);
            handleReload();
          }}
          onFilter={handleFilter}
        />

        {/* 构建一个列表 */}
        <div className={styles.list}>
          {(dataList?.list || [])?.map((item: any, index: number) => {
            const item_data = item;
            item_data['members'] = [];
            if (item_data.operatorName) {
              item_data['members'].push(`${item_data.operatorName}(操作人)`);
            }
            if (item_data.guardianName) {
              item_data['members'].push(`${item_data.guardianName}(监护人)`);
            }
            if (item_data.watchName) {
              item_data['members'].push(`${item_data.watchName}(值班负责人)`);
            }
            if (item_data.firstShiftSupervisorName) {
              item_data['members'].push(`${item_data.firstShiftSupervisorName}(值长)`);
            }
            return (
              <TodoCard
                key={index}
                item={item_data}
                onProcess={(id) => {
                  // history.push(`/twotickets/operation/detail/${id}`);
                  history.push(`/twotickets/workmnt/mechanical/two/details?id=${id}`);
                }}
              />
            );
          })}
          <InfiniteScroll loadMore={loadMore} hasMore={hasMore} />
        </div>
      </div>
    </div>
  );
};

export default TwoPage;
