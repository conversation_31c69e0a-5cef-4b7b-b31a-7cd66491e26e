/** 机械票 */
import request from '@/utils/request';

/**
 * 获取机械票列表
 */
export async function getMechanicalTicketList(params) {
  return request(`/WorkBase/listPage`, {
    method: 'get',
    params: params,
  });
}

/**
 * 获取机械票详情
 */
export const getMechanicalTicketAPI = (id: any, params: any): Promise<any> => {
  return request(`/WorkBase/${id}`, { method: 'get', params });
};

//类型字典
export const getDeviceType = (): Promise<any> => {
  return request('/ToolDict/getTypeMap');
};
