import { Form, Selector, SideBar, TextArea } from 'antd-mobile';
import { WORKFLOW_STATE_MAP_COLOR, WORKFLOW_STATE_MAP_TEXT } from '@/constant';
import styles from '@/pages/twotickets/operation/list/operation-list.page.less';
import { usePagination, useInfiniteScroll, useRequest } from 'ahooks';
import { getOperationList, getOrganizationTree } from '@/services/twotickets/operation/statistics';
import { useEffect, useState } from 'react';
import { LinkedSelection } from '@/component/linked-selection/linked-selection.component';

/**
 * 筛选侧边栏
 * @returns
 */
const FilterSideBar = ({ onSubmit, onReset }: any) => {
  const { data: organizationTreeData } = useRequest(() => getOrganizationTree().then((res) => res.data), { manual: false });
  const [activeTab, setActiveTab] = useState('1');
  const SIDEBAR_ITEM = [
    {
      key: '1',
      title: '综合筛选',
    },
    {
      key: '2',
      title: '操作单位',
    },
    {
      key: '3',
      title: '搜索',
    },
  ];
  return (
    <div className={styles.filter}>
      <div className={styles.side}>
        <SideBar activeKey={activeTab} onChange={(key: string) => setActiveTab(key)}>
          {SIDEBAR_ITEM.map((item) => (
            <SideBar.Item key={item.key} title={item.title} />
          ))}
        </SideBar>
      </div>
      <div className={styles.content}>
        <div
          style={{
            display: activeTab === '1' ? '' : 'none',
            overflowY: 'auto',
            height: '100%',
          }}
        >
          <Form.Item name="examineResult" label="审核结果">
            <Selector
              style={{
                '--border-radius': '.6667rem',
                '--border': 'none',
                '--checked-border': 'none',
                '--padding': '0',
                '--color': 'rgba(245, 245, 245, 1)',
                '--text-color': 'rgba(51, 51, 51, 1)',
                '--checked-color': 'rgba(215, 225, 250, 1)',
                '--checked-text-color': 'rgba(17, 84, 237, 1)',
                '--gap': '8px',
              }}
              showCheckMark={false}
              options={[
                {
                  label: '全部',
                  value: '',
                },
                {
                  label: '合格',
                  value: '1',
                },
                {
                  label: '不合格',
                  value: '0',
                },
              ]}
              defaultValue={['']}
            />
          </Form.Item>
          <Form.Item name="state" label="状态">
            <Selector
              style={{
                '--border-radius': '.6667rem',
                '--border': 'none',
                '--checked-border': 'none',
                '--padding': '0',
                '--color': 'rgba(245, 245, 245, 1)',
                '--text-color': 'rgba(51, 51, 51, 1)',
                '--checked-color': 'rgba(215, 225, 250, 1)',
                '--checked-text-color': 'rgba(17, 84, 237, 1)',
                '--gap': '8px',
              }}
              showCheckMark={false}
              options={[
                {
                  label: '全部',
                  value: '',
                },
                ...Object.keys(WORKFLOW_STATE_MAP_TEXT)
                  ?.filter((key) => key)
                  .map((key) => ({
                    label: WORKFLOW_STATE_MAP_TEXT[key],
                    value: key,
                  })),
              ]}
              defaultValue={['']}
            />
          </Form.Item>
        </div>
        <Form.Item
          name="unitIds"
          style={{
            display: activeTab === '2' ? '' : 'none',
            height: '100%',
          }}
        >
          <LinkedSelection
            columns={2}
            data={organizationTreeData}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: '#fff',
              zIndex: 1,
              borderRadius: '.6667rem',
              overflow: 'hidden',
              boxSizing: 'border-box',
              padding: '.8rem 1.2rem',
              border: 'none',
            }}
          />
        </Form.Item>
        <div
          style={{
            display: activeTab === '3' ? '' : 'none',
            overflowY: 'auto',
            height: '100%',
          }}
        >
          <Form.Item name="task" label="操作任务">
            <TextArea placeholder="请输入" rows={2} />
          </Form.Item>
          <Form.Item name="code" label="操作票号">
            <TextArea placeholder="请输入" rows={2} />
          </Form.Item>
          <Form.Item name="operatorName" label="操作人">
            <TextArea placeholder="请输入" rows={2} />
          </Form.Item>
          <Form.Item name="guardianName" label="操作人">
            <TextArea placeholder="请输入" rows={2} />
          </Form.Item>
        </div>
      </div>
    </div>
  );
};

export default FilterSideBar;
