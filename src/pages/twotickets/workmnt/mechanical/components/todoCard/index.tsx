import { Card, Button, List, Image } from 'antd-mobile';
import { WORKFLOW_STATE_MAP_COLOR, WORKFLOW_STATE_MAP_TEXT } from '@/constant';
import styles from '@/pages/twotickets/operation/list/operation-list.page.less';
import jixie from '@/assets/twotickets/jixie.png';
import dianqi from '@/assets/twotickets/dianqi.png';
/**
 * 是否有处理按钮
 * @param record 操作票信息
 * @returns
 */
const hasReview = (record: any) => {
  return true;
  console.log(record.curTask);
  if (record.curTask) {
    return true;
  }
  return false;
};

/* 单个卡片组件，负责展示单个列表项 */
const TodoCard = ({ item, onProcess }) => (
  <Card
    key={item.id}
    style={{ marginBottom: '1.1111vw' }}
    title={
      <span
        style={{
          fontWeight: 'bold',
          color: Boolean(item?.state === 1 ? item?.guardianId : '')
            ? '#f00'
            : WORKFLOW_STATE_MAP_COLOR[WORKFLOW_STATE_MAP_TEXT?.[item?.state === undefined || item?.state === null ? 1 : item.state]],
        }}
      >
        {' '}
        {WORKFLOW_STATE_MAP_TEXT[item?.state === undefined || item?.state === null ? 1 : item.state]}
      </span>
    }
    extra={
      hasReview(item) ? (
        <Button
          size="small"
          onClick={() => onProcess(item.id)}
          style={{
            '--background-color': 'rgba(215, 225, 250, 1)',
            '--text-color': 'rgba(17, 84, 237, 1)',
            '--border-width': '0rem',
            '--border-radius': '1.2rem',
            fontSize: '1.2rem',
          }}
        >
          处理
        </Button>
      ) : null
    }
  >
    <List
      border={false}
      style={{
        '--border-top': 'none',
        '--border-bottom': 'none',
        '--border-inner': 'none',
        '--padding-left': 0,
        '--padding-right': 0,
      }}
    >
      <List.Item>
        <div className={styles.name}>
          <Image
            src={item?.type === '机械操作票' ? jixie : dianqi}
            width={'8.8889vw'}
            height={'4.5369vw'}
            style={{
              display: 'inline-block',
              marginRight: '2.2222vw',
              lineHeight: '6.4814vw',
              verticalAlign: 'middle',
            }}
          />

          {item.name}
        </div>
      </List.Item>
      <List.Item className={styles.item}>
        <span className={styles.label}>单位</span>: {item.unitName}
      </List.Item>
      <List.Item className={styles.item}>
        <span className={styles.label}>成员</span>: {item?.members?.join('，')}
      </List.Item>
    </List>
  </Card>
);

export default TodoCard;
