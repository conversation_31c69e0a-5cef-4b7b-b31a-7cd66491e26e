import React from 'react';
import { Card, Button, Toast, Space } from 'antd-mobile';
import { AntOutline, RightOutline, DownOutline } from 'antd-mobile-icons';
import styles from './index.less';
import { ProcessCard } from '@/component/process-card/process-card.component';

const ProcessCards = () => {
  const records = [
    {
      taskName: '任务一',
      approvalResult: '待提交',
      // approvalTime: '2023-10-01T10:00:00Z',
      // opinion: '已提交至下一节点',
    },
    {
      taskName: '任务二',
      approvalResult: '通过',
      approvalTime: '2023-10-02T12:00:00Z',
      opinion: '审核通过',
    },
  ];
  //body区域点击事件
  const onBodyClick = (e) => {
    console.log(e, '点击了body');
  };

  //header区域点击事件
  const onHeaderClick = (e) => {
    console.log(e, '点击了header');
  };

  return (
    // <Card
    //   title={<div style={{ fontWeight: 'normal' }}>处理流程</div>}
    //   extra={
    //     <div style={{ fontSize: '1.2rem', color: 'rgba(17, 84, 237, 1)' }}>
    //       查看流程图
    //       <RightOutline />
    //     </div>
    //   }
    //   onBodyClick={onBodyClick}
    //   onHeaderClick={onHeaderClick}
    //   style={{ borderRadius: '16px' }}
    // >
    //   <div className={styles.content}>
    //     <ProcessCard id={1} records={records} taskUserName="张三" onProcess={(id) => console.log('处理任务:', id)} />
    //   </div>
    //   <div className={styles.footer} onClick={(e) => e.stopPropagation()}>
    //     <Space style={{ fontSize: '1.4rem' }}>
    //       <a>全部流程</a>
    //       <DownOutline />
    //     </Space>
    //   </div>
    // </Card>
    <ProcessCard id={1} records={records} taskUserName="张三" onProcess={(id) => console.log('处理任务:', id)} />
  );
};
export default ProcessCards;
