import { Button } from 'antd-mobile';
import { FilterOutline } from 'antd-mobile-icons';
import { FilterPopup } from '@/component/filter-popup/filter-popup.component';
import FilterSideBar from '../filterSideBar';

/* 顶部按钮组件，负责状态选择和筛选按钮 */
const HeaderButtons = ({ activeTab, setActiveTab, onFilter }) => (
  <div className="header" style={{ padding: '12px', display: 'flex', justifyContent: 'space-between' }}>
    <div className="flex-1" style={{ display: 'flex', flexWrap: 'wrap' }}>
      {['全部', '待办', '已办'].map((tab) => (
        <Button
          key={tab}
          style={{ marginRight: '8px', marginBottom: '8px' }}
          onClick={() => setActiveTab(tab)}
          shape="rounded"
          className={`tag-type ${activeTab === tab ? 'active' : ''}`}
        >
          {tab}
        </Button>
      ))}
    </div>
    {/* 筛选弹框 */}
    <FilterPopup
      iconRender={<FilterOutline fontSize={24} />}
      onSubmit={(values) => {
        onFilter(values);
      }}
    >
      <FilterSideBar />
    </FilterPopup>
  </div>
);

export default HeaderButtons;
