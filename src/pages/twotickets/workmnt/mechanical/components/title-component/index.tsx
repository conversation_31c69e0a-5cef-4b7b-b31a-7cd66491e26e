import { NavBar } from 'antd-mobile';
import { styled } from 'umi';

type TwoTicketsNavBarProps = {
  title: string; // 标题
  subTitle?: string; // 副标题
  right?: React.ReactNode; // 右侧操作按钮
};

const NavBarWrapper = styled(NavBar)`
  background-color: transparent;
  /* color: #fff; */
  font-weight: 700;
  font-size: 0.8rem;
  height: auto;
  .adm-nav-bar-left {
    flex: none;
    .adm-nav-bar-back {
      margin-right: 0;
    }
  }
  .adm-nav-bar-title {
    text-align: left;
    font-size: 1.8rem;
    font-weight: 400;
    letter-spacing: 0.009rem;
    line-height: 2.3667rem;
    /* color: rgba(255, 255, 255, 1); */
    text-align: left;
    vertical-align: top;
    padding: 0;
    &-subtitle {
      font-size: 1.2rem;
      font-weight: 400;
    }
  }
`;

/**
 * 两票导航栏
 * @param porps
 * @returns
 */
const TitleComponent = ({ title, subTitle = null, right = null }: TwoTicketsNavBarProps) => {
  return (
    <NavBarWrapper
      onBack={() => {
        history.back();
      }}
      right={right}
    >
      {title}
      {subTitle && <div className="adm-nav-bar-title-subtitle">{subTitle}</div>}
    </NavBarWrapper>
  );
};

export default TitleComponent;
