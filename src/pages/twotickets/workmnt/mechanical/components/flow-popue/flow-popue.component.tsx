import { useEffect } from 'react';
import { Button, Form, Grid, Popup, TextArea, Radio, Space } from 'antd-mobile';
import TwoTicketsNavBar from '@/component/twotickets-navbar/twotickets-navbar.component';
import styles from './flow-popue.component.less';
import { DatePickerItem } from '@/component/dynamic-form/items/date-picker-popup/date-picker-popup.component';
import UserPicker from '@/pages/twotickets/workmnt/components/user-picker/user-picker.component';
import RadioGroup from '@/pages/twotickets/workmnt/electrical/components/radio-group/component';

interface FlowPopupProps {
  title: string; // 标题
  visible: boolean; // 是否显示
  onClose: () => void; //  关闭
  onSubmit: (values: any) => void; // 提交
  children?: React.ReactNode;
  options?: any; //用于调试，传表单多配置
}

/**
 * 流程审核的弹窗组件
 * @param props
 * @param props.title 标题
 * @param props.visible 是否显示
 * @param props.onClose 关闭
 * @param props.onSubmit 提交
 * @param props.children 子组件
 * @returns
 */
const FlowPopup: React.FC<FlowPopupProps> = (props: FlowPopupProps) => {
  const { title, visible, onClose, onSubmit, children, options = null } = props;
  const [form] = Form.useForm();

  useEffect(() => {
    form.resetFields();
  }, [visible]);

  useEffect(() => {
    let arr = Object.keys(options);

    arr.forEach((key) => {
      if (options[key].defaultValue) {
        form.setFieldValue(options[key].name, options[key].defaultValue);
      }
    });
  }, [options, visible]);

  return (
    <Popup
      visible={visible}
      onMaskClick={onClose}
      showCloseButton={false}
      destroyOnClose
      className={styles.popup}
      bodyStyle={{
        borderTopLeftRadius: '0',
        borderTopRightRadius: '0',
        minHeight: '100vh',
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      <TwoTicketsNavBar title={title} bgcolor={'#fff'} color={'#000'} onBack={onClose} />

      <div className={styles.body} style={{ backgroundColor: '#FFF' }}>
        <Form
          form={form}
          className={styles.form}
          onFinish={(values) => {
            onSubmit(values);
            // onClose();
          }}
          style={
            {
              // '--adm-color-background': 'rgba(245, 245, 245, 1)',
            }
          }
        >
          {children}
          {options?.resultRadio && (
            <Form.Item label="审核结果" name="behavior" required>
              <Radio.Group>
                <Space direction="horizontal">
                  <Radio value="合格">合格</Radio>
                  <Radio value="不合格">不合格</Radio>
                </Space>
              </Radio.Group>
            </Form.Item>
          )}
          {options?.userCheck && (
            <UserPicker
              label={options?.userCheck?.label}
              name={options?.userCheck?.name}
              rules={options?.userCheck?.rules || []}
              multiple={false}
              showUsers={true}
              placeholder="请选择用户"
              title="用户选择"
            />
          )}
          {options?.date && (
            <DatePickerItem
              name={options?.date?.name}
              label={options?.date?.label}
              rules={options?.date?.rules || []}
              format="YYYY-MM-DD HH:mm"
              datePickerConfig={{
                dateType: 'single',
                value: { date: Date.now(), mode: 'minute' },
                initialMode: 'minute',
                onChange: (value) => console.log(value),
              }}
            />
          )}
          {options?.textarea && (
            <Form.Item
              name={options?.textarea?.name}
              label={options?.textarea?.label}
              rules={options?.textarea.rules || []}
              layout="vertical"
            >
              <TextArea placeholder={options?.TextArea?.placeholder || '请输入'} maxLength={5000} rows={2} showCount />
            </Form.Item>
          )}
        </Form>
      </div>
      <Grid
        className={styles.footer}
        columns={2}
        gap={8}
        style={{
          width: '100%',
        }}
      >
        <Grid.Item>
          <Button
            block
            size="large"
            className={styles.btn}
            style={{
              borderRadius: '3.3333vw',
              color: 'rgba(17, 84, 237, 1)',
              '--border-color': 'rgba(17, 84, 237, 1)',
            }}
            // disabled={loading || authLoading}
            data-testid="operate-save-btn"
            onClick={onClose}
          >
            取消
          </Button>
        </Grid.Item>
        <Grid.Item span={1}>
          <Button
            block
            color="primary"
            size="large"
            className={styles.btn}
            style={{
              borderRadius: '3.3333vw',
            }}
            data-testid="operate-auth-btn"
            // disabled={loading || authLoading}
            onClick={() => {
              form.submit();
            }}
          >
            提交
          </Button>
        </Grid.Item>
      </Grid>
    </Popup>
  );
};

export default FlowPopup;
