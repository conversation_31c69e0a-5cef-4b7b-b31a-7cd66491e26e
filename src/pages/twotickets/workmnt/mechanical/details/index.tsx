import React, { useEffect, useRef, useState, useMemo } from 'react';
import { ProcessCard } from '@/component/process-card/process-card.component';
import styles from './index.less';
import { Tabs, Toast } from 'antd-mobile';
import SafetyTool from '@/pages/twotickets/workmnt/mechanical/tab-content/safetyTool/index';
import SafetyAnalysisVoucher from '@/pages/twotickets/workmnt/mechanical/tab-content/safety-analysis-voucher/index';
import TwoTicketsNavBar from '@/component/twotickets-navbar/twotickets-navbar.component';

import { getWorkBaseDetail, updateWorkBase } from '@/services/twotickets/workmnt/workmnt';
import { OperationHeaderBtn } from '@/pages/twotickets/operation/detail/components/operation-btn/operation-header-btn.component';
import SecurityRiskControlContent from '@/pages/twotickets/workmnt/components/security-risk-control-content/security-risk-control.content';
import { useParams, history } from 'umi';
import { useRequest, useThrottleFn } from 'ahooks';
import { getTicketTypeAndTitle, WORKMNT_ELECTRICAL_STATUSES_MAP } from '@/constant';
import { BasicInfoComponent } from '../tab-content/basic-info/basic-info.component';
import SafetyMeasureComponent from '../tab-content/safety-measure/index';
import ActionButtons from '@/pages/twotickets/workmnt/components/action-buttons/action-buttons.component';
import dayjs from 'dayjs';
// import FlowPopup from '@/pages/twotickets/workmnt/components/flow-popue/flow-popue.component';
import FlowPopup from '../components/flow-popue/flow-popue.component';
import { delayAudit, headerExchangeAudit, ticketReturnAudit } from '@/services/twotickets/workmnt/workmnt';
import _ from 'lodash';
const TAB_HEIGHT = 42;

function formatDatesInObject(obj: any) {
  // 遍历对象的每个键值对
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const value = obj[key];

      // 如果值是对象且不为null（因为typeof null === 'object'）
      if (typeof value === 'object' && value !== null) {
        // 检查是否有date属性
        if ('date' in value) {
          const dateValue: number = value.date;

          // 检查是否是时间戳（数字或可转换为数字的字符串）
          if (
            (typeof dateValue === 'number' || (typeof dateValue === 'string' && !isNaN(dateValue))) &&
            dateValue.toString().length >= 10
          ) {
            try {
              // 使用dayjs转换时间戳
              const formattedDate = dayjs(Number(dateValue)).format('YYYY-MM-DD HH:mm:ss');
              // 将格式化后的日期赋值给上级对象
              obj[key] = formattedDate;
              continue; // 已经转换，跳过递归处理
            } catch (e) {
              console.warn(`无法转换date值: ${dateValue}`, e);
            }
          }
        }

        // 递归处理嵌套对象
        formatDatesInObject(value);
      }
    }
  }
  return obj;
}

interface ConditionParams {
  pass: string;
  behavior: string;
  status: string;
  isOwnerSignerAudit?: string;
  sub?: string;
  memo?: string;
  extra?: boolean;
  nextStatus?: string;
  code?: string;
  variables?: any;
}

const DetailsPage: React.FC = () => {
  const [activeKey, setActiveKey] = useState('1');
  const safetyMeasureRef = useRef<any>(null);
  const executeRef = useRef<any>(null);
  const [visible, setVisible] = useState(false);
  const [variablesValue, setVariablesValue] = useState<ConditionParams>();
  const [flowButtonText, setFlowButtonText] = useState('');

  const isReturn = useMemo(() => {
    return variablesValue?.pass === 'false' ? false : true;
  }, [variablesValue]);

  const [btnLoading, setBtnLoading] = useState(false);
  // 获取url参数
  const { type, id, actionType } = useParams();
  const { title, ticketType } = getTicketTypeAndTitle('mechanical', type);
  /* 下方新增 */
  const getDetail = () => {
    return getWorkBaseDetail({
      id,
      ticketType,
    }).then((res) => res.data);
  };

  const { data, loading } = useRequest(getDetail);

  const { run: handleScroll } = useThrottleFn(
    () => {
      let currentKey = TAB_ITEMS[0].key;
      for (const item of TAB_ITEMS) {
        const element = document.getElementById(`anchor-${item.key}`);
        if (!element) continue;
        const rect = element.getBoundingClientRect();
        if (rect.top <= TAB_HEIGHT) {
          currentKey = item.key;
        } else {
          break;
        }
      }
      setActiveKey(currentKey);
    },
    {
      leading: true,
      trailing: true,
      wait: 100,
    },
  );

  useEffect(() => {
    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  const TAB_ITEMS = useMemo(
    () => [
      {
        key: '1',
        title: '基本信息',
        content: <BasicInfoComponent data={data} />,
      },
      {
        key: '2',
        title: '工作票执行',
        content:
          (Number(data?.status) >= 8 && ticketType === '3') || (Number(data?.status) >= 7 && ticketType === '8') ? (
            <SafetyAnalysisVoucher ref={executeRef} current={data} ticketType={ticketType} />
          ) : null,
      },
      {
        key: '3',
        title: '安全风险控制卡',
        content: <SecurityRiskControlContent data={data} />,
      },
      {
        key: '4',
        title: '安全措施',
        content: <SafetyMeasureComponent ref={safetyMeasureRef} data={data} ticketType={ticketType} />,
      },
      {
        key: '5',
        title: '安全工器具',
        content: <SafetyTool current={data} />,
      },
    ],
    [data],
  );

  //配置弹窗
  const options = useMemo(() => {
    let obj: any = {};
    let status = data?.status;
    let sub = variablesValue?.sub;
    let memo = variablesValue?.memo ? Number(variablesValue?.memo) : null;
    //子流程的弹窗配置
    if (memo && sub) {
      //有子流程
      //延期
      if (sub === 'delay') {
        if (memo === 2) {
          obj.textarea = {
            name: 'opinion',
            label: '延期理由',
            placeholder: '请输入延期理由',
          };
        } else if (memo === 5 && isReturn) {
          obj.date = {
            name: 'newEndTime',
            label: '有效期延长到',
            rules: [{ required: true, message: '此项必填' }], //有则有校验，无则无校验
            defaultValue: {
              date: dayjs(data?.planEndTime).valueOf(),
              mode: 'minute',
            },
          };
          obj.textarea = {
            name: 'opinion',
            label: '审核意见',
            placeholder: '请输入...',
          };
        } else {
          obj.textarea = {
            name: 'opinion',
            label: '审核意见',
            placeholder: '请输入...',
          };
        }
        return obj;
      }
      //变更
      if (sub === 'change') {
        if (memo === 2) {
          obj.userCheck = {
            name: 'newHeadId',
            label: '变更负责人',
            rules: [{ required: true, message: '此项必填' }],
          };
          obj.date = {
            name: 'changeTime',
            label: '变更时间',
            rules: [{ required: true, message: '此项必填' }], //有则有校验，无则无校验
            defaultValue: {
              date: Date.now(),
              mode: 'minute',
            },
          };
          obj.textarea = {
            name: 'opinion',
            label: '变动情况说明',
            placeholder: '请输入...',
          };
        } else {
          obj.textarea = {
            name: 'opinion',
            label: '审核意见',
            placeholder: '请输入...',
          };
        }
        return obj;
      }

      //交回
      let newReturnMemo = data?.workTicketExecute?.workFlowReturns[0]?.memo;
      if (sub === 'return') {
        // if (newReturnMemo || ['1', '4'].includes(newReturnMemo)) {
        if (memo === 2) {
          obj.date = {
            name: 'submitTime',
            label: '时间',
            rules: [{ required: true, message: '此项必填' }], //有则有校验，无则无校验
          };
          obj.textarea = {
            name: 'remark',
            label: '备注',
            placeholder: '请输入...',
          };
        } else if (newReturnMemo === '3') {
          obj.date = {
            name: 'grantTime',
            label: '时间',
            rules: [{ required: true, message: '此项必填' }], //有则有校验，无则无校验
          };
          obj.textarea = {
            name: 'grantRemark',
            label: '备注',
            placeholder: '请输入...',
          };
        } else {
          obj.textarea = {
            name: 'opinion',
            label: '审核意见',
            placeholder: '请输入...',
          };
        }
        return obj;
      }
    }

    //主流程的
    if (status === '4' && isReturn) {
      obj.date = {
        name: 'accessTime',
        label: '确认时间',
        rules: [{ required: true, message: '此项必选' }], //有则有校验，无则无校验
        defaultValue: {
          date: Date.now(),
          mode: 'minute',
        },
      };
    }
    if (status === '5' && ticketType === '3' && isReturn) {
      obj.date = {
        name: 'approveEndTime',
        label: '批准工作结束时间',
        rules: [{ required: true, message: '此项必选' }],
        defaultValue: {
          date: dayjs(data?.planEndTime).valueOf(),
          mode: 'minute',
        },
      };
    }
    if (((status === '6' && ticketType === '3') || (status === '5' && ticketType === '8')) && isReturn) {
      obj.date = {
        name: 'allowBeginTime',
        label: '许可开始工作时间',
        rules: [{ required: true, message: '此项必选' }],
        defaultValue: {
          date: Date.now(),
          mode: 'minute',
        },
      };
    }

    if (((status === '13' && ticketType === '3') || (status === '11' && ticketType === '8')) && isReturn) {
      obj.date = {
        name: 'endTime',
        label: '工作结束时间',
        rules: [{ required: true, message: '此项必选' }],
      };
    }

    if ((status === '13' && ticketType === '3') || (status === '11' && ticketType === '8')) {
      obj.textarea = {
        name: 'remark',
        label: '备注',
      };
    } else if ((status === '14' && ticketType === '3') || (status === '12' && ticketType === '8')) {
      obj.resultRadio = {
        name: 'behavior',
        defaultValue: '不合格',
      };
      obj.textarea = {
        name: 'opinion',
        label: '审核意见',
        placeholder: '请输入...',
      };
    } else {
      obj.textarea = {
        name: 'opinion',
        label: '审核意见',
        placeholder: ['2', '3'].includes(status) ? '确认本工作票上述各项内容：' : '请输入...',
      };
    }

    return obj;
  }, [data, isReturn, visible]);

  const closeSaveBtn = useMemo(() => {
    if (ticketType === '3' && [4, 5, 6, 7, 8].includes(Number(data?.status))) {
      return true;
    } else if (ticketType === '8' && [4, 5, 7].includes(Number(data?.status))) {
      return true;
    } else {
      return false;
    }
  }, [data, ticketType]);

  const disabledConfig = useMemo(() => {
    return {
      工作延期: data?.workTicketExecute?.workFlowExecuteDelay?.memo === '5' ? true : false,
      工作负责人变更: data?.workTicketExecute?.workFlowExecuteHeadExchange?.memo === '4' ? true : false,
    };
  }, [data]);

  /**
   * 验证表单提交时的必要条件
   * @param status - 状态
   * @returns 是否验证通过
   */
  const validateFormSubmission = (payload: any) => {
    const status = data?.status ? Number(data?.status) : 0; // 当前流程状态
    const { planBeginTime, planEndTime, workFlowConfirm, workFlowRatify, workFlowLicense, workFlowExecuteConfirm, workFlowEnd } = payload;
    return new Promise((resolve) => {
      Promise.all([executeRef?.current?.validateFields?.()])
        .then(() => {
          const planEndTimeD = dayjs(planEndTime), //计划结束时间
            planBeginTimeD = dayjs(planBeginTime), //计划开始时间
            accessTimeD = dayjs(workFlowConfirm?.accessTime), //值班负责人确认时间（1/2票都是4）
            approveEndTimeD = dayjs(workFlowRatify?.approveEndTime), //值长批准工作结束时间（一票5，二票无）
            approveTuneD = dayjs(workFlowRatify?.approveTune), //自动生成，值长批准时间
            allowBeginTimeD = dayjs(workFlowLicense?.allowBeginTime), //许可人许可开始时间（1票6,2票5）
            endTimeD = dayjs(workFlowEnd?.endTime), //工作结束时间（1票13，2票11）
            shiftSupervisorTimeD = dayjs(workFlowExecuteConfirm?.shiftSupervisorTime);

          let errorMessage = '';
          if (ticketType === '3' && status === 5 && (approveEndTimeD.isBefore(accessTimeD) || approveEndTimeD.isAfter(planEndTimeD))) {
            // 值长审批：批准工作结束时间默认是计划结束日期（修改时要求不能早于确认时间，晚于计划结束日期，之前页面设计时写的默认当前时间，需要修改一下； 备注：一般批准工作结束时间就是计划结束日期 ）
            errorMessage = '批准工作结束时间不能早于运行值班负责人确认时间，晚于计划结束时间';
          } else if (
            ticketType === '3' &&
            status === 6 &&
            allowBeginTimeD.isBefore(approveTuneD) &&
            allowBeginTimeD.isAfter(approveEndTimeD)
          ) {
            //机械一（有值长）
            // 许可人审批：许可开始工作时间默认是当前时间（修改时要求：不能早于值长批准时间，晚于批准工作结束时间）
            errorMessage = '许可开始工作时间不能早于值长批准时间，晚于批准工作结束时间';
          } else if (
            ticketType === '8' &&
            status === 5 &&
            allowBeginTimeD.isBefore(planBeginTimeD) &&
            allowBeginTimeD.isAfter(planEndTimeD)
          ) {
            //机械二（无值长）
            // 许可人审批：许可开始工作时间默认是当前时间（修改时要求：不能早于值长批准时间，晚于批准工作结束时间）
            errorMessage = '许可开始工作时间不能早于计划开始，晚于计划结束时间';
          } else if (
            ticketType === '3' &&
            status === 13 &&
            (endTimeD.isBefore(shiftSupervisorTimeD) || allowBeginTimeD.isAfter(approveEndTimeD))
          ) {
            // 许可人终结：工作结束时间默认是当前时间 （修改时要求：不能早于工作票执行确认时间，晚于批准作业结束时间）
            errorMessage = '许可开始工作时间不能早于值长批准时间，晚于批准工作结束时间';
          }

          if (errorMessage) {
            Toast.show({
              icon: 'fail',
              content: errorMessage,
            });
            resolve(false);
          } else {
            resolve(true);
          }
        })
        .catch((error) => {
          Toast.show({
            icon: 'fail',
            content: `请填写必填项`,
          });
          resolve(false);
          console.log(error);
        });
    });
  };

  // 暂存、提交
  const onSubmit = async (values: any) => {
    const safetyMeasureFormData = safetyMeasureRef?.current?.getData(); // 安全措施
    let executeData = null;
    if (data?.status && ['8', '9', '10'].includes(data.status)) {
      executeData = executeRef?.current?.getWorkTicketExecute() || null; // 工作票执行
    }
    const params = {
      ...data,
      ticketType,
      workSafeMeasureTickets: safetyMeasureFormData,
      workTicketExecute: executeData,
      isStandardTicket: 0,
      id: data?.id ? data.id : null,
      isAudit: 1,
      ...variablesValue,
      variables: {
        ...variablesValue,
      },
      ...values,
    };
    // 校验
    if (params.isAudit === 1) {
      // 走通过的审批或者终结的时候做校验
      const validateForm = await validateFormSubmission({ ...params });
      if (!validateForm) {
        return;
      } else {
        //走流程，且校验通过的审批接口
        setBtnLoading(true);
        const res = await updateWorkBase(params);
        setBtnLoading(false);
        if (res?.code === '1') {
          Toast.show({
            icon: 'success',
            content: '处理成功',
          });
          history.back();
        } else {
          Toast.show({
            icon: 'fail',
            content: res.message,
          });
        }
      }
    } else {
      //不保存，无需校验
      setBtnLoading(true);
      const res = await updateWorkBase(params);
      setBtnLoading(false);
      if (res?.code === '1') {
        Toast.show({
          icon: 'success',
          content: '保存成功',
        });
        history.back();
      } else {
        Toast.show({
          icon: 'fail',
          content: res.message,
        });
      }
    }
    // 审批接口
  };

  const handleSubmit = (values: any) => {
    //判断是否子流程，子流程有子流程的请求
    let formattedObject: any = formatDatesInObject(values);
    if (formattedObject?.accessTime) {
      formattedObject = {
        ...formattedObject,
        workFlowConfirm: {
          accessTime: formattedObject?.accessTime,
        },
      };
    }
    if (formattedObject?.approveEndTime) {
      formattedObject = {
        ...formattedObject,
        workFlowRatify: {
          approveEndTime: formattedObject?.approveEndTime,
        },
      };
    }
    if (formattedObject?.allowBeginTime) {
      formattedObject = {
        ...formattedObject,
        workFlowLicense: {
          allowBeginTime: formattedObject?.allowBeginTime,
        },
      };
    }
    if (formattedObject?.endTime) {
      formattedObject = {
        ...formattedObject,
        workFlowEnd: {
          endTime: formattedObject?.endTime,
        },
      };
    }
    if (variablesValue?.sub && variablesValue?.sub !== 'cancel' && variablesValue?.sub !== 'back') {
      let params = {
        // ...data?.workTicketExecute?.workFlowExecuteHeadExchange,
        ticketType,
        mainId: data?.id,
        taskId: data?.taskId,
        processInstanceId: data?.processInstanceId || null,
        ...variablesValue,
        variables: {
          ...variablesValue,
        },
        ...values,
      };

      //是子流程，分别判断
      if (variablesValue?.sub === 'delay') {
        const newEndTimeD = dayjs(params?.newEndTime), //延期时间
          planEndTimeD = dayjs(data?.planEndTime), //计划结束时间
          approveEndTimeD = dayjs(data?.workFlowRatify?.approveEndTime); //值长批准结束时间
        if (params?.newEndTime && newEndTimeD.isBefore(planEndTimeD) && newEndTimeD.isBefore(approveEndTimeD) && ticketType === '3') {
          Toast.show({
            icon: 'fail',
            content: '延期时间不得早于计划结束时,不得早于值长批准时间',
          });
          return;
        }
        if (params?.newEndTime && newEndTimeD.isBefore(planEndTimeD) && ticketType === '8') {
          Toast.show({
            icon: 'fail',
            content: '延期时间不得早于计划结束时',
          });
          return;
        }
        delayAudit({ ...data?.workTicketExecute?.workFlowExecuteDelay, ...params })
          .then((res) => {
            setBtnLoading(false);
            if (res.code === '1') {
              history.back();
              Toast.show({
                icon: 'success',
                content: '提交成功',
              });
            } else {
              Toast.show({
                icon: 'fail',
                content: res.message,
              });
            }
          })
          .catch((err) => {
            console.log(err, '延期提交失败');
          });
      } else if (variablesValue?.sub === 'change') {
        //变更负责人
        if (variablesValue?.memo === '2' && params.newHeadId) {
          params.nowHeadId = values?.newHeadId?.[0].userId;
          params.nowHeadName = values?.newHeadId?.[0].name;
          params.changeTime = dayjs(values?.changeTime?.date).format('YYYY-MM-DD HH:mm:ss');
        } else if (variablesValue?.memo === '2' && !params.newHeadId) {
          Toast.show({
            icon: 'fail',
            content: '请选择变更负责人',
          });
          return;
        }
        headerExchangeAudit({ ...data?.workTicketExecute?.workFlowExecuteHeadExchange, ...params })
          .then((res) => {
            setBtnLoading(false);
            if (res.code === '1') {
              history.back();
              Toast.show({
                icon: 'success',
                content: '提交成功',
              });
            } else {
              Toast.show({
                icon: 'fail',
                content: res.message,
              });
            }
          })
          .catch((err) => {
            console.log(err, '提交失败');
          });
      } else {
        //交回
        let returnParams = {
          ...data?.workTicketExecute?.workFlowReturns[0],
          ...params,
          submitTime: values?.submitTime
            ? dayjs(values?.submitTime?.date).format('YYYY-MM-DD HH:mm:ss')
            : dayjs().format('YYYY-MM-DD HH:mm:ss'),
          grantTime: values?.grantTime ? dayjs(values?.grantTime?.date).format('YYYY-MM-DD HH:mm:ss') : null,
          id: data?.workTicketExecute?.workFlowReturns[0]?.memo === '4' ? null : data?.workTicketExecute?.workFlowReturns[0]?.id,
        };
        ticketReturnAudit(returnParams)
          .then((res) => {
            setBtnLoading(false);
            if (res.code === '1') {
              history.back();
              Toast.show({
                icon: 'success',
                content: '提交成功',
              });
            } else {
              Toast.show({
                icon: 'fail',
                content: res.message,
              });
            }
          })
          .catch((err) => {
            console.log(err, '提交失败');
          });
      }
    } else {
      onSubmit({ ...variablesValue, ...formattedObject });
    }
    //此处需判断主流程和子流程（弹窗提交）
    //此处需判断通过还是退回
    // setVisible(false);
  };

  const handleClose = () => {
    setVisible(false);
  };

  return (
    <div className={styles.container} style={{ paddingTop: `${window.top?.localStorage?.topHeight ?? 0}px` }}>
      <TwoTicketsNavBar
        title={data?.taskName}
        subTitle={title}
        bgcolor={'#fff'}
        color={'#000'}
        right={<OperationHeaderBtn data={data} />}
      />
      <div className={styles.body}>
        <div className={styles.content}>
          <ProcessCard
            id={data?.id}
            records={_.cloneDeep(data?.workflowApprovalRecordList || []).reverse()}
            onProcess={function (id: number): void {
              history.push(`/twotickets/common/check-list/process-viewer/${data?.id}?ticketType=${ticketType}`);
            }}
            taskUserName={data?.taskUserName}
          />
        </div>

        <div className={styles.tabsContainer}>
          <Tabs
            activeKey={activeKey}
            onChange={(key) => {
              setActiveKey(key);
              document.getElementById(`anchor-${key}`)?.scrollIntoView();
              window.scrollTo({
                top: window.scrollY - TAB_HEIGHT,
              });
            }}
          >
            {TAB_ITEMS.map((item) => item.content && <Tabs.Tab title={item.title} key={item.key} />)}
          </Tabs>
        </div>

        <div className={styles.tabsContent}>
          {TAB_ITEMS.map(
            (item: any) =>
              item.content && (
                <div key={item.key} id={`anchor-${item.key}`}>
                  {item.content}
                </div>
              ),
          )}
        </div>
        <div className={styles.bottom}>已经到底了</div>
      </div>
      {actionType === 'edit' && data?.curTask && (
        <div className={styles.operation}>
          {/* 操作按钮 */}
          <ActionButtons
            onClick={(actionType, variables, title) => {
              setVariablesValue(variables);
              if (actionType !== '保存') {
                if (variables?.extra) {
                  setFlowButtonText(title as string);
                  setVisible(true);
                } else {
                  onSubmit({ ...variables, variables: { ...variables } });
                }
              } else {
                onSubmit({ isAudit: 0 });
              }
            }}
            showSaveBtn={closeSaveBtn}
            taskId={data?.taskId}
            disabledConfig={disabledConfig}
            loading={btnLoading}
            detailData={data}
          // moreBtn={[
          //   {
          //     text: 'item.name',
          //     key: 'item.code',
          //     disabled: false,
          //     onClick: () => {
          //       console.log('111111111111');
          //     },
          //   },
          //   {
          //     text: 'item.name2',
          //     key: 'item.code2',
          //     disabled: false,
          //     onClick: () => {},
          //   },
          // ]}
          />
        </div>
      )}

      {/* 审核流程弹窗 */}
      <FlowPopup title={flowButtonText} visible={visible} onClose={handleClose} onSubmit={handleSubmit} options={options}></FlowPopup>
    </div>
  );
};
export default DetailsPage;
