import React from 'react';

import { useParams } from 'umi';
import { useRequest } from 'ahooks';
import { PullToRefresh, InfiniteScroll, List, DotLoading, Button, Empty } from 'antd-mobile';
import { RightOutline } from 'antd-mobile-icons';
import _ from 'lodash';

import TwoTicketsNavBar from '@/component/twotickets-navbar/twotickets-navbar.component';
import HeaderFilterComponent from '@/component/header-filter/header-filter.component';
import { getOrganizationTree } from '@/services/twotickets/operation/statistics';
import { getTicketTypeAndTitle, WORKMNT_ELECTRICAL_STATUSES_MAP } from '@/constant';
import { useUserSelectOptions } from '@/hooks/useUserSelectOptions';

import { useStatusInfo } from './hooks/useStatusInfo';
import { useProcessorsDisplay } from './hooks/useProcessorsDisplay';
import { useTicketFilter } from './hooks/useTicketFilter';
import { useFormConfig } from './hooks/useFormConfig';
import { useTicketData } from './hooks/useTicketData';
import { useTicketActions } from './hooks/useTicketActions';
import { createStatusOptions, SEARCH_TYPES, ALL_OPTION } from './constants/selectOptions';

import styles from './workmnt.page.less';

interface TicketItemProps {
  item: {
    id: string;
    name?: string;
    unitName?: string;
    member?: string;
    status: string;
    ticketType?: string;
    workFireUpdateDelete?: {
      canUpdate: boolean;
    };
    curTask?: any;
    headName?: string;
    ownerSignerName?: string;
    allowName?: string;
    finalAllowUserName?: string;
    dutyName?: string;
    fireExecuteName?: string;
  };
  onView: (ticketId: string) => void;
  onProcess: (ticketId: string) => void;
  ticketType?: string;
  userOptions?: any[];
}

const TicketItem = React.memo(({ item, onView, onProcess, ticketType = '1', userOptions = [] }: TicketItemProps) => {
  const { getStatusLabel, getStatusColor } = useStatusInfo(ticketType);
  const { getProcessorsDisplay } = useProcessorsDisplay(ticketType, userOptions);

  return (
    // @ts-ignore
    <List mode="card" key={item.id}>
      <List.Item>
        <div className={styles.row} onClick={() => onView(item.id)}>
          {item?.workFireUpdateDelete?.canUpdate || (item.curTask && ![6, 7]?.includes(Number(item?.ticketType))) ? (
            <div className={styles.dealwith}>
              {/* @ts-ignore */}
              <Button
                shape="rounded"
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  onProcess(item.id);
                }}
                style={{
                  '--background-color': 'rgba(215, 225, 250, 1)',
                  '--border-color': 'rgba(215, 225, 250, 1)',
                  '--text-color': 'rgba(17, 84, 237, 1)',
                }}
              >
                处理
              </Button>
            </div>
          ) : null}
          <div className={styles.title} style={{ color: getStatusColor(item.status) }}>
            <div className={styles.label}>{getStatusLabel(item.status)}</div>
            <RightOutline fontSize={16} />
          </div>
          <div className={styles.item}>
            <div className={styles['name-value']}>{item.name || '暂无名称'}</div>
          </div>
          <div className={styles.item}>
            <div className={styles.label}>单位：</div>
            <div className={styles.value}>{item.unitName || '暂无单位'}</div>
          </div>
          <div className={styles.item}>
            <div className={styles.label}>处理人：</div>
            <div className={styles.value}>{getProcessorsDisplay(item, ticketType)}</div>
          </div>
        </div>
      </List.Item>
    </List>
  );
});

interface WorkmngPageLayoutProps {
  [key: string]: any;
}

const WorkmngPageLayout = (props: WorkmngPageLayoutProps) => {
  const params = useParams();
  const { data: organizationTreeData } = useRequest(() => getOrganizationTree().then((res) => res.data), { manual: false });

  const { type = '', workmntType = '' } = params;
  const { title, ticketType } = getTicketTypeAndTitle(workmntType, type);

  // 只在动火票类型时获取用户数据
  const shouldLoadUsers = ticketType === '6' || ticketType === '7';
  const userOptions = useUserSelectOptions('id', { enabled: shouldLoadUsers });
  
  // 使用抽离的 hooks
  const { filterValues, handleFilterSubmit, handleFilterTypeChange } = useTicketFilter();
  const { dataList, loading, loadMore, loadingMore, onRefresh, hasMore } = useTicketData(ticketType, filterValues);
  const { handleProcess, handleView } = useTicketActions(workmntType, type);

  // 票据状态选项
  const WORKFLOW_STATE_MAP_TEXT = WORKMNT_ELECTRICAL_STATUSES_MAP[ticketType as keyof typeof WORKMNT_ELECTRICAL_STATUSES_MAP] || {};
  const TICKET_STATUS_OPTIONS = Object.keys(WORKFLOW_STATE_MAP_TEXT).length > 0
    ? createStatusOptions(WORKFLOW_STATE_MAP_TEXT as Record<string, { label: string }>)
    : [ALL_OPTION];

  // 使用表单配置 hook
  const { customMenuItems } = useFormConfig(TICKET_STATUS_OPTIONS, organizationTreeData);

  return (
    <div className={styles.container} style={{ paddingTop: `${window.top?.localStorage?.topHeight ?? 0}px` }}>
      {TwoTicketsNavBar({
        title,
        bgcolor: 'rgba(17, 84, 237, 1)',
        color: '#fff',
      })}
      <HeaderFilterComponent
        menuItems={customMenuItems}
        activeType={filterValues?.isDone || ""}
        initialValues={{ examineResult: [''], state: [''] }}
        onFilterSubmit={handleFilterSubmit}
        onTypeChange={handleFilterTypeChange}
        searchTypes={SEARCH_TYPES}
      />

      <div className={styles.content}>
        {/* 列表 */}
        {dataList?.list?.length ? (
          <PullToRefresh
            onRefresh={onRefresh}
            threshold={40}
            completeDelay={500}
            renderText={(status) => {
              return (
                <div className={styles.pullingText}>
                  {status === 'pulling' && '下拉刷新'}
                  {status === 'canRelease' && '释放立即刷新'}
                  {status === 'refreshing' && (
                    <div>
                      刷新中
                      {/* @ts-ignore */}
                      <DotLoading />
                    </div>
                  )}
                  {status === 'complete' && '刷新完成'}
                </div>
              );
            }}
          >
            {/* @ts-ignore */}
            {dataList?.list?.map?.((item: any) => (
              // @ts-ignore
              <TicketItem 
                key={item.id} 
                item={item} 
                onProcess={handleProcess} 
                onView={handleView} 
                ticketType={ticketType} 
                userOptions={shouldLoadUsers ? userOptions : []}
              />
            ))}

            <InfiniteScroll loadMore={async () => {
              if (!loading && !loadingMore) {
                await loadMore();
              }
            }} hasMore={hasMore || false} threshold={100}>
            </InfiniteScroll>
          </PullToRefresh>
        ) : (
          <Empty description="暂无数据" />
        )}
      </div>
    </div>
  );
};

export default WorkmngPageLayout;
