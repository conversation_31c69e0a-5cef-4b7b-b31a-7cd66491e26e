
export interface OperateBase {
  /**
   * 操作票编号
   */
  code?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 创建人id
   */
  createUserId?: number;
  /**
   * 创建人名称
   */
  createUserName?: string;
  /**
   * kks编码
   */
  deviceKksCode?: string;
  /**
   * 设备名称
   */
  deviceName?: string;
  /**
   * 审核结果
   */
  examineResult?: number;
  /**
   * 审核意见
   */
  examineView?: string;
  /**
   * 第一次审核值长id
   */
  firstShiftSupervisorId?: number;
  /**
   * 第一次审核值长名称
   */
  firstShiftSupervisorName?: string;
  formKey?: string;
  /**
   * 监护人id
   */
  guardianId?: number;
  /**
   * 监护人名称
   */
  guardianName?: string;
  /**
   * id
   */
  id?: string;
  /**
   * 图片id
   */
  imgId?: number;
  isCurTask?: boolean;
  /**
   * 操作地点
   */
  location?: string;
  /**
   * memo
   */
  memo?: string;
  /**
   * 当月序号
   */
  mouthNumber?: number;
  /**
   * 操作开始时间
   */
  operateBeginTime?: string | Dayjs | null;
  /**
   * 操作结束时间
   */
  operateEndTime?: string | Dayjs | null;
  /**
   * 危险点全部删除标识
   */
  operateRiskDelete?: number;
  /**
   * 危险点与预防措施
   */
  operateRiskList?: OperateRiskNew[];
  /**
   * 操作人id
   */
  operatorId?: string;
  /**
   * 操作人名称
   */
  operatorName?: string;
  /**
   * 操作备注
   */
  operatorRemark?: string;
  /**
   * 操作项全部删除标识
   */
  operatorStepDelete?: number;
  /**
   * 操作项
   */
  operatorStepList?: OperatorStepNew[];
  /**
   * 发令值长id
   */
  orderShiftSupervisorId?: number;
  /**
   * 发令值长名称
   */
  orderShiftSupervisorName?: string;
  /**
   * 发令提交时间
   */
  orderSubmitTime?: string;
  /**
   * 发令时间
   */
  orderTime?: string;
  /**
   * 计划开始时间
   */
  planBeginTime?: string;
  /**
   * 计划结束时间
   */
  planEndTime?: string;
  /**
   * 流程id
   */
  processInstanceId?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 文件id
   */
  reportId?: number;
  /**
   * 第二次审核值长id
   */
  secondShiftSupervisorId?: number;
  /**
   * 第二次审核值长名称
   */
  secondShiftSupervisorName?: string;
  /**
   * 安全工器具全部删除标识
   */
  standardOperatorToolTypeDelete?: number;
  /**
   * 安全工器具
   */
  standardOperatorToolTypeList?: StandardOperatorToolTypeNew[];
  /**
   * 状态
   */
  state?: number;
  /**
   * 状态名称
   */
  stateName?: string;
  /**
   * 操作任务
   */
  task?: string;
  taskDefKey?: string;
  taskId?: string;
  taskName?: string;
  taskUserName?: string;
  /**
   * 操作票类型
   */
  type?: string;
  /**
   * 操作单位id
   */
  unitId?: number;
  /**
   * 操作单位名称
   */
  unitName?: string;
  /**
   * 修改时间
   */
  updateTime?: string;
  /**
   * 值班负责人id
   */
  watchId?: number;
  /**
   * 值班负责人名称
   */
  watchName?: string;
  /**
   * 流程状态
   */
  workflowState?: number;
  workFlowStateName?: string;
  [property: string]: any;
}

