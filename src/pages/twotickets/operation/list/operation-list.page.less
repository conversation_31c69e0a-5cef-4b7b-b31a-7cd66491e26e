.operationList{
  font-family: PingFang SC;
  width: 100%;
  height: 100vh;
  border: 0;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  :global{
    .tag-type{
      font-size: 42px;
      font-weight: 400;
      letter-spacing: 0px;
      color: rgba(102, 102, 102, 1);
      &.active{
        color: rgba(17, 84, 237, 1);
        font-weight: 500;
      }
    }
  }
  .list{
    width: 100%;
    flex: 1;
    padding: 0 36px;
    box-sizing: border-box;
    overflow-y: auto;
    :global{
      .adm-card{
        padding: 39.999px 36px;
      }
      .adm-card-header, .adm-list-body{
        border-bottom: none
      }
      .adm-card-header{
        padding: 0 0 21px;
      }
      .adm-card-body{
        padding: 0;
        .adm-list-item-content-main {
          padding: 0 0 15.999px;
        }
      }
    }
    .name{
      font-size: 42px;
      font-weight: 400;
      letter-spacing: 0px;
      line-height: 69.999px;
      color: rgba(56, 56, 56, 1);
    }
    .item{
      font-size: 36px;
      font-weight: 400;
      letter-spacing: 0px;
      line-height: 52.131px;
      color: rgba(51, 51, 51, 1);
      .label{
        color: rgba(153, 153, 153, 1);
      }
    }
  }
}
.filter{
  flex: 1;
  height: 100%;
  background-color: #ffffff;
  display: flex;
  justify-content: flex-start;
  align-items: stretch;
  [data-prefers-color='dark'] & {
    background-color: unset;
  }
  .side{
    flex: none;
  }
  .content{
    box-sizing: border-box;
    font-family: PingFang SC;
    width: 100%;
    :global{
      // .adm-list-body {
      //   border: none;
        .adm-form-item-label {
          font-size: 48px;
          font-weight: 500;
          letter-spacing: 0px;
          line-height: 69.999px;
          color: rgba(51, 51, 51, 1);
          margin-bottom: 39px;
        }
        .adm-text-area {
          box-sizing: border-box;
          padding: 21.999px 44.001px;
          border-radius: 20.001px;
          background: rgba(245, 245, 245, 1);
          // 提示文字改小点
          .adm-text-area-element::placeholder {
            font-size: 36px;
          }
        }
        .adm-list-default .adm-list-body{
          border: none;
        }
        .adm-selector .adm-space.adm-space {
          --gap: 15px;
        }
        .adm-selector-item {
          display: flex;
          align-items: center;
          justify-content: center;
          text-align: center;
          width: 216px;
          height: 102px;
          font-size: 36px;
          font-weight: 400;
          letter-spacing: 0px;
        }
      }
    // }
  }
}