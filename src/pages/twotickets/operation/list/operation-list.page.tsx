import { But<PERSON>, Card, List, Tag, Toast, Image, InfiniteScroll, SideBar, Form, TextArea, Input, Selector, Skeleton } from 'antd-mobile';
import { FilterOutline } from 'antd-mobile-icons';
import VirtualizedList from 'react-virtualized-list';
import TwoTicketsNavBar from '@/component/twotickets-navbar/twotickets-navbar.component';
import styles from './operation-list.page.less';
import React, { useEffect, useMemo, useState } from 'react';
import { getOperationList, getOrganizationTree } from '@/services/twotickets/operation/statistics';
import { usePagination, useInfiniteScroll, useRequest } from 'ahooks';
import { WORKFLOW_STATE_MAP_COLOR, WORKFLOW_STATE_MAP_TEXT } from '@/constant';
import jixie from '@/assets/twotickets/jixie.png';
import dianqi from '@/assets/twotickets/dianqi.png';
import { FilterPopup } from '@/component/filter-popup/filter-popup.component';
import { LinkedSelection } from '@/component/linked-selection/linked-selection.component';
import { getLastNonEmpty } from '@/utils/utils';
import { history, useSearchParams } from 'umi';
import FlowTitleComponent from '@/component/flow-title/flow-title.component';
import { Empty } from 'antd';
import { Statuses } from '@/utils/constant';
import { useOperationsStore } from '../../stores/operation.store';
/**
 * 是否有处理按钮
 * @param record 操作票信息
 * @returns
 */
export const hasReview = (record: any) => {
  if (record?.curTask) {
    return true;
  }
  return false;
};

/* 顶部按钮组件，负责状态选择和筛选按钮 */
const HeaderButtons = ({ activeTab, onChangeActiveTab, onFilter }) => {
  const [filterPopupVisible, setFilterPopupVisible] = useState(false);
  return (
    <div className="header" style={{ padding: '1.1111vw', display: 'flex', justifyContent: 'space-between' }}>
      <div className="flex-1" style={{ display: 'flex', flexWrap: 'wrap' }}>
        {['全部', '待办', '已办'].map((tab) => (
          <Button
            key={tab}
            style={{ marginRight: '.7407vw', marginBottom: '.7407vw' }}
            onClick={() => onChangeActiveTab(tab)}
            shape="rounded"
            data-testid={`filter-is-complete-${tab}`}
            className={`tag-type ${activeTab === tab ? 'active' : ''}`}
          >
            {tab}
          </Button>
        ))}
      </div>
      {/* 筛选弹框 */}
      <FilterPopup
        iconRender={<FilterOutline fontSize={24} />}
        onSubmit={(values) => {
          onFilter(values);
        }}
        onVisible={(visible) => {
          setFilterPopupVisible(visible);
        }}
      >
        {filterPopupVisible && <FilterSideBar />}
      </FilterPopup>
    </div>
  );
};

/* 单个卡片组件，负责展示单个列表项 */
const TodoCard = ({ item, onProcess, index }) => (
  <Card
    key={item.id}
    style={{ marginBottom: '1.1111vw' }}
    title={<FlowTitleComponent state={item?.state} guardianId={item?.guardianId} />}
    data-testid={`row-${index + 1}`}
    extra={
      hasReview(item) && item.state > 1 ? (
        <Button
          size="small"
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
            onProcess(item.id, 'edit');
          }}
          style={{
            '--background-color': 'rgba(215, 225, 250, 1)',
            '--text-color': 'rgba(17, 84, 237, 1)',
            '--border-width': '0vw',
            '--border-radius': '3.3333vw',
            fontSize: '3.3333vw',
          }}
        >
          处理
        </Button>
      ) : null
    }
    onClick={(e) => {
      e.stopPropagation();
      onProcess(item.id, 'view');
    }}
  >
    <List
      border={false}
      style={{
        '--border-top': 'none',
        '--border-bottom': 'none',
        '--border-inner': 'none',
        '--padding-left': 0,
        '--padding-right': 0,
      }}
    >
      {/* 第一行：显示标签及首条数据 */}
      <List.Item>
        <div className={styles.name}>
          <Image
            src={item?.type === '机械操作票' ? jixie : dianqi}
            width={'8.8889vw'}
            height={'4.8842vw'}
            fit="fill"
            style={{
              display: 'inline-block',
              marginRight: '2.2222vw',
              lineHeight: '6.4814vw',
              verticalAlign: 'middle',
            }}
          />

          {item.name}
        </div>
      </List.Item>
      {/* 后续数据行 */}
      <List.Item className={styles.item}>
        <span className={styles.label}>单位</span>: {item.unitName}
      </List.Item>
      <List.Item className={styles.item}>
        <span className={styles.label}>成员</span>: {item?.members?.join('，')}
      </List.Item>
    </List>
  </Card>
);

/**
 * 筛选侧边栏
 * @returns
 */
const FilterSideBar = ({ onSubmit, onReset }: any) => {
  const { data: organizationTreeData, run: getOrganizationTreeData } = useRequest(() => getOrganizationTree().then((res) => res.data), {
    manual: true,
  });
  const [activeTab, setActiveTab] = useState('1');
  const SIDEBAR_ITEM = [
    {
      key: '1',
      title: '综合筛选',
    },
    {
      key: '2',
      title: '操作单位',
    },
    {
      key: '3',
      title: '搜索',
    },
  ];
  useEffect(() => {
    getOrganizationTreeData();
  }, []);
  return (
    <div className={styles.filter} style={{ paddingTop: `${window.top?.localStorage?.topHeight ?? 0}px` }}>
      <div className={styles.side} style={{ paddingTop: `${window.top?.localStorage?.topHeight ?? 0}px` }}>
        <SideBar activeKey={activeTab} onChange={(key: string) => setActiveTab(key)}>
          {SIDEBAR_ITEM.map((item) => (
            <SideBar.Item key={item.key} title={item.title} data-testid={`filter-sidebar-${item.title}`} />
          ))}
        </SideBar>
      </div>
      <div className={styles.content} data-testid="filter-sidebar-content">
        <div
          style={{
            display: activeTab === '1' ? '' : 'none',
            overflowY: 'auto',
            height: '100%',
          }}
          data-testid="filter-sidebar-content-综合筛选"
        >
          <Form.Item name="examineResult" label="审核结果" data-testid="filter-examine-result">
            <Selector
              style={{
                '--border-radius': '1.8519vw',
                '--border': 'none',
                '--checked-border': 'none',
                '--padding': '0',
                '--color': 'rgba(245, 245, 245, 1)',
                '--text-color': 'rgba(51, 51, 51, 1)',
                '--checked-color': 'rgba(215, 225, 250, 1)',
                '--checked-text-color': 'rgba(17, 84, 237, 1)',
                '--gap': '.7407vw',
              }}
              showCheckMark={false}
              options={[
                {
                  label: '全部',
                  value: '',
                },
                {
                  label: '合格',
                  value: '1',
                },
                {
                  label: '不合格',
                  value: '0',
                },
              ]}
              defaultValue={['']}
            />
          </Form.Item>
          <Form.Item name="state" label="状态" data-testid="filter-state">
            <Selector
              style={{
                '--border-radius': '1.8519vw',
                '--border': 'none',
                '--checked-border': 'none',
                '--padding': '0',
                '--color': 'rgba(245, 245, 245, 1)',
                '--text-color': 'rgba(51, 51, 51, 1)',
                '--checked-color': 'rgba(215, 225, 250, 1)',
                '--checked-text-color': 'rgba(17, 84, 237, 1)',
              }}
              showCheckMark={false}
              options={[
                {
                  label: '全部',
                  value: '',
                },
                ...Object.keys(Statuses).map((key) => ({
                  label: Statuses[key].name,
                  value: key,
                })),
              ]}
              defaultValue={['']}
            />
          </Form.Item>
        </div>
        <div
          style={{
            display: activeTab === '2' ? '' : 'none',
            height: '100%',
          }}
          data-testid="filter-sidebar-content-操作单位"
        >
          <Form.Item
            name="unitIds"
            style={{
              height: '100%',
            }}
            data-testid="filter-unitIds"
          >
            <LinkedSelection
              columns={2}
              data={organizationTreeData}
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                backgroundColor: '#fff',
                zIndex: 1,
                borderRadius: '1.8519vw',
                overflow: 'hidden',
                boxSizing: 'border-box',
                padding: '2.2222vw 3.3333vw',
                border: 'none',
              }}
            />
          </Form.Item>
        </div>
        <div
          style={{
            display: activeTab === '3' ? '' : 'none',
            overflowY: 'auto',
            height: '100%',
          }}
          data-testid="filter-sidebar-content-搜索"
        >
          <Form.Item name="task" label="操作任务" data-testid="filter-task">
            <TextArea placeholder="请输入" rows={2} />
          </Form.Item>
          <Form.Item name="code" label="操作票号" data-testid="filter-code">
            <TextArea placeholder="请输入" rows={2} />
          </Form.Item>
          <Form.Item name="operatorName" label="操作人" data-testid="filter-operatorName">
            <TextArea placeholder="请输入" rows={2} />
          </Form.Item>
          <Form.Item name="guardianName" label="监护人" data-testid="filter-guardianName">
            <TextArea placeholder="请输入" rows={2} />
          </Form.Item>
        </div>
      </div>
    </div>
  );
};

const PAGE_SIZE = 10;

/**
 * 操作列表
 * @returns
 */
const OperationList = () => {
  // 使用ahooks的usePagination实现react-virtualized-list滚动分页加载功能
  // 状态管理，用于控制顶部按钮的选中状态和筛选按钮的显示隐藏
  const [activeTab, setActiveTab] = useState('全部');
  const [filterValues, setFilterValues] = useState({});
  // 分页配置
  const {
    data: dataList,
    loading,
    loadMore,
    loadingMore,
    reload: handleReload,
  } = useInfiniteScroll(
    (d) => {
      // 设置参数
      const page = d ? Math.ceil(d.list.length / PAGE_SIZE) + 1 : 1;
      // 从 filterValues 中解构出需要特殊处理的字段，其它属性放到 otherFilters 中
      const { unitIds, state, examineResult, ...otherFilters } = filterValues || ({} as any);

      // 定义需要按照“取最后有效值”逻辑处理的字段映射
      // key：原参数名称，value：最终 params 中的键名
      const mapping = { unitIds: 'unitId', state: 'state', examineResult: 'examineResult' };

      const params = {};
      if (activeTab !== '全部') {
        params['isDone'] = activeTab !== '已办' ? 1 : 0;
      }
      // 统一处理 mapping 中字段：过滤数组、获取最后一项（非空），存在则赋值到 params
      Object.entries(mapping).forEach(([key, paramKey]) => {
        const value = getLastNonEmpty(filterValues[key] || []);
        if (value !== null) {
          params[paramKey] = value;
        }
      });

      // 处理其余其他字段：当值为 null 或空字符串时，直接赋值到 params
      Object.entries(otherFilters).forEach(([key, value]) => {
        if (value !== null && value !== '') {
          params[key] = value;
        }
      });
      return getOperationList({ pageNum: page, pageSize: PAGE_SIZE, ...params }).then((res) => ({
        total: res?.data?.total ? Number(res?.data.total) : 0,
        list: res?.data?.list || [],
      }));
    },
    {
      threshold: 1000,
      reloadDeps: [activeTab, filterValues],
    },
  );

  const hasMore = useMemo(() => {
    return dataList && dataList.list.length < dataList.total;
  }, [dataList, loadingMore, loading]);

  // 筛选按钮的回调
  const handleFilter = (values) => {
    setFilterValues(values);
  };

  useEffect(() => {
    if (!loading && !loadingMore) {
      handleReload();
    }
  }, [filterValues, activeTab]);

  return (
    <div className={styles.operationList} data-testid="operation-list" style={{ paddingTop: `${window.top?.localStorage?.topHeight ?? 0}px` }}>
      <TwoTicketsNavBar title="操作票" bgcolor={'rgba(17, 84, 237, 1)'} color={'#fff'} />
      {dataList?.list?.length ? (
        <>
          <HeaderButtons
            activeTab={activeTab}
            onChangeActiveTab={(tabValue) => {
              setActiveTab(tabValue);
            }}
            onFilter={handleFilter}
          />

          {/* 构建一个列表 */}
          {!loading ? (
            <div className={styles.list}>
              {(dataList?.list || [])?.map((item: any, index: number) => {
                const item_data = item;
                item_data['members'] = [];
                if (item_data.operatorName) {
                  item_data['members'].push(`${item_data.operatorName}(操作人)`);
                }
                if (item_data.guardianName) {
                  item_data['members'].push(`${item_data.guardianName}(监护人)`);
                }
                if (item_data.watchName) {
                  item_data['members'].push(`${item_data.watchName}(值班负责人)`);
                }
                if (item_data.firstShiftSupervisorName) {
                  item_data['members'].push(`${item_data.firstShiftSupervisorName}(值长)`);
                }
                return (
                  <TodoCard
                    key={index}
                    index={index}
                    item={item_data}
                    onProcess={(id, type) => {
                      history.push(`/twotickets/operation/detail/${id}?type=${type}`);
                    }}
                  />
                );
              })}
              <InfiniteScroll
                loadMore={() => {
                  if (!loading && !loadingMore) {
                    loadMore();
                  }
                }}
                hasMore={hasMore}
              />
            </div>
          ) : (
            <Skeleton.Paragraph />
          )}
        </>
      ) : (
        <Empty description="暂无数据" />
      )}
    </div>
  );
};

export default OperationList;
