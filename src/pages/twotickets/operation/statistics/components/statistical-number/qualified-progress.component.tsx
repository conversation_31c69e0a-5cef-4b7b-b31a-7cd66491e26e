import { Slider } from 'antd-mobile';
import {styled} from 'umi';

const QualifiedProgressWapper = styled.div`
  margin-top: 3.8889vw;
  .title {
    /** 文本1 */
    font-size: 3.7036vw;
    font-weight: 400;
    letter-spacing: .0186vw;
    line-height: 4.9075vw;
    color: rgba(102, 102, 102, 1);
    text-align: left;
    vertical-align: top;
  }
  .steps{
    display: flex;
    flex-direction: row;
    align-items: center;
    font-size: 3.3333vw;
    font-weight: 400;
    letter-spacing: .0167vw;
    line-height: 5.0925vw;
    color: rgba(153, 153, 153, 1);
    font-family: pangmeng;
    .adm-slider{
      flex: 1;
      padding-left: 0;
      padding-right: 0;
      &-track{
        height: 1.4814vw;
          opacity: 1;
          border-radius: .7408vw;
          background: rgba(244, 244, 246, 1);
      }
      &-fill{
        height: 1.4814vw;
        opacity: 1;
        border-radius: .7408vw;
        background: linear-gradient(90deg, rgba(17, 84, 237, 1) 0%, rgba(72, 168, 238, 1) 57%, rgba(113, 232, 239, 1) 100%);
      }
      &-thumb-container{
        width: 2.7778vw;
        height: 2.7778vw;
        opacity: 1;
        background: rgba(113, 232, 239, 1);
      }
      &-thumb{
        display: none;
      }
    }
    .num{
      margin-left: 7.3147vw;
      font-size: 4.4444vw;
      font-weight: 400;
      letter-spacing: .0222vw;
      line-height: 5.0925vw;
      color: rgba(17, 84, 237, 1);
    }
  }
`;

type QualifiedProgressProps = {
  num?: number; // 合格率, 默认值 0
};

/**
 * 审核票合格率
 * @param {number} num 数值
 * @return {*}
 */
const QualifiedProgress = (props: QualifiedProgressProps) => {
  const { num = 0 } = props;

  return (
    <QualifiedProgressWapper>
      <div className='title'>审核合格率</div>
      <div className='steps'>
        <Slider value={num} /> <span className='num'>{num}</span>%
      </div>
    </QualifiedProgressWapper>
  );
};

export default QualifiedProgress;
