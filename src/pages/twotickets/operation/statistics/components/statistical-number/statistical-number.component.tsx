import {styled} from 'umi';
type StatisticalNumberProps = {
  num: number; // 数值
  title: string; // 标题
  unit?: string; // 单位, 可以不传，默认值 '个'
  position: 'top' | 'bottom'; // 默认值 bottom，可选值 top, bottom
};

// 上下结构样式，上层内容包含数字和单位，数字在左侧，单位右侧，居中显示，下层显示标题
const StatisticalNumberWapper = styled.div`
  text-align: center;
  .statistical-number-content {
    font-family: pangmeng;
    font-size: 5.3703vw;
    font-weight: 400;
    letter-spacing: .0269vw;
    line-height: 6.1111vw;
    color: rgba(17, 84, 237, 1);
    .num {
      margin-right: 1.1111vw;
      &.zero{
        color: rgba(102, 102, 102, 1);
      }
    }
    .unit {
      /** 文本3 */
      font-size: 3.3333vw;
      font-weight: 400;
      letter-spacing: .0167vw;
      line-height: 6.1111vw;
      color: rgba(153, 153, 153, 1);
    }
  }
  .statistical-number-title {
    /** 文本1 */
    font-size: 3.8889vw;
    font-weight: 400;
    letter-spacing: .0194vw;
    line-height: 5.1853vw;
    color: rgba(51, 51, 51, 1);
    vertical-align: top;
    margin-bottom: .7407vw;
  }
`;
/**
 * @description: 统计数字组件
 * @param {number} num 数值
 * @param {string} title 标题
 * @param {string} unit 单位, 可以不传，默认值 '个'
 * @return {*}
 */
const StatisticalNumber = (props: StatisticalNumberProps) => {
  const { num, title, unit = '个', position = 'bottom' } = props;
  return (
    <StatisticalNumberWapper>
      {position === 'top' && <div className="statistical-number-title">{title}</div>}
      <div className="statistical-number-content">
        <span className={`num ${!num && 'zero'}`}>{num}</span>
        <span className="unit">{unit}</span>
      </div>
      {position === 'bottom' && <div className="statistical-number-title">{title}</div>}
    </StatisticalNumberWapper>
  );
};

export default StatisticalNumber;
