import PopupUser from '@/component/popup-user';
import { <PERSON><PERSON>, Button, Form, Input, Popup, Space } from 'antd-mobile';
import { FilterOutline } from 'antd-mobile-icons';
import { useState, useEffect } from 'react';
import { styled } from 'umi';
import { FilterDatePicker } from '@/component/filter-datepicker/filter-datepicker.component';
import dayjs from 'dayjs';

const PopupTitle = styled.div`
  box-sizing: border-box;
  font-family: PingFang SC;
  font-size: 4.4444vw;
  font-weight: 400;
  letter-spacing: 0vw;
  line-height: 6.4353vw;
  color: rgba(0, 0, 0, 1);
  text-align: center;
  vertical-align: top;
  margin-top: 2.9631vw;
`;

const FilterForm = styled(Form)`
  box-sizing: border-box;
  flex: 1;
  font-family: PingFang SC;
  .adm-list-body {
    border: none;
    .adm-form-item-label {
      font-size: 4.4444vw;
      font-weight: 500;
      letter-spacing: 0vw;
      line-height: 6.4814vw;
      color: rgba(51, 51, 51, 1);
      margin-bottom: 3.6111vw;
    }
    .adm-input {
      box-sizing: border-box;
      padding: 2.0369vw 4.0742vw;
      border-radius: 1.8519vw;
      background: rgba(245, 245, 245, 1);
    }
  }
`;

const PopupFooter = styled.div`
  box-sizing: border-box;
  overflow: hidden;
  border-radius: 3.3333vw;
  margin: 0 3.3333vw 2.2222vw;
  border: .3703vw solid rgba(17, 84, 237, 1);
  .btn {
    display: inline-block;
    width: 50%;
    text-align: center;
    height: 11.1111vw;
    line-height: 11.1111vw;
    font-size: 3.8881vw;
    font-weight: 400;
    letter-spacing: 0vw;
    color: #2b488c;
    &.btn-submit {
      color: #fff;
      background: rgba(17, 84, 237, 1);
    }
  }
`;

type FilterPopueComponentProps = {
  filterValue: any;
  onFilter?: (value: any) => void;
};

export const FilterPopueComponent = (props: FilterPopueComponentProps) => {
  const { filterValue, onFilter } = props;
  const [form] = Form.useForm();
  const [visible, setVisible] = useState<boolean>(false);

  const onFinish = (values) => {
    const data = {};
    if (values?.operatorName) {
      data['operatorName'] = values.operatorName;
    }
    if (values?.dateMode?.startDate && values?.dateMode?.endDate) {
      if (values?.dateMode?.mode === 'year') {
        data['timeUnit'] = 'year';
        if (values?.dateMode?.startDate) {
          data['startDate'] = dayjs(values?.dateMode?.startDate).format('YYYY');
        }
        if (values?.dateMode?.endDate) {
          data['endDate'] = dayjs(values.dateMode.endDate).format('YYYY');
        }
      } else if (values?.dateMode?.mode === 'month') {
        data['timeUnit'] = 'month';
        if (values?.dateMode?.startDate) {
          data['startDate'] = dayjs(values?.dateMode?.startDate).format('YYYY-MM');
        }
        if (values?.dateMode?.endDate) {
          data['endDate'] = dayjs(values.dateMode.endDate).format('YYYY-MM');
        }
      } else {
        data['timeUnit'] = 'day';
        if (values?.dateMode?.startDate) {
          data['startDate'] = dayjs(values?.dateMode?.startDate).format('YYYY-MM-DD');
        }
        if (values?.dateMode?.endDate) {
          data['endDate'] = dayjs(values.dateMode.endDate).format('YYYY-MM-DD');
        }
      }
    }
    setVisible(false);
    onFilter && onFilter(data);
  };

  useEffect(() => {
    if (visible) {
      form.setFieldsValue(filterValue);
    }
  }, [filterValue, visible]);

  return (
    <>
      <FilterOutline
        fontSize={24}
        color="#fff"
        onClick={() => {
          setVisible(true);
        }}
      />
      {/* 底部筛选弹出框 */}
      <Popup
        visible={visible}
        onMaskClick={() => {
          setVisible(false);
        }}
        showCloseButton
        destroyOnClose
        onClose={() => {
          setVisible(false);
        }}
        bodyStyle={{
          borderTopLeftRadius: '.7407vw',
          borderTopRightRadius: '.7407vw',
          minHeight: '90vh',
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        <PopupTitle>筛选</PopupTitle>
        <FilterForm form={form} onFinish={onFinish}>
          <Form.Item label="操作人" name="operatorName">
            <Input placeholder="请输入..." />
          </Form.Item>
          <Form.Item label="时间" name="dateMode">
            <FilterDatePicker
             
            />
          </Form.Item>
        </FilterForm>
        <PopupFooter>
          <span
            className="btn btn-reset"
            onClick={() => {
              // 重置所有数据
              form.resetFields();
              onFilter({});
            }}
          >
            重置
          </span>
          <span
            className="btn btn-submit"
            onClick={() => {
              // 提交数据
              form.submit();
            }}
          >
            确定
          </span>
        </PopupFooter>
      </Popup>
    </>
  );
};
