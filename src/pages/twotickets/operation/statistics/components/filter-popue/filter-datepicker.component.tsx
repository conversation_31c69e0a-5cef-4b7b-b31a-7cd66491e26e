import { useState, useEffect, useMemo, useCallback } from 'react';
import { PickerView } from 'antd-mobile';
import { styled } from 'umi';
import dayjs from 'dayjs';
import { CustomDatePicker } from './custom-date-picker.component';
import _, { set } from 'lodash';
import { useDeepCompareEffect } from 'ahooks';

const FilterDatePickerWrapper = styled.div`
  .header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin: 0 auto 8.2408vw;
    .types {
      .type-item {
        display: inline-block;
        width: 12.5925vw;
        height: 6.6667vw;
        opacity: 1;
        /** 文本1 */
        font-size: 3.3333vw;
        font-weight: 400;
        letter-spacing: 0vw;
        line-height: 6.6667vw;
        color: rgba(102, 102, 102, 1);
        text-align: center;
        &.active {
          color: rgba(17, 84, 237, 1);
          border-radius: 3.3333vw;
          border: .3703vw solid rgba(17, 84, 237, 1);
        }
      }
    }
    .reset {
      font-size: 3.8889vw;
      font-weight: 400;
      letter-spacing: 0vw;
      line-height: 5.3703vw;
      color: rgba(228, 20, 18, 1);
    }
  }
  .body {
    .date-picker {
      display: flex;
      flex-direction: row;
      justify-content: space-around;
      align-items: flex-start;
      font-size: 3.8889vw;
      font-weight: 400;
      letter-spacing: -0.1631vw;
      line-height: 5.3703vw;
      color: rgba(153, 153, 153, 1);
      .date-picker-item {
        display: inline-block;
        width: 33.3333vw;
        padding-bottom: 3.8889vw;
        font-size: 3.8889vw;
        font-weight: 400;
        letter-spacing: -0.1481vw;
        line-height: 4.9075vw;
        color: rgba(153, 153, 153, 1);
        border-bottom: .3703vw solid rgba(229, 229, 229, 1);
        text-align: center;
        &.has {
          color: rgba(51, 51, 51, 1);
          border-color: rgba(51, 51, 51, 1);
        }
        &.hover {
          color: rgba(17, 84, 237, 1);
          border-color: rgba(17, 84, 237, 1);
        }
      }
    }
  }
`;

type FilterDatePickerProps = {
  value?: {
    startDate: number | null;
    endDate: number | null;
    mode: 'year' | 'month' | 'day';
  };
  onChange?: (value: { startDate: number | null; endDate: number | null; mode: 'year' | 'month' | 'day' }) => void;
};

export const FilterDatePicker = (props: FilterDatePickerProps) => {
  const { value, onChange } = props;
  const [mode, setMode] = useState<'year' | 'month' | 'day'>(value?.mode || 'year');
  const [startDate, setStartDate] = useState<number | null>(value?.startDate || null);
  const [endDate, setEndDate] = useState<number | null>(value?.endDate || null);

  useDeepCompareEffect(() => {
    setStartDate(value?.startDate || null);
    setEndDate(value?.endDate || null);
    setMode(value?.mode || 'year');
  }, [value]);
  const [currentDateType, setCurrentDateType] = useState<'start' | 'end'>('start');
  const DATE_TYPE = [
    {
      label: '年',
      value: 'year',
    },
    {
      label: '月',
      value: 'month',
    },
    {
      label: '日',
      value: 'day',
    },
  ];

  const formatShowDate = (date: number) => {
    if (mode === 'year') {
      return dayjs(date).format('YYYY');
    } else if (mode === 'month') {
      return dayjs(date).format('YYYY-MM');
    } else {
      return dayjs(date).format('YYYY-MM-DD');
    }
  };

  const onDateChange = useCallback(
    (data: { startDate?: number | null; endDate?: number | null; mode?: 'year' | 'month' | 'day' }) => {
      onChange(
        _.merge(
          {
            startDate,
            endDate,
            mode: mode,
          },
          data as any,
        ),
      );
    },
    [startDate, endDate, mode],
  );

  return (
    <FilterDatePickerWrapper>
      {/* 显示选中时间区间类型和重置按钮 */}
      <div className="header">
        <div className="types">
          {DATE_TYPE.map((item: any) => (
            <span
              key={item.value}
              className={`type-item ${item.value === mode && 'active'}`}
              onClick={() => {
                setCurrentDateType('start');
                onDateChange?.({
                  startDate: null,
                  endDate: null,
                  mode: item.value as any,
                });
                // setMode(item.value as any);
              }}
            >
              {item.label}
            </span>
          ))}
        </div>
        <div
          className="reset"
          onClick={() => {
            setCurrentDateType('start');
            onDateChange?.({
              startDate: null,
              endDate: null,
              mode: 'year',
            });
          }}
        >
          重置
        </div>
      </div>
      <div className="body">
        <div className="date-picker">
          <span
            className={`date-picker-item ${startDate && 'has'} ${currentDateType === 'start' && 'hover'}`}
            onClick={() => {
              setCurrentDateType('start');
            }}
          >
            {startDate ? formatShowDate(startDate) : '开始日期'}
          </span>
          <span className="date-picker-split">到</span>
          <span
            className={`date-picker-item ${endDate && 'has'}  ${currentDateType === 'end' && 'hover'}`}
            onClick={() => {
              setCurrentDateType('end');
            }}
          >
            {endDate ? formatShowDate(endDate) : '结束日期'}
          </span>
        </div>
        <CustomDatePicker
          mode={mode}
          value={currentDateType === 'start' ? startDate : endDate}
          onChange={(value) => {
            if (currentDateType === 'start') {
              if (endDate && value > endDate) {
                onDateChange?.({
                  startDate: endDate,
                  endDate: value,
                });
                return;
              }
              onDateChange?.({
                startDate: value,
              });
            } else {
              if (startDate && value < startDate) {
                onDateChange?.({
                  startDate: value,
                  endDate: startDate,
                });
                return;
              }
              onDateChange?.({
                endDate: value,
              });
            }
          }}
        />
      </div>
    </FilterDatePickerWrapper>
  );
};
