import { PickerView } from 'antd-mobile';
import dayjs from 'dayjs';
import { useEffect, useMemo, useState } from 'react';

// 生成数据的辅助函数（未变，依然可以使用原有实现）
const generateYears = (start = 2000, end = 2030) => {
  const years = [];
  for (let i = start; i <= end; i++) {
    years.push({ label: i, value: i });
  }
  return years;
};

const generateMonths = () => {
  const months = [];
  for (let i = 1; i <= 12; i++) {
    const label = (i < 10 ? '0' + i : i) + '月';
    months.push({ label, value: i });
  }
  return months;
};

const getDays = (year, month) => {
  // 构造指定年月的第一天，dayjs 会自动处理各个月天数
  const daysInMonth = dayjs(`${year}-${month}-01`).daysInMonth();
  const days = [];
  for (let i = 1; i <= daysInMonth; i++) {
    // 自动补零，比如 '01日'
    const label = (i < 10 ? '0' + i : i) + '日';
    days.push({ label, value: i });
  }
  return days;
};

type CustomDatePickerProps = {
  mode: 'year' | 'month' | 'day';
  value: number | null;
  onChange: (value: number) => void;
};

/**
 * 自定义日期选择器组件
 *
 * @param props 组件属性
 * @param mode 选择器模式，可以是 'year'、'month' 或 'day'
 * @param value 选择的日期值
 * @param onChange 日期值变化时的回调函数
 * @returns 返回日期选择器组件
 */
export const CustomDatePicker = (props: CustomDatePickerProps) => {
  const { mode, value, onChange } = props;

  // 初始化选定的日期，默认为当前日期
  const [selected, setSelected] = useState<any>([dayjs().year(), dayjs().month() + 1, dayjs().date()]);

  // 根据选定的年份和月份动态计算天数
  const [days, setDays] = useState(mode === 'day' ? getDays(selected[0], selected[1]) : []);

  // 固定数据列
  const years = generateYears();
  const months = generateMonths();

  // 当年份或月份变化时，使用 dayjs 重新计算“日”列
  useEffect(() => {
    if (mode === 'day') {
      const year = dayjs(value).year();
      const month = dayjs(value).month() + 1;
      const day = dayjs(value).day();
      const newDays = getDays(year, month);
      setDays(newDays);
      if (day > newDays.length) {
        setSelected([year, month, newDays.length]);
      }
    }
  }, [value, mode]);

  // 根据选择器的值和模式设置选定的日期
  useEffect(() => {
    if (value) {
      if (mode === 'day') {
        setSelected([dayjs(value).year(), dayjs(value).month() + 1, dayjs(value).date()]);
      } else if (mode === 'month') {
        setSelected([dayjs(value).year(), dayjs(value).month() + 1, 1]);
      } else {
        setSelected([dayjs(value).year(), 1, 1]);
      }
    } else {
      setSelected([dayjs().year(), dayjs().month() + 1, dayjs().date()]);
    }
  }, [value]);

  // 根据模式动态生成列
  const columns: any = useMemo(() => {
    if (mode === 'year') {
      return [years];
    } else if (mode === 'month') {
      return [years, months];
    } else {
      return [years, months, days];
    }
  }, [mode]);

  // 返回日期选择器组件
  return (
    <>
      <PickerView
        columns={columns}
        value={selected}
        onChange={(val: number[]) => {
          // setSelected(val)
          onChange(dayjs(`${val[0]}-${val[1]}-${val[2]}`).valueOf());
        }}
      />
    </>
  );
};
