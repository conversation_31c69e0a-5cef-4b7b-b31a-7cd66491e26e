
import { CapsuleTabs } from 'antd-mobile';
import { DownOutline, UpOutline } from 'antd-mobile-icons';
import { useEffect, useMemo, useState } from 'react';
import { styled } from 'umi';

import { F2ColumnsComponent } from '@/component/f2Plot/columns-comonpent/columns-component';
import GridList, { ColumnsProps } from '@/component/grid-list/grid-list.component';

import {
  getOperationItemStatistics,
  getOperationTicketStatistics,
  getPersonnelCompletionRank,
} from '@/services/twotickets/operation/statistics';
import { useDeepCompareEffect } from 'ahooks';
import { Empty } from 'antd';

const ImplementationStatusWrapper = styled.div`
  width: 100%;
  text-align: left;
  border-top: .0925vw solid rgba(229, 229, 229, 1);
  .item {
    position: relative;
    .title {
      padding: 2.4075vw 0;
      font-size: 3.7036vw;
      font-weight: 500;
      letter-spacing: 0vw;
      line-height: 6.6667vw;
      color: rgba(56, 56, 56, 1);
    }
    .type {
      position: absolute;
      right: 0;
      top: 2.4075vw;
      .adm-capsule-tabs-header {
        padding: 0;
        border: none;
        .adm-capsule-tabs-tab {
          display: inline-block;
          width: 16.6667vw;
          height: 6.6667vw;
          opacity: 1;
          border-radius: 3.3333vw;
          background: rgba(255, 255, 255, 1);
          font-size: 3.3333vw;
          font-weight: 400;
          letter-spacing: 0vw;
          line-height: 6.6667vw;
          padding: 0;
          color: rgba(102, 102, 102, 1);
          &.adm-capsule-tabs-tab-active {
            color: rgba(17, 84, 237, 1);
            border: .3703vw solid rgba(17, 84, 237, 1);
          }
        }
      }
    }
    .plot {
      height: 46.2964vw;
      .g2-slider-handle {
        display: none !important;
      }
    }
  }
`;

const ImplementationListShowAllDiv = styled.div`
  font-size: 3.8889vw;
  font-weight: 400;
  letter-spacing: 0vw;
  line-height: 5.6314vw;
  margin-top: 3.3333vw;
  color: rgba(17, 84, 237, 1);
  text-align: center;
`;

type ImplementationStatusProps = {
  filterValue: any
};

const ImplementationStatus = (props: ImplementationStatusProps) => {
  const {filterValue} = props;
  const [implementationStatusType, setImplementationStatusType] = useState<'ticket' | 'item'>('ticket'); // 操作人员执行情况统计排行 查看类型
  const [implementationListShowAll, setImplementationListShowAll] = useState<boolean>(false); // 是否展示说有操作列表
  const [implementationList, setImplementationList] = useState<any[]>([]);
  const [columnsComponentData, setColumnsComponentData] = useState<
    {
      name: string;
      xData: any;
      yData: any;
    }[]
  >([]);

  const TABLE_COLUMNS: ColumnsProps[] = [
    {
      dataIndex: 'index',
      title: '排序',
      align: 'left',
      render: (dataIndex, item, index) => {
        return <div>{index + 1}</div>;
      },
    },
    {
      title: '姓名',
      dataIndex: 'name',
      align: 'center',
      span: 2,
    },
    {
      title: '已完成操作票',
      dataIndex: 'finishedNum',
      align: 'center',
      span: 2,
    },
    {
      title: '已执行操作项',
      dataIndex: 'executedNum',
      align: 'center',
      span: 2,
    },
  ];

  useDeepCompareEffect(() => {
    getColumnsComponentData(implementationStatusType, filterValue);
    getImplementationList(filterValue);
  }, [filterValue, implementationStatusType]);

  const { GridListData } = useMemo(() => {
    return {
      GridListData: implementationList?.length > 6 && !implementationListShowAll ? implementationList?.slice(0, 6) : implementationList,
    };
  }, [implementationList, implementationListShowAll]);
  return (
    <ImplementationStatusWrapper>
      <div className="item">
        <div className="title">操作人员执行情况统计排行</div>
        <CapsuleTabs
          className="type"
          activeKey={implementationStatusType}
          onChange={(key: 'ticket' | 'item') => setImplementationStatusType(key)}
        >
          <CapsuleTabs.Tab title="操作票" key="ticket" />
          <CapsuleTabs.Tab title="操作项" key="item" />
        </CapsuleTabs>
        {/* 操作票柱状图 */}
        <div className="plot">
          {columnsComponentData?.length ? <F2ColumnsComponent data={columnsComponentData} /> : <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description="暂无数据" />}
        </div>
      </div>
      <div className="item">
        <div className="title">操作人员完成任务列表排行</div>
        <GridList
          columns={TABLE_COLUMNS}
          dataSource={GridListData}
          rowKey={'index'}
          gridConfig={{
            columns: 7,
            gap: 0,
          }}
        />
        {implementationList?.length > 6 && (
          <ImplementationListShowAllDiv onClick={() => setImplementationListShowAll(!implementationListShowAll)}>
            {!implementationListShowAll ? (
              <>
                更多({implementationList?.length}) <DownOutline />
              </>
            ) : (
              <>
                隐藏 <UpOutline />
              </>
            )}
          </ImplementationListShowAllDiv>
        )}
      </div>
    </ImplementationStatusWrapper>
  );
  /**
   * 根据类型获取柱状图数据
   *
   * 此函数根据提供的类型参数，调用相应的API获取数据，并将数据格式化后传递给setColumnsComponentData函数
   * 以更新组件的状态数据
   *
   * @param type 数据类型，'ticket'代表操作票，'item'代表操作项
   */
  function getColumnsComponentData(type, params: any) {
    if (type === 'ticket') {
      // 操作票柱状图数据请求
      getOperationItemStatistics(params).then((res) => {
        if (res.status === 200) {
           setColumnsComponentData(
            Object.keys(res?.data || {}).map((item) => ({
              xData: item,
              yData: res?.data?.[item] ? Number(res?.data?.[item]) : 0,
              name: '操作票',
            })).sort((a, b) => b.yData - a.yData),
          );
        }
      });
    } else if (type === 'item') {
      // 操作项柱状图数据请求
      getOperationTicketStatistics(params).then((res) => {
        if (res.status === 200) {
          setColumnsComponentData(
            Object.keys(res?.data || {}).map((item) => ({
              xData: item,
              yData: res?.data?.[item] ? Number(res?.data?.[item]) : 0,
              name: '操作项',
            })).sort((a, b) => b.yData - a.yData),
          );
        }
      });
    }
  }

  function getImplementationList( params: any) {
    getPersonnelCompletionRank(params).then((res) => {
      if (res.status === 200) {
        setImplementationList(res?.data?.sort((a, b) => b.finishedNum - a.finishedNum) || []);
      }
    });
  }
};

export default ImplementationStatus;
