import { title } from 'process';
import TwoTicketsNavBar from '@/component/twotickets-navbar/twotickets-navbar.component';
import styles from './operation-statistics.page.less';
import { Card, Grid } from 'antd-mobile';
import StatisticalNumber from './components/statistical-number/statistical-number.component';
import { useEffect, useState } from 'react';
import QualifiedProgress from './components/statistical-number/qualified-progress.component';
import ImplementationStatus from './components/implementation-status/implementation-status.components';
import { getOperationStatistics } from '../../../../services/twotickets/operation/statistics';
import { FilterPopueComponent } from './components/filter-popue/filter-popue.component';
import { useDeepCompareEffect } from 'ahooks';

type operationStatisticsProps = {};

const STATISTICALNUMBERMAP = [
  {
    name: '操作票总数',
    value: 'total',
  },
  {
    name: '操作票已完成',
    value: 'completed',
  },
  {
    name: '监察审核合格',
    value: 'qualified',
  },
];

/**
 * 操作票统计分析
 * @param props
 * @returns
 */
const operationStatistics = (props: operationStatisticsProps) => {
  // 统计数字
  const [statisticalNumber, setStatisticalNumber] = useState<any>({
    // 总数
    total: 27,
    // 已完成
    completed: 19,
    // 审核合格
    qualified: 18,
    // 合格率
    qualifiedRate: 65,
  });

  // 筛选条件
  const [filterData, setFilterData] = useState<any>({});

  useDeepCompareEffect(() => {
    getStatisticalNumber(filterData || {});
  }, [filterData]);

  return (
    <div className={styles.operationStatistics} style={{ paddingTop: `${window.top?.localStorage?.topHeight ?? 0}px` }}>
      <TwoTicketsNavBar
        title="操作票管理-统计分析"
        right={
          <FilterPopueComponent
            filterValue={filterData}
            onFilter={(values) => {
              setFilterData(values);
            }}
          />
        }
      />
      <div className={styles.operationStatisticsBody}>
        <Card
          title="操作票统计"
          className={styles.operationStatisticsCard}
          style={{
            marginTop: '5.4631vw',
          }}
        >
          {/* 展示统计数字 */}
          <Grid columns={3} gap={8}>
            {STATISTICALNUMBERMAP.map((item, index) => (
              <Grid.Item key={'statistica_number' + index} style={{ textAlign: 'center' }}>
                <StatisticalNumber num={statisticalNumber[item.value]} title={item.name} />
              </Grid.Item>
            ))}
          </Grid>
          {/* 展示审核合格率进度条 */}
          <QualifiedProgress num={statisticalNumber['qualifiedRate']} />
        </Card>
        <Card title="操作票执行情况统计" className={styles.operationStatisticsCard}>
          {/* 操作人员执行情况统计排行 */}
          <ImplementationStatus filterValue={filterData} />
        </Card>
      </div>
    </div>
  );

  /**
   * 获取统计数字
   */
  function getStatisticalNumber(params: any) {
    getOperationStatistics(params).then((res) => {
      if (res.status === 200) {
        // setStatisticalNumber(res?.data);
        setStatisticalNumber({
          // 总数
          total: res?.data?.total || 0,
          // 已完成
          completed: res?.data?.finished || 0,
          // 审核合格
          qualified: res?.data?.pass || 0,
          // 合格率
          qualifiedRate: res?.data?.passRate || 0,
        });
      }
    });
  }
};

export default operationStatistics;
