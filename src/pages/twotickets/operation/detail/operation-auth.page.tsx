import { WORKFLOW_STATE_MAP_TEXT } from '@/constant';
import TwoTicketsNavBar from '@/component/twotickets-navbar/twotickets-navbar.component';
import styles from './operation-auth.page.less';
import { useRequest } from 'ahooks';
import { getOperationDetail } from '@/services/twotickets/operation/statistics';
import { Button, Form, Image, Radio, Space, TextArea } from 'antd-mobile';
import { useNavigate, useParams, useSearchParams } from 'umi';
import useLPAdmin from '@/hooks/useLPAdmin';
import { useEffect, useState } from 'react';
import useNextTask, { NextTaskProps } from '@/hooks/useNextTask';

import checkbox from '@/assets/twotickets/checkbox.png';
import checkboxed from '@/assets/twotickets/checkboxed.png';
import { useTwoTicketAuth } from '@/hooks/useTwoTicketAuth';

export const adopt = [
  { label: '通过', value: 1 },
  { label: '驳回', value: 0 },
];
const assigneeRegex = /\${(.*?)}/;
const defaultNextTask: NextTaskProps[] = [];
const OperationAuth = () => {
  // 获取url参数
  const params = useParams();
  // 使用useRequest获取数据
  const { data: info, loading } = useRequest(() => getOperationDetail({ id: params.id }).then((res) => res.data), {});

  const [form] = Form.useForm();

  // 流程按钮
  const { operateTypeOptions, methods } = useTwoTicketAuth(info);

  const onFinish = async (values: any) => {
    await methods.auth(values)
    history.go(-2)
  };
  return (
    <div className={styles.container} data-testid={`${WORKFLOW_STATE_MAP_TEXT[info?.state === undefined || info?.state === null ? 1 : info.state]}-form`} style={{ paddingTop: `${window.top?.localStorage?.topHeight ?? 0}px` }}
    >
      <TwoTicketsNavBar
        title={WORKFLOW_STATE_MAP_TEXT[info?.state === undefined || info?.state === null ? 1 : info.state]}
        subTitle="操作票"
        bgcolor={'#fff'}
        color={'#000'}
      />
      <div className={styles.content} data-testid={`state-${info?.state}-indicator`}>
        <Form
          layout="horizontal"
          style={{
            '--border-bottom': 'none',
            '--border-top': 'none',
            '--border-inner': 'solid .0926vw var(--adm-border-color)',
          }}
          form={form}
          initialValues={{
            operatorType: operateTypeOptions[0].value
          }}
          onFinish={onFinish}
        >
          <Form.Item label="审核结果" name="operatorType">
            <Radio.Group data-testid="approve-radio">
              <Space direction="horizontal" block align="end" justify="end">
                {operateTypeOptions.map(item => (<Radio
                  value={item.value}
                  key={item.value}
                  icon={(checked) =>
                    checked ? (
                      <Image src={checkboxed} width={'5vw'} height={'5vw'} />
                    ) : (
                      <Image src={checkbox} width={'5vw'} height={'5vw'} />
                    )
                  }
                >
                  <div data-testid={`approve-radio-${item.value}`}>{item.label}</div>
                </Radio>))}
              </Space>
            </Radio.Group>
          </Form.Item>
          <Form.Item label="审核意见" name="remark" layout="vertical">
            <TextArea rows={4} placeholder="请输入" data-testid="opinion-input" />
          </Form.Item>
        </Form>
      </div>
      <div className={styles.footer}>
        <Button color="primary" block shape="rounded" size="large" className={styles.btn} disabled={loading} onClick={() => {
          form.submit()
        }} data-testid="submit-btn">
          {WORKFLOW_STATE_MAP_TEXT[info?.state === undefined || info?.state === null ? 1 : info.state]}
        </Button>
      </div>
    </div>
  );
};

export default OperationAuth;
