import { Button, Checkbox, DatePicker, DatePickerRef, Divider, Form, Grid, Input, Skeleton, Space, TextArea, Toast } from 'antd-mobile';
import { CheckCircleOutline, FillinOutline, RightOutline } from 'antd-mobile-icons';
import styles from './operation-fill.page.less';
import dayjs from 'dayjs';
import { styled, useParams, history } from 'umi';
import { useRequest } from 'ahooks';
import { getOperationDetail, saveOperateInfo } from '@/services/twotickets/operation/statistics';
import TwoTicketsNavBar from '@/component/twotickets-navbar/twotickets-navbar.component';
import { WORKFLOW_STATE_MAP_TEXT } from '@/constant';
import { FilterPopup as EditPopup } from '@/component/filter-popup/filter-popup.component';
import { RefObject, useEffect } from 'react';
import useMessageHandler from '@/hooks/useMessageHandler';
import { clone, cloneDeep, curryRight } from 'lodash';
import useUserInfo, { UserInfo } from '@/hooks/useUserInfo';
import useNextTask from '@/hooks/useNextTask';
import { useTwoTicketAuth } from '@/hooks/useTwoTicketAuth';
const assigneeRegex = /\${(.*?)}/;
const OperateItem = (props: any) => {
  const { index, onChange, ...rest } = props;
  const OperateItemWrapper = styled.div`
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    justify-content: flex-start;
    width: 100%;
    overflow: hidden;
    .index {
      font-size: 3.8889vw;
      font-weight: 400;
      letter-spacing: 0vw;
      line-height: 5.6314vw;
      color: rgba(51, 51, 51, 1);
      margin-right: 2.4075vw;
    }
    .content {
      flex: 1;
      .operationStep {
        display: flex;
        align-items: flex-start;
        font-size: 3.8889vw;
        font-weight: 400;
        letter-spacing: 0vw;
        line-height: 5.6314vw;
        color: rgba(51, 51, 51, 1);
        .name {
          flex: 1;
        }
        .edit {
          margin-top: 0.8333vw;
        }
      }
      .body {
        padding: 2.3147vw 2.9631vw;
        border-radius: 1.6667vw;
        background: rgba(245, 245, 245, 1);
        font-size: 3.3333vw;
        font-weight: 400;
        letter-spacing: 0vw;
        line-height: 4.8269vw;
        color: rgba(51, 51, 51, 1);
      }
    }
  `;
  const handleSubmit = ({ operationTime, remark }: { operationTime?: any; remark?: string }) => {
    onChange(
      {
        ...rest,
        operationTime: operationTime ? dayjs(operationTime).format('YYYY-MM-DD HH:mm:ss') : null,
        remark: remark,
      },
      index,
    );
  };
  return (
    <OperateItemWrapper>
      <div className="index">{index + 1}</div>
      <div className="content">
        <div className="operationStep">
          <div className="name">{rest?.operationStep}</div>
          <EditPopup
            title="编辑"
            popupProps={{
              className: styles.operatorItemEditPopup,
            }}
            iconRender={<FillinOutline className="edit" />}
            footer={(submit) => {
              return (
                <div
                  className="footer"
                  style={{
                    padding: '2.3147vw 3.2408vw',
                  }}
                >
                  <Button block color="primary" shape="rounded" size="middle" className="btn" onClick={submit}>
                    提交
                  </Button>
                </div>
              );
            }}
            onSubmit={handleSubmit}
          >
            <Form.Item
              name="operationTime"
              layout="vertical"
              label="操作时间"
              trigger="onConfirm"
              initialValue={rest?.operationTime ? dayjs(rest?.operationTime, 'YYYY-MM-DD HH:mm:ss').toDate() : null}
              arrow={false}
              onClick={(e, datePickerRef: RefObject<DatePickerRef>) => {
                datePickerRef.current?.open();
              }}
            >
              <DatePicker precision="second">
                {(value) => (
                  <div
                    className="adm-text-area"
                    style={{
                      textAlign: 'left',
                    }}
                  >
                    {value ? dayjs(value).format('YYYY/MM/DD HH:mm:ss') : '请选择'}
                    <RightOutline
                      style={{
                        position: 'absolute',
                        right: '.9258vw',
                        top: '50%',
                        transform: 'translateY(-50%)',
                      }}
                    />
                  </div>
                )}
              </DatePicker>
            </Form.Item>
            <Form.Item label="备注" name="remark" layout="vertical" initialValue={rest?.remark}>
              <TextArea placeholder="请输入备注" />
            </Form.Item>
          </EditPopup>
        </div>
        {(rest?.operationTime || rest?.remark) && (
          <div className="body">
            {rest?.operationTime && <div className="operationTime">操作时间：{rest?.operationTime}</div>}
            {rest?.remark && <div className="remark">备注：{rest?.remark}</div>}
          </div>
        )}
      </div>
    </OperateItemWrapper>
  );
};

const defaultNextTask: NextTaskProps[] = [];

export const qualified = [
  { label: '合格', value: 1 },
  { label: '不合格', value: 0 },
];
export const adopt = [
  { label: '通过', value: 1 },
  { label: '驳回', value: 0 },
];

const stateUserMapping: Record<number, string> = {
  1: 'operator',
  2: 'guardian',
  3: 'watch',
  4: 'firstShiftSupervisor',
  5: 'orderShiftSupervisor',
  6: 'guardian',
  7: 'secondShiftSupervisor',
  8: 'guardian',
  10: 'secondShiftSupervisor',
};

const CheckBoxLabel = styled.span`
  font-size: 3.8889vw;
  font-weight: 400;
  letter-spacing: 0vw;
  line-height: 5.6314vw;
  color: rgba(51, 51, 51, 1);
`;

export default function OperationFill() {
  // 获取url参数
  const { id, type } = useParams();
  // 使用useRequest获取数据
  const { data, loading, mutate } = useRequest(() => getOperationDetail({ id }).then((res) => res.data), {});
  const [form] = Form.useForm();
  const operatorStepList = Form.useWatch('operatorStepList', form);
  const { methods, loading: authLoading } = useTwoTicketAuth(data);
  return (
    <div
      className={styles.container}
      data-testid={`${WORKFLOW_STATE_MAP_TEXT[data?.state === undefined || data?.state === null ? 1 : data.state]}-form`}
    >
      <TwoTicketsNavBar
        title={WORKFLOW_STATE_MAP_TEXT[data?.state === undefined || data?.state === null ? 1 : data.state]}
        bgcolor={'#fff'}
        color={'#000'}
      />
      <div className={styles.content}>
        {!loading ? (
          <Form
            layout="horizontal"
            style={{
              '--border-bottom': 'none',
              '--border-top': 'none',
              backgroundColor: 'rgba(245, 245, 245, 1)',
            }}
            form={form}
            initialValues={{
              operateBeginTime: data?.operateBeginTime ? dayjs(data?.operateBeginTime, 'YYYY-MM-DD HH:mm:ss').toDate() : null,
              operateEndTime: data?.operateEndTime ? dayjs(data?.operateEndTime, 'YYYY-MM-DD HH:mm:ss').toDate() : null,
              operatorStepList: data?.operatorStepList?.filter((item) => item.isMainOperation)?.map((item, index) => index),
              remark: data?.remark,
            }}
            onValuesChange={() => {
              const payload = clone(data);
              const values = form.getFieldsValue();
              mutate({
                ...payload,
                operateBeginTime: values?.operateBeginTime ? dayjs(values?.operateBeginTime).format('YYYY-MM-DD HH:mm:ss') : null,
                operateEndTime: values?.operateEndTime ? dayjs(values?.operateEndTime).format('YYYY-MM-DD HH:mm:ss') : null,
                operatorStepList: data?.operatorStepList?.map((item, index) => ({
                  ...item,
                  isMainOperation: values?.operatorStepList?.includes(index) ? 1 : '',
                })),
                remark: data?.remark,
              });
            }}
            onFinish={async (values) => {
              const { active, ...rest } = values;
              if (values.active) {
                try {
                  const result = await methods?.supervisionOrReport({
                    opinions: rest.remark,
                  });
                  if (result) {
                    history.go(-2);
                  }
                } catch (error) {}
              } else {
                await methods?.adminSave(data.state);
                history.go(-2);
              }
            }}
          >
            {/* 定义一个提交类型 */}
            <Form.Item name="active" hidden />
            <Form.Item
              label="操作开始时间"
              name="operateBeginTime"
              required
              trigger="onConfirm"
              data-testid="operate-begin-time-picker"
              onClick={(e, datePickerRef: RefObject<DatePickerRef>) => {
                datePickerRef.current?.open();
              }}
            >
              <DatePicker precision="second" data-testid="operate-begin-time-picker-date-picker">
                {(value) =>
                  value ? (
                    <div
                      style={{
                        textAlign: 'right',
                      }}
                      data-testid={`operate-begin-time-picker-date-picker-${dayjs(value).format('YYYY-MM-DD_HH:mm:ss')}`}
                    >
                      {dayjs(value).format('YYYY/MM/DD HH:mm:ss')}
                    </div>
                  ) : (
                    <div
                      style={{
                        textAlign: 'right',
                      }}
                    >
                      请选择
                    </div>
                  )
                }
              </DatePicker>
            </Form.Item>
            <Form.Item
              label="操作结束时间"
              name="operateEndTime"
              required
              trigger="onConfirm"
              data-testid="operate-end-time-picker"
              onClick={(e, datePickerRef: RefObject<DatePickerRef>) => {
                datePickerRef.current?.open();
              }}
            >
              <DatePicker
                precision="second"
                style={{
                  textAlign: 'right',
                }}
                data-testid="operate-end-time-picker-date-picker"
              >
                {(value) =>
                  value ? (
                    <div
                      style={{
                        textAlign: 'right',
                      }}
                      data-testid={`operate-end-time-picker-date-picker-${dayjs(value).format('YYYY-MM-DD_HH:mm:ss')}`}
                    >
                      {dayjs(value).format('YYYY/MM/DD HH:mm:ss')}
                    </div>
                  ) : (
                    <div
                      style={{
                        textAlign: 'right',
                      }}
                    >
                      请选择
                    </div>
                  )
                }
              </DatePicker>
            </Form.Item>
            <Divider
              style={{
                borderColor: 'rgba(245, 245, 245, 1)',
                borderWidth: '2.2222vw',
              }}
            />
            <Form.Item
              name="operatorStepList"
              label={
                <>
                  <Space block justify="between" className={styles.operatorTitle}>
                    <span>
                      操作项{' '}
                      <span className={styles.num}>
                        （<span className={styles.checked}>{operatorStepList?.length || 0}</span>/{data?.operatorStepList?.length}）
                      </span>
                    </span>
                    <Checkbox
                      // indeterminate={
                      //   operatorStepList?.length > 0 && operatorStepList?.length < data?.operatorStepList?.length
                      // }
                      checked={operatorStepList?.length === data?.operatorStepList?.length}
                      onChange={(checked) => {
                        if (checked) {
                          form.setFieldValue(
                            'operatorStepList',
                            data?.operatorStepList?.map((item: any, index: any) => index),
                          );
                          mutate({
                            ...data,
                            operatorStepList: data?.operatorStepList?.map((item, index) => ({
                              ...item,
                              isMainOperation: 1,
                            })),
                          });
                        } else {
                          form.setFieldValue('operatorStepList', []);

                          mutate({
                            ...data,
                            operatorStepList: data?.operatorStepList?.map((item, index) => ({
                              ...item,
                              isMainOperation: '',
                            })),
                          });
                        }
                      }}
                      style={{
                        '--icon-size': '4.4444vw',
                      }}
                    >
                      <CheckBoxLabel>全部已完成</CheckBoxLabel>
                    </Checkbox>
                  </Space>

                  {/* <div>{operatorStepList?.length === data?.operatorStepList?.length ? (
                      <CheckCircleOutline
                        color="rgba(17, 84, 237, 1)"
                        onClick={() => {
                          form.setFieldValue('operatorStepList', []);
                        }}
                      />
                    ) : (
                      <CircleOutline
                        color="--adm-color-light"
                        onClick={() => {
                          form.setFieldValue(
                            'operatorStepList',
                            data?.operatorStepList?.map((item, index) => index),
                          );
                        }}
                      />
                    )} 全部已完成</div> */}
                  {/* <div className={styles.note}>注：默认倒数第二项操作项目为“全面检查”，倒数第一项为“汇报批准人”</div> */}
                </>
              }
              style={{
                '--border-inner': 'none',
              }}
              layout="vertical"
            >
              <Checkbox.Group
                style={{
                  '--border-inner': '.0925vw solid #E6E6E6',
                }}
              >
                <Space direction="vertical" className={styles.operators}>
                  {data?.operatorStepList?.map((item, index) => (
                    <Checkbox
                      key={index}
                      value={index}
                      block
                      data-testid={`operator-step-${index}`}
                      style={{
                        '--icon-size': '4.4444vw',
                      }}
                    >
                      <OperateItem
                        index={index}
                        {...item}
                        onChange={(value, i) => {
                          mutate({
                            ...data,
                            operatorStepList: data?.operatorStepList?.map((item, index) => {
                              if (index === i) {
                                return {
                                  ...value,
                                };
                              }
                              return item;
                            }),
                          });
                        }}
                      />
                    </Checkbox>
                  ))}
                </Space>
              </Checkbox.Group>
            </Form.Item>
            <Form.Item
              style={{
                '--border-bottom': 'none',
                '--border-top': 'none',
                '--border-inner': 'none',
              }}
              label="备注"
              name="remark"
              layout="vertical"
            >
              <TextArea
                style={{
                  '--font-size': '4.2592vw',
                }}
                autoSize={{ minRows: 3, maxRows: 8 }}
                placeholder="请输入备注"
                data-testid="remark-input"
              />
            </Form.Item>
          </Form>
        ) : (
          <Skeleton.Paragraph />
        )}
        <div className={styles.bottom}>已经到底了</div>
      </div>
      <Grid
        className={styles.footer}
        columns={2}
        gap={8}
        style={{
          width: '100%',
        }}
      >
        <Grid.Item>
          <Button
            block
            size="large"
            className={styles.btn}
            style={{
              borderRadius: '3.3333vw',
              color: 'rgba(17, 84, 237, 1)',
              '--border-color': 'rgba(17, 84, 237, 1)',
            }}
            disabled={loading || authLoading}
            data-testid="operate-save-btn"
            onClick={() => {
              form.setFieldValue('active', 0);
              form.submit();
            }}
          >
            保存数据
          </Button>
        </Grid.Item>
        <Grid.Item span={1}>
          <Button
            block
            color="primary"
            size="large"
            className={styles.btn}
            style={{
              borderRadius: '3.3333vw',
            }}
            data-testid="operate-auth-btn"
            disabled={loading || authLoading}
            onClick={() => {
              form.setFieldValue('active', 1);
              form.submit();
            }}
          >
            发起审批
          </Button>
        </Grid.Item>
      </Grid>
    </div>
  );
}
