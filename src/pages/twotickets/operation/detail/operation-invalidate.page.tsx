import TwoTicketsNavBar from '@/component/twotickets-navbar/twotickets-navbar.component';
import styles from './operation-auth.page.less';
import { useRequest } from 'ahooks';
import { getOperationDetail } from '@/services/twotickets/operation/statistics';
import { Button, Form, TextArea } from 'antd-mobile';
import { useParams } from 'umi';
import { useTwoTicketAuth } from '@/hooks/useTwoTicketAuth';

const OperationAuth = () => {
  // 获取url参数
  const params = useParams();
  const [form] = Form.useForm();
  // 使用useRequest获取数据
  const { data, loading } = useRequest(() => getOperationDetail({ id: params.id }).then((res) => res.data), {});

  const { nextTask, methods } = useTwoTicketAuth(data);

  return (
    <div className={styles.container}>
      <TwoTicketsNavBar title={'操作票作废'} bgcolor={'#fff'} color={'#000'} />
      <div className={styles.content}>
        <Form
          layout="horizontal"
          form={form}
          style={{
            '--border-bottom': 'none',
            '--border-top': 'none',
            '--border-inner': 'solid 1px var(--adm-border-color)',
          }}
          onFinish={async (values)=>{
            const activeConditionParams = nextTask?.find?.((item: any)=>item?.conditionParams?.variables?.sub === 'cancel' && item?.conditionParams?.variables?.pass === 'false')
            await methods.voidOrder(values, activeConditionParams);
            history.go(-1)
          }}
        >
          <Form.Item label="作废原因" name="remark" layout="vertical">
            <TextArea rows={4} placeholder="请输入" />
          </Form.Item>
        </Form>
      </div>
      <div className={styles.footer}>
        <Button
          color="primary"
          disabled={loading}
          block
          shape="rounded"
          size="large"
          className={styles.btn}
          onClick={async () => {
            form.submit();
          }}
        >
          提交
        </Button>
      </div>
    </div>
  );
};

export default OperationAuth;
