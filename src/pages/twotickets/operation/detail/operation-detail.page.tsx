import { Button, Image, Loading, Popover, Skeleton, Space, Tabs } from 'antd-mobile';
import { Action } from 'antd-mobile/es/components/popover';

import TwoTicketsNavBar from '@/component/twotickets-navbar/twotickets-navbar.component';
import styles from './operation-detail.page.less';
import print from '@/assets/twotickets/printer.png';
import invalidate from '@/assets/twotickets/invalidate.png';
import more from '@/assets/twotickets/more.png';
import { useRequest, useThrottleFn } from 'ahooks';
import { useLocation, useParams, history } from 'umi';
import { getOperationDetail } from '@/services/twotickets/operation/statistics';
import { WORKFLOW_STATE_MAP_TEXT } from '@/constant';
import { ProcessCard } from '@/component/process-card/process-card.component';
import React, { useCallback, useEffect, useState } from 'react';
import { BasicInfoComponent } from './components/basic_info/basic_info.component';
import { OperationContentComponent } from './components/operation-content/operation-content.component';
import { SafeToolComponent } from './components/safe-tool/safe-tool.component';
import { OperationBtnGroupComponent } from './components/operation-btn/operation-btn.component';
import { OperationHeaderBtn } from './components/operation-btn/operation-header-btn.component';
import { hasReview } from '../list/operation-list.page';

const TAB_ITEMS = [
  {
    key: '1',
    title: '基本信息',
    content: (data: any) => <BasicInfoComponent data={data} />,
  },
  {
    key: '2',
    title: '操作内容',
    content: (data: any) => <OperationContentComponent data={data} />,
  },
  {
    key: '3',
    title: '安全工器具',
    content: (data: any) => <SafeToolComponent data={data} />,
  },
];

const TAB_HEIGHT = 42;

const NavButton = () => {
  // 根据状态展示不同的按钮
  // 1. 展示打印图标
  // 2. 展示打印和作废按钮
  // 3. 展示更多按钮，更多点击后展示生成标准票，打印和下载
  const actions: Action[] = [
    { key: 'generate', text: '生成标准票' },
    { key: 'print', text: '打印' },
    { key: 'download', text: '下载' },
  ];
  return (
    <Space>
      {/* 打印按钮 */}
      <Image src={print} alt="打印" width={'5vw'} height={'5vw'} />
      {/* 作废按钮 */}
      <Image src={invalidate} alt="作废" width={'5vw'} height={'5vw'} />
      {/* 更多按钮 */}
      <Popover.Menu
        actions={actions}
        placement="bottom-start"
        onAction={(node) => {
          console.log(node);
        }}
        trigger="click"
      >
        <Image src={more} alt="更多" width={'5vw'} height={'5vw'} />
      </Popover.Menu>
    </Space>
  );
};

const OperationDetail = () => {
  // 获取url参数
  const params = useParams();
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const pageType = searchParams.get('type') || 'view';
  // 使用useRequest获取数据
  const { data, loading } = useRequest(() => getOperationDetail({ id: params.id }).then((res) => res.data), {});

  const containerRef = React.useRef<HTMLDivElement>(null);
  const [activeKey, setActiveKey] = useState('1');

  const { run: handleScroll } = useThrottleFn(
    () => {
      let currentKey = TAB_ITEMS[0].key;
      for (const item of TAB_ITEMS) {
        const element = document.getElementById(`anchor-${item.key}`);
        if (!element) continue;
        const rect = element.getBoundingClientRect();
        if (rect.top <= TAB_HEIGHT) {
          currentKey = item.key;
        } else {
          break;
        }
      }
      setActiveKey(currentKey);
    },
    {
      leading: true,
      trailing: true,
      wait: 100,
    },
  );
  // 通过 callbackRef，在组件挂载后，操作该DOM
  // 注意：还要用useCallback来包装函数，不是为了性能优化，
  // 而是为了不生成新的回调函数让diff对比时发现差异，再次执行回调
  const callbackRef = useCallback((ref) => {
    // 并且卸载组件时，会再次传入null调用该函数，会引发报错
    // 所以：遇到null阻止运行
    if (!ref) return;
    // 给dom绑定事件
    ref.addEventListener('scroll', handleScroll);
    // 保留ref，便于组件卸载时清除副作用
    containerRef.current = ref;
  }, []);

  useEffect(() => {
    return () => {
      containerRef.current?.removeEventListener('scroll', handleScroll);
    };
  }, []);
  if (loading) return <Skeleton.Paragraph />;
  return (
    <div className={styles.container} data-testid="operation-detail">
      <TwoTicketsNavBar
        title={data?.taskName || '操作票详情'}
        subTitle="操作票"
        bgcolor={'#fff'}
        color={'#000'}
        right={<OperationHeaderBtn data={data} />}
      />
      {!loading ? (
        <div className={styles.body} ref={callbackRef} data-testid="operation-detail-body">
          <div className={styles.content}>
            <ProcessCard
              id={data?.id}
              records={(data?.workflowApprovalRecordList || []).reverse()}
              onProcess={function (id: number): void {
                history.push(`/twotickets/common/check-list/process-viewer/${data.id}`);
              }}
              taskUserName={data?.taskUserName}
            />
          </div>

          <div className={styles.tabsContainer}>
            <Tabs
              activeKey={activeKey}
              onChange={(key) => {
                document.getElementById(`anchor-${key}`)?.scrollIntoView();
                window.scrollTo({
                  top: window.scrollY - TAB_HEIGHT,
                });
                setTimeout(() => {
                  setActiveKey(key);
                }, 500);
              }}
            >
              {TAB_ITEMS.map((item) => (
                <Tabs.Tab title={item.title} key={item.key} />
              ))}
            </Tabs>
          </div>

          <div className={styles.tabsContent}>
            {TAB_ITEMS.map((item: any) => (
              <div key={item.key} id={`anchor-${item.key}`}>
                {item.content(data)}
              </div>
            ))}
          </div>
          <div className={styles.bottom}>已经到底了</div>
        </div>
      ) : (
        <Skeleton.Paragraph />
      )}
      {!loading && pageType === 'edit' && hasReview(data) && (
        <div className={styles.operation}>
          <OperationBtnGroupComponent info={data} />
        </div>
      )}
    </div>
  );
};

export default OperationDetail;
