import { history, styled } from 'umi';
import React, { useMemo, useState } from 'react';
import { ActionSheet, Button, DatePicker, Dialog, Form, Grid, TextArea } from 'antd-mobile';
import { FilterPopup } from '@/component/filter-popup/filter-popup.component';
import dayjs from 'dayjs';

interface CommonProps {
  loading: boolean;
  info?: {
    id?: string;
    state?: number;
  };
  methods: {
    supervisionOrReport: (params: Record<string, any>) => Promise<void>;
  };
}

const TerminationGuardianshipActionSheet = styled(ActionSheet)`
  .adm-action-sheet-button-list,
  .adm-action-sheet-cancel {
    margin: 2.2222vw 3.3333vw;
    border-radius: 3.3333vw;
    overflow: hidden;
    .adm-action-sheet-button-item > .adm-action-sheet-button-item-name {
      font-size: 4.4444vw;
      font-weight: 400;
      letter-spacing: 0vw;
      /* line-height: 6.4353vw; */
      color: rgba(17, 84, 237, 1);
    }
    &.adm-action-sheet-cancel {
      padding-top: 0;
      .adm-action-sheet-button-item > .adm-action-sheet-button-item-name {
        color: rgba(102, 102, 102, 1);
      }
    }
    .adm-action-sheet-button-item {
      padding: 0;
      height: 15.5556vw;
      line-height: 15.5556vw;
    }
  }
`;
const State5Popup = ({ wrapped, ...props }: CommonProps & { wrapped: React.ReactElement }) => {
  return (
    <FilterPopup
      iconRender={wrapped}
      {...props}
      title="选择发令时间"
      popupProps={{
        bodyStyle: {
          borderTopLeftRadius: '.7407vw',
          borderTopRightRadius: '.7407vw',
          minHeight: '25vh',
          display: 'flex',
          flexDirection: 'column',
        },
        className: 'command-popup',
      }}
      footer={(submit) => {
        return (
          <div
            style={{
              boxSizing: 'border-box',
              padding: '1.8519vw 3.3333vw',
            }}
            data-testid="order-time-btn"
          >
            <Button
              block
              color="primary"
              shape="rounded"
              size="middle"
              className="btn"
              disabled={props.loading}
              onClick={() => {
                submit();
              }}
            >
              确定
            </Button>
          </div>
        );
      }}
      onSubmit={async (values: any) => {
        await props?.methods?.supervisionOrReport({
          orderTime: values?.orderTime ? dayjs(values?.orderTime).format('YYYY-MM-DD HH:mm:ss') : '',
        });
        history.go(-1);
      }}
    >
      <Form.Item
        name="orderTime"
        label="发令时间"
        layout="horizontal"
        trigger="onConfirm"
        data-testid="order-time-picker"
        onClick={(e, datePickerRef: RefObject<DatePickerRef>) => {
          datePickerRef.current?.open();
        }}
      >
        <DatePicker
          precision="second"
          style={{
            textAlign: 'right',
          }}
          data-testid="order-time-picker-date-picker"
        >
          {(value) => (value ? <div data-testid={`order-time-picker-date-picker-day-${dayjs(value).format('YYYY-MM-DD_HH:mm:ss')}`}>{dayjs(value).format('YYYY/MM/DD HH:mm:ss')}</div>  : '请选择发令时间')}
        </DatePicker>
      </Form.Item>
    </FilterPopup>
  );
};

const State6Grid = ({ wrapped, ...props }: CommonProps & { wrapped: React.ReactElement }) => {
  return (
    <Grid
      columns={3}
      gap={8}
      style={{
        width: '100%',
      }}
    >
      <Grid.Item>
        <Button
          block
          size="large"
          className="btn"
          style={{
            borderRadius: '3.3333vw',
            color: 'rgba(153, 153, 153, 1)',
          }}
          disabled={props?.loading}
          data-testid="operate-auth-btn"
          onClick={async () => {
            try {
              const res: any = await props?.methods.supervisionOrReport({
                remark: '',
              });
              if(res) {
                history.go(-1);
              }
            } catch (error) {
              
            }
          }}
        >
          发起审批
        </Button>
      </Grid.Item>
      <Grid.Item span={2}>
        {React.cloneElement(wrapped, {
          children: '汇报填报',
          style: {
            borderRadius: '3.3333vw',
          },
          onClick: () => {
            history.push('/twotickets/operation/fill/' + props?.info?.id);
          },
        })}
      </Grid.Item>
    </Grid>
  );
};

const State8ActionSheet = ({ wrapped, ...props }: CommonProps & { wrapped: React.ReactElement }) => {
  const [visible, setVisible] = useState(false);
  return props?.info?(
    <>
      {React.cloneElement(wrapped, {
        ...wrapped.props,
        onClick: () => {
          setVisible(true);
        },
      })}
      <TerminationGuardianshipActionSheet
        cancelText="取消"
        visible={visible}
        onClose={() => setVisible(false)}
        actions={[
          {
            text: <div data-testid="termination-confirm-btn">终结操作票</div>,
            key: 'guardianship',
          },
        ]}
        onAction={async (action) => {
          if (action.key === 'guardianship') {
            const result = await Dialog.confirm({
              content: '是否确认终结该操作票？',
            });
            if (result) {
              const res: any = await props?.methods?.supervisionOrReport({});
              if(res){
                setVisible(false);
                history.go(-1);
              }
            } else {
              setVisible(false);
            }
          }
        }}
        styles={{
          body: {
            backgroundColor: 'transparent',
            dataTestid:"termination-confirmation"
          },
        }}
      />
    </>
  ):null;
};

const State9AuditPopup = ({ wrapped, ...props }: CommonProps & { wrapped: React.ReactElement }) => {
  return (
    <FilterPopup
      iconRender={wrapped}
      title="监察审核"
      popupProps={{
        bodyStyle: {
          borderTopLeftRadius: '.7407vw',
          borderTopRightRadius: '.7407vw',
          minHeight: '70vh',
          display: 'flex',
          flexDirection: 'column',
        },
      }}
      footer={(submit, rest, custom) => {
        return (
          <Grid
            columns={2}
            gap={'3.3333vw'}
            style={{
              boxSizing: 'border-box',
              padding: '1.8519vw 3.3333vw',
            }}
          >
            <Grid.Item span={1}>
              <Button
                block
                size="middle"
                className="btn"
                disabled={props?.loading}
                data-testid="audit-unqualified-btn"
                onClick={() => {
                  custom();
                }}
                style={{
                  '--border-color': 'rgba(252, 89, 90, 1)',
                  '--border-radius': '3.3333vw',
                  '--text-color': 'rgba(252, 89, 90, 1)',
                }}
              >
                不合格
              </Button>
            </Grid.Item>
            <Grid.Item span={1}>
              <Button
                block
                color="primary"
                size="middle"
                className="btn"
                disabled={props?.loading}
                data-testid="audit-qualified-btn"
                onClick={() => {
                  submit();
                }}
                style={{
                  '--border-radius': '3.3333vw',
                }}
              >
                合格
              </Button>
            </Grid.Item>
          </Grid>
        );
      }}
      onSubmit={async (values: any) => {
        await props?.methods?.supervisionOrReport({
          operateType: values?.isCustom === 0 ? 1 : 0,
          remark: values?.remark,
        });
        history.go(-1);
      }}
    >
      <Form.Item name="remark" label="备注" layout="vertical" data-testid="audit-remark">
        <TextArea
          placeholder="请输入"
          style={{
            '--font-size': '4.2592vw',
          }}
          autoSize={{ minRows: 3, maxRows: 8 }}
        />
      </Form.Item>
    </FilterPopup>
  );
};

const handleRedirect = (id: string | undefined) => {
  if (id) {
    history.push(`/twotickets/operation/auth/${id}`);
  }
};

// 示例：创建跳转处理器工厂
const createRedirectHandler =
  (states: number[]) =>
  ({ wrapped, info }: CommonProps & { wrapped: React.ReactElement }) =>
    states.includes(info?.state || 0) ? React.cloneElement(wrapped, { 
      ...wrapped.props, 
      onClick: ()=>handleRedirect(info?.id)
   }) : null;

// 独立的状态处理模块
const stateHandlers = {
  2: createRedirectHandler([2, 3, 4, 7]),
  5: State5Popup,
  6: State6Grid,
  8: State8ActionSheet,
  9: State9AuditPopup,
};

const btnGroupWithHoc = <P extends CommonProps>(WrappedComponent: React.ComponentType<P>) => {
  const StateHandlers: React.FC<CommonProps & { wrapped: React.ReactElement }> = (props) => {
    const state = props.info?.state;
    const handler = stateHandlers[[2, 3, 4, 7]?.includes(state) ? 2 : state] || null;
    return handler ? handler(props) : null;
  };
  return (props: CommonProps & Omit<P, keyof CommonProps>) => <StateHandlers {...props} wrapped={WrappedComponent as any} />;
};

export default btnGroupWithHoc;
