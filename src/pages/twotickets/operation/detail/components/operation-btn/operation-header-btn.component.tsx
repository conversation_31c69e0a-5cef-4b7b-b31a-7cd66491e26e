import { useTwoTicketAuth } from '@/hooks/useTwoTicketAuth';
import { Grid, Image, Popover, Space } from 'antd-mobile';
import { useMemo } from 'react';
import { history } from 'umi';
import { exportWordUrl } from '@/services/twotickets/operation/statistics';
import { handleExport } from '@/utils/utils';

/**
 * 2监护人审核  ['打印']
 * 3值班负责人审核 ['打印']
 * 4值长审核 ['打印']
 * 5值长发令 ['打印']
 * 16值长发令后 ['作废', '打印'] （填写发令时间之后）
 * （
 *   值长发令后，监护人可以打印线下执行，
 *   监护人打印，由操作人、监护人、值班负责人、值长进行手工签字确认
 *   (打印时打开网页预览页面，打印的表单上加上二维码，手机通过扫面二维码可以查看表单信息)
 * ）
 * 26监护人填写 ['打印']
 * 点击暂存后，这里的按钮又变回“打印”和“汇报填报'
 * 36监护人汇报 [打印]
 * 7值长查看 ['打印']
 * 8监护人终结 ['打印']
 * 9监测审核 ['打印']
 * 10已完成 ['生成标准票', '打印', '导出']
 * 已终结 ['生成标准票', '打印', '导出']
 * -11已作废 ['生成标准票', '打印', '导出']
 * 11未执行完成 （监护人汇报的时候填写的） ['生成标准票', '打印', '导出']
 * 12已作废 ['生成标准票', '打印', '导出']
 * @param param0
 * @returns
 */
export const OperationHeaderBtn = ({ data }) => {
  // 根据状态展示不同的按钮
  // 1. 展示打印图标
  // 2. 展示打印和作废按钮
  // 3. 展示更多按钮，更多点击后展示生成标准票，打印和下载
  const { nextTask, methods } = useTwoTicketAuth(data, '7');
  const btnGroup = useMemo(() => {
    if (data?.ticketType) {
      // 工作票
      if(data?.ticketType === '6' || data?.ticketType === '7'){
        return [
          { key: '1', text: '已填报工作票' },
          { key: '2', text: '工作票（模板）' },
          { key: '3', text: '作业安全风险控制卡' },
          { key: '4', text: '作业安全风险控制卡（模板）' },
        ];
      }
    } else {
      // 操作票
      const btns = [];
      // 监护人审核、值班负责人审核、值长审核、值长发令、值长发令后、监护人填写、监测审核、已完成、已终结、已作废、未执行完成 都存在打印按钮
      if (Number(data?.state) >= 2 && Number(data?.state) <= 12) btns.push({ key: 'printer', text: '打印' });
      // 作废按钮单独处理 根据请求接口返回的nextTask判断
      if (nextTask?.filter?.((item: any) => item?.conditionParams?.state === '12')?.length) btns.push({ key: 'invalidate', text: '作废' });
      //已完成、已终结、已作废、未执行完成 都存在生成标准票, 下载按钮
      if (Number(data?.state) >= 10) {
        btns.unshift({ key: 'generate', text: '生成标准票' });
        btns.push({ key: 'download', text: '下载' });
      }
      return btns;
    }
  }, [data, nextTask]);

  // 顶部按钮根据上面逻辑分析
  if (!btnGroup?.length) return null;
  else if (!data?.ticketType)  return (
      <>
        {btnGroup?.length < 3 ? (
          <Grid columns={btnGroup?.length} gap={8}>
            {btnGroup.map((item, index) => (
              <Grid.Item
                key={index}
                style={{
                  justifyContent: 'flex-end',
                }}
                onClick={() => {
                  if (item.key === 'invalidate') {
                    history.push(`/twotickets/operation/invalidate/${data.id}`);
                  } else {
                    methods.print();
                  }
                }}
              >
                <Image
                  key={index}
                  src={require(`@/assets/twotickets/${item.key}.png`)}
                  alt={item.text}
                  width={'5vw'}
                  height={'5vw'}
                  data-testid={`header-${item.key}-action-btn`}
                  style={{
                    display: 'inline-block',
                  }}
                />
              </Grid.Item>
            ))}
          </Grid>
        ) : (
          <Popover.Menu
            actions={btnGroup}
            className="operation-header-btn-popover"
            placement="bottom-start"
            onAction={async (node) => {
              if (node.key === 'download') {
                await methods.download();
              } else if (node.key === 'generate') {
                await methods.generateStandard();
              } else {
                await methods.print();
              }
            }}
            trigger="click"
          >
            <Image
              src={require(`@/assets/twotickets/more.png`)}
              alt="更多"
              width={'5vw'}
              height={'5vw'}
              data-testid={`header-more-action-btn`}
              style={{
                display: 'inline-block',
              }}
            />
          </Popover.Menu>
        )}
      </>
    );
  else return ((
    <Popover.Menu
      actions={btnGroup}
      className="operation-header-btn-popover"
      placement="bottom-start"
      onAction={async (node) => {
        const printMap: any = {
          '1': '26',
          '2': '21',
          '3': '3',
          '4': '4',
          '5': '1',
          '6': '2'
        }
        handleExport({
          id: data?.id,
          ticketType: '6',
          printType: printMap[printMap[node.key as any]],
        })
      }}
      trigger="click"
    >
      <Image
        src={require(`@/assets/twotickets/printer.png`)}
        alt="更多"
        width={'5vw'}
        height={'5vw'}
        data-testid={`header-more-action-btn`}
        style={{
          display: 'inline-block',
        }}
      />
    </Popover.Menu>
  )
  )
};
