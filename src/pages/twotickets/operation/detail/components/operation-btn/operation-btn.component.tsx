
import { styled } from 'umi';
import { OperateBase } from '../../../typing';
import { Button } from 'antd-mobile';
import { useTwoTicketAuth } from '@/hooks/useTwoTicketAuth';
import { Statuses } from '@/utils/constant';
import btnGroupWithHoc from '../../hocs/btnGroupWithHoc';

type OperationBtnGroupComponentProps = {
  info: OperateBase;
};

const OperationBtnGroupComponentWrapper = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  height: 15.5556vw;
  padding: 2.1297vw 3.3333vw;
  background-color: #fff;
  .btn {
    font-size: 3.8881vw;
    font-weight: 400;
    letter-spacing: 0vw;
    line-height: 5.1853vw;
    color: rgba(255, 255, 255, 1);
  }
`;

/**
 * 操作票按钮
 * 2. 监护人审核  ['监护人审批'， ‘打印’]
 * 3. 值班负责人审核 ['值班负责人审批'， ‘打印’]
 * 4. 值长审核 ['值班负责人审批 '， ‘打印’]
 * @param props
 * @returns
 */
export const OperationBtnGroupComponent = (props: OperationBtnGroupComponentProps) => {
  const { info } = props;

  const { nextTask, methods, loading } = useTwoTicketAuth(info);
  const BtnGroup = btnGroupWithHoc(
    (
      <Button block color="primary" shape="rounded" size="large" className="btn" disabled={loading} data-testid={`state-${info?.state}-action-btn`} data-state={Statuses?.[info?.state]?.state}>
        {Statuses?.[info?.state]?.name}
      </Button>
    ) as any,
  );
  return (
    <OperationBtnGroupComponentWrapper>
      <BtnGroup info={info} nextTask={nextTask} methods={methods} loading={loading} />
    </OperationBtnGroupComponentWrapper>
  );
};
