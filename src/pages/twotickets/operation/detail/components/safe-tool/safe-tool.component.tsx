import { Card, List, Space } from 'antd-mobile';
import { DownOutline, RightOutline, UnorderedListOutline, UpOutline } from 'antd-mobile-icons';
import { styled } from 'umi';
import { useMemo, useState } from 'react';
import useInterleave from '@/hooks/useInterleave';
import { ListCard } from '@/component/list-card/list-card.component';
import { OperatorStandardTool } from '@/styles/twotickets.style';

const SafeToolComponentWrapper = styled(Space)`
  width: 100%;
  font-family: PingFang SC;
  .adm-card-body {
    padding-bottom: 5vw;
  }
  .module_name {
    margin: 4.6297vw 2.9631vw 2.5vw;
    font-size: 4.2592vw;
    font-weight: 500;
    letter-spacing: 0vw;
    line-height: 6.1675vw;
    color: rgba(51, 51, 51, 1);
  }
`;


type SafeToolComponentProps = {
  data: any;
};

export const SafeToolComponent = (props: SafeToolComponentProps) => {
  const { data } = props;
  const [showStandardOperatorToolTypeAllFlowChart, setShowStandardOperatorToolTypeAllFlowChart] = useState(false);
  const standardOperatorToolTypeList = useMemo(() => {
    if (showStandardOperatorToolTypeAllFlowChart || data?.standardOperatorToolTypeList?.length <= 2) {
      return data?.standardOperatorToolTypeList;
    } else {
      return data?.standardOperatorToolTypeList?.slice(0, 3);
    }
  }, [data, showStandardOperatorToolTypeAllFlowChart]);
  const SplitPoint = (
    <div
      style={{
        display: 'inline-block',
        width: '.7408vw',
        height: '.7408vw',
        borderRadius: '.3703vw',
        margin: '0 1.1111vw',
        backgroundColor: 'rgba(102, 102, 102, 1)',
      }}
    />
  );

  return (
    <SafeToolComponentWrapper direction="vertical">
      <div className="module_name">安全工器具</div>
      <ListCard title="安全工器具" isMore data={data?.standardOperatorToolTypeList || []} maxLength={3} moreLink={`/twotickets/common/check-list/standard-operator-tool/${data?.id}`}>
        {(dataList) => (
          <OperatorStandardTool
            style={{
              '--border-top': 'none',
              '--border-bottom': 'none',
            }}
          >
            {dataList?.map((item: any, index: number) => (
              <List.Item key={index}>
                <div className="tool_name">{item?.toolName}</div>
                <div className="tool_info">
                  {useInterleave(
                    [item?.type, item?.kind, item?.responsibility]?.filter((i) => i),
                    SplitPoint,
                  )}
                </div>
              </List.Item>
            ))}
          </OperatorStandardTool>
        )}
      </ListCard>
    </SafeToolComponentWrapper>
  );
};
