import { Image, Card, Grid, Divider, Space, List } from 'antd-mobile';
import { styled } from 'umi';

import jixie from '@/assets/twotickets/jixie.png';
import dianqi from '@/assets/twotickets/dianqi.png';
import { SHOW_TIME_FORMAT } from '@/utils/constant';
import dayjs from 'dayjs';
import { RightOutline, UnorderedListOutline } from 'antd-mobile-icons';
import ImageView from '@/component/image-view/image-view.component';
import useInterleave from '@/hooks/useInterleave';
import { ListCard } from '@/component/list-card/list-card.component';
import { OperationFileList } from '@/styles/twotickets.style';
import { AttachmentItemRow } from '@/pages/twotickets/common/[type]/components/attachment-content/attachment-content.component';
// 基本新戏

const BasicInfoComponentWrapper = styled(Space)`
  width: 100%;
  font-family: PingFang SC;
  .adm-card-body {
    padding-bottom: 5vw;
  }
  .title {
    font-size: 4.4444vw;
    font-weight: 500;
    letter-spacing: 0vw;
    line-height: 6.4814vw;
    color: rgba(56, 56, 56, 1);
    text-align: left;
  }
  .project {
    margin: 1.1111vw 0;
    font-size: 3.3333vw;
    font-weight: 400;
    letter-spacing: 0vw;
    line-height: 4.8269vw;
    color: rgba(102, 102, 102, 1);
    text-align: left;
  }
  .item {
    font-size: 3.3333vw;
    font-weight: 400;
    letter-spacing: 0vw;
    line-height: 4.8269vw;
    color: rgba(153, 153, 153, 1);
    .name {
      font-size: 3.3333vw;
      font-weight: 400;
      letter-spacing: 0vw;
      line-height: 4.8269vw;
      color: rgba(51, 51, 51, 1);
    }
  }
  .remark {
    font-size: 4.2592vw;
    font-weight: 400;
    letter-spacing: 0vw;
    line-height: 6.1675vw;
    color: rgba(51, 51, 51, 1);
  }
`;

type BasicInfoComponentProps = {
  data: any;
};

export const BasicInfoComponent = (props: BasicInfoComponentProps) => {
  const { data } = props;
  const SplitPoint = (
    <div
      style={{
        display: 'inline-block',
        width: '.7408vw',
        height: '.7408vw',
        borderRadius: '.3703vw',
        margin: '0 1.1111vw',
        backgroundColor: 'rgba(102, 102, 102, 1)',
      }}
    />
  );

  return (
    <BasicInfoComponentWrapper direction="vertical">
      <Card>
        <div className="title">
          <Image
            src={data?.type === '机械操作票' ? jixie : dianqi}
            width={'8.8889vw'}
            height={'4.5369vw'}
            style={{
              display: 'inline-block',
              marginRight: '2.2222vw',
              lineHeight: '6.4814vw',
              verticalAlign: 'middle',
            }}
          />
          {data?.name}
        </div>
        <div className="project">{data?.unitName}</div>
        <Grid columns={3}>
          {data?.operatorName && (
            <Grid.Item>
              <div className="item">
                操作人：<span className="name">{data.operatorName}</span>
              </div>
            </Grid.Item>
          )}
          {data?.guardianName && (
            <Grid.Item>
              <div className="item">
                监护人：<span className="name">{data.guardianName}</span>
              </div>
            </Grid.Item>
          )}
          {data?.watchName && (
            <Grid.Item>
              <div className="item">
                值班负责人：<span className="name">{data.watchName}</span>
              </div>
            </Grid.Item>
          )}
          {data?.firstShiftSupervisorName && (
            <Grid.Item>
              <div className="item">
                值长：<span className="name">{data?.firstShiftSupervisorName}</span>
              </div>
            </Grid.Item>
          )}
        </Grid>
        <Divider />
        <div className="item">
          操作地点：<span className="name">{data?.location}</span>
        </div>
        <div className="item">
          计划时间：
          {(data?.planBeginTime || data?.planEndTime) && <span className="name">{`${data?.planBeginTime ? dayjs(data.planBeginTime).format(SHOW_TIME_FORMAT) : ''} ~ ${
            data?.planEndTime ? dayjs(data.planEndTime).format(SHOW_TIME_FORMAT) : ''
          }`}</span>}
        </div>
        <Divider />
        <div className="remark">
          <div className="title">备注</div>
          <div className="content">{data?.remark}</div>
        </div>
      </Card>
      {data?.pictureContentList?.length && (
        <Card title="图片">
          {ImageView({ imgList: data?.pictureContentList?.map(item=>item.id)})}
        </Card>
      )}
     <ListCard title="附件" data={data?.attachmentContentList || []} moreLink={`/twotickets/common/check-list/attachment-content/${data?.id}`} maxLength={5}>
        {(dataList) => (
          <OperationFileList
            style={{
              '--border-top': 'none',
              '--border-bottom': 'none',
            }}
          >
            {dataList?.map((item: any, index: number) => (
              <AttachmentItemRow key={item.id} item={item as any} />
            ))}
          </OperationFileList>
        )}
      </ListCard>
    </BasicInfoComponentWrapper>
  );
};
