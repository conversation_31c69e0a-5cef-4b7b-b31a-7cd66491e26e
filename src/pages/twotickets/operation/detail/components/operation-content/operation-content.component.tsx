import { ListCard } from '@/component/list-card/list-card.component';
import { OperateRiskList, OperateStepsList } from '@/styles/twotickets.style';
import { Button, Card, List, Space, Tag } from 'antd-mobile';
import { RightOutline } from 'antd-mobile-icons';
import { styled, history, useLocation } from 'umi';
import { hasReview } from '../../../list/operation-list.page';

const OperationContentComponentWrapper = styled(Space)`
  width: 100%;
  font-family: PingFang SC;
  .adm-card-body {
    padding-bottom: 5vw;
  }
  .module_name {
    margin: 4.6297vw 2.9631vw 2.5vw;
    font-size: 4.2592vw;
    font-weight: 500;
    letter-spacing: 0vw;
    line-height: 6.1675vw;
    color: rgba(51, 51, 51, 1);
  }
`;

type OperationContentComponentProps = {
  data: any;
};

export const OperationContentComponent = (props: OperationContentComponentProps) => {
  const { data } = props;
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const pageType = searchParams.get('type') || 'view';
  const OPERATOR_CARD_CONFIG = {
    extra: (
      <Space>
        {pageType === 'edit' && hasReview(data) && data.state === '6' && (
          <Button
            color="primary"
            size="mini"
            shape="rounded"
            onClick={() => {
              history.push(`/twotickets/operation/fill/${data.id}`);
            }}
            style={{
              '--background-color': 'rgba(215, 225, 250, 1)',
              '--border-color': 'rgba(215, 225, 250, 1)',
              '--text-color': 'rgba(17, 84, 237, 1)',
            }}
          >
            填报
          </Button>
        )}
        <RightOutline
          fontSize={'3.3333vw'}
          onClick={() => {
            history.push(`/twotickets/common/check-list/operation-step/${data.id}`);
          }}
        />
      </Space>
    ),
  };
  return (
    <OperationContentComponentWrapper direction="vertical">
      <div className="module_name">操作内容</div>
      <ListCard
        title="危险点与预防措施"
        isMore
        data={data?.operateRiskList || []}
        maxLength={3}
        moreLink={`/twotickets/common/check-list/operation-risk/${data?.id}`}
      >
        {(dataList) => (
          <OperateRiskList
            style={{
              '--border-bottom': 'none',
              '--border-top': 'none',
              '--border-inner': 'none',
              '--align-items': 'flex-start',
            }}
          >
            {dataList?.map((item: any, index: number) => (
              <List.Item key={index} prefix={<div className="prefix">{index + 1}、</div>}>
                <div className="operate_risk">
                  <Space className="label">危&ensp;险&ensp;点：</Space>
                  {item?.perilPoint}
                </div>
                <div className="operate_risk">
                  <Space className="label">预防措施：</Space>
                  {item?.measure}
                </div>
              </List.Item>
            ))}
          </OperateRiskList>
        )}
      </ListCard>
      <ListCard
        title={
          <div>
            操作项
            {data?.operatorStepList?.filter((item) => item?.isMainOperation === '1')?.length ? (
              <>
                （
                <span
                  style={{
                    color: 'rgba(17, 84, 237, 1)',
                  }}
                >
                  {data?.operatorStepList?.filter((item) => item?.isMainOperation === '1')?.length}
                </span>
                /{data?.operatorStepList?.length}）
              </>
            ) : (
              ''
            )}
          </div>
        }
        type="操作项"
        subtitle="注：默认倒数第二项操作项目为“全面检查”，倒数第一项为“汇报批准人”"
        isMore
        data={data?.operatorStepList || []}
        maxLength={3}
        moreLink={`/twotickets/common/check-list/operation-step/${data?.id}`}
        {...OPERATOR_CARD_CONFIG}
      >
        {(dataList) => (
          <OperateStepsList
            style={{
              '--border-top': 'none',
              '--border-bottom': 'none',
              '--align-items': 'flex-start',
            }}
          >
            {dataList?.map((item: any, index: number) => (
              <List.Item key={index} prefix={<div className="prefix">{index + 1}</div>}>
                <div className="operate_steps">
                  {data?.operatorStepList?.filter((step) => step?.isMainOperation === '1')?.length && item?.isMainOperation !== '1' ? (
                    <Tag
                     color="rgba(253, 146, 40, 1)" 
                     fill="outline" 
                     style={{ '--border-radius': '.5556vw', marginRight: ".9258vw" }}>
                      未执行
                    </Tag>
                  ) : null}
                  {item?.operationStep}
                </div>
                {(item?.operationTime || item?.remark) && (
                  <div className="body">
                    {item?.operationTime && <div className="operationTime">操作时间：{item?.operationTime}</div>}
                    {item?.remark && <div className="remark">备注：{item?.remark}</div>}
                  </div>
                )}
              </List.Item>
            ))}
          </OperateStepsList>
        )}
      </ListCard>
    </OperationContentComponentWrapper>
  );
};
