.container{
  font-family: PingFang SC;
  width: 100%;
  height: 100vh;
  border: 0;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  .content{
    flex: 1;
    overflow-y: auto;
    padding: 36px 0;
    :global{
      .adm-form-item-label{
        font-size: 45.999px;
        font-weight: 400;
        letter-spacing: 0px;
        line-height: 66.609px;
        color: rgba(51, 51, 51, 1);
      }
      .adm-radio-content{
        font-size: 45.999px;
        font-weight: 400;
        letter-spacing: 0px;
        line-height: 66.609px;
        color: rgba(51, 51, 51, 1);
      }
      .adm-checkbox-content{
        flex: 1;
      }
      .adm-text-area {
        box-sizing: border-box;
        padding: 21.999px 44.001px;
        border-radius: 20.001px;
        background: rgba(245, 245, 245, 1);
        // 提示文字改小点
        .adm-text-area-element::placeholder {
          font-size: 36px;
        }
      }
    }
    .operatorTitle{
      position: relative;
      font-size: 45.999px;
      font-weight: 500;
      letter-spacing: 0px;
      line-height: 66.609px;
      color: rgba(51, 51, 51, 1);
      .num{
        font-size: 42px;
        font-weight: 400;
        letter-spacing: 0px;
        line-height: 60.819px;
        color: rgba(51, 51, 51, 1);
        .checked{
          color: rgba(17, 84, 237, 1);
        }
      }
    }
      
    .note{
      margin-top: 15px;
      width: 100%;
      font-size: 36px;
      font-weight: 400;
      letter-spacing: 0px;
      line-height: 52.131px;
      color: rgba(153, 153, 153, 1);
    }
    .operators{
      width: 100%;
      :global{
        .adm-space-item{
          padding-top: 35.001px;
          padding-bottom: 35.001px;
          margin-bottom: 0;
          border-bottom: .999px solid rgba(229, 229, 229, 1);
          .adm-checkbox{
            align-items: flex-start;
            .adm-checkbox-icon{
              margin-top: 5.001px;
            }
          }
        }
      }
    }
    .bottom{
      height: 144px;
      font-size: 39.999px;
      font-weight: 400;
      letter-spacing: -1.599px;
      line-height: 144px;
      color: rgba(153, 153, 153, 1);
      text-align: center;
    }
  }
  .footer{
    padding: 32.001px 35.001px;
    box-sizing: border-box;
    background-color: #fff;
    .btn{
      font-size: 41.991px;
      font-weight: 400;
      letter-spacing: 0px;
      line-height: 56.001px;
      color: rgba(255, 255, 255, 1);
    }
  }
}
.operatorItemEditPopup{
  :global{
    .adm-form-item-label {
      font-size: 48px;
      font-weight: 500;
      letter-spacing: 0px;
      line-height: 69.999px;
      color: rgba(51, 51, 51, 1);
      margin-bottom: 39px;
    }
    .adm-text-area {
      box-sizing: border-box;
      padding: 21.999px 44.001px;
      border-radius: 20.001px;
      background: rgba(245, 245, 245, 1);
      // 提示文字改小点
      .adm-text-area-element::placeholder {
        font-size: 36px;
      }
    }
  }
}