import { WORKFLOW_STATE_MAP_TEXT } from '@/constant';
import TwoTicketsNavBar from '@/component/twotickets-navbar/twotickets-navbar.component';
import styles from './operation-more.page.less';
import { useParams } from 'umi';
import { useRequest } from 'ahooks';
import { getOperationDetail } from '@/services/twotickets/operation/statistics';
import ProcessViewerComponent from '@/component/process-viewer/process-viewer.component';

const OperationMore = () => {
  // 获取url参数
  const params = useParams();
  // 使用useRequest获取数据
  const { data, loading } = useRequest(() => getOperationDetail({ id: params.id }).then((res) => res.data), {});
  const types = {
    1: {
      title: '流程图',
      children: (
        <div className={styles.processViewer}>
          <ProcessViewerComponent
            processInstanceId={data?.processInstanceId}
            modelId={data?.modelId}
            toolTips={
              (data?.workflowApprovalRecordList?.map((item: any) => ({
                userTaskId: item.taskDefKey,
                approver: item.approverName,
                comment: item.opinion,
              })) as IToolTip[]) || []
            }
          />
        </div>
      ),
    },
  };
  return (
    <div className={styles.container}>
      <TwoTicketsNavBar title={types[params.type].title} bgcolor={'#fff'} color={'#000'} />

      <div className={styles.content}>{!loading && types[params.type].children}</div>
    </div>
  );
};

export default OperationMore;
