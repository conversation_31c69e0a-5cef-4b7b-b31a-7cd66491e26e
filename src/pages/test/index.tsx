import React, { useEffect } from 'react';
import { Line } from '@antv/g2plot';

const data = [
  {
    name: '家具家电',
    sales: 38,
  },
  {
    name: '粮油副食',
    sales: 52,
  },
  {
    name: '生鲜水果',
    sales: 61,
  },
  {
    name: '美容洗护',
    sales: 145,
  },
  {
    name: '母婴用品',
    sales: 48,
  },
  {
    name: '进口食品',
    sales: 38,
  },
  {
    name: '食品饮料',
    sales: 38,
  },
  {
    name: '家庭清洁',
    sales: 38,
  },
];

const Test = () => {
  useEffect(() => {
    fetch('https://gw.alipayobjects.com/os/bmw-prod/1d565782-dde4-4bb6-8946-ea6a38ccf184.json')
      .then((res) => res.json())
      .then((data) => {
        const line = new Line('container', {
          data,
          padding: 'auto',
          xField: 'Date',
          yField: 'scales',
          xAxis: {
            // type: 'timeCat',
            tickCount: 5,
          },
        });

        line.render();
      });


  }, [])
  return (
    <div style={{ height: 300 }} id='container'></div>
  )
}

export default Test;