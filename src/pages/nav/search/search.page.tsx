import { List, SearchBar } from "antd-mobile";
import { DeleteOutline, CloseOutline } from "antd-mobile-icons";
import { history, useSearchParams } from "umi";
import { useEffect, useState } from "react";
import { useDebounceFn } from "ahooks";
import styles from "./search.page.less";
import ResultPage from "./component/result.component";
import { searchDocument } from "@/services/info-manage.service";
import { getModules } from "@/services/nav.service";
import { deepForEachFn } from '../../../utils/data-processing-tools';

const SearchPage: React.FC = () => {
  const [value, setValue] = useState("");
  const [allList, setAllList] = useState([])
  const [historyList, setHistoryList] = useState([]);
  const [searchList, setSearchList] = useState<any[]>([]);
  const [showResult, setShowResult] = useState(false);

  const [getParams, setParams] = useSearchParams(); // 获取url参数

  const { run } = useDebounceFn(
    (value, fun?: () => void) => {
      console.log(value, 'value=======');
      const filter = allList.filter(item => item.name.includes(value))
      setSearchList(filter)
      fun()
    },
    { wait: 500 }
  );

  // 监听搜索改变
  const handleChange = (value: string) => {
    setValue(value);

    if (!value) {
      setShowResult(false)
    } else {
      run(value, () => setShowResult(true));
    }
  };

  // 通过搜索框按钮搜索
  const handleSearch = () => {
    if (value && !historyList.includes(value)) {
      const list = [...historyList, value];
      setHistoryList(list);
      localStorage.setItem("searchHistory", JSON.stringify(list));
    }
    run(value, () => setShowResult(true));
  };

  // 清空搜索
  const handleClear = () => {
    setValue("");
    setShowResult(false);
  };

  // // 通过搜索联动数据查询
  // const handleSearchByName = (checkName: string) => {
  //   setValue(checkName);
  //   handleSearch();
  //   setShowResult(true);
  // };

  // 通过历史数据查询
  const handleSearchHistory = (checkName: string) => {
    setValue(checkName);
    run(checkName, () => setShowResult(true));
  };

  // 删除历史记录
  const handleDeleteHistory = () => {
    setHistoryList([]);
    localStorage.setItem("searchHistory", "");
  };

  // 删除搜索结果
  const handleDeleteResult = (id: string) => {
    setSearchList(searchList?.filter((item) => item.id !== id));
  };

  useEffect(() => {
    const data = localStorage.getItem("searchHistory");
    if (data?.length) {
      setHistoryList(JSON.parse(data));
    }
    // 接口获取所有的可查询数据列表
    getModules().then((res) => {
      const data = res.data?.userPrivilegeList
      const list = []
      deepForEachFn(data, (item) => {
        if (item.displayLocation === '_h5page') {
          list.push(item)
        }
      })
      setAllList(list)
    })
  }, []);

  return (
    <div className={styles.pageWrap} style={{ paddingTop: `${window.top.localStorage?.topHeight ?? 0}px` }}>
      <div className={styles.searchTop}>
        <SearchBar
          value={value}
          onChange={handleChange}
          onClear={handleClear}
          placeholder="请输入名称"
        />
        <span onClick={handleSearch}>搜索</span>
        <CloseOutline onClick={() => history.push(`/nav/function-manage`)} />
      </div>
      {showResult ? (
        <ResultPage searchList={searchList} onDelete={handleDeleteResult} searchWord={value} />
      ) : (
        <div
          className={[
            styles.searchHistory,
          ].join(" ")}
        >
          {historyList?.length ? (
            <div className={styles.title}>
              历史数据
              <DeleteOutline onClick={handleDeleteHistory} />
            </div>
          ) : (
            ""
          )}
          <div className={styles.historyList}>
            {historyList?.map((item, index) => (
              <span
                key={index}
                onClick={() => {
                  handleSearchHistory(item);
                }}
              >
                {item}
              </span>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default SearchPage;
