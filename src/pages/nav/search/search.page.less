.pageWrap {
  width: 100%;
  height: 100%;
  background-color: var(--adm-color-background);
  .searchTop {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
    padding: 22px 40px;
    border-bottom: 1px solid #E6E6E6;
    font-size: 42px;
    background-color: var(--adm-color-background);
    :global {
      .antd-mobile-icon {
        font-size: 46px;
        margin-left: 10px;
      }
      .adm-search-bar {
        width: 820px;
        height: 90px;
        .adm-search-bar-input-box {
          border-radius: 45px;
        }
        .adm-button {
          line-height: 1;
        }
      }
    }
  }
  .searchHistory {
    padding: 53px 41px;
    &.isSearching {
      padding: 0;
    }
    .title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 44px;
    }
    .historyList {
      display: flex;
      gap: 30px;
      margin-top: 40px;
      flex-wrap: wrap;
      span {
        padding: 20px 40px;
        border-radius: 40px;
        color: var(--adm-color-text-secondary);
        font-size: 42px;
        background-color: var(--adm-color-box);
      }
    }
    :global {
      .adm-list-body {
        border-top: none;
      }
    }
  }
}
