import { <PERSON>, Divider, Error<PERSON><PERSON>, List, SwipeAction, Toast } from "antd-mobile";
import { history } from "umi";
import styles from "./result.component.less";
import { useEffect, useState } from "react";

interface Props {
  searchList: any[];
  onDelete: (id: string) => void;
  searchWord: string
}

const ResultPage: React.FC<Props> = ({ searchList, searchWord }) => {

  // 高亮函数
  const highlightText = (text: string): React.ReactNode => {
    if (!searchWord) return text;
    const regex = new RegExp(`(${searchWord})`, 'gi');
    const parts = text.split(regex);
    return (
      <>
        {parts.map((part, index) => {
          if (index % 2 === 1) {
            // 如果是匹配的部分，则包裹在 span 标签内并添加样式
            return <span key={index} style={{ color: 'blue' }}>{part}</span>;
          } else {
            // 其他部分保持原样输出
            return part;
          }
        })}
      </>
    );
  };

  return (
    <div className={styles.resultPanel}>
      {searchList?.length ?
        (<List>
          {searchList.map((item, index) => (
            <List.Item key={index} onClick={() => {
              history.push(item.routingUrl);
            }}>{highlightText(item.name)}</List.Item>
          ))}
        </List>
        ) :
        (
          <ErrorBlock status="empty" />
        )}

    </div>
  );
};

export default ResultPage;
