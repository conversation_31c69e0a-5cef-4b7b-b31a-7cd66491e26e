.resultPanel {
  display: flex;
  flex-direction: column;
  gap: 15px;
  padding: 0 30px;
  :global {
    .adm-card {
      background-color: var(--adm-color-box);
    }
  }
}
.cardContent {
  display: flex;
  flex-direction: column;
  gap: 29px;
  .title {
    font-size: 48px;
    font-weight: 700;
  }
  .content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 42px;
    color: var(--adm-color-text-secondary);
    .status {
      display: flex;
      align-items: center;
      gap: 21px;
      color: var(--adm-color-text);
      span {
        display: inline-block;
        width: 16px;
        height: 16px;
        border-radius: 50%;
      }
    }
  }
}
