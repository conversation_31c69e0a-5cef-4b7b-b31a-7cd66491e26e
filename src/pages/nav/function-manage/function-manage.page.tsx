import { Card, Toast } from 'antd-mobile';
import styles from './function-manage.page.less';
import { EditIconMap, MenuIconMap } from '../constant';
import { history, useSearchParams } from 'umi';
import { useEffect, useState } from 'react';
import { getModules, postCustomizeModules } from '@/services/nav.service';
import { deepForEachFn, deepMapTreeFn, sortArrByTarget } from '@/utils/data-processing-tools';
import { RightOutline, SearchOutline } from 'antd-mobile-icons';

const developing = ['/defects-manage/equip-defects'];
const FunctionManage: React.FC = () => {
  const [getParams, setParams] = useSearchParams();
  const [commonData, setCommonData] = useState<any[]>([]);
  const [allList, setAllList] = useState<any[]>([]);
  const isEdit = getParams.get('edit');

  // 点击图标
  const onIconClick = (data: any, isEdit: any) => {
    if (isEdit) return; // 编辑
    if (developing.includes(data.routingUrl)) {
      Toast.show({
        icon: 'success',
        content: '正在开发中...',
      });
    } else {
      history.push(data.routingUrl); // 跳转
    }
  };

  // 编辑图标
  const handleEdit = (commonType: string, id: string) => {
    //所有模块-非常用状态，添加到常用
    if (commonType === '0') {
      // 修改常用模块数据
      const commonObj = {} as any;
      deepForEachFn(allList, (item) => {
        if (item.id === id) {
          commonObj['privilegeProductId'] = item.id;
          commonObj['sequence'] = commonData.length;
          commonObj['name'] = item.name;
          commonObj['routingUrl'] = item.routingUrl;
          commonObj['iconClass'] = item.iconClass;
        }
      });
      setCommonData([...commonData, commonObj]);

      // 修改所有模块的是否常用状态
      const newAllList = deepMapTreeFn(allList, (item) => {
        if (item.id === id) {
          return {
            ...item,
            memo3: '1',
          };
        }
      });
      // deepMapTreeFn之后嵌套的children对象的值变了但是地址没有变，JSON做处理保证children字段的map渲染正常
      setAllList(JSON.parse(JSON.stringify(newAllList)));
    }

    // 常用模块,取消常用
    else if (commonType === '1') {
      // 修改常用模块数据
      const newCommonList = commonData.filter((item) => item.privilegeProductId !== id);
      const sortCommonList = sortArrByTarget(newCommonList, 'sequence', 'up').map((item, index) => {
        return { ...item, sequence: index };
      });
      setCommonData(sortCommonList);

      // 修改所有模块的是否常用状态
      const newAllList = deepMapTreeFn(allList, (item) => {
        if (item.id === id) {
          return {
            ...item,
            memo3: '0',
          };
        }
      });
      setAllList(JSON.parse(JSON.stringify(newAllList)));
    }
  };

  // 完成
  const onFinish = () => {
    postCustomizeModules(commonData).then((res) => {
      if (res.code === '1') {
        getModulesData();
        history.push('/nav/function-manage');
      }
    });
  };

  // 将二级节点的children扁平化处理，返回第三层节点
  const flattenThirdLevel = (nodes: any[]): any[] => {
    return nodes.map((node) => {
      if (!node.children) return node; // 无子节点直接返回
      const newChildren = node.children.flatMap((child: any) => {
        // 如果二级节点有第三层子节点，直接返回第三层节点
        return child.children ? child.children : child;
      });
      // 递归处理新生成的children（防止更深嵌套）
      return {
        ...node,
        children: flattenThirdLevel(newChildren),
      };
    });
  };

  // 接口-获取用户权限下功能模块
  const getModulesData = () => {
    getModules().then((res) => {
      console.log('res', res);
      if (res.data) {
        const commonList = sortArrByTarget(res.data?.commonList, 'sequence', 'up');
        commonList?.map((item, index) => {
          return {
            ...item,
            sequence: index,
          };
        });
        setCommonData(commonList);

        const userPrivilegeList = res.data?.userPrivilegeList; // 用户权限列表
        const processedList = flattenThirdLevel(userPrivilegeList); // 扁平化处理
        setAllList(processedList);
      }
    });
  };

  // 挂载
  useEffect(() => {
    getModulesData();
  }, []);

  return (
    <div className={styles.page}>
      <div className={styles.addBtn} style={{ paddingTop: `${window.top?.localStorage?.topHeight ?? 0}px` }}>
        {getParams.get('edit') ? (
          <span onClick={onFinish}>完成</span>
        ) : (
          <>
            <SearchOutline
              onClick={() => {
                history.push('/nav/search');
              }}
            />
            <span onClick={() => history.push('/nav/function-manage?edit=true')}>编辑</span>
          </>
        )}
      </div>
      <Card>
        <div className={styles.commonHeader}>
          <div className={styles.headerLeft}>常用功能</div>
          <div className={styles.headerRight}>可自定义首页常用功能</div>
        </div>
        <div className={styles.icons}>
          {commonData?.length > 0 &&
            commonData?.map((item, index) => (
              <div key={index} className={styles.iconBox} onClick={() => onIconClick(item, isEdit)}>
                <img src={MenuIconMap[item.iconClass as keyof typeof MenuIconMap]} className={styles.iconImg} />
                {isEdit && (
                  <img src={EditIconMap['delete']} className={styles.editIcon} onClick={() => handleEdit('1', item.privilegeProductId)} />
                )}
                <div className={styles.iconName}>{item.name}</div>
              </div>
            ))}
        </div>
      </Card>
      {allList?.length > 0 &&
        allList.map((item: any, _index: number) => {
          return (
            <Card key={item.id}>
              <div className={styles.commonHeader} onClick={() => onIconClick(item, false)}>
                <div className={styles.headerLeft}>{item?.name}</div>
                {item.routingUrl !== '0' && (
                  <div className={`${styles.headerRight} ${styles.headerRightIcon}`}>
                    <RightOutline />
                  </div>
                )}
              </div>
              <div className={styles.icons}>
                {item?.children?.length > 0 &&
                  item?.children?.map((it: any, _index: number) => {
                    return (
                      <div className={styles.iconBox} key={it.id} onClick={() => onIconClick(it, isEdit)}>
                        <img src={MenuIconMap[it.iconClass as keyof typeof MenuIconMap]} className={styles.iconImg} />
                        {isEdit && it.memo3 === '0' ? (
                          <img src={EditIconMap['add']} className={styles.editIcon} onClick={() => handleEdit('0', it.id)} />
                        ) : null}
                        <div className={styles.iconName}>{it.name}</div>
                      </div>
                    );
                  })}
              </div>
            </Card>
          );
        })}
    </div>
  );
};
export default FunctionManage;
