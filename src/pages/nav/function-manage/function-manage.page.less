.page {
  width: 100%;
  background: linear-gradient(0deg, #F5F5F5 0%, #1154ED 100%);
  background-size: 100% 200px;
  background-repeat: no-repeat;
  padding: 10px 0px 0 0px;
  // box-sizing: border-box;

  :global {
    .adm-card {
      margin: 40px 40px 0px 40px;
    }
  }

  .commonHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 40px;

    .headerLeft {
      font-family: PingFang SC;
      font-weight: bold;
      font-size: 46px;
      color: #383838;
    }

    .headerRight {
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 36px;
      color: #666666;

      &.headerRightIcon {
        font-size: 50px;
      }
    }
  }
}

.icons {
  display: flex;
  flex-wrap: wrap;

  .iconBox {
    margin: 20px 0 20px 0;
    width: 25%;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;

    .iconImg {
      width: 35%;
    }

    .editIcon {
      position: absolute;
      right: 20%;
      top: -15%;
      width: 22%
    }

    .iconName {
      margin: 10px 0 0 0;
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 36px;
      color: #333333;
    }
  }


}



.addBtn {
  position: absolute;
  right: 60px;
  top: 30px;
  color: #fff;
  display: flex;
  align-items: center;
  justify-self: baseline;
  gap: 63px;
  font-size: 44px;

  :global {
    .antd-mobile-icon {
      font-size: 60px;
    }
  }
}