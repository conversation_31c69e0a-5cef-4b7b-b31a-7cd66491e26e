import MoreIcon from '../../assets/nav/function_more.png';
import StandardIcon from '../../assets/nav/function_standard.png';
import OperationticketIcon from '../../assets/nav/function_operationticket.png';
import ElectricalIcon1 from '../../assets/nav/function_electrical1.png';
import ElectricalIcon2 from '../../assets/nav/function_electrical2.png';
import WaterworkIcon1 from '../../assets/nav/function_waterwork1.png';
import WaterworkIcon2 from '../../assets/nav/function_waterwork2.png';
import MechanicalIcon1 from '../../assets/nav/function_mechanical1.png';
import MechanicalIcon2 from '../../assets/nav/function_mechanical2.png';
import HorworkIcon1 from '../../assets/nav/function_horwork1.png';
import HorworkIcon2 from '../../assets/nav/function_horwork2.png';
import AddIcon from '../../assets/nav/function_add.png';
import DeleteIcon from '../../assets/nav/function_delet.png';
/**
 * @description 机电生命周期
 */
// 缺陷管理
import DefectManageIcon from '../../assets/nav/function_defect.png';

// 白名单
export const WhiteList = ['一人一档', '危大工程先决条件审批', '危大工程项申报', '安全责任审批'];
// 功能图标
export const MenuIconMap = {
  gd: MoreIcon,
  tjfx: StandardIcon,
  czp: OperationticketIcon,
  dq1: ElectricalIcon1,
  dq2: ElectricalIcon2,
  sg1: WaterworkIcon1,
  sg2: WaterworkIcon2,
  jx1: MechanicalIcon1,
  jx2: MechanicalIcon2,
  dh1: HorworkIcon1,
  dh2: HorworkIcon2,
  DEFECT_MANAGE: DefectManageIcon,
};

// 新增或者删除的角标
export const EditIconMap = {
  add: AddIcon,
  delete: DeleteIcon,
};

// 任务状态对应颜色
export const TaskStatusColorMap = {
  检查中: '#E88A33',
  未处理: '#E41412',
};

// 筛选类型
export const FilterTypeMap = {
  类型: 'type',
  状态: 'status',
  时间: 'time',
};
