.todoItem {

  .title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 40px 28px;
    font-size: 48px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;

    span {
      font-size: 40px;
      color: var(--adm-color-weak);
    }
  }

  .content {
    font-size: 40px;
    padding: 20px 28px 17px;
    color: #666666;

    .line {
      display: flex;
      align-items: center;
      color: var(--adm-color-text);

      .key {
        flex: 1;
      }

      .value {
        flex: 5;
      }
    }

    &>div {
      margin-bottom: 39px;
      position: relative;

    }
  }

  .footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 28px;
    height: 120px;
    border-top: 1px solid #E6E6E6;
    font-size: 40px;

    &>div {
      color: var(--adm-color-primary);
    }
  }
}