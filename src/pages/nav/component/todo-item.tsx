import moment from 'moment';
import styles from './todo-item.less';
import { RightOutline } from 'antd-mobile-icons';
import { history } from 'umi';
import { TaskStatusColorMap, WhiteList } from '../constant';
import { Toast } from 'antd-mobile';
import { KoubeiOutline } from 'antd-mobile-icons';

export interface TodoItemDataType {
  businessType: string;
  businessName: String;
  messageTime: string;
  businessTypeName: string;
  createTime: string;
  timeState: string;
  taskName: string;
  formKey: string; //处理路由
  viewUrl: string; //查看路由
  businessKey: string;
}
interface Props {
  data: TodoItemDataType;
  footText?: string;
}
const TodoItem: React.FC<Props> = ({ data, footText = '去处理' }) => {
  const {
    businessName,
    businessKey,
    messageTime,
    businessTypeName,
    createTime,
    timeState = '',
    taskName = '',
    formKey = '',
    viewUrl = '',
  } = data;
  const replacePlaceholders = (obj, str) => {
    return str.replace(/\{(\w+)\}/g, (_, key) => obj[key] || '');
  };
  const onTodoItemClick = () => {
    if (WhiteList.includes(businessTypeName)) {
      Toast.show({
        content: '请前往web端处理或查看',
        icon: <KoubeiOutline />,
      });
      return;
    } else {
      if (formKey && formKey !== '/') {
        history.push(formKey);
      } else if (viewUrl && viewUrl !== '/') {
        history.push(viewUrl);
      } else {
        Toast.show({
          content: '找不到相关路径',
        });
      }
    }
  };
  return (
    <div className={styles.todoItem}>
      <div className={styles.title}>
        {businessTypeName || '-'}
        {/* <span>{moment(messageTime).format('YYYY-MM-DD hh:mm')}</span> */}
      </div>
      <div className={styles.content}>
        <div className={styles.line}>
          <div className={styles.key}>名称</div>
          <div className={styles.value}>{businessName || '-'}</div>
        </div>
        <div className={styles.line}>
          <div className={styles.key}>时间</div>
          <div className={styles.value}>{moment(createTime).format('YYYY-MM-DD')}</div>
        </div>
        <div
          style={{
            color: TaskStatusColorMap[taskName] || '#E41412',
            display: 'flex',

            justifyContent: 'flex-end',
          }}
        >
          {taskName}
        </div>
      </div>
      <div className={styles.footer} onClick={() => onTodoItemClick()}>
        <div>{footText}</div>
        <RightOutline color="rgb(204,204,204)" />
      </div>
    </div>
  );
};
export default TodoItem;
