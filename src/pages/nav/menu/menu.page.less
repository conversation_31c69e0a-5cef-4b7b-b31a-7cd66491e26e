.box {
  width: 100%;
  // min-height: 100vh;
  height: 100%;
  background: url(../../../assets/navigation_backimg.png) top center no-repeat;
  background-color: #e0eafd;
  background-size: 100% auto;

  display: flex;
  flex-direction: column;

  :global {
    .adm-card {
      margin: 0 40px 40px 40px;
    }
  }
}

.fixedBox {

  .navTitle {
    font-size: 52px;
    font-weight: 700;
    text-align: center;
    line-height: 200px;
    margin-bottom: 30px;
  }


  .icons {
    display: flex;
    flex-wrap: wrap;

    .iconBox {
      margin: 20px 0 20px 0;
      width: 25%;
      display: flex;
      flex-direction: column;
      align-items: center;

      .iconImg {
        width: 35%;
      }

      .iconName {
        margin: 10px 0 0 0;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 36px;
        color: #333333;
      }
    }


  }

  .todoCard {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-left: 20px;
    padding-right: 20px;

    .todoName {
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 48px;
      color: #333333;
    }

    .todoDesc {
      display: flex;
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 40px;
      color: #666666;
      align-items: center;
      justify-content: space-between;

      .todoNum {
        background: #CC5252;
        border-radius: 50%;
        width: fit-content;
        height: 60px;
        text-align: center;
        line-height: 60px;
        color: #fff;
        margin: 0 10px 0 10px;
        padding: 0 20px;
      }
    }
  }

}

.scrollListBox {
  overflow-y: scroll;
  flex: 1 1;
}

.todoItem {
  // padding: 0 19px;
  // background: rgba(255, 255, 255, 0.35);
  // border: 1px solid #FFFFFF;
  // box-shadow: 0px 4px 19px 1px rgba(230, 230, 230, 0.35), 0px 3px 8px 0px rgba(238, 245, 254, 0.35), 0px 3px 8px 0px rgba(238, 245, 254, 0.35);
  // border-radius: 40px;

  // .title {
  //     display: flex;
  //     justify-content: space-between;
  //     align-items: center;
  //     padding: 40px 28px;
  //     font-size: 48px;

  //     span {
  //         font-size: 40px;
  //         color: var(--adm-color-weak);
  //     }
  // }

  // .content {
  //     font-size: 40px;
  //     padding: 20px 28px 17px;
  //     color: #666666;

  //     &>div {
  //         margin-bottom: 39px;
  //         position: relative;

  //         span {
  //             margin-left: 51px;
  //             color: var(--adm-color-text);
  //         }
  //     }
  // }

  // .footer {
  //     display: flex;
  //     justify-content: space-between;
  //     align-items: center;
  //     padding: 0 28px;
  //     height: 120px;
  //     border-top: 1px solid #E6E6E6;
  //     font-size: 40px;

  //     &>div {
  //         color: var(--adm-color-primary);
  //     }
  // }
}

// .navList {
//     display: flex;
//     flex-wrap: wrap;
//     justify-content: space-between;
//     padding: 0 71px;
// }

// .navItem {
//     background: url(../../../assets/navigation_backbox.png);
//     background-size: 100% 100%;
//     width: 420px;
//     height: 260px;
//     background: rgba(255, 255, 255, 0.35);
//     border: 1px solid rgba(255, 255, 255, 0.8);
//     box-shadow: 0px 4px 19px 1px rgba(230, 230, 230, 0.35), 0px 3px 8px 0px rgba(238, 245, 254, 0.35), 0px 3px 8px 0px rgba(238, 245, 254, 0.35);
//     border-radius: 40px;
//     margin-bottom: 39px;
//     position: relative;

//     .name {
//         padding: 48px;
//         font-size: 48px;
//     }

//     .img {
//         position: absolute;
//         top: 89px;
//         right: 25px;
//         width: 160px;
//         height: 152px
//     }

//     .mask {
//         position: absolute;
//         width: 100%;
//         height: 100%;
//         top: 50%;
//         left: 50%;
//         transform: translate(-50%, -50%);
//         background: rgba(0, 0, 0, 0.4);
//         border: 1px solid rgba(0, 0, 0, 0.4);
//         box-shadow: 0px 4px 19px 1px rgba(0, 0, 0, 0.4), 0px 3px 8px 0px rgba(0, 0, 0, 0.4), 0px 3px 8px 0px rgba(0, 0, 0, 0.4);
//         border-radius: 40px;
//     }
// }