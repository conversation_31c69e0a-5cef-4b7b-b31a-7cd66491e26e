import { useEffect, useRef, useState } from 'react';
import { Outlet, history } from 'umi';
import { Card, Dialog, List, SwipeAction, Toast } from 'antd-mobile';
import styles from './menu.page.less';
import { MenuIconMap, TaskStatusColorMap, WhiteList } from '../constant';
import { receiveBlob, receiveBase64 } from '../../../utils/native-fun';
import { RightOutline } from 'antd-mobile-icons';
import moment from 'moment';
import TodoItem, { TodoItemDataType } from '../component/todo-item';
import { getModules, getMyTodoList } from '@/services/nav.service';
import { sortArrByTarget } from '@/utils/data-processing-tools';
import ScrollList from '@/component/scroll-list/scroll-list.component';
type Menu = {
  key: string;
  name: string;
  hide: boolean;
  url: string;
  path?: string;
  code?: string; // 模块编码，跟PC模块对应，为了保障权限一致
};

const Menu: React.FC = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [todoTotal, setTodoTotal] = useState('0');
  const [todoItemData, setTodoItemData] = useState<TodoItemDataType[]>([]);

  const [commonData, setCommonData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [topHeight, setTopHeight] = useState(0);
  const onTodoNumClick = () => {
    history.push('/nav/workbench');
  };

  //接口-获取用户权限下功能模块
  const getModulesData = () => {
    getModules().then((res) => {
      if (res.data) {
        const commonList = sortArrByTarget(res.data?.commonList, 'sequence', 'up');
        commonList?.map((item, index) => {
          return {
            ...item,
            sequence: index,
          };
        });
        setCommonData(commonList);
      }
    });
  };

  // 接口-待办分页查询
  const getMyTodoListData = async () => {
    try {
      const res = await getMyTodoList({ pageNum: currentPage, pageSize: 10, orderColumn: 'create_time', orderCondition: 'desc' })
      setIsLoading(false)
      if (res.code === '1' && Number(res.data?.total) > todoItemData?.length) {
        setTodoTotal(res.data.total);
        const list = todoItemData.concat(res.data.list); //?.filter((item: any) => !WhiteList.includes(item.businessTypeName))
        setTodoItemData(list);
        if (res.data.pages === currentPage || Number(res.data.total) === list.length) {
          setHasMore(false);
        }
        setCurrentPage(currentPage + 1);
      } else {
        setIsLoading(false);
        return Promise.reject();
      }
    } catch (error) {
      setIsLoading(false);
      return Promise.reject();
    }
  };

  // 模板-待办事项盒子
  const itemBox = (item: TodoItemDataType, index: number) => {
    return (
      <Card bodyClassName={styles.todoItem} key={String(index)}>
        <TodoItem data={item} />
      </Card>
    );
  };

  useEffect(() => {
    getModulesData();

    setIsLoading(true);
    getMyTodoListData();
    // sendFlutterMessage(true);\
    // 注册window事件
    window.receiveBlob = receiveBlob;
    window.receiveBase64 = receiveBase64;
    setTopHeight(window.top?.localStorage?.topHeight);
  }, []);

  return (
    <div className={styles.box} style={{ paddingTop: `${topHeight}px` }}>
      <div className={styles.fixedBox}>
        <div className={styles.navTitle}>黄河羊曲水电站</div>
        <Card>
          <div className={styles.icons}>
            {commonData?.length > 0 &&
              commonData.map((item) => {
                return (
                  <div
                    className={styles.iconBox}
                    onClick={() => {
                      history.push(item.routingUrl);
                    }}
                    key={item.id}
                  >
                    <img src={MenuIconMap[item.iconClass]} className={styles.iconImg} />
                    <div className={styles.iconName}>{item.name}</div>
                  </div>
                );
              })}
            <div className={styles.iconBox} onClick={() => history.push('/nav/function-manage')}>
              <img src={MenuIconMap['gd']} className={styles.iconImg} />
              <div className={styles.iconName}>更多</div>
            </div>
          </div>
        </Card>
        <Card bodyClassName={styles.todoCard}>
          <div className={styles.todoName}>待办事项</div>
          <div className={styles.todoDesc} onClick={() => onTodoNumClick()}>
            共<div className={styles.todoNum}>{todoTotal}</div>条<RightOutline color="rgb(204,204,204)" />
          </div>
        </Card>
      </div>
      <div className={styles.scrollListBox}>
        <ScrollList dataSource={todoItemData} itemComponent={itemBox} loading={isLoading} hasMore={hasMore} loadMore={getMyTodoListData} />
      </div>
    </div>
  );
};

export default Menu;
