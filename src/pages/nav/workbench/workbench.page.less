.page {
  width: 100%;
  height: 100%;
  background: url(../../../assets/navigation_backimg.png) top center no-repeat;
  background-color: #e0eafd;
  background-size: 100% auto;
  display: flex;
  flex-direction: column;

  :global {
    .adm-card {
      margin: 0 40px 40px 40px;
    }
  }

  :global {
    .myDropdown {
      &.adm-dropdown {
        background: transparent;
        position: absolute;
        right: 0;

        &.adm-dropdown-open {
          .adm-dropdown-nav {
            border: 0;
          }
        }
      }
    }
  }

  .fixedBox {


    .head {
      width: 100%;
      height: 140px;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 20px 0;
      position: relative;

      .filterIcon {
        width: 60px;
      }
    }


    .cardBody1 {
      display: flex;
      flex-wrap: wrap;
      max-height: 540px;
      overflow-y: scroll;

      .card1Box {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: calc((100% - 150px) / 3);
        padding: 20px 10px;
        border: 2px solid white;
        border-radius: 20px;

        .boxTitle {
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 40px;
          color: #333333;
          margin-bottom: 10px;
          text-align: center;
          // padding: 0 40px;
          word-break: break-all;
        }

        .boxValue {
          display: flex;
          align-items: baseline;
          justify-content: center;

          .valueNum {
            font-size: 42px;
            color: #1154ED;
            font-family: PangMenZhengDao;
            font-weight: 400;
            margin-right: 20px;
          }

          .valueUnit {
            font-family: PingFang-SC-Regular;
            font-size: 36px;
            color: #999999;
          }
        }
      }
    }
  }

  .scrollListBox {
    overflow-y: scroll;
    flex: 1 1;
  }
}