import { useEffect, useRef, useState } from 'react'
import { Outlet, history, useParams, useSearchParams } from "umi";
import styles from "./workbench.page.less";
import { staticPath } from "@/utils/constant";
import FilterIcon0 from '../../../assets/nav/filterIcon0.png'
import FilterIcon1 from '../../../assets/nav/filterIcon1.png'
import RadioButton from '@/component/radio-button/radio-button';
import { Button, Card, Dropdown, Popup } from 'antd-mobile';
import TodoItem, { TodoItemDataType } from '../component/todo-item';
import TodoFilter from './component/todo-filter';
import { getMyDoneCount, getMyDoneList, getMyTodoCount, getMyTodoList } from '@/services/nav.service';
import ScrollList from '@/component/scroll-list/scroll-list.component';
import { WhiteList } from '../constant';

const Workbench: React.FC = () => {
  const dropRef = useRef(null)
  const [visible, setVisible] = useState<boolean>(false)
  const [radioValue, setRadioValue] = useState('待办')//单选框值
  const [numStatistic, setNumStatistic] = useState<{ business_type_name: string; count: string | number; }[]>([])
  const [todoItemData, setTodoItemData] = useState<TodoItemDataType[]>([])
  const [currentPage, setCurrentPage] = useState(1)
  const [hasMore, setHasMore] = useState(true)
  const [getParams, setParams] = useSearchParams();
  const [filtParams, setFiltParams] = useState({})
  const [topHeight, setTopHeight] = useState(0);
  const [choosenBox, setChoosenBox] = useState(null)

  const onRadioChange = (value: any) => {
    if (value !== radioValue) {
      setRadioValue(value)
      setCurrentPage(1)
      setTodoItemData([])
      // setNumStatistic([])
      setHasMore(true)
      setParams({ type: value })
      setChoosenBox(null)
      setFiltParams({ ...filtParams, businessTypeName: '', taskName: '' })
    }

  }

  const onFilter = (params) => {
    setVisible(!visible)
    dropRef.current.close()
    setFiltParams(params)
    setCurrentPage(1)
    setTodoItemData([])
    // setNumStatistic([])
    setHasMore(true)
    setChoosenBox(null)
  }

  const onBoxClick = (e, item) => {
    setTodoItemData([])
    if (choosenBox === item.business_type_name) {
      setChoosenBox(null)
      setFiltParams({ ...filtParams, businessTypeName: '' })
      setCurrentPage(1)
      setHasMore(true)
    } else {
      setChoosenBox(item.business_type_name)
      setFiltParams({ ...filtParams, businessTypeName: item.business_type_name })
      setCurrentPage(1)
      setHasMore(true)
    }
  }

  // 接口-待办分页查询
  const getMyTodoListData = async () => {
    try {
      const res = await getMyTodoList({ pageNum: currentPage, pageSize: 18, ...filtParams, orderColumn: 'create_time', orderCondition: 'desc' })
      if (res.code === '1' && Number(res.data?.total) > todoItemData?.length) {
        const list = todoItemData.concat(res.data.list)//?.filter((item: any) => !WhiteList.includes(item.businessTypeName));
        setTodoItemData(list)
        if (res.data.pages === currentPage || Number(res.data.total) === list.length) {
          setHasMore(false)
        }
        setCurrentPage(currentPage + 1)
      } else {
        return Promise.reject()
      }
    } catch (error) {
      return Promise.reject()
    }
  }

  // 接口-已办分页查询
  const getMyDoneListData = async () => {
    try {
      const res = await getMyDoneList({ pageNum: currentPage, pageSize: 18, ...filtParams, orderBy: 'start_time' })
      if (res.code === '1' && Number(res.data?.total) > todoItemData?.length) {
        const list = todoItemData.concat(res.data.list)//?.filter((item: any) => !WhiteList.includes(item.businessTypeName));
        setTodoItemData(list)
        if (res.data.pages === currentPage) {
          setHasMore(false)
        }
        setCurrentPage(currentPage + 1)
      } else {
        return Promise.reject()
      }
    } catch (error) {
      return Promise.reject()
    }
  }

  // 接口-待办统计
  const getMyTodoCountData = () => {
    getMyTodoCount({ groupColum: 'business_Type_Name' }).then((res) => {
      const data = res.data//?.filter(item => !WhiteList.includes(item.business_type_name))
      setNumStatistic(data)
    })
  }

  // 接口-已办统计
  const getMyDoneCountData = () => {
    getMyDoneCount({ groupColum: 'business_Type_Name' }).then((res) => {
      const data = res.data//?.filter(item => !WhiteList.includes(item.business_type_name))
      setNumStatistic(data)
    })
  }

  // 模板-事项盒子
  const itemBox = (item, index) => {
    return <Card bodyClassName={styles.todoItem} key={item.businessKey}>
      <TodoItem data={item} footText={radioValue === '待办' ? '去处理' : '去查看'} />
    </Card>
  }

  useEffect(() => {
    if (radioValue === '待办') {
      getMyTodoCountData()
    } else {
      getMyDoneCountData()
    }
  }, [radioValue]);

  useEffect(() => {
    if (radioValue === '待办') {
      getMyTodoListData()
    } else {
      getMyDoneListData()
    }
  }, [radioValue, filtParams]);

  useEffect(() => {
    setTopHeight(window.top.localStorage?.topHeight ?? 0);
  }, []);

  return (
    <div className={styles.page} style={{ paddingTop: `${topHeight}px` }}>
      <div className={styles.fixedBox}>
        <div className={styles.head}>
          <RadioButton data={['待办', '已办']} value={radioValue} onChange={onRadioChange} />
          <Dropdown className='myDropdown' onChange={() => setVisible(!visible)} ref={dropRef}>
            <Dropdown.Item key='sorter' arrow={''} title={<img className={styles.filterIcon} src={visible ? FilterIcon1 : FilterIcon0} />}>
              <TodoFilter onFilter={onFilter} />
            </Dropdown.Item>
          </Dropdown>
        </div>

        <Card bodyClassName={styles.cardBody1}>
          {
            numStatistic?.map((item, index) => {
              return <div className={styles.card1Box} key={index} style={{ borderColor: `${choosenBox === item.business_type_name ? '#97b8ff' : '#fff'}` }} onClick={(e) => onBoxClick(e, item)}>
                <div className={styles.boxTitle}>{item.business_type_name}</div>
                <div className={styles.boxValue}>
                  <div className={styles.valueNum}>{item.count}</div>
                  <div className={styles.valueUnit}>个</div>
                </div>
              </div>
            })
          }
        </Card>
      </div>

      <div className={styles.scrollListBox}>
        <ScrollList
          dataSource={todoItemData}
          itemComponent={itemBox}
          hasMore={hasMore}
          loadMore={radioValue === '待办' ? getMyTodoListData : getMyDoneListData}
        />
      </div>
    </div>
  )
};
export default Workbench;
