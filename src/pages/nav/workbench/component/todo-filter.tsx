import { But<PERSON>, <PERSON><PERSON><PERSON>, Toast } from 'antd-mobile';
import styles from './todo-filter.less'
import { useEffect, useState } from 'react';
import { FilterTypeMap } from '../../constant';
import DateRange from '@/component/date-range/date-range';
import { getAllMyDoneList, getAllMyTodoList } from '@/services/nav.service';
import { useSearchParams } from 'umi';
import classnames from 'classnames';

interface Props {
  onFilter: (params: any) => void//返回过滤条件
}
const TodoFilter: React.FC<Props> = ({ onFilter }) => {
  const [type, setType] = useState<'type' | 'status' | 'time'>('time')//当前筛选种类
  const [typeValue, setTypeValue] = useState<(string | number)[]>([])
  const [statusValue, setStatusValue] = useState<(string | number)[]>([])
  const [timeValue, setTimeValue] = useState<{ startTime: string, endTime: string }>({ startTime: '', endTime: '' })
  const [resetFn, setResetFn] = useState<() => void>(null);//重置时间的方法
  const [getParams, setParams] = useSearchParams();

  const [option1, setOption1] = useState([])
  const [option2, setOption2] = useState([])
  // 筛选种类切换
  const onLeftClick = (e) => {
    if (e.target.parentNode.className.split('_')[0] === 'left') {
      e.target.parentNode.childNodes?.forEach((node) => {
        node.className = styles.normal
      })
      e.target.className = styles.active
      setType(FilterTypeMap[e.target.innerText])
    }
  }

  // 类型选择
  const onTypeChange = (value) => {
    setTypeValue(value)
  }

  //状态选择
  const onStatusChange = (value) => {
    setStatusValue(value)
  }
  // 时间
  const onRangeChange = (time) => {
    setTimeValue(time)
  }
  // 获取重置时间的方法
  const onResetTime = (restTimeFn) => {
    setResetFn(restTimeFn)
  }
  // 重置
  const onResetClick = (e) => {
    onFilter({})
    setTypeValue([])
    setStatusValue([])
    resetFn()
  }
  // 确认
  const onConfirmClick = (e) => {
    if (timeValue.startTime || timeValue.endTime) {
      if (timeValue.startTime && timeValue.endTime) {
        onFilter({
          businessTypeName: typeValue?.[0] || '',
          taskName: getParams.get('type') === '已办' ? '' : (statusValue?.[0] || ''),
          startTime: timeValue.startTime || '',
          endTime: timeValue.endTime || '',
        })
      } else {
        Toast.show({
          content: '请选择完整的时间范围',
        })
      }
    } else {
      onFilter({
        businessTypeName: typeValue?.[0] || '',
        taskName: getParams.get('type') === '已办' ? '' : (statusValue?.[0] || ''),
        startTime: timeValue.startTime || '',
        endTime: timeValue.endTime || '',
      })
    }

  }

  // 接口-获取所有的待办事项
  const getAllMyTodoListData = () => {
    getAllMyTodoList().then(res => {
      const businessTypeNames = []
      const taskNames = []
      res.data?.forEach((item) => {
        businessTypeNames.push(item.businessTypeName)
        taskNames.push(item.taskName)
      })
      setOption1([...new Set(businessTypeNames)])
      setOption2([...new Set(taskNames)])
    })
  }
  // 接口-获取所有的已办事项
  const getAllMyDoneListData = () => {
    getAllMyDoneList().then(res => {
      const businessTypeNames = []
      res.data?.forEach((item) => {
        businessTypeNames.push(item.businessTypeName)
      })
      setOption1([...new Set(businessTypeNames)])
      setOption2([])
    })
  }

  useEffect(() => {
    setType('time')
    setStatusValue([''])
    if (getParams.get('type') === '已办') {
      getAllMyDoneListData()
    } else {
      getAllMyTodoListData()
    }
  }, [getParams.get('type')])


  return <div className={styles.todoFilter}>
    <div className={styles.mainContent}>
      <div className={styles.left} onClick={onLeftClick}>
        {/* <div className={styles.active}>类型</div> */}
        <div className={styles.active}>时间</div>
        {option2?.length > 0 && <div className={styles.normal}>状态</div>}

      </div>
      <div className={styles.right}>
        {/* {type === 'type' && option1?.length > 0 && <CheckList value={typeValue} onChange={onTypeChange}>
          {option1?.map((item, index) => {
            return <CheckList.Item value={item} key={index}>{item}</CheckList.Item>
          })}
        </CheckList>} */}
        {type === 'status' && option2?.length > 0 && <CheckList value={statusValue} onChange={onStatusChange}>
          {option2?.map((item, index) => {
            return <CheckList.Item value={item} key={index}>{item}</CheckList.Item>
          })}
        </CheckList>}
        {
          type === 'time' && <div className={styles.date}>
            <div className={styles.dateHead}>
              <div className={styles.title}>时间</div>
              <div className={styles.reset} onClick={() => resetFn()}>重置</div>
            </div>
            <DateRange onRangeChange={onRangeChange} onReset={onResetTime} />
          </div>
        }
      </div>
    </div>
    <div className={styles.footer}>
      <Button color='primary' fill='outline' className='myBtn' onClick={onResetClick}>
        重置
      </Button>
      <Button color='primary' fill='solid' className='myBtn' onClick={onConfirmClick}>
        确定
      </Button>

    </div>
  </div>
}

export default TodoFilter