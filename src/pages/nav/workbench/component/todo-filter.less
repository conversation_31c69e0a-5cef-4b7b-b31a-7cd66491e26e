.todoFilter {
  font-family: <PERSON>Fang SC;
  font-weight: 500;
  font-size: 40px;
  color: #333333;

  .mainContent {
    width: 100vw;
    display: flex;

    .left {
      width: 350px;
      background: #F5F5F5;
      min-height: 30vh;

      .normal {
        display: flex;
        align-items: center;
        width: 310px;
        height: 130px;
        padding-left: 60px;
      }

      .active {
        display: flex;
        align-items: center;
        width: 310px;
        height: 130px;
        padding-left: 60px;
        background: #fff;
      }
    }

    .right {
      width: calc(100% - 350px);
      max-height: 70vh;
      overflow-y: scroll;

      .dateHead {
        height: 60px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 60px 40px;

        .title {
          font-size: 45px;
          font-weight: bold;

        }

        .reset {
          font-size: 40px;
          color: #1154ED;
        }
      }
    }

  }

  .footer {
    display: flex;
    justify-content: center;
    padding: 20px 0;

    :global {
      .myBtn {
        &.adm-button {
          width: calc(50% - 40px);
        }
      }
    }
  }
}