import TabBarComponent from "@/component/tabbar/tab-bar.component";
import BoxLayout from "@/layouts/box-layout/box-layout"
import { Outlet, useLocation, history, useSearchParams, useParams } from 'umi';

import styles from './nav.page.less';
import TabHome from '../../assets/nav/tab_homepage.png'
import ActiveTabHome from '../../assets/nav/tab_homepage1.png'
import TabTask from '../../assets/nav/tab_task.png'
import ActiveTabTask from '../../assets/nav/tab_task1.png'
import TabMy from '../../assets/nav/tab_my.png'
import ActiveTabMy from '../../assets/nav/tab_my1.png'
export interface pathOptions {
  listPath: string;
  addPath?: string;
  defImg?: string;
  activeImg?: string;
}
const showSearchPaths = [''];
const showHeaderPaths = ['/nav/function-manage', '/nav/portal/twotickets-portal'];
const searchPage = [];
const showFooterPaths: pathOptions[] = [
  { listPath: '/nav/menu', addPath: '', defImg: TabHome, activeImg: ActiveTabHome },
  { listPath: '/nav/workbench', addPath: '', defImg: TabTask, activeImg: ActiveTabTask },
  { listPath: '/nav/user-center', addPath: '', defImg: TabMy, activeImg: ActiveTabMy }
];
const listPath: string[] = showFooterPaths.map(item => item.listPath)
const headerNames = [
  { listPath: '/nav/function-manage', name: '功能管理' },
  { listPath: '/nav/portal/twotickets-portal', name: '两票管理' },
]
const Nav: React.FC = () => {
  const location = useLocation();
  const [getParams, setParams] = useSearchParams();
  const { pathname } = location;

  const showSearch = showSearchPaths.includes(pathname);
  const showHeader = showHeaderPaths.includes(pathname);
  const showFooter = listPath.includes(pathname);
  const showSearchPage = searchPage.includes(pathname);
  const curName = headerNames.find(item => item.listPath === pathname)?.name || ''
  return <BoxLayout
    className={showHeader ? 'functionBoxLayout' : ''}
    header={showHeader ? {
      hidden: false,
      backArrow: true,
      currentProjectName: curName,
      defineTitle: curName,
      onBack: () => {
        if (pathname?.split('/')?.length > 3) {
          history.back()
          return
        }
        history.push('/nav/menu')
      },
      // right: 
    } : { hidden: true }}
    footer={showSearchPage ? false :
      showFooter ? <TabBarComponent options={showFooterPaths} tabBar="nav" /> : false}
  >
    <Outlet />
  </BoxLayout>
}
export default Nav   