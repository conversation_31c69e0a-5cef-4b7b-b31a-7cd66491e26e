@textColor: #333;

.pageWrap {
  padding: 40px 40px 0 40px;
  width: calc(100% - 80px);
  height: calc(100% - 40px);
  background: url(../../../../assets/navigation_backimg.png) top center no-repeat;
  background-color: #e0eafd;
  background-size: 100% auto;
  overflow: hidden;

  .headTitle {
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: PingFang SC;
    font-weight: bold;
    font-size: 52px;
    color: #333333;
    padding: 20px 0 60px 0;
    position: relative; 

    .popoverMenu {
      position: absolute;
      right: 0;
      top: 20px;
      transform: translateY(-50%);
    }
  }

  :global {
    .adm-card {
      margin: 0 40px 40px 40px;
    }
  }

  :global {
    .centerform {
      .adm-list-body {
        background-color: transparent !important;
        border-radius: 25px;
      }

      .adm-form-item-label {
        color: @textColor;
      }

      .adm-list-item {
        padding-left: 69px;
        border-radius: 25px;
      }

      .adm-list-item-content {
        align-items: center;
        border-top: none;
      }

      .adm-form-item-child-inner {
        min-height: 67px;
        display: flex;
        font-size: 46px;
      }

      .adm-list-item-content-main {
        display: flex;
        align-items: center;
        justify-content: flex-end;
      }

      .adm-list-item-content-prefix {
        width: 50%;
      }

      .adm-form-item-label {
        font-size: 46px;
      }



      .adm-divider {
        margin: 0 30px;
      }
    }
  }

  :global {
    .centerbtn {
      &.adm-button {
        margin-top: 40px;
        font-size: 48px;
        color: @textColor;
        border-radius: 25px;

      }
    }
  }

  .contentBox {
    background-color: white;
    border-radius: 25px;

  }

  .centerBox {
    margin: 20px 0;
    background-color: white;
    border-radius: 25px;
  }
}