import { useEffect, useState } from "react";
import { But<PERSON>, Form, Toast, Divider, Popover } from "antd-mobile";
import { history } from "umi";
import { ScanningOutline, AddSquareOutline } from 'antd-mobile-icons'

import { Action } from 'antd-mobile/es/components/popover'
import InforJump from "../infor-jump.component";
import SexPopup from "../sex-popup.component";
import AvatarPopup from "../avatar-popup.component";
import { nativeCallback } from '@/utils/native-fun';

import { getUserDetail, putUserInformation } from "@/services/user.service";
import { accountLoginout } from '@/services/user.service';

import styles from "./user-information.page.less";

interface UserInfo {
  email: string; // 邮箱
  id: string; // 用户id
  memo: 0 | 1; // 性别
  name: string; // 姓名
  attachId: number; // 头像图片的id
  phone: string; // 电话
  username: string; // 账号
}

const sexMapping = {
  0: "女",
  1: "男",
};

const actions: Action[] = [
  { key: 'scan', icon: <ScanningOutline />, text: '扫一扫' },
]

const UserInformation = () => {
  const [userInfo, setUserInfo] = useState<Partial<UserInfo>>({});
  const [topHeight, setTopHeight] = useState(0);

  // 设置用户详情
  const getUserInfomation = async (id: string) => {
    const resData = await getUserDetail(JSON.parse(id));
    if (resData.code === "1") {
      setUserInfo(resData.data);
      localStorage.setItem("userinfo", JSON.stringify(resData.data));
    }
  };

  useEffect(() => {
    // 尝试从localStorage获取用户信息
    const rawDefaultUserInfo = localStorage.getItem("userinfo");
    const rawUserInfoId = window.top.localStorage?.userinfoId;

    // 解析用户信息ID，如果不存在则使用默认用户信息
    const userInfoId = rawUserInfoId ? JSON.parse(rawUserInfoId) : null;
    const defaultUserInfo = rawDefaultUserInfo ? JSON.parse(rawDefaultUserInfo) : null;

    // 设置顶部高度
    const topHeight = window.top.localStorage?.topHeight;
    if (topHeight) {
      setTopHeight(topHeight);
    }

    if (userInfoId) {
      // 如果有userInfoId，则使用它获取用户信息
      getUserInfomation(userInfoId);
    } else if (defaultUserInfo) {
      // 如果没有userInfoId但有默认用户信息，则使用默认用户信息
      getUserInfomation(defaultUserInfo.id);
    } else {
      // 如果两者都没有，则重定向到首页
      history.push("/");
    }
  }, []);

  // 请求修改用户信息借口
  const putUserDetail = (data: any) => {
    putUserInformation(userInfo.id, data).then((resData) => {
      if (resData.code === "1") {
        getUserInfomation(userInfo.id);
      } else {
        Toast.show({
          icon: "fail",
          content: resData.message,
        });
      }
    });
  };

  // 性别点击回调
  const sexHandleCallback = (action: { text: string; key: number }) => {
    const { key } = action;
    putUserDetail({ ...userInfo, memo: key });
  };

  // 退出登录事件
  const handleLogingout = () => {
    localStorage.removeItem("userinfo");
    localStorage.removeItem('UserAcountAndPassword'); // 清除自动登录
    if (window.top.localStorage?.userinfoId) {
      nativeCallback('logout');
    } else {
      history.push("/");
    }
    accountLoginout();
  };

  const handleAction = (node: Action) => {
    console.log('node', node)
    if (node.key === 'scan') {
      nativeCallback('QRCodeScan');
    }
  }

  return (
    <>
      <div className={styles.pageWrap}>
        <div className={styles.headTitle} style={{ marginTop: `${topHeight}px` }}>
          <span>个人中心</span>
          <span className={styles['popoverMenu']} style={{ marginTop: `10px` }}>
            <Popover.Menu
              actions={actions}
              placement='bottom-start'
              onAction={node => handleAction(node)}
              trigger='click'
            >
              <span><AddSquareOutline fontSize={24} /></span>
            </Popover.Menu>
          </span>
        </div>
        <Form layout="horizontal" footer={false} className="centerform">
          <div className={styles.contentBox}>
            <Form.Item name="icon" label="修改头像">
              <AvatarPopup userInfo={userInfo} changeCallback={putUserDetail} />
            </Form.Item>
            <Divider />
            <Form.Item
              name="account"
              label="账号"
              rules={[{ required: true, message: "账号不能为空！" }]}
            >
              <InforJump
                showText={userInfo.username}
                pushUrl={`/nav/user-center/account-change/${userInfo.id}`}
              />
            </Form.Item>
            <Divider />
            <Form.Item
              name="name"
              label="姓名"
              rules={[{ required: true, message: "姓名不能为空！" }]}
            >
              <InforJump
                showText={userInfo.name}
                pushUrl={`/nav/user-center/name-change/${userInfo.id}`}
              />
            </Form.Item>
            <Divider />
            <Form.Item
              name="sex"
              label="性别"
              rules={[{ required: true, message: "性别不能为空！" }]}
            >
              <SexPopup
                showText={sexMapping[userInfo.memo]}
                handleCallback={sexHandleCallback}
              />
            </Form.Item>
          </div>

          <div className={styles.centerBox}>
            <Form.Item
              name="phone"
              label="电话"
              rules={[{ required: true, message: "电话不能为空！" }]}
            >
              <InforJump
                showText={userInfo.phone}
                pushUrl={`/nav/user-center/phone-change/${userInfo.id}`}
              />
            </Form.Item>
            <Divider />
            <Form.Item name="emial" label="邮箱">
              <InforJump
                showText={userInfo.email}
                pushUrl={`/nav/user-center/email-change/${userInfo.id}`}
              />
            </Form.Item>
          </div>
          <Form.Item name="password" label="修改密码">
            <InforJump showText="" pushUrl="/nav/user-center/password-change/1" />
          </Form.Item>
        </Form>
        <Button block size="large" onClick={handleLogingout} className="centerbtn">
          退出登录
        </Button>
      </div>
    </>
  );
};

export default UserInformation;
