import React, { useState, useRef, Fragment, useEffect } from "react";
import { ActionSheet, Avatar, Toast } from "antd-mobile";
import type { Action } from "antd-mobile/es/components/action-sheet";
import { attachmentUpload } from "@/services/attachment";
import { urlPrefix } from "@/utils/request";
import { Subject } from 'rxjs';

import { nativeCallback, picFileSubject, usePicFile } from '../../../utils/native-fun';
import { useModel } from 'umi';

import InforJump from "./infor-jump.component";

import styles from "./sex-popup.component.less";

interface Props {
  userInfo: any; // 用户列表
  changeCallback: (body: any) => void; // 头像改变回调事件
}

const actions = [
  { text: "本地上传", key: "photo" },
  { text: "拍照上传", key: "camera" },
];

const AvatarPopup: React.FC<Props> = (props: any) => {
  const { userInfo, changeCallback } = props;
  const { picFile } = usePicFile();

  const [visible, setVisible] = useState<boolean>(false); // 动作面板展示控制变量
  const [isUpload, setIsUpload] = useState<boolean>(false); // 是否上传图片

  const inputRef = useRef<any>(null);

  useEffect(() => {
    if (picFile && isUpload) {
      uploadFile1(picFile);
    }
  }, [picFile, isUpload])

  // 性别点击事件
  const handleClick = () => {
    setVisible(true);
  };

  // 动作面板关闭隐藏事件
  const onClose = () => {
    setVisible(false);
  };

  // 点击选项触发事件
  const handleAction = async (action: Action) => {
    const { key } = action;
    if (key === "photo") {
      inputRef.current.click();
    } else if (key === "camera") {
      // nativeCallback();
      const _isCallback = await nativeCallback('camera');
      setIsUpload(_isCallback);
      if (picFile) {
        uploadFile1(picFile);
      }
    }
  };

  const uploadFile1 = async (file: any) => {
    const resp = await attachmentUpload({
      files: [file],
      id: userInfo?.attachId ?? undefined,
    });
    if (resp.code === "1") {
      setIsUpload(false);
      changeCallback({
        ...userInfo,
        attachId: resp?.data?.id,
        attachmentContentList: resp.data.attachmentContentList,
      });
    } else {
      setIsUpload(false);
      Toast.show({
        icon: "fail",
        content: resp.message,
      });
    }
  }

  // 上传图片操作
  const uploadFile = async (event: any) => {
    const file = event.target.files[0];
    const resp = await attachmentUpload({
      files: [file],
      id: userInfo?.attachId ?? undefined,
    });
    if (resp.code === "1") {
      changeCallback({
        ...userInfo,
        attachId: resp?.data?.id,
        attachmentContentList: resp.data.attachmentContentList,
      });
    } else {
      Toast.show({
        icon: "fail",
        content: resp.message,
      });
    }
  };

  return (
    <Fragment>
      <div className={styles.popup}>
        <InforJump
          showText={
            <Avatar
              src={`${urlPrefix}/Attachment/downloadAttachment/${userInfo?.attachmentContentList
                ? userInfo.attachmentContentList[
                  userInfo.attachmentContentList.length - 1
                ].id
                : undefined
                }`}
              style={{ "--border-radius": "50%" }}
            />
          }
        // clickFunction={handleClick}
        />
        <ActionSheet
          cancelText="取消"
          visible={visible}
          actions={actions}
          onClose={onClose}
          onAction={handleAction}
          closeOnAction
          getContainer={null}
        />
      </div>
      <input
        className={styles.inputImg}
        ref={inputRef}
        id="avatarInput"
        type="file"
        accept="image/*"
        onChange={uploadFile}
        capture
      />
    </Fragment>
  );
};

export default AvatarPopup;
