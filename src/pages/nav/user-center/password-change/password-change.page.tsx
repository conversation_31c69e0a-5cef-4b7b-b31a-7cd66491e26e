import { useState } from "react";
import { history } from "umi";
import { <PERSON>ton, SpinLoading, Toast } from "antd-mobile";
import BoxLayout from "@/layouts/box-layout/box-layout";
import { changePasswprd } from "@/services/user.service";
import FormInput from "../form-input.component";

import styles from "./password-change.page.less";

const PasswordChange = () => {
  const [oldPassword, setOldPassword] = useState<string>(""); // 原密码
  const [newPassword, setNewPassword] = useState<string>(""); // 新密码
  const [confirmPassword, setConfirmPassword] = useState<string>(""); // 确认新密码
  const [loading, setLoading] = useState<boolean>(false); // 是否在等待中
  const [isError, setIsError] = useState<boolean>(false); // 不可以保存状态

  // 校验两次新输入的密码是否一致
  const checkTwicePW = (value1: string, value2: string) => {
    if (value1 === value2) {
      setIsError(false);
    } else {
      setIsError(true);
    }
  };

  // 输入框内容改变事件
  const handleChange = (type: string, value: string) => {
    if (type === "old") {
      setOldPassword(value);
    } else if (type === "new") {
      setNewPassword(value);
      checkTwicePW(value, confirmPassword);
    } else {
      setConfirmPassword(value);
      checkTwicePW(value, newPassword);
    }
  };

  // 修改密码保存事件
  const handleSave = () => {
    setLoading(true);
    changePasswprd({
      newPwd: newPassword,
      oldPwd: oldPassword,
    })
      .then((resData) => {
        if (resData.code === "1") {
          history.back();
        } else {
          Toast.show({
            icon: "fail",
            content: resData.message,
          });
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  return (
    <BoxLayout
      header={{
        defineTitle: <p style={{ textAlign: "center" }}>密码修改</p>,
        backArrow: true,
        right: (
          <span className={styles.saveText}>
            <Button
              color="primary"
              fill="none"
              onClick={handleSave}
              loading={loading}
              disabled={isError}
              loadingIcon={<SpinLoading color="white" />}
            >
              保存
            </Button>
          </span>
        ),
      }}
    >
      <div className={styles.pageWrap}>
        <FormInput
          label="原密码"
          inputType="eye"
          onChange={(value: string) => handleChange("old", value)}
        />
        <div
          className={`${styles.newPassword} ${isError ? styles.danger : ""}`}
        >
          <FormInput
            label="新密码"
            inputType="eye"
            onChange={(value: string) => handleChange("new", value)}
            bottomBorder
          />
          <FormInput
            label="再次输入"
            inputType="eye"
            onChange={(value: string) => handleChange("conf", value)}
          />
        </div>
        {isError && <div className={styles.dangerText}>两次输入密码不一致</div>}
      </div>
    </BoxLayout>
  );
};

export default PasswordChange;
