import { useMemo, useState, useEffect } from 'react'
import BoxLayout from '@/layouts/box-layout/box-layout'
import { useLocation, history, useParams } from 'umi'
import { Toast, Button } from 'antd-mobile'
import FormInput from './form-input.component'

import { putUserInformation, getUserDetail } from '@/services/user.service'

import styles from './user-info.page.less'

const titleMap = {
  '/user-center/account-change': {
    title: '修改账号',
    label: '账号',
    key: 'username',
  },
  '/user-center/name-change': {
    title: '修改姓名',
    label: '姓名',
    key: 'name',
  },
  '/user-center/phone-change': {
    title: '修改电话',
    label: '电话',
    key: 'phone',
    reg: [/^(?:(?:\d{3}-)?\d{8}|^(?:\d{4}-)?\d{7,8})(?:-\d+)?$/, /^(?:(?:\+|00)86)?1\d{10}$/],
    errorText: '请输入正确的电话！',
  },
  '/user-center/email-change': {
    title: '修改邮箱',
    label: '邮箱',
    key: 'email',
    reg: [
      /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
      /^[A-Za-z0-9\u4e00-\u9fa5]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/,
    ],
    errorText: '请输入正确的邮箱！',
  },
}
const urlArray: string[] = [
  '/user-center/account-change',
  '/user-center/name-change',
  '/user-center/phone-change',
  '/user-center/email-change',
]

const AccountChange = () => {
  const [userInfo, setUserInfo] = useState<any>({}) // 用户信息
  const [inputValue, setInputValue] = useState<string>('') // 输入框内容
  const [loading, setLoading] = useState<boolean>(false) // 是否为加载中
  const [isErrorInput, setIsErrorInput] = useState<boolean>(false) // 是否输入内容有误

  // 设置用户详情
  const getUserInfomation = async (id: string) => {
    const resData = await getUserDetail(id)
    if (resData.code === '1') {
      setUserInfo(resData.data)
    }
  }

  const location = useLocation()
  const { pathname } = location

  const params = useParams()

  useEffect(() => {
    getUserInfomation(params.id)
  }, [params])

  // 获取路由对应的文本obj
  const targetObj = useMemo(() => {
    const finalUrl = urlArray.find((item) => pathname.indexOf(item) !== -1)
    const returnObj = titleMap[finalUrl] || { title: '', label: '' }
    return returnObj
  }, [pathname])

  // 输入框内容改变回调函数
  const handleChange = (value: string) => {
    setInputValue(value)
    const { reg } = targetObj
    if (reg) {
      const isFail = reg.every((item) => !item.test(value))
      setIsErrorInput(isFail)
    }
  }

  // 请求修改用户信息借口
  const putUserDetail = (data: any) => {
    putUserInformation(params.id, data)
      .then((resData) => {
        if (resData.code === '1') {
          history.back()
        } else {
          Toast.show({
            icon: 'fail',
            content: '修改失败',
          })
        }
      })
      .finally(() => {
        setLoading(false)
      })
  }

  // 修改保存事件
  const handleSave = () => {
    if (!inputValue) return

    setLoading(true)
    const body: any = { ...userInfo }
    const targetKey = targetObj.key
    body[targetKey] = inputValue
    putUserDetail(body)
  }

  return (
    <BoxLayout
      header={{
        defineTitle: <p style={{ textAlign: 'center' }}>{targetObj.title}</p>,
        backArrow: true,
        right: (
          <span className={styles.saveText}>
            <Button
              color="primary"
              fill="none"
              onClick={handleSave}
              loading={loading}
              disabled={isErrorInput}
            >
              保存
            </Button>
          </span>
        ),
      }}
    >
      <div className={styles.pageWrap}>
        <div className={isErrorInput ? styles.danger : ''}>
          <FormInput label={targetObj.label} onChange={handleChange} />
        </div>
        {isErrorInput && <div className={styles.dangerText}>{targetObj.errorText}</div>}
      </div>
    </BoxLayout>
  )
}

export default AccountChange
