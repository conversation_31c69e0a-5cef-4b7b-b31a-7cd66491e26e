import { useState } from "react";
import { ActionSheet } from "antd-mobile";
import type { Action } from "antd-mobile/es/components/action-sheet";

import InforJump from "./infor-jump.component";

import styles from "./sex-popup.component.less";

interface Props {
  showText: string; // 默认展示名称
  handleCallback?: (action: any) => void; // 选择回调事件
}

const actions = [
  { text: "男", key: 1 },
  { text: "女", key: 0 },
];

const SexPopup: React.FC<Props> = (props) => {
  const { showText, handleCallback } = props;

  const [visible, setVisible] = useState<boolean>(false);

  // 性别点击事件
  const handleClick = () => {
    setVisible(true);
  };

  // 关闭隐藏事件
  const onClose = () => {
    setVisible(false);
  };

  // 点击选项触发事件
  const handleAction = (action: Action) => {
    if (handleCallback) {
      handleCallback(action);
    }
  };

  return (
    <div className={styles.popup}>
      <InforJump showText={showText} clickFunction={handleClick} />
      <ActionSheet
        cancelText="取消"
        visible={visible}
        actions={actions}
        onClose={onClose}
        onAction={handleAction}
        closeOnAction
        getContainer={null}
      />
    </div>
  );
};

export default SexPopup;
