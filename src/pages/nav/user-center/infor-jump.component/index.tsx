import { RightOutline } from "antd-mobile-icons";
import type { ReactElement } from "react";
import { history } from "umi";
import styles from "./style.less";

interface Props {
  showText: string | ReactElement | undefined; // 展示文字
  pushUrl?: string; // 跳转路由
  textAlign?: "right" | "center" | "left"; // 文本对其方式
  clickFunction?: () => void; // 点击事件
}

const InforJump: React.FC<Props> = (props) => {
  const { showText, pushUrl, textAlign = "right", clickFunction } = props;

  // 跳转操作
  const handleClick = () => {
    if (pushUrl) {
      history.push(pushUrl);
    }
  };

  return (
    <div className={styles.showBox} onClick={clickFunction || handleClick}>
      <div style={{ textAlign }} className={styles.showText}>
        {showText}
      </div>
      <RightOutline />
    </div>
  );
};

export default InforJump;
