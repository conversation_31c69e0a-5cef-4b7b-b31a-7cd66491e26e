.wrapBox {
  position: relative;
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  height: 150px;
  padding: 0 29px;
  background-color: #fff;
  border-radius: 20px;
  font-size: 46px;

  .textEllipsis {
    width: 50%;
    overflow: hidden;
    color: #333333;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .inputBox {
    display: flex;
    align-items: center;
    width: 50%;

    :global {
      .adm-input-element {
        font-size: 46px;
      }
    }

    .input {
      flex: auto;
    }
    .eye {
      flex: none;
      margin-left: 8px;
      padding: 4px;
      cursor: pointer;
      svg {
        display: block;
        font-size: var(--adm-font-size-7);
      }
    }
  }
}

.bottomBorder {
  &::after {
    position: absolute;
    bottom: 0;
    width: calc(100% - 58px);
    height: 1px;
    background-color: #e6e6e6;
    content: " ";
  }
}
