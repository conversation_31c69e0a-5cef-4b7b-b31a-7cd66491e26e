import { useState } from "react";
import { Input } from "antd-mobile";
import { EyeInvisibleOutline, EyeOutline } from "antd-mobile-icons";
import styles from "./style.less";

interface Props {
  label: string; // 名称
  onChange?: (value: string) => void; // 输入框改变回调
  rightPlaceholder?: string; // 右侧默认提示文字
  placeholder?: string; // 输入框提示文字
  inputType?: "text" | "password" | "eye"; // 输入框类型
  bottomBorder?: boolean; // 是否有底部边框
}

const InputForm: React.FC<Props> = (props) => {
  const {
    label,
    onChange,
    placeholder = "请输入",
    inputType = "text",
    bottomBorder = false,
  } = props;

  const [visible, setVisible] = useState<boolean>(false); // 是否可见

  // 输入框内容改变回调函数
  const handleChange = (value: string) => {
    if (onChange) {
      onChange(value);
    }
  };

  return (
    <div className={`${styles.wrapBox} ${bottomBorder && styles.bottomBorder}`}>
      <div className={styles.textEllipsis}>{label}</div>
      <div className={styles.inputBox}>
        <Input
          placeholder={placeholder}
          style={{ "--text-align": "right" }}
          clearable
          onChange={handleChange}
          type={
            inputType === "eye" ? (visible ? "text" : "password") : inputType
          }
        />
        {inputType === "eye" && (
          <div className={styles.eye}>
            {!visible ? (
              <EyeInvisibleOutline onClick={() => setVisible(true)} />
            ) : (
              <EyeOutline onClick={() => setVisible(false)} />
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default InputForm;
