import { Card } from 'antd-mobile';
import styles from './twotickets-portal.page.less';
import { history, useSearchParams } from 'umi';
import { useEffect, useState } from 'react';
import { getModules, getTicketStatistical } from '@/services/nav.service';
import { MenuIconMap } from '../../constant';
import BannerImg from '@/assets/nav/twotickets-portal/banner.png';
import { TicketStatistical, UserPrivilegeList } from '@/services/nav.service.typing';

const TwoticketsPortal: React.FC = () => {
  const [getParams] = useSearchParams();
  const [functionData, setFunctionData] = useState<UserPrivilegeList[]>([]);
  const [statisticData, setStatisticData] = useState<TicketStatistical[]>([]);

  // 取用户权限下功能模块
  const getModulesData = () => {
    getModules().then((res) => {
      if (res.data) {
        const userPrivilegeList = res.data?.userPrivilegeList?.find((item) => item.code === getParams.get('code'))?.children || [];
        setFunctionData(userPrivilegeList);
      }
    });
  };

  // 取两票进行中数据统计
  const getStatisticData = () => {
    getTicketStatistical({ ticketType: '0' }).then((res) => {
      if (res.data) {
        setStatisticData(res.data);
      }
    });
  };

  // 挂载
  useEffect(() => {
    getModulesData();
    getStatisticData();
  }, []);

  return (
    <div className={styles.page}>
      {/* 顶部图 */}
      <div className={styles.banner}>
        <img src={BannerImg}></img>
      </div>
      {/* 应用入口 */}
      {functionData?.length > 0 &&
        functionData.map((item) => {
          return (
            <Card key={item.id}>
              <div className={styles.commonHeader}>
                <div className={styles.headerLeft}>{item?.name}</div>
              </div>
              <div className={styles.icons}>
                {item?.children?.length > 0 &&
                  item?.children?.map((it) => {
                    return (
                      <div
                        className={styles.iconBox}
                        key={it.id}
                        onClick={() => {
                          history.push(it.routingUrl);
                        }}
                      >
                        <img src={MenuIconMap[it.iconClass as keyof typeof MenuIconMap]} className={styles.iconImg} />
                        <div className={styles.iconName}>{it.name}</div>
                      </div>
                    );
                  })}
              </div>
            </Card>
          );
        })}
      {/* 统计 */}
      <Card>
        <div className={styles.commonHeader}>
          <div className={styles.headerLeft}>两票进行中数据统计</div>
        </div>
        <div className={styles.icons}>
          {statisticData?.length > 0 &&
            statisticData?.map((it) => {
              return (
                <div className={styles.iconBox} key={it.name}>
                  <div className={styles.statisticNum}>{it.count}</div>
                  <div className={styles.iconName}>{it.name}</div>
                </div>
              );
            })}
        </div>
      </Card>
    </div>
  );
};
export default TwoticketsPortal;
