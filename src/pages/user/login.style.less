.container {
  background-image: url(~@/assets/login_backimg.png);
  width: 100%;
  min-height: 100vh;
  background-size: cover;
  background-repeat: no-repeat;

  .title {
    padding: 269px 0;
    font-family: pangmeng;
    color: #fff;
    font-size: 82px;
    text-align: center;
  }
}

.form {
  :global {
    .adm-list-body {
      background-color: transparent !important;
    }

    .adm-list-body-inner {
      margin-top: 0;
    }

    .adm-list-item {
      display: flex;
      align-items: center;
      width: 801px;
      height: 131px;
      padding: 0;
      margin: 0 auto;
      background: #fff2;
      border-radius: 60px;
    }

    .adm-form-item+.adm-form-item {
      margin-top: 69px;
    }

    .adm-form-item-child-inner {
      display: flex;
      align-items: center;
      gap: 29px;
    }

    .adm-list-default .adm-list-body {
      border: none;
    }

    .adm-list-item-content {
      border-top: none !important;
      padding: 0 40px;
      width: 100%;
      .adm-list-item-content-main {
        padding: 0;
      }
    }

    .adm-input-element {
      color: #fff;
      line-height: normal;
    }

    input:-internal-autofill-selected {
      background-color: #0000 !important;
      background-image: none !important;
    }

    input:-webkit-autofill {
      transition: background-color 5000s ease-in-out 0s;
      -webkit-text-fill-color: #fff; //颜色是设置成你需要的颜色
    }

    .adm-form .adm-form-footer {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .adm-button.adm-button-large {
      border-radius: 60px;
      margin: 160px -12px 0;
      width: 801px;
      height: 131px;
      color: #0C6BED;
      background-color: #fff;
    }
    .adm-checkbox-icon {
      width: 40px;
      height: 40px;
    }
    .adm-checkbox-content {
      font-size: 40px;
    }
  }
  .userNameIcon, .passwordIcon {
    display: inline-block;
    width: 46px;
    height: 46px;
  }
  .userNameIcon {
    background: url('../../assets/login_user.png') center no-repeat;
    background-size: 100% 100%;
  }
  .passwordIcon {
    background: url('../../assets/login_possword.png') center no-repeat;
    background-size: 100% 100%;
  }
}

.password {
  display: flex;
  align-items: center;
  width: 100%;

  .input {
    flex: auto;
  }

  .eye {
    flex: none;
    margin-left: 29px;
    padding: 4px;
    cursor: pointer;
    color: #fff;

    svg {
      display: block;
      font-size: var(--adm-font-size-7);
    }
  }
}

.checked {
  border: none !important;
  background: #0000 !important;
  color: #fff;
  margin-top: 0 !important;
}
