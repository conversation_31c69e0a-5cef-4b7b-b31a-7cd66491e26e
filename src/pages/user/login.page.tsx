import { useState, useEffect } from 'react';
import { history } from 'umi';
import { Form, Input, Button, Toast, Checkbox } from 'antd-mobile';
import { EyeInvisibleOutline, EyeOutline } from 'antd-mobile-icons';
import styles from './login.style.less';
import { accountLogin, getLoginTitle } from '@/services/user.service';
import { rsaEncrypt } from '@/utils/jsencrypt.util';

const PasswordInput = ({ value, onChange }: { value?: string; onChange?: (value: string) => void }) => {
  const [visible, setVisible] = useState(false);
  return (
    <div className={styles.password}>
      <Input
        value={value}
        onChange={onChange}
        className={styles.input}
        placeholder='密 码'
        type={visible ? 'text' : 'password'}
      />
      <div className={styles.eye}>
        {!visible ? (
          <EyeInvisibleOutline onClick={() => setVisible(true)} />
        ) : (
          <EyeOutline onClick={() => setVisible(false)} />
        )}
      </div>
    </div>
  )
}

const defaultInitialValues = {
  j_username: '',
  j_password: '',
  rememberPassword: true,
}

const Login: React.FC = () => {
  const [form] = Form.useForm();
  const [autoLogin, setAutoLogin] = useState(false);
  const [title, setTitle] = useState<string>('')
  const onFinish = () => {
    const values = form.getFieldsValue();
    const payload = {
      j_username: encodeURIComponent(rsaEncrypt(values.j_username)),
      j_password: encodeURIComponent(rsaEncrypt(values.j_password)),
    };
    Toast.show({
      icon: 'loading',
      content: autoLogin ? '自动登录中...' : '登录中…',
      maskClickable: false,
      duration: 0,
    })
    accountLogin(payload).then(res => {
      if (res && res.code === '1') {
        try {
          console.log('res.data', res.data)
          localStorage.setItem('userinfo', JSON.stringify(res.data))
        } catch (error) {
          console.error(error)
        }
        setTimeout(() => {
          Toast.clear()
          Toast.show({
            icon: 'success',
            content: "登录成功",
            maskClickable: false,
            duration: 0,
          })
          localStorage.setItem('UserAcountAndPassword', JSON.stringify(values))
          setTimeout(() => {
            history.replace('/nav')
          }, 1000);
        }, 1500);
      } else if (res.message) {
        Toast.show({
          icon: 'fail',
          content: res.message
        })
      }
    }).finally(() => {
      setTimeout(() => {
        Toast.clear()
      }, 3000);
    })
  }

  useEffect(() => {
    try {
      const AccountValue = JSON.parse(localStorage.getItem('UserAcountAndPassword') || '{}');
      if (AccountValue && AccountValue.rememberPassword && AccountValue.j_password && AccountValue.j_password) {
        setAutoLogin(true);
        form.setFieldsValue(AccountValue);
      } else {
        form.setFieldsValue({ ...AccountValue, rememberPassword: false });
      }
    } catch (error) {
      console.error(error)
    }
  }, [])

  useEffect(() => {
    if (autoLogin) onFinish();
  }, [autoLogin])

  useEffect(() => {
    //   获取标题
    getLoginTitle().then(res => {
      if (res.code === '1') {
        setTitle(res.data.title)
      } else {
        setTitle('羊曲智慧电厂')
      }
    })
  }, [])

  return (
    <div className={styles.container}>
      <div className={styles.title}>{title}</div>
      <div className={styles.form}>
        <Form
          form={form}
          initialValues={defaultInitialValues}
          footer={<Button block color='primary' onClick={onFinish} size='large'>登录</Button>}>
          <Form.Item name='j_username' label=''>
            {/* <span className={styles.userNameIcon} /> */}
            <Input placeholder='用户名' />
          </Form.Item>
          <Form.Item name='j_password' label=''>
            {/* <span className={styles.passwordIcon} /> */}
            <PasswordInput />
          </Form.Item>
          <Form.Item name='rememberPassword' valuePropName="checked" className={styles.checked} label=''>
            <Checkbox block>自动登录</Checkbox>
          </Form.Item>
        </Form>
      </div>
    </div>
  )
}

export default Login;
