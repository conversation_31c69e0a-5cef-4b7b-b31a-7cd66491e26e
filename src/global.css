body {
  touch-action: none;
  overscroll-behavior: contain;
}
.adm-card-header-title {
  font-size: 46px;
}

.adm-popup-body {
  border-radius: 0 0 20px 20px;
  overflow: hidden;
  .adm-tree-select-column {
    font-size: 42px;
    color: var(--adm-color-text);
  }
  .adm-tree-select-item-active {
    font-weight: normal;
  }
}

.adm-step-content {
  padding-bottom: 20px !important;
}

/* ImageViewer 组件图片预览样式问题 */
.adm-image-viewer-slides-inner {
  transform: none !important;
}
.ellipsis {
  white-space: nowrap; /* 防止文字换行 */
  overflow: hidden; /* 隐藏溢出部分 */
  text-overflow: ellipsis; /* 显示省略号 */
}

/* ActionSheet */
.adm-action-sheet-popup > .adm-popup-body {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}
