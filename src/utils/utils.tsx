import { exportFileById, exportWordUrl } from '@/services/twotickets/operation/statistics';
import { Toast } from 'antd-mobile';
import dayjs from 'dayjs';
import FileSaver from 'file-saver';
import moment from 'moment';

const getDateDiff = (str, end) => {
  if (!end || !str) return '-';
  let formatStr = str;
  let formatEnd = end;
  if (typeof formatStr === 'object') {
    formatStr = moment(str).format('YYYY-MM-DD');
  }
  if (typeof formatEnd === 'object') {
    formatEnd = moment(end).format('YYYY-MM-DD');
  }
  return moment(formatEnd).diff(moment(formatStr), 'days');
};

const showhtml = (htmlString) => {
  var html = { __html: htmlString };
  return <div dangerouslySetInnerHTML={html}></div>;
};

// 判断手机 - ios/andriod
function isIOS() {
  const u = navigator.userAgent;
  return !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); //ios终端
}

/**
 * @description: 键盘弹起，输入框被遮挡
 */
function judgeInput() {
  if (isIOS()) {
    window.addEventListener('focusin', function () {
      console.log(1 + document.activeElement.tagName);
      if (document.activeElement.tagName === 'INPUT' || document.activeElement.tagName === 'TEXTAREA') {
        setTimeout(function () {
          document.documentElement.scrollTop = document.body.scrollHeight;
        }, 0);
      }
    });
  } else {
    window.addEventListener('resize', function () {
      console.log(2 + document.activeElement.tagName);
      if (document.activeElement.tagName === 'INPUT' || document.activeElement.tagName === 'TEXTAREA') {
        setTimeout(function () {
          document.activeElement.scrollIntoView();
        }, 0);
      }
    });
  }
}

// 公共函数：过滤数组中非空项，返回最后一个有效项
const getLastNonEmpty = (arr) => {
  if (!Array.isArray(arr)) return null;
  const filtered = arr.filter((item) => item);
  return filtered.length ? filtered[filtered.length - 1] : null;
};

/**
 * 格式化日期
 * @param value - 日期值
 * @param format - 日期格式，默认为 'YYYY/MM/DD HH:mm:ss'
 * @returns 格式化后的日期字符串
 */
const formatDate = (value: dayjs.Dayjs | Date | null, format: string = 'YYYY/MM/DD HH:mm:ss'): string => {
  return value ? dayjs(value).format(format) : '请选择';
};

// 文件下载处理方法
async function handleDownload(fileId: string, fileName: string) {
  try {
    Toast.show({
      icon: 'loading',
      content: '正在下载中…',
      duration: 0,
    });
    const res = await exportFileById({
      id: fileId,
    });
    const pdfBlob = new Blob([res], { type: 'application/pdf' });
    // 将 Blob 转换为 Object URL
    const url = URL.createObjectURL(pdfBlob);
    await FileSaver.saveAs(pdfBlob, fileName); // 使用 FileSaver.js 进行下载
  } catch (error) {
  } finally {
    Toast.clear();
  }
}

const handleExport = async (info: any) => {
  try {
    Toast.show({
      icon: 'loading',
      content: '正在下载中…',
      duration: 0,
    });
    const res = await exportWordUrl(info);
    const pdfBlob = new Blob([res], { type: 'application/pdf' });
    // 将 Blob 转换为 Object URL
    const url = URL.createObjectURL(pdfBlob);
    await FileSaver.saveAs(pdfBlob, '操作票.pdf'); // 使用 FileSaver.js 进行下载
  } catch (error) {
  } finally {
    Toast.clear();
  }
  // 1. 打开新标签页进行预览
  // window.open(url);
  // 手机h5下载
  // try {
  //   const a = document.createElement('a');
  //   a.href = url;
  //   a.download = '操作票.pdf'; // 设置下载文件名
  //   document.body.appendChild(a);
  //   a.click(); // 触发下载
  //   document.body.removeChild(a); // 移除临时链接
  //   URL.revokeObjectURL(url);
  //   alert("成功下载");
  // } catch (error) {
  //   alert(error);
  // }
  // window.location.href = url; // 直接下载
  // if(window.NativeBridge ) {
  //   // IE浏览器
  //   window.NativeBridge.downFile(pdfBlob, '操作票.pdf');
  // }
};

export { getDateDiff, showhtml, judgeInput, getLastNonEmpty, formatDate, handleDownload, handleExport };
