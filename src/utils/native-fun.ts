import { BehaviorSubject } from 'rxjs';
import { useState, useEffect } from 'react';

export const nativeCallback = async (type: string) => {
  if (type === 'camera') {
    if (window?.flutter_inappwebview?.callHandler) {
      await window.flutter_inappwebview.callHandler('cameraFun', { data: 'cameraFun' });
      return true;
    }
  }
  if (type === 'logout') {
    if (window?.flutter_inappwebview?.callHandler) {
      await window.flutter_inappwebview.callHandler('logout', { data: 'logout' });
      return true;
    }
  }
  if (type === 'QRCodeScan') {
    if (window?.flutter_inappwebview?.callHandler) {
      await window.flutter_inappwebview.callHandler('QRCodeScan', { data: 'QRCodeScan' });
      return true;
    }
  }
};
export const picFileSubject = new BehaviorSubject(null);

export const receiveBlob = async (blob: any) => {
  picFileSubject.next(null);
  // 创建一个新的BehaviorSubject来保存picFile状态
  const picFile = await blobToFile(blob);
  picFileSubject.next(picFile);
};

export const receiveBase64 = (base64: any) => {
  console.log('receiveBase64 base64', base64);
  // const nweBlob = base64ToBlob(base64);
  // console.log('nweBlobnweBlobnweBlob', nweBlob);
  console.log('base64 is blob:', base64 instanceof Blob);
};

function base64ToBlob(base64) {
  // 移除 Data URL 的前缀
  var base64Content = base64.split(',')[1];
  if (!base64Content) {
    throw new Error('Invalid base64 string');
  }
  // 将 base64 编码的字符串转换为二进制字符串
  var binaryString = window.atob(base64Content);
  // 创建一个长度为二进制字符串长度的字节数组
  var len = binaryString.length;
  var bytes = new Uint8Array(len);
  // 将二进制字符串的每个字符的字符代码填充到字节数组中
  for (var i = 0; i < len; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }
  // 使用 Blob 构造函数将字节数组转换为 Blob 对象
  var blob = new Blob([bytes], { type: 'application/octet-stream' });
  return blob;
}

// 判断是否为base64格式字符串
function isBase64(str) {
  //正则表达式判断
  var reg = /^\s*data:([a-z]+\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s]*?)\s*$/i;
  return reg.test(str); //返回 true or false
}

// Base64转换为Blob
function dataURLtoBlob(dataurl) {
  var arr = dataurl.split(','),
    mime = arr[0].match(/:(.*?);/)[1],
    bstr = atob(arr[1]),
    n = bstr.length,
    u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new Blob([u8arr], { type: mime });
}

function blobToFile(blob) {
  return new File([blob], 'screenshot.png', { type: 'image/jpeg' });
}

export const getWindowProperty = (property: string) => {
  return window[property];
};

export function usePicFile() {
  const [picFile, setPicFile] = useState(picFileSubject.value);

  useEffect(() => {
    const subscription = picFileSubject.subscribe((newPicFile) => {
      setPicFile(newPicFile);
    });

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const clearPicFile = () => {
    setPicFile(null);
  };

  return { picFile, clearPicFile };
}
