import request, { jsonHeader, formHeader } from '@/utils/request';
import { ResponseType } from 'umi-request/types';
import { parseParamFromData } from './from-data';

export function requests({ url, method, data, type, dataType }: requestOptions) {
  return requestHandle({ url, data, method, type, dataType });
}

const requestMap = new Map([
  //请求方法map
  ['GET', request.get],
  ['POST', request.post],
  ['DELETE', request.delete],
  ['PUT', request.put],
  ['OPTIONS', request.options],
]);

interface requestOptions {
  //请求参数
  method?: string;
  url: string;
  data?: Object;
  dataType?: string; //传输数据格式,json:json数据，formData：表单数据，缺省：json
  type?: ResponseType; //返回数据格式，blob:二进制,json:json,text:文本，缺省：json
}

function requestHandle({ url, method = 'GET', data = {}, type = 'json', dataType = 'json' }: requestOptions) {
  //请求类型处理，GET之外请求使用json数据传输，可以自定义响应类型
  const methodCase = method.toUpperCase();
  const http = requestMap.get(methodCase);
  if (typeof data === 'object') {
    if (http != undefined) {
      if (methodCase === 'GET') {
        return http(url, data);
      } else {
        let datas = data;
        let headers = {};
        if (dataType === 'json') {
          datas = JSON.stringify(data);
          headers = jsonHeader;
        }

        if (dataType === 'formData') {
          datas = parseParamFromData(data);
          headers = formHeader;
        }

        return http(url, { data: datas, headers, responseType: type });
      }
    } else {
      return request.get(url, data);
    }
  } else {
    throw new Error('请求数据需要Object/Array类型');
  }
}
