import routes from "../../config/routes";
import type { Routes } from '../../config/routes';

/**
 * 获取当前路由对象
 * @param pathname
 * @returns routeObj{path,name}
 */
const getRouteObject = (pathname: string): Partial<Routes> => {
    let flag = false;
    let routeObj: Partial<Routes> = {};
    let path = '';
    let curPathname = pathname;
    const loop = (arr: Routes[]) => {
        if (Array.isArray(arr))
            arr.forEach(item => {
                const index = item.path.indexOf('/:');
                if (index !== -1) {
                    path = item.path.slice(0, index)
                    curPathname = pathname.slice(0, pathname.lastIndexOf('/'))
                } else {
                    path = item.path;
                    curPathname = pathname;
                }
                if (path === curPathname) {
                    routeObj = { ...item };
                    flag = true;
                } else if (!flag && item.routes) {
                    loop(item.routes)
                }
            })
    }
    loop(routes);
    return routeObj;
}


/**
 *
 * @param tarbar
 * @returns Routes[]
 */
const getTarBars = (tarbar: string): Routes[] => {
    let tarbars: Routes[] = [];
    let flag: boolean = false;
    const loop = (arr: Routes[]) => {
        if (Array.isArray(arr)) {
            arr.forEach(item => {

                if (item.tabBar === tarbar) {
                    flag = true;
                    if (item.routes) {
                        tarbars = item.routes.filter(p => p.tabBar === tarbar)
                    }
                } else if (!flag && item.routes) {
                    loop(item.routes)
                }
            })
        }
    }
    loop(routes)
    return tarbars;
}

/**
 * @param pathname 路由地址  找出该路由上是否有hideHome为true节点
 */
const gethideHome = (pathname: string): boolean => {
    let flag: boolean = false;
    const loop = (arr: Routes[]) => {
        if (Array.isArray(arr)) {
            arr.forEach(item => {
                let target: string = item.path;
                const result = target.match(/(\S*):id/)
                if (result) {
                    target = result[1]
                }
                if (item.path.includes(":id")) {//判断是否id路径，不然有些情况无法进入routes
                    if (pathname.includes(target)) {
                        flag = item.hideHome || false;
                    } else if (!flag && item.routes) {
                        loop(item.routes)
                    }
                } else {
                    if (pathname === item.path) {
                        flag = item.hideHome || false;
                    } else if (!flag && item.routes) {
                        loop(item.routes)
                    }
                }

            })
        }
    }
    loop(routes)
    return flag;
}

export { getRouteObject, getTarBars, gethideHome }
