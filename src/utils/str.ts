/**
 * 字符串工具类，采用单例模式（Singleton Pattern）
 * 单例模式确保全局只有一个实例，适用于工具类场景，节省资源并统一管理。
 */
class StringUtils {
  // 私有静态实例
  private static instance: StringUtils;
  SplitPoint: string;
  // 私有构造函数，防止外部实例化
  private constructor() {
    this.SplitPoint = `<span
      style="
        display: inline-block;
        width: .7408vw;
        height: .7408vw;
        border-radius: .3703vw;
        margin: 0 1.1111vw;
        background-color: rgba(102, 102, 102, 1)
     "
    ></span>`;
  }

  // 获取唯一实例的方法
  public static getInstance(): StringUtils {
    if (!StringUtils.instance) {
      StringUtils.instance = new StringUtils();
    }
    return StringUtils.instance;
  }

  /**
   * 判断字符串是否为空或仅包含空白字符
   * @param str 输入字符串
   * @returns 如果为空或仅空白返回true，否则false
   */
  public isBlank(str: string | null | undefined): boolean {
    return !str || str.trim().length === 0;
  }

  /**
   * 首字母大写
   * @param str 输入字符串
   * @returns 首字母大写后的字符串
   */
  public capitalize(str: string): string {
    if (!str) return '';
    return str.charAt(0).toUpperCase() + str.slice(1);
  }

  /**
   * 将字符串转为驼峰格式
   * @param str 输入字符串
   * @returns 驼峰格式字符串
   */
  public toCamelCase(str: string): string {
    return str.replace(/[-_](\w)/g, (_, c) => (c ? c.toUpperCase() : ''));
  }

  /**
   * 限制字符串最大长度，超出部分用省略号表示
   * @param str 输入字符串
   * @param maxLength 最大长度
   * @returns 处理后的字符串
   */
  public truncate(str: string, maxLength: number): string {
    if (str.length <= maxLength) return str;
    return str.slice(0, maxLength) + '...';
  }

  /**
   * 将字符串数组拼接为以指定分隔符分割的字符串，默认分隔符为 " · "
   * @param arr 字符串数组
   * @param separator 分隔符，默认为 " · "
   * @returns 拼接后的字符串
   */
  public join(arr: string[], separator?: string): string {
    return arr.join(separator || this.SplitPoint);
  }
}

// 使用方式：
// const strUtil = StringUtils.getInstance();
// strUtil.isBlank('  ');

export default StringUtils.getInstance();
