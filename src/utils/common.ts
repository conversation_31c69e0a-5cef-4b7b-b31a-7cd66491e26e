import { urlPrefix } from './request';

export function getImgParams(attachmentId: string) {
  if (attachmentId === '0') return [];
  const imglist = attachmentId.split(',');
  const res = imglist?.map((item: any) => ({
    url: `${urlPrefix}/Attachment/downloadAttachment/${item}`,
    fileId: item,
    id: item,
  }));
  return res;
}

//上传分解图片拼成字符串
export function getImgIds(fileList: any) {
  const ids = fileList.map((item: any) => item.fileId).join(',');
  return ids;
}

/**
 * 是否为有效的文件id
 */
export const isValidAttachId = (attachId: string | null | undefined) => {
  if (!attachId || attachId === '0') return false;
  return true;
};

/**
 * 组装文件list预览信息
 */
export const assembleFileList = (fileListOrFile: any, majorKey: string = 'attachId', id: any = '') => {
  if (Array.isArray(fileListOrFile)) {
    return fileListOrFile
      .filter((item) => isValidAttachId(item[majorKey]))
      .map((item) => {
        return {
          name: item.name || '无文件名文件~',
          id: id || item[majorKey],
          fileId: item[majorKey],
          url: `${urlPrefix}/Attachment/downloadAttachment/${item[majorKey]}`,
        };
      });
  }
  return [
    {
      name: fileListOrFile?.name || '无文件名文件~',
      id: id || fileListOrFile[majorKey],
      fileId: fileListOrFile?.[majorKey],
      url: `${urlPrefix}/Attachment/downloadAttachment/${fileListOrFile?.[majorKey]}`,
    },
  ].filter((item) => isValidAttachId(item.fileId));
};

/**
 * 提交接口时，处理文件list
 */
export const getAttachIdAsFileList = (fileList: any[], majorKey: string = 'id') => {
  return (fileList || []).filter((item) => isValidAttachId(item[majorKey])).map((item) => item[majorKey]);
};

// 根据偏移量计算数据
export function calculateCellPositions(colStart: number, colEnd: number, rowStart: number, rowEnd: number) {
  let cellPositions = [];

  for (let row = rowStart; row <= rowEnd; row++) {
    for (let col = colStart; col <= colEnd; col++) {
      let cellPosition = {
        row: row,
        col: col,
      };
      cellPositions.push({ ...cellPosition });
    }
  }

  return cellPositions;
}
