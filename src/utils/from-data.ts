/**
 * 将对象转换成FormData
 * @export
 * @param {object} [param={}]
 * @returns
 */
export function parseParamFromData(param: object = {}) {
  const formData = new FormData();
  for (const key in param) {
    if (key && param[key] !== undefined) {
      if (Object.prototype.toString.call(param[key]) === '[object Array]') {
        for (let i = 0; i < param[key].length; i += 1) {
          formData.append(key, param[key][i]);
        }
      } else {
        formData.append(key, param[key]);
      }
    }
  }
  return formData;
}








// const addressHead = "/api";  // 打包
// const addressHead = "http://*************:8000/sz13";
const addressHead = "/fsdt";
export { addressHead };

const projectId = "6245721945602523136";
export { projectId };
