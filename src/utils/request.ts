/**
 * request 网络请求工具
 * 更详细的 api 文档: https://github.com/umijs/umi-request
 */
import { extend } from 'umi-request';
import { Toast } from 'antd-mobile';
import { debounce } from 'lodash';
import { history } from 'umi';

const codeMessage: Record<number, string> = {
  200: '服务器成功返回请求的数据。',
  201: '新建或修改数据成功。',
  202: '一个请求已经进入后台排队（异步任务）。',
  204: '删除数据成功。',
  400: '发出的请求有错误，服务器没有进行新建或修改数据的操作。',
  401: '用户没有权限（令牌、用户名、密码错误）。',
  403: '用户得到授权，但是访问是被禁止的。',
  404: '发出的请求针对的是不存在的记录，服务器没有进行操作。',
  406: '请求的格式不可得。',
  410: '请求的资源被永久删除，且不会再得到的。',
  422: '当创建一个对象时，发生一个验证错误。',
  500: '服务器发生错误，请检查服务器。',
  502: '网关错误。',
  503: '服务不可用，服务器暂时过载或维护。',
  504: '网关超时。',
};

const notice = debounce((status?: number, errorText?: string, url?: string) => {
  if (status === 401 || status === 404 || status === 504) {
    Toast.show({
      icon: 'fail',
      content: `Request: ${status}`,
    });
  }
  Toast.show({
    icon: 'fail',
    content: errorText,
  });
}, 1000);

/** 请求URL的前缀 */
export const urlPrefix = '/dxdsapi';
export const urlPreview = '/preview';

export const formHeader = {
  Accept: '*/*',
  'Access-Control-Allow-Origin': '*',
};

export const jsonHeader = {
  'Content-Type': 'application/json',
};

/**
 * 配置request请求时的默认参数
 */
const request = extend({
  prefix: urlPrefix,
  credentials: 'include', // 默认请求是否带上cookie
  requestType: 'form',
});

/** 全局拦截器 */
request.interceptors.response.use((response, options) => {
  if (response && response.status !== 200) {
    const errorText = codeMessage[response.status] || response.statusText;
    const { status, url } = response;
    notice(status, errorText, url);

  } else if (!response) {
    Toast.show({
      icon: 'fail',
      content: '异常，请重试',
    });
  }
  // 转xml为blob对象
  if (response.headers.get('content-type') === 'application/octet-stream' || response.headers.get('content-type') === 'text/xml' || options?.responseType === 'blob')
    return response.blob();

  return response.json().then((resData) => {
    const errorMessage = resData.state ? resData.state.msg : resData.message;
    return {
      ...resData,
      message: errorMessage || '',
    };
  });
});

export default request;
