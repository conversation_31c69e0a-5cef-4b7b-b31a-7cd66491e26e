import moment from 'moment';

// 根据key合并对象数组
export type mergedArrayType = (arr1: any[], arr2: any[], key: string) => any[];
export const mergedArray: mergedArrayType = (arr1, arr2, key) => {
  return arr1.map((obj1) => {
    const matchingObj2 = arr2.find((obj2) => obj2[key] === obj1[key]);
    return { ...obj1, ...matchingObj2 };
  });
};

// 累加对象数组中的指定属性的数值
export type totalValueType = (arr: any[], key: string) => number;
export const totalValue: totalValueType = (arr, key) => {
  return arr?.reduce((accumulator, currentValue) => {
    return accumulator + Number(currentValue[key]);
  }, 0);
};

//  计算两个数相除之后转成百分比的数值
export const calculatePercentage = (dividend: number, divisor: number, num: number = 2): number => {
  // 分母为0，分子不为0，返回100
  if (divisor === 0 && dividend !== 0) {
    return 100;
  }
  // 分母为0，分子为0，返回0
  if (divisor === 0 && dividend === 0) {
    return 0;
  }
  if (!(dividend / divisor)) return 0;
  const percentage = (dividend / divisor) * 100;
  return Number(percentage.toFixed(num));
};

// 获取对象数组中指定属性值的对象
export type findObjType = (arr: Array<any>, key: string, value: string) => any;
export const findObj: findObjType = (arr, key, value) => {
  return arr?.find((obj) => obj?.[key] === value);
};

// 将数字转成金额格式
export type formatCountType = (number: number | string) => string;
export const formatCount: formatCountType = (number) => {
  return Number(number)
    .toLocaleString('zh-CN', {
      style: 'currency',
      currency: 'CNY',
    })
    .substring(1);
};

// 模糊查询
export type fuzzySearchType = (array: any[], key: string, query: string) => any[];
export const fuzzySearch: fuzzySearchType = (array, key, query) => {
  // 使用filter方法筛选出所有指定属性包含查询字符串的元素
  return array.filter((item) => item?.[key].includes(query));
};

// 获取树满足条件的叶子节点或叶节点上指定的target属性
export const getTreeLeafTarget = (
  data: any[],
  target: string,
  key: string = 'children',
  callback: (node?: any) => boolean = () => true,
): any[] => {
  const targetArr: any[] = [];
  const traverse = (node: any) => {
    if (Array.isArray(node?.[key]) && node?.[key]?.length) {
      node?.[key].forEach((it: any) => traverse(it));
    } else if ((node?.[key]?.length === 0 || !node?.[key]) && target && node?.[target] && callback(node)) {
      targetArr.push(node?.[target]); //获取指定的target属性
    } else if ((node?.[key]?.length === 0 || !node?.[key]) && !target && callback(node)) {
      targetArr.push(node); //获取叶子节点
    }
  };
  data?.forEach((item) => traverse(item));
  return targetArr;
};

// Map深序遍历树,callback的返回值用来修改每一项
export const deepMapTreeFn = (arr: any[], callback: (node: any) => any, entryKey: string = 'children') => {
  if (!Array.isArray(arr)) {
    return arr;
  }
  // 递归遍历数组
  for (let i = 0; i < arr.length; i++) {
    const item = arr[i];
    // 修改每一项数据
    arr[i] = callback(item) ? callback(item) : arr[i];
    // 递归遍历子数组
    if (Array.isArray(item[entryKey])) {
      deepMapTreeFn(item[entryKey], callback, entryKey);
    }
  }
  return arr;
};

// forEach深序遍历树，不返回新数组
export const deepForEachFn = (arr: any[], fn = (params: any) => {}, children = 'children') => {
  if (Array.isArray(arr)) {
    arr.forEach((item) => {
      if (Array.isArray(item[children])) {
        deepForEachFn(item[children], fn, children);
      }
      if (fn) fn(item);
    });
  }
};

// 根据两个key值是否相同来将叶子数组拼接到根数组的叶子结点下,如果叶子树需要删除的上面几层，可以传入removeNum参数
export const mergeTree = (arrRoot: any[], arrLeafs: any[], rootKey: string = 'id', leafKey: string = 'parentId', removeNum?: number) => {
  //删除叶子树指定层数的节点
  const removeTopNLevels = (tree: any[], num: number, depth: number = 0, result: any[] = []) => {
    if (depth === num) {
      // 如果已经到达第num层，则将当前节点添加到结果数组中
      result.push(...tree);
    } else if (Array.isArray(tree) && tree.length > 0) {
      // 如果还没到达第num层，并且当前层级还有子节点
      tree.forEach((node) => {
        removeTopNLevels(node.children || [], num, depth + 1, result);
      });
    }
    return result;
  };

  // 拼接
  const mergeNode = (node: any, leaf: any) => {
    if (node[rootKey] === leaf[leafKey]) {
      // 如果需要删除叶子树层级
      if (removeNum) {
        const removedLeaf = removeTopNLevels([leaf], removeNum);
        removedLeaf.forEach((item: any) => {
          node?.children.push(item);
        });
      } else {
        node?.children.push(leaf);
      }
    } else {
      for (let i = 0; i < node?.children?.length; i++) {
        mergeNode(node?.children[i], leaf);
      }
    }
  };

  // 遍历根树，调用拼接函数
  for (let i = 0; i < arrLeafs.length; i++) {
    const leaf = arrLeafs[i];
    for (let j = 0; j < arrRoot.length; j++) {
      const root = arrRoot[j];
      mergeNode(root, leaf);
    }
  }
  return arrRoot;
};

// 获取树节点的所有父节点
export const getAllParentIds = (tree: any[], id: string): any => {
  // eslint-disable-next-line @typescript-eslint/no-for-in-array
  for (const i in tree) {
    if (tree[i].id === id) {
      return [tree[i].id];
    }
    if (tree[i].children != null) {
      const node = getAllParentIds(tree[i].children, id);
      if (node !== undefined) {
        return node.concat(tree[i].id);
      }
    }
  }
};

// 动态为对象数组添加颜色
export const addColor = (
  arr: any[],
  key: string,
  colorArr: any[] = ['#0088FF', '#00FF7F', '#00FF00', '#FF0000', '#FF00FF', '#FFFF00', '#FF7F00', '#FF8000'],
) => {
  const map = new Map();
  const newArr: any[] = [];
  const newColor = [...colorArr];
  arr?.forEach((item) => {
    const color = newColor?.[0];
    if (!map.get(item[key])) {
      newColor?.splice(0, 1);
      map.set(item[key], color);
      newArr.push({
        ...item,
        color,
      });
    } else {
      newArr.push({
        ...item,
        color: map.get(item[key]),
      });
    }
  });
  return newArr;
};

// 根据映射表将对象转对象数组
export const mapToObjectArr = (obj: any, Map: any) => {
  const arr = [];
  for (let key in Map) {
    arr.push({
      key,
      label: Map?.[key],
      value: obj?.[key] ?? '-',
    });
  }
  return arr;
};

// 删除对象中值为falsy、空数组或空对象的属性
export const removeFalsyValues = (obj: any) => {
  if (obj && typeof obj === 'object') {
    for (let key in obj) {
      if (obj.hasOwnProperty(key)) {
        if (
          !obj[key] ||
          (Array.isArray(obj[key]) && obj[key].length === 0) ||
          (typeof obj[key] === 'object' && Object.keys(obj[key]).length === 0)
        ) {
          // 如果值是 falsy 值、空数组或空对象
          delete obj[key]; // 删除该属性
        } else if (typeof obj[key] === 'object' && Object.keys(obj[key]).length > 0) {
          // 如果值是嵌套非空对象
          removeFalsyValues(obj[key]); // 递归处理子对象
        }
      }
    }
  }
  return obj;
};

// 根据字符串query在对象的属性名中进行模糊筛选，将筛选的属性的值用数组返回
export const filterInObject = (obj: any, query: string) => {
  const result = [];
  for (let key in obj) {
    if (key?.includes(query)) {
      result.push(obj[key]);
    }
  }
  return result;
};

// 通过key属性，根据模板对象数组补充接口数据
export const fillData = (actualData: any[], defaultData: any[], key: string) => {
  const filledData = defaultData.map((defaultItem: any) => {
    const foundItem = actualData.find((item: any) => item[key] === defaultItem[key]);

    return foundItem ?? defaultItem;
  });
  return filledData;
};

/** 对象枚举值转下拉框options */
export function enumObjToArray(value: Record<string, any>) {
  return Object.keys(value).map((key) => {
    return {
      label: value[key],
      value: key,
      id: key,
    };
  });
}
// 返回两个数组中不重复的元素
export function mergeAndFindUniqueElements(array1: string[], array2: string[]): string[] {
  const set1 = new Set(array1);
  const set2 = new Set(array2);

  // 找出仅在 array1 中存在的元素
  const uniqueInArray1 = array1.filter((item) => !set2.has(item));

  // 找出仅在 array2 中存在的元素
  const uniqueInArray2 = array2.filter((item) => !set1.has(item));

  // 合并两个数组中的独特元素
  return [...uniqueInArray1, ...uniqueInArray2];
}

// 将对象数组转树结构并排序（支持多层嵌套）
export const buildSortedTree = (items: { id: string | number; parentId: string | number | null; sort: number }[]) => {
  // 创建一个临时映射表，便于根据parentId快速查找节点
  const map = new Map<string | number, { id: string | number; parentId: string | number | null; sort: number; children?: any[] }>();
  // 遍历所有项，构建映射表并将每个节点的children属性初始化为空数组
  items.forEach((item) => {
    if (!map.has(item.id)) {
      map.set(item.id, { ...item, children: [] });
    }
  });
  // 递归方法，用于处理子节点的构建和排序
  const processChildren = (node: { id: string | number; parentId: string | number | null; sort: number; children: any[] }) => {
    node.children.sort((a, b) => a.sort - b.sort);
    node.children.forEach((childId) => {
      if (map.has(childId)) {
        const childNode = map.get(childId);
        processChildren(childNode);
        node.children.push(childNode);
      }
    });
  };
  // 再次遍历所有项，根据parentId将子节点添加到父节点的children数组中，并启动递归过程
  items.forEach((item) => {
    if (item.parentId !== null && map.has(item.parentId)) {
      const parent = map.get(item.parentId);
      // 添加子节点对象
      parent.children.push(map.get(item.id));
      processChildren(parent);
    }
  });
  // 查找根节点（parentId为空的节点）
  const roots: { id: string | number; parentId: string | number | null; sort: number; children?: any[] }[] = [];
  map.forEach((node) => {
    if (node.parentId === null) {
      roots.push(node);
    }
  });
  // 根节点排序
  roots.sort((a, b) => a.sort - b.sort);
  // 返回根节点构成的数组
  return roots;
};

// 根据计划和实际的日期得到开始和结束的提前滞后情况
export const comparActAndPlanTime = (
  planStartTime: string | null,
  planEndTime: string | null,
  actStartTime: string | null,
  actEndTime: string | null,
) => {
  const result: string[] = ['', '']; //[开始日期的比较结果，结束日期的比较结果]
  const today = moment(moment().format('YYYY-MM-DD'));
  // 如果没有日期返回‘-’
  if (!planStartTime) {
    result[0] = '-';
  }
  if (!planEndTime) {
    result[1] = '-';
  }
  // 没有实际就计算距离计划还有多久
  // 有实际就计算滞后提前多久
  if (planStartTime && !actStartTime) {
    const days = today.diff(moment(planStartTime, 'YYYY-MM-DD'), 'days');
    result[0] = days > 0 ? `已推迟${days}天开始` : `距开始还有${Math.abs(days)}天`;
  }
  if (planStartTime && actStartTime) {
    const days = moment(actStartTime, 'YYYY-MM-DD').diff(moment(planStartTime, 'YYYY-MM-DD'), 'days');
    result[0] = days > 0 ? `滞后${days}天开始` : `提前${Math.abs(days)}天开始`;
  }
  if (planEndTime && !actEndTime) {
    const days = today.diff(moment(planEndTime, 'YYYY-MM-DD'), 'days');
    result[1] = days > 0 ? `已逾期${days}天` : `距结束还有${Math.abs(days)}天`;
  }
  if (planEndTime && actEndTime) {
    const days = moment(actEndTime, 'YYYY-MM-DD').diff(moment(planEndTime, 'YYYY-MM-DD'), 'days');
    result[1] = days > 0 ? `滞后${days}天结束` : `提前${Math.abs(days)}天结束`;
  }
  return result;
};

// 对象数组去重
export function slimeArrayByTarget(arr: any[], taget: string) {
  return arr?.reduce((accumulator: any[], currentObj) => {
    // 如果当前数组中还没有找到code相同的对象，则将当前对象添加到结果数组中
    if (!accumulator.find((obj) => obj?.[taget] === currentObj?.[taget])) {
      accumulator.push(currentObj);
    }
    return accumulator;
  }, []);
}

// 扁平的对象数组根据对象属性排序
export function sortArrByTarget(arr: any[], taget: string, type: 'up' | 'down' = 'up') {
  const data = [...arr];
  data?.sort((a, b) => {
    return type === 'down' ? b[taget] - a[taget] : a[taget] - b[taget];
  });
  return data;
}

export function getUserName() {
  return localStorage.getItem('j_username');
}

// 为项目树叶子节点添加bimModelId属性并瘦身，projectTree是项目树，modelObjs是用项目id查询的得到的模型基本信息
export const addModelId = (projectTree: any[], modelObjs: any[]) => {
  return deepMapTreeFn(projectTree, (node) => {
    let obj = {};
    let flag = false;
    modelObjs?.forEach((item) => {
      if (item.nodeId === node.id) {
        obj = {
          code: node.code,
          name: node.name,
          id: node.id,
          parentId: node.parentId,
          loadModel: node.loadModel,
          children: node.children,
          modelId: item.bimModelId,
        };
        flag = true;
      }
    });
    if (flag) {
      return obj;
    } else
      return {
        code: node.code,
        name: node.name,
        id: node.id,
        parentId: node.parentId,
        loadModel: node.loadModel,
        children: node.children,
      };
  });
};
