/**
 * tree.ts
 * 树形结构数据处理的常用工具函数
 */

/**
 * 基础树节点类型
 */
export interface TreeNode {
  id: string | number;
  children?: TreeNode[];
  [key: string]: any;
}

/**
 * 遍历树结构
 * @param treeData 树结构数据
 * @param callback 遍历回调函数
 */
export function traverseTree(
  treeData: TreeNode[],
  callback: (node: TreeNode, parentNode: TreeNode | null, level: number) => void
): void {
  const traverse = (
    nodes: TreeNode[],
    parent: TreeNode | null = null,
    level = 0
  ) => {
    nodes.forEach((node) => {
      // 执行回调
      callback(node, parent, level);
      
      // 如果有子节点，递归遍历
      if (node.children && node.children.length > 0) {
        traverse(node.children, node, level + 1);
      }
    });
  };
  
  traverse(treeData);
}

/**
 * 查找树中符合条件的节点
 * @param treeData 树结构数据
 * @param predicate 判断函数
 * @returns 找到的节点，如果没找到则返回null
 */
export function findNode(
  treeData: TreeNode[],
  predicate: (node: TreeNode) => boolean
): TreeNode | null {
  for (const node of treeData) {
    // 检查当前节点
    if (predicate(node)) {
      return node;
    }
    
    // 如果有子节点，递归查找
    if (node.children && node.children.length > 0) {
      const found = findNode(node.children, predicate);
      if (found) return found;
    }
  }
  
  return null;
}

/**
 * 根据ID查找树节点
 * @param treeData 树结构数据
 * @param id 节点ID
 * @returns 找到的节点，如果没找到则返回null
 */
export function findNodeById(
  treeData: TreeNode[],
  id: string | number
): TreeNode | null {
  return findNode(treeData, (node) => node.id === id);
}

/**
 * 查找树中所有符合条件的节点
 * @param treeData 树结构数据
 * @param predicate 判断函数
 * @returns 找到的所有节点数组
 */
export function findNodes(
  treeData: TreeNode[],
  predicate: (node: TreeNode) => boolean
): TreeNode[] {
  const result: TreeNode[] = [];
  
  traverseTree(treeData, (node) => {
    if (predicate(node)) {
      result.push(node);
    }
  });
  
  return result;
}

/**
 * 查找节点的所有父节点路径
 * @param treeData 树结构数据
 * @param targetId 目标节点ID
 * @returns 从根到目标节点的路径数组，如果没找到返回空数组
 */
export function findNodePath(
  treeData: TreeNode[],
  targetId: string | number
): TreeNode[] {
  const path: TreeNode[] = [];
  
  const findPath = (nodes: TreeNode[], currentPath: TreeNode[]): boolean => {
    for (const node of nodes) {
      // 将当前节点添加到路径
      currentPath.push(node);
      
      // 检查当前节点
      if (node.id === targetId) {
        return true;
      }
      
      // 检查子节点
      if (node.children && node.children.length > 0) {
        if (findPath(node.children, currentPath)) {
          return true;
        }
      }
      
      // 如果没找到，从路径中移除当前节点
      currentPath.pop();
    }
    
    return false;
  };
  
  findPath(treeData, path);
  return path;
}

/**
 * 树形结构转为扁平数组
 * @param treeData 树结构数据
 * @returns 扁平化后的数组
 */
export function flattenTree(treeData: TreeNode[]): TreeNode[] {
  const result: TreeNode[] = [];
  
  traverseTree(treeData, (node) => {
    result.push(node);
  });
  
  return result;
}

/**
 * 过滤树节点
 * @param treeData 树结构数据
 * @param predicate 过滤函数
 * @returns 过滤后的树
 */
export function filterTree(
  treeData: TreeNode[],
  predicate: (node: TreeNode) => boolean
): TreeNode[] {
  return treeData.reduce<TreeNode[]>((acc, node) => {
    // 创建节点副本，避免修改原始数据
    const newNode = { ...node };
    
    // 如果有子节点，递归过滤
    if (node.children && node.children.length > 0) {
      newNode.children = filterTree(node.children, predicate);
      
      // 如果当前节点符合条件或有符合条件的子节点，则保留
      if (predicate(node) || (newNode.children && newNode.children.length > 0)) {
        acc.push(newNode);
      }
    } else if (predicate(node)) {
      // 如果没有子节点且符合条件，直接添加
      acc.push(newNode);
    }
    
    return acc;
  }, []);
}

/**
 * 获取树的所有叶子节点
 * @param treeData 树结构数据
 * @returns 所有叶子节点数组
 */
export function getLeafNodes(treeData: TreeNode[]): TreeNode[] {
  const leaves: TreeNode[] = [];
  
  traverseTree(treeData, (node) => {
    if (!node.children || node.children.length === 0) {
      leaves.push(node);
    }
  });
  
  return leaves;
}

/**
 * 更新树中的节点
 * @param treeData 树结构数据
 * @param id 需要更新的节点ID
 * @param updateFn 更新函数
 * @returns 更新后的树
 */
export function updateNode(
  treeData: TreeNode[],
  id: string | number,
  updateFn: (node: TreeNode) => TreeNode
): TreeNode[] {
  return treeData.map(node => {
    // 如果是要更新的节点，应用更新函数
    if (node.id === id) {
      return updateFn({ ...node });
    }
    
    // 如果有子节点，递归更新
    if (node.children && node.children.length > 0) {
      return {
        ...node,
        children: updateNode(node.children, id, updateFn)
      };
    }
    
    // 不需要更新，返回原节点
    return node;
  });
}

/**
 * 在树中添加节点
 * @param treeData 树结构数据
 * @param parentId 父节点ID，如果为null则添加为根节点
 * @param newNode 新节点
 * @returns 添加节点后的树
 */
export function addNode(
  treeData: TreeNode[],
  parentId: string | number | null,
  newNode: TreeNode
): TreeNode[] {
  // 如果parentId为null，添加为根节点
  if (parentId === null) {
    return [...treeData, newNode];
  }
  
  return treeData.map(node => {
    // 如果当前节点是父节点，添加新节点到其children
    if (node.id === parentId) {
      return {
        ...node,
        children: node.children ? [...node.children, newNode] : [newNode]
      };
    }
    
    // 如果有子节点，递归查找父节点
    if (node.children && node.children.length > 0) {
      return {
        ...node,
        children: addNode(node.children, parentId, newNode)
      };
    }
    
    // 不是父节点，返回原节点
    return node;
  });
}

/**
 * 从树中删除节点
 * @param treeData 树结构数据
 * @param id 要删除的节点ID
 * @returns 删除节点后的树
 */
export function removeNode(
  treeData: TreeNode[],
  id: string | number
): TreeNode[] {
  // 过滤掉当前层级中匹配的节点
  const filteredTree = treeData.filter(node => node.id !== id);
  
  // 如果没有节点被删除，则递归检查子节点
  if (filteredTree.length === treeData.length) {
    return filteredTree.map(node => {
      // 如果有子节点，递归检查
      if (node.children && node.children.length > 0) {
        return {
          ...node,
          children: removeNode(node.children, id)
        };
      }
      
      return node;
    });
  }
  
  return filteredTree;
}

/**
 * 扁平数组转为树形结构
 * @param items 扁平数组
 * @param options 配置选项
 * @returns 树形结构
 */
export function arrayToTree(
  items: any[],
  options: {
    idKey?: string;
    parentIdKey?: string;
    rootParentValue?: any;
  } = {}
): TreeNode[] {
  const {
    idKey = 'id',
    parentIdKey = 'parentId',
    rootParentValue = null
  } = options;
  
  const itemMap = new Map();
  const result: TreeNode[] = [];
  
  // 创建一个所有节点的映射
  items.forEach(item => {
    itemMap.set(item[idKey], { ...item, children: [] });
  });
  
  // 构建树形结构
  items.forEach(item => {
    const id = item[idKey];
    const parentId = item[parentIdKey];
    const treeItem = itemMap.get(id);
    
    if (parentId === rootParentValue || parentId === undefined || !itemMap.has(parentId)) {
      // 没有父节点或父节点不存在，作为根节点
      result.push(treeItem);
    } else {
      // 将当前节点添加到父节点的children中
      const parentItem = itemMap.get(parentId);
      parentItem.children.push(treeItem);
    }
  });
  
  return result;
}

/**
 * 计算树的最大深度
 * @param treeData 树结构数据
 * @returns 树的最大深度
 */
export function getMaxDepth(treeData: TreeNode[]): number {
  let maxDepth = 0;
  
  const getDepth = (nodes: TreeNode[], currentDepth: number): void => {
    // 更新最大深度
    maxDepth = Math.max(maxDepth, currentDepth);
    
    // 递归计算子节点深度
    nodes.forEach(node => {
      if (node.children && node.children.length > 0) {
        getDepth(node.children, currentDepth + 1);
      }
    });
  };
  
  getDepth(treeData, 1);
  return maxDepth;
}

/**
 * 获取节点所在的深度级别
 * @param treeData 树结构数据
 * @param id 节点ID
 * @returns 节点的深度，如果未找到节点则返回0
 */
export function getNodeLevel(
  treeData: TreeNode[],
  id: string | number
): number {
  const path = findNodePath(treeData, id);
  return path.length;
} 