// utils/recoilUtils.ts
export const localStorageEffect = (key: string) => ({ setSelf, onSet }) => {
  const savedValue = localStorage.getItem(key);
  if (savedValue != null) {
    setSelf(JSON.parse(savedValue));
  }

  onSet((newValue, _, isReset) => {
    isReset 
      ? localStorage.removeItem(key)
      : localStorage.setItem(key, JSON.stringify(newValue));
  });
};

// 使用示例
export const themeState = atom<Theme>({
  key: 'themeState',
  default: 'light',
  effects: [localStorageEffect('app_theme')]
});