export function formatDate(date: Date) {
  // 获取年、月、日
  const year = date.getFullYear();
  let month: number | string = date.getMonth() + 1; // 月份从0开始，所以要加1
  let day: number | string = date.getDate();

  // 将月份和日期格式化为两位数
  if (month < 10) month = '0' + month;
  if (day < 10) day = '0' + day;

  // 返回 YYYY-MM-DD 格式的日期字符串
  return year + '-' + month + '-' + day;
}

export function getDateBefore(days: number) {
  const date = new Date();
  date.setDate(date.getDate() - days);
  return date;
}
