export const dateSlice = {
  weekArr: ['星期天', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'],
  getDateTime: function (date: string): Number {
    return new Date(date).getTime();
  },
  // 获取时间详情对象 (传入毫秒数)
  getDateObj: function (_time: string | number) {
    let date: any = _time ? new Date(_time) : new Date();
    let yyyy = this.dateStrFormat(date.getFullYear());
    let mm = this.dateStrFormat(date.getMonth() + 1);
    let m = date.getMonth() + 1;
    let dd = this.dateStrFormat(date.getDate());
    let d = date.getMonth() + 1;
    let hours = this.dateStrFormat(date.getHours());
    let minute = this.dateStrFormat(date.getMinutes());
    let seconds = this.dateStrFormat(date.getSeconds());
    let week = this.weekArr[date.getDay()];
    let dateTime = `${yyyy}-${mm}-${dd}`;
    let time = `${hours}:${minute}:${seconds}`;
    let dateAll = `${dateTime} ${time}`;
    const oneJan: any = new Date(Number(yyyy), 0, 1);
    const currentWeeks = Math.ceil((date - oneJan) / 86400000 / 7) + 1;
    const oneDay = 1000 * 60 * 60 * 24;
    const diffDays = Math.round((date - oneJan) / oneDay);
    const mWeeks = this.weekIndexInMonth(date);
    return {
      yyyy: yyyy,
      mm: mm,
      dd: dd,
      month: m,
      currentWeeks, //年第几周
      diffDays, //年第几天
      mWeeks, //月第几周
      d: d,
      week: week,
      hours: hours,
      minute: minute,
      seconds: seconds,
      dateTime: dateTime,
      dateAll: dateAll,
    };
  },
  dateStrFormat: function (_str: number) {
    return _str >= 10 ? _str + '' : '0' + (_str + '');
  },
  getMonthRange: function (year: number, month: number) {
    let date = new Date(year, month, 0);
    let days = date.getDate(); //获取月份最大天数
    let start = 1;
    let end = days;
    if (year === new Date().getFullYear() && month === new Date().getMonth() + 1) {
      //如果为当前月，则不能超过当前日期
      end = new Date().getDate();
    }
    let dates = [];

    for (let i = start; i <= end; i++) {
      //循环出月份范围
      const curdate = new Date(date.getFullYear(), date.getMonth(), i);
      dates.push({
        day: curdate.getDate(),
        week: this.weekIndexInMonth(new Date(date.getFullYear(), date.getMonth(), i).getTime()),
        month: curdate.getMonth() + 1,
        year: curdate.getFullYear(),
      });
    }
    return dates;
  },
  weekIndexInMonth: function (time: string | number) {
    //计算时间是本月第几周

    //设置时间为本月的1号
    const date = new Date(new Date(time).setDate(1) || new Date().setDate(1));
    //获取今天的日期
    const today = new Date(time);
    const d = today.getDate();
    let firstWeekDate;
    if (date.getDay() === 0) {
      // 判断1号是周日
      firstWeekDate = 6;
    } else {
      // 判断1号是周一至周六之间
      firstWeekDate = date.getDay() - 1;
    }
    return Math.ceil((d + firstWeekDate) / 7);
  },
  weekInMonthCount: function (time: string) {
    //计算当前时间有几周

    //设置时间为本月的1号
    const date = new Date(new Date(time).setDate(1));
    let firstWeekDate;
    if (date.getDay() === 0) {
      // 判断1号是周日
      firstWeekDate = 6;
    } else {
      // 判断1号是周一至周六之间
      firstWeekDate = date.getDay() - 1;
    }
    date.setMonth(date.getMonth() + 1);
    date.setDate(0);
    const monthHasDays = date.getDate() + firstWeekDate;
    return Math.ceil(monthHasDays / 7); // 计算本月有几周
  },
  getMonthWeekRange(year: any, month: any, week: any, isLimit: boolean = false) {
    //获取某年某月某周的日期范围
    //isLimit:是否不能超过当前日期
    year = Number(year);
    month = Number(month);
    week = Number(week);
    let nowMonth = month - 1;

    let startMonth; //本月的开始时间

    let endMonth; //本月的结束时间

    //获得某月的天数

    function getMonthDays(month: any) {
      let monthStartDate: any = new Date(year, nowMonth, 1);

      let monthEndDate: any = new Date(year, month + 1, 1);

      let days = (monthEndDate - monthStartDate) / (1000 * 60 * 60 * 24);

      return days;
    }

    //获得本月的开始日期

    function getMonthStartDate() {
      let monthStartDate = new Date(year, nowMonth, 1);

      return monthStartDate.getDate();
    }

    //获得本月的结束日期

    function getMonthEndDate() {
      let monthEndDate = new Date(year, nowMonth, getMonthDays(nowMonth));

      return monthEndDate.getDate();
    }

    startMonth = getMonthStartDate();

    endMonth = getMonthEndDate();

    let d = new Date(); //

    // what day is first day

    d.setFullYear(year, month - 1, 1);

    let w1 = d.getDay(); //某月的第一天是星期几

    if (w1 == 0) {
      w1 = 7;
    }

    let firtWeek_count = 7 - w1 + 1; //第一周有几天

    // total day of month

    d.setFullYear(year, month, 0);

    let dd = d.getDate(); //某月的总天数

    let d1;

    // first Monday

    if (w1 != 1) {
      //第一天不是星期一

      d1 = 7 - w1 + 2;
    } else {
      d1 = 1;
    }

    let week_count = Math.ceil((dd - d1 + 1) / 7);

    if (w1 == 1) {
      week_count = week_count;
    } else {
      week_count = week_count + 1;
    }

    for (let i = 0; i < week_count; i++) {
      let monday = firtWeek_count + (i - 1) * 7 + 1;
      let diffVal = 6;
      const dateTime = new Date();
      if (dateTime.getFullYear() === Number(year) && dateTime.getMonth() + 1 === Number(month)) {
        diffVal = dateTime.getDate() < monday + 6 ? dateTime.getDate() - monday : 6;
      }
      let sunday = isLimit ? monday + diffVal : monday + 6;
      let startTime = monday;

      let endTime;
      if (i == 0) {
        startTime = startMonth;
        endTime = firtWeek_count;
      } else if (i == week_count - 1) {
        startTime = firtWeek_count + (week_count - 2) * 7 + 1;
        endTime = endMonth;
      } else {
        endTime = sunday;
      }
      if (week === i + 1) {
        return Array.from({ length: endTime - startTime + 1 }, (_, i) => String(startTime + i));
      }
    }
  },
  getDaysBetween(startDate: string, enDate: string) {
    //计算2个日期差
    const sDate: any = new Date(startDate);
    let eDate: any = new Date(enDate);
    const dateTime = new Date(eDate.setDate(eDate.getDate() + 1));
    eDate = new Date(dateTime);
    if (sDate > eDate) {
      return 0;
    }
    if (sDate === eDate) {
      return 1;
    }
    const days = (eDate - sDate) / (1 * 24 * 60 * 60 * 1000);
    return Math.floor(days);
  },
  getAllFormat(date: Date) {
    let s = '';
    let mouth = date.getMonth() + 1 >= 10 ? date.getMonth() + 1 : '0' + (date.getMonth() + 1);
    let day = date.getDate() >= 10 ? date.getDate() : '0' + date.getDate();
    s += date.getFullYear() + '-'; // 获取年份。
    s += mouth + '-'; // 获取月份。
    s += day; // 获取日。
    return s; // 返回日期。
  },

  getDaysBetweenAll(begin: any, end: any) {
    //获取两个日期之间的时间
    let arr = [];
    let ab = begin.split('-');
    let ae = end.split('-');
    let db = new Date();
    db.setUTCFullYear(ab[0], ab[1] - 1, ab[2]);
    let de = new Date();
    de.setUTCFullYear(ae[0], ae[1] - 1, ae[2]);
    let unixDb = db.getTime() - 24 * 60 * 60 * 1000;
    let unixDe = de.getTime() - 24 * 60 * 60 * 1000;
    for (let k: any = unixDb; k <= unixDe; ) {
      k = k + 24 * 60 * 60 * 1000;
      arr.push(this.getAllFormat(new Date(parseInt(k))));
    }
    return arr;
  },
};
