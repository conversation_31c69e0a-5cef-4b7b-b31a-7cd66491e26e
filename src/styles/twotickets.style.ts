import { ActionSheet, List } from 'antd-mobile';
import { styled } from 'umi';

// 附件
export const OperationFileList = styled(List).attrs({
  className: (props: { className: any }) => `${props.className} operation-file-list` as any,
})`
  .file_name {
    font-size: 4.2592vw;
    font-weight: 400;
    letter-spacing: 0vw;
    line-height: 6.1675vw;
    color: rgba(51, 51, 51, 1);
  }
  .file_info {
    font-size: 3.3333vw;
    font-weight: 400;
    letter-spacing: 0vw;
    line-height: 4.8269vw;
    color: rgba(102, 102, 102, 1);
    display: flex;
    flex-direction: row;
    align-items: center;
    white-space: normal;
    overflow-wrap: break-word;
    flex-wrap: wrap;
  }
`;

// 危险点与预防措施
export const OperateRiskList = styled(List).attrs({
  className: (props: { className: any }) => `${props.className} operation-risk-list` as any,
})`
  .adm-list-item-content-prefix {
    padding-top: .5926vw;
    padding-bottom: .5926vw;
  }
  .operate_risk {
    font-size: 3.8889vw;
    font-weight: 400;
    letter-spacing: 0vw;
    line-height: 5.6314vw;
    color: rgba(51, 51, 51, 1);
    margin-top: .7408vw;
    .label {
      font-size: 3.8889vw;
      font-weight: 400;
      letter-spacing: 0vw;
      line-height: 5.6314vw;
      color: rgba(17, 84, 237, 1);
    }
  }
`;
// 操作项
export const OperateStepsList = styled(List).attrs({
  className: (props: { className: any }) => `${props.className} operation-steps-list` as any,
})`
  .adm-list-item-content-prefix {
    padding-top: 12px;
    padding-bottom: 12px;
  }
  .prefix {
    font-size: 3.3333vw;
    font-weight: 400;
    letter-spacing: 0vw;
    line-height: 4.8269vw;
    color: rgba(51, 51, 51, 1);
  }
  .operate_steps {
    font-size: 3.3333vw;
    font-weight: 400;
    letter-spacing: 0vw;
    line-height: 4.8269vw;
    padding-left: 1.8519vw;
    color: rgba(51, 51, 51, 1);
  }
  .body {
    padding: 2.3147vw 2.9631vw;
    border-radius: 1.6667vw;
    background: rgba(245, 245, 245, 1);
    font-size: 3.3333vw;
    font-weight: 400;
    letter-spacing: 0vw;
    line-height: 4.8269vw;
    color: rgba(51, 51, 51, 1);
    margin-top: 1.8519vw;
  }
`;

// 安全工器具
export const OperatorStandardTool = styled(List).attrs({
  className: (props: { className: any }) => `${props.className} operation-standard-tool` as any,
})`
  .tool_name {
    font-size: 4.2592vw;
    font-weight: 400;
    letter-spacing: 0vw;
    line-height: 6.1675vw;
    color: rgba(51, 51, 51, 1);
  }
  .tool_info {
    font-size: 3.3333vw;
    font-weight: 400;
    letter-spacing: 0vw;
    line-height: 4.8269vw;
    color: rgba(102, 102, 102, 1);
    display: flex;
    flex-direction: row;
    align-items: center;
  }
`;


export const DetailTabTitleWrapper = styled.div`
  margin: 4.8148vw 0 2.3148vw 3.4259vw;
  font-size: 4.2593vw;
  font-weight: 500;
  letter-spacing: 0vw;
  line-height: 6.1676vw;
  color: rgba(51, 51, 51, 1);
`


export const FooterWrapper = styled.div`
  height: 13.3333vw;
  font-size: 3.7036vw;
  font-weight: 400;
  letter-spacing: -0.1481vw;
  line-height: 13.3333vw;
  color: rgba(153, 153, 153, 1);
  text-align: center;
`;


export const AddOrFillBtn = styled.span`
  display: inline-block;
  border-radius: 7.7778vw;
  background: rgba(215, 225, 250, 1);
  font-size: 3.8889vw;
  font-weight: 400;
  letter-spacing: 0vw;
  line-height: 5.6315vw;
  color: rgba(17, 84, 237, 1);
  min-width: 16.6667vw;
  height: 7.7778vw;
  text-align: center;
  line-height: 7.7778vw;
  padding: 0 1.1111vw;
`;

export const SelectWrapper = styled(ActionSheet)`
  box-sizing: border-box;
  margin: 4.537vw 3.3333vw;
  .adm-action-sheet-button-list{
    border-bottom: none;
    .adm-action-sheet-button-item-wrapper{
      border-bottom: none;
      height: 15.5556vw;
      opacity: 1;
      border-radius: 3.3333vw;
      background: rgba(255, 255, 255, 1);
      margin-bottom: 1.4815vw;
      .adm-action-sheet-button-item-name{
        font-size: 4.4444vw;
        font-weight: 400;
        letter-spacing: 0vw;
        line-height: 6.4352vw;
        color: rgba(51, 51, 51, 1);
      }
    }
  }
  .adm-action-sheet-cancel{
    margin-top: 2.963vw;
    padding-top: 0;
    height: 15.5556vw;
      opacity: 1;
      border-radius: 3.3333vw;
      background: rgba(255, 255, 255, 1);
  }
`;
