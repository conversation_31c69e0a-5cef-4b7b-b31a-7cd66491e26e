/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-04-28 17:28:23
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-05-30 11:01:31
 * @FilePath: /mobile-yw/config/config.ts
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */
import { defineConfig } from 'umi';
import postCssPxToViewport from 'postcss-px-to-viewport';
import proxy from './proxy';
import routes from './routes';

const { REACT_APP_ENV, NODE_ENV } = process.env;
const publicPath = NODE_ENV === 'development' ? '/' : '/mobile/';
export default defineConfig({
  routes,
  esbuildMinifyIIFE: true,
  proxy: proxy[REACT_APP_ENV || 'dev'],
  history: { type: 'hash' },
  publicPath,
  outputPath: 'mobile',
  ignoreMomentLocale: true,
  scripts: [{ src: `${publicPath}js/screen-adaptation.js` }],
  plugins: ['@umijs/plugins/dist/styled-components', '@umijs/plugins/dist/initial-state', '@umijs/plugins/dist/model'],
  styledComponents: {
    babelPlugin: {
      fileName: true, // 根据文件路径增强类名唯一性
      displayName: true,
      pure: true,
    },
  },
  initialState: {},
  model: {},
  mfsu: false,
  extraPostCSSPlugins: [
    postCssPxToViewport({
      unitToConvert: 'px',
      viewportWidth: 1080,
      unitPrecision: 5,
      viewportUnit: 'vw',
      fontViewportUnit: 'vw',
      exclude: [/node_modules/],
      minPixelValue: 1,
      mediaQuery: false,
    }),
  ],
  extraBabelPlugins: [
    ["import", { "libraryName": "antd-mobile", "libraryDirectory": "es/components", "style": false}]
  ],
});
