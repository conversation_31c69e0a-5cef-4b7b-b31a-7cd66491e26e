// game.js

const canvas = document.getElementById('gameCanvas');
const ctx = canvas.getContext('2d');

// 游戏配置
const TILE_SIZE = 32; // 瓦片大小
const CANVAS_WIDTH = 800; // 视窗宽度
const CANVAS_HEIGHT = 480; // 视窗高度

canvas.width = CANVAS_WIDTH;
canvas.height = CANVAS_HEIGHT;

// 颜色主题 (赛博朋克风格)
const COLORS = {
    BACKGROUND: '#1a1a2e',      // 深蓝紫
    PLAYER: '#00f0f0',          // 霓虹青
    PLATFORM: '#3f3f5a',        // 深灰紫
    GOOMBA: '#ff007f',          // 霓虹粉
    COIN: '#ffd700',            // 金色 (芯片)
    TEXT: '#9d00ff',            // 霓虹紫
    NEON_BLUE: '#00f0f0',
    NEON_PINK: '#ff007f',
    NEON_GREEN: '#00ff00',
    DARK_BUILDING: '#0a0a1a',
    LIGHT_GLOW: 'rgba(0, 240, 240, 0.3)'
};

// 游戏状态
const GAME_STATE = {
    RUNNING: 'RUNNING',
    GAME_OVER: 'GAME_OVER',
    WIN: 'WIN'
};
let currentState = GAME_STATE.RUNNING;

// 物理常量
const GRAVITY = 0.8;
const JUMP_VELOCITY = -15;
const PLAYER_SPEED = 5;

// 玩家对象
const player = {
    x: 50,
    y: CANVAS_HEIGHT - TILE_SIZE * 2,
    width: TILE_SIZE,
    height: TILE_SIZE * 1.5, // 让玩家稍高一些
    dx: 0,
    dy: 0,
    onGround: false,
    facing: 'right', // 方向
    score: 0,
    lives: 3
};

// 敌人对象 (简化的Goomba，赛博朋克风格可以叫“巡逻机器人”)
class Enemy {
    constructor(x, y, speed = 2) {
        this.x = x;
        this.y = y;
        this.width = TILE_SIZE;
        this.height = TILE_SIZE;
        this.dx = speed;
        this.dy = 0;
        this.initialSpeed = speed;
    }

    update(deltaTime, tiles) {
        this.x += this.dx;

        // 简单的碰撞检测，遇到边缘或平台就转向
        let hitWall = false;
        // 检查前方是否有阻碍 (简化处理)
        const nextX = this.dx > 0 ? this.x + this.width + 1 : this.x - 1;
        const nextY = this.y + this.height + 1; // 检查脚下是否还有平台
        
        const currentTileX = Math.floor(this.x / TILE_SIZE);
        const currentTileY = Math.floor(this.y / TILE_SIZE);
        const nextTileX = Math.floor(nextX / TILE_SIZE);
        const nextTileY = Math.floor(nextY / TILE_SIZE);

        if (nextTileX < 0 || nextTileX >= map[0].length) { // 碰到世界边缘
            hitWall = true;
        } else if (map[currentTileY] && map[currentTileY][nextTileX] === 1) { // 碰到前方平台
            hitWall = true;
        }

        // 检查前方下方是否有地面 (防止掉落)
        if (map[nextTileY] && map[nextTileY][nextTileX] === 0) { // 前方没有地面
            hitWall = true;
        }


        if (hitWall) {
            this.dx *= -1; // 转向
        }

        // 应用重力 (如果需要敌人跳跃或掉落)
        // this.dy += GRAVITY;
        // this.y += this.dy;

        // 简单的边界检查 (防止敌人掉出屏幕底部)
        if (this.y > CANVAS_HEIGHT) {
            this.x = -100; // 移出屏幕，可以销毁或重置
        }
    }

    draw(ctx, cameraX) {
        ctx.fillStyle = COLORS.GOOMBA;
        ctx.fillRect(this.x - cameraX, this.y, this.width, this.height);
        // 绘制赛博朋克风格的细节
        ctx.strokeStyle = COLORS.NEON_BLUE;
        ctx.lineWidth = 1;
        ctx.strokeRect(this.x - cameraX + 2, this.y + 2, this.width - 4, this.height - 4);
    }
}

// 瓦片地图 (1: 平台, 0: 空白, 2: 收集品/芯片)
// 这是一个非常简单的地图，实际游戏中需要更复杂的设计
const map = [
    [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
    [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1] // 地面
];

// 动态生成一些平台和收集品、敌人 (为了演示)
function generateLevelElements() {
    // 增加一些平台
    map[map.length - 5] = [0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0];
    map[map.length - 8] = [0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
    map[map.length - 10] = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0];

    // 增加一些芯片 (2)
    map[map.length - 6][5] = 2;
    map[map.length - 6][6] = 2;
    map[map.length - 9][10] = 2;
    map[map.length - 9][11] = 2;
    map[map.length - 11][14] = 2;

    // 增加敌人
    enemies.push(new Enemy(TILE_SIZE * 8, CANVAS_HEIGHT - TILE_SIZE * 3));
    enemies.push(new Enemy(TILE_SIZE * 15, CANVAS_HEIGHT - TILE_SIZE * 6, -2));
    enemies.push(new Enemy(TILE_SIZE * 3, CANVAS_HEIGHT - TILE_SIZE * 11, 1.5)); // 浮空敌人
}

const enemies = [];
generateLevelElements(); // 初始化生成关卡元素

let cameraX = 0; // 摄像头位置，用于实现横向卷轴

// 键盘输入
const keys = {
    left: false,
    right: false,
    up: false
};

document.addEventListener('keydown', (e) => {
    if (currentState !== GAME_STATE.RUNNING) return;
    if (e.key === 'ArrowLeft' || e.key === 'a') {
        keys.left = true;
        player.facing = 'left';
    }
    if (e.key === 'ArrowRight' || e.key === 'd') {
        keys.right = true;
        player.facing = 'right';
    }
    if (e.key === 'ArrowUp' || e.key === 'w' || e.key === ' ') {
        keys.up = true;
    }
});

document.addEventListener('keyup', (e) => {
    if (e.key === 'ArrowLeft' || e.key === 'a') {
        keys.left = false;
    }
    if (e.key === 'ArrowRight' || e.key === 'd') {
        keys.right = false;
    }
    if (e.key === 'ArrowUp' || e.key === 'w' || e.key === ' ') {
        keys.up = false;
    }
});

// 碰撞检测函数 (AABB 轴对齐包围盒)
function checkCollision(rect1, rect2) {
    return rect1.x < rect2.x + rect2.width &&
           rect1.x + rect1.width > rect2.x &&
           rect1.y < rect2.y + rect2.height &&
           rect1.y + rect1.height > rect2.y;
}

// 更新游戏逻辑
function update(deltaTime) {
    if (currentState !== GAME_STATE.RUNNING) return;

    // 玩家水平移动
    player.dx = 0;
    if (keys.left) {
        player.dx = -PLAYER_SPEED;
    }
    if (keys.right) {
        player.dx = PLAYER_SPEED;
    }

    // 玩家跳跃
    if (keys.up && player.onGround) {
        player.dy = JUMP_VELOCITY;
        player.onGround = false; // 离开地面
    }

    // 应用重力
    player.dy += GRAVITY;
    player.y += player.dy;
    player.x += player.dx;

    // 边界检查 (防止玩家掉出屏幕左侧)
    if (player.x < 0) {
        player.x = 0;
    }
    // TODO: 限制玩家在地图的最大宽度内

    // 瓦片碰撞检测
    player.onGround = false; // 每次更新都假设不在地面，然后通过碰撞检测修正

    const playerRect = {
        x: player.x,
        y: player.y,
        width: player.width,
        height: player.height
    };

    // 遍历地图瓦片进行碰撞检测
    for (let row = 0; row < map.length; row++) {
        for (let col = 0; col < map[row].length; col++) {
            const tileType = map[row][col];
            if (tileType === 1 || tileType === 2) { // 平台或收集品
                const tileRect = {
                    x: col * TILE_SIZE,
                    y: row * TILE_SIZE,
                    width: TILE_SIZE,
                    height: TILE_SIZE
                };

                if (checkCollision(playerRect, tileRect)) {
                    if (tileType === 1) { // 碰到平台
                        // 从下方碰到平台
                        if (player.dy > 0 && playerRect.y + playerRect.height - player.dy <= tileRect.y) {
                            player.y = tileRect.y - player.height; // 放在平台上
                            player.dy = 0;
                            player.onGround = true;
                        }
                        // 从上方碰到平台 (跳跃碰到天花板)
                        else if (player.dy < 0 && playerRect.y - player.dy >= tileRect.y + tileRect.height) {
                            player.y = tileRect.y + tileRect.height;
                            player.dy = 0;
                        }
                        // 水平碰撞
                        else if (player.dx > 0 && playerRect.x + playerRect.width - player.dx <= tileRect.x) {
                            player.x = tileRect.x - player.width;
                            player.dx = 0;
                        }
                        else if (player.dx < 0 && playerRect.x - player.dx >= tileRect.x + tileRect.width) {
                            player.x = tileRect.x + tileRect.width;
                            player.dx = 0;
                        }
                    } else if (tileType === 2) { // 碰到收集品 (芯片)
                        player.score += 100;
                        map[row][col] = 0; // 移除芯片
                    }
                }
            }
        }
    }

    // 敌人更新和碰撞检测
    enemies.forEach(enemy => {
        enemy.update(deltaTime, map); // 传递地图数据给敌人进行更智能的碰撞判断
        if (checkCollision(playerRect, enemy)) {
            // 玩家踩到敌人 (跳跃攻击)
            if (player.dy > 0 && playerRect.y + playerRect.height - player.dy <= enemy.y + enemy.height / 2) {
                player.dy = JUMP_VELOCITY * 0.7; // 弹跳一下
                enemy.x = -1000; // 将敌人移出屏幕，可以销毁或播放死亡动画
                player.score += 500;
            } else {
                // 玩家被敌人碰到
                player.lives--;
                if (player.lives <= 0) {
                    currentState = GAME_STATE.GAME_OVER;
                } else {
                    // 重置玩家位置 (简单处理，实际应有无敌帧和闪烁)
                    player.x = 50;
                    player.y = CANVAS_HEIGHT - TILE_SIZE * 2;
                    player.dx = 0;
                    player.dy = 0;
                }
            }
        }
    });

    // 玩家掉落到屏幕下方
    if (player.y > CANVAS_HEIGHT + 50) { // 留一点缓冲
        player.lives--;
        if (player.lives <= 0) {
            currentState = GAME_STATE.GAME_OVER;
        } else {
            // 重置玩家位置
            player.x = 50;
            player.y = CANVAS_HEIGHT - TILE_SIZE * 2;
            player.dx = 0;
            player.dy = 0;
        }
    }

    // 摄像头跟随玩家 (平滑滚动)
    const targetCameraX = player.x - CANVAS_WIDTH / 2 + player.width / 2;
    cameraX += (targetCameraX - cameraX) * 0.05; // 0.05 是平滑系数
    cameraX = Math.max(0, cameraX); // 防止摄像头滚到地图左边之外
    // 限制摄像头在地图最大宽度内
    const maxCameraX = map[0].length * TILE_SIZE - CANVAS_WIDTH;
    cameraX = Math.min(cameraX, maxCameraX);

    // 胜利条件 (简化：收集所有芯片)
    const remainingChips = map.flat().filter(tile => tile === 2).length;
    if (remainingChips === 0 && currentState === GAME_STATE.RUNNING) {
        currentState = GAME_STATE.WIN;
    }
}

// 绘制游戏画面
function draw() {
    ctx.clearRect(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT);
    ctx.fillStyle = COLORS.BACKGROUND;
    ctx.fillRect(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT);

    // 绘制赛博朋克背景细节 (例如摩天大楼剪影)
    ctx.fillStyle = COLORS.DARK_BUILDING;
    // 简单的背景建筑
    ctx.fillRect(0, CANVAS_HEIGHT - 200, CANVAS_WIDTH, 200);
    ctx.fillRect(CANVAS_WIDTH * 0.2, CANVAS_HEIGHT - 300, CANVAS_WIDTH * 0.1, 300);
    ctx.fillRect(CANVAS_WIDTH * 0.7, CANVAS_HEIGHT - 250, CANVAS_WIDTH * 0.15, 250);

    // 霓虹灯效果
    ctx.shadowBlur = 15;
    ctx.shadowColor = COLORS.NEON_BLUE;
    ctx.fillStyle = COLORS.NEON_BLUE;
    ctx.fillRect(50, CANVAS_HEIGHT - 180, 10, 80);
    ctx.shadowColor = COLORS.NEON_PINK;
    ctx.fillStyle = COLORS.NEON_PINK;
    ctx.fillRect(CANVAS_WIDTH * 0.75, CANVAS_HEIGHT - 220, 15, 150);
    ctx.shadowBlur = 0; // 重置阴影

    // 绘制瓦片地图
    for (let row = 0; row < map.length; row++) {
        for (let col = 0; col < map[row].length; col++) {
            const tileType = map[row][col];
            const x = col * TILE_SIZE - cameraX;
            const y = row * TILE_SIZE;

            if (x + TILE_SIZE < 0 || x > CANVAS_WIDTH) continue; // 只绘制屏幕内的瓦片

            if (tileType === 1) { // 平台
                ctx.fillStyle = COLORS.PLATFORM;
                ctx.fillRect(x, y, TILE_SIZE, TILE_SIZE);
                // 绘制赛博朋克风格的线条
                ctx.strokeStyle = COLORS.NEON_BLUE;
                ctx.lineWidth = 1;
                ctx.strokeRect(x, y, TILE_SIZE, TILE_SIZE);
            } else if (tileType === 2) { // 芯片 (收集品)
                ctx.fillStyle = COLORS.COIN;
                ctx.beginPath();
                ctx.arc(x + TILE_SIZE / 2, y + TILE_SIZE / 2, TILE_SIZE / 3, 0, Math.PI * 2);
                ctx.fill();
                // 芯片的霓虹光晕
                ctx.shadowBlur = 8;
                ctx.shadowColor = COLORS.COIN;
                ctx.beginPath();
                ctx.arc(x + TILE_SIZE / 2, y + TILE_SIZE / 2, TILE_SIZE / 3, 0, Math.PI * 2);
                ctx.fill();
                ctx.shadowBlur = 0;
            }
        }
    }

    // 绘制玩家
    ctx.fillStyle = COLORS.PLAYER;
    ctx.fillRect(player.x - cameraX, player.y, player.width, player.height);
    // 玩家的霓虹光晕
    ctx.shadowBlur = 10;
    ctx.shadowColor = COLORS.NEON_BLUE;
    ctx.fillRect(player.x - cameraX, player.y, player.width, player.height);
    ctx.shadowBlur = 0;

    // 绘制敌人
    enemies.forEach(enemy => {
        enemy.draw(ctx, cameraX);
    });

    // 绘制UI (分数和生命值)
    ctx.fillStyle = COLORS.TEXT;
    ctx.font = '20px "Press Start 2P", cursive'; // 尝试使用像素字体，需要引入
    ctx.fillText(`芯片: ${player.score}`, 10, 30);
    ctx.fillText(`生命: ${player.lives}`, 10, 60);

    // 游戏结束/胜利画面
    if (currentState === GAME_STATE.GAME_OVER) {
        ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        ctx.fillRect(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT);
        ctx.fillStyle = COLORS.NEON_PINK;
        ctx.font = '40px "Press Start 2P", cursive';
        ctx.textAlign = 'center';
        ctx.fillText('游戏结束', CANVAS_WIDTH / 2, CANVAS_HEIGHT / 2 - 20);
        ctx.font = '20px "Press Start 2P", cursive';
        ctx.fillText('按下 R 重新开始', CANVAS_WIDTH / 2, CANVAS_HEIGHT / 2 + 30);
    } else if (currentState === GAME_STATE.WIN) {
        ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        ctx.fillRect(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT);
        ctx.fillStyle = COLORS.NEON_GREEN;
        ctx.font = '40px "Press Start 2P", cursive';
        ctx.textAlign = 'center';
        ctx.fillText('任务完成！', CANVAS_WIDTH / 2, CANVAS_HEIGHT / 2 - 20);
        ctx.font = '20px "Press Start 2P", cursive';
        ctx.fillText('你的分数: ' + player.score, CANVAS_WIDTH / 2, CANVAS_HEIGHT / 2 + 30);
    }
}

// 游戏主循环
let lastTime = 0;
function gameLoop(currentTime) {
    const deltaTime = (currentTime - lastTime) / 1000; // 转换为秒
    lastTime = currentTime;

    update(deltaTime);
    draw();

    requestAnimationFrame(gameLoop);
}

// 重新开始游戏
document.addEventListener('keydown', (e) => {
    if (e.key === 'r' && (currentState === GAME_STATE.GAME_OVER || currentState === GAME_STATE.WIN)) {
        resetGame();
    }
});

function resetGame() {
    player.x = 50;
    player.y = CANVAS_HEIGHT - TILE_SIZE * 2;
    player.dx = 0;
    player.dy = 0;
    player.score = 0;
    player.lives = 3;
    currentState = GAME_STATE.RUNNING;
    cameraX = 0;

    // 重置地图和敌人（这里简单地重新生成，实际应有更精细的地图加载机制）
    for (let r = 0; r < map.length; r++) {
        for (let c = 0; c < map[r].length; c++) {
            if (map[r][c] === 2) {
                map[r][c] = 0; // 清除已收集的芯片
            }
        }
    }
    // 重新生成部分元素 (为了演示方便，实际关卡应从模板加载)
    generateLevelElements();
    enemies.length = 0; // 清空敌人
    enemies.push(new Enemy(TILE_SIZE * 8, CANVAS_HEIGHT - TILE_SIZE * 3));
    enemies.push(new Enemy(TILE_SIZE * 15, CANVAS_HEIGHT - TILE_SIZE * 6, -2));
    enemies.push(new Enemy(TILE_SIZE * 3, CANVAS_HEIGHT - TILE_SIZE * 11, 1.5));
}


// 开始游戏循环
requestAnimationFrame(gameLoop);

// 尝试加载一个像素字体 (可选，但推荐用于赛博朋克风格)
const fontLink = document.createElement('link');
fontLink.href = 'https://fonts.googleapis.com/css2?family=Press+Start+2P&display=swap';
fontLink.rel = 'stylesheet';
document.head.appendChild(fontLink);