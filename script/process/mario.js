// @ts-nocheck
/**
 * Mario Game (Ghibli Style) - 无限地图基础
 */

const canvas = document.getElementById('gameCanvas');
const ctx = canvas.getContext('2d');

// 游戏参数
const GAME_WIDTH = canvas.width;
const GAME_HEIGHT = canvas.height;
const HERO_SCREEN_X = Math.floor(GAME_WIDTH / 3); // 主角在屏幕上的固定x

// 资源占位（后续可替换为吉卜力风格图片）
const assets = {
  bg: null,
  mario: null,
  enemy: null,
  coin: null,
  platform: null
};

// 加载图片资源
function loadImage(src) {
  return new Promise((resolve) => {
    const img = new Image();
    img.src = src;
    img.onload = () => resolve(img);
    img.onerror = () => resolve(null);
  });
}

async function loadAssets() {
  assets.bg = await loadImage('assets/bg.png');
  assets.mario = await loadImage('assets/mario.png');
  assets.enemy = await loadImage('assets/enemy.png');
  assets.coin = await loadImage('assets/coin.png');
  assets.platform = await loadImage('assets/platform.png');
}

// 游戏主角
const mario = {
  x: 100, // 世界坐标
  y: 400,
  vx: 0,
  vy: 0,
  width: 48,
  height: 48,
  speed: 4,
  jumpPower: 13,
  onGround: false,
  lives: 3,
  score: 0,
  invincible: 0 // 无敌帧，避免连续死亡
};

// 世界偏移量
let worldOffsetX = 0;

// 平台（动态生成）
let platforms = [
  { x: 0, y: 500, w: 960, h: 40 },
  { x: 200, y: 400, w: 120, h: 20 },
  { x: 400, y: 320, w: 120, h: 20 },
  { x: 700, y: 380, w: 120, h: 20 }
];

// 怪物（动态生成）
let enemies = [
  { x: 600, w: 40, h: 48, vx: 2, dir: 1, range: [550, 800], platformIndex: 0 },
  { x: 300, w: 40, h: 48, vx: 1.5, dir: -1, range: [200, 400], platformIndex: 1 }
];

// 怪物初始化：让怪物y坐标自动贴合平台顶部
function initEnemiesOnPlatform() {
  for (const enemy of enemies) {
    // 如果指定了platformIndex，则直接用
    let pf = platforms[enemy.platformIndex];
    if (!pf) {
      // 自动寻找下方最近的平台
      pf = null;
      let minDy = Infinity;
      for (const p of platforms) {
        if (
          enemy.x + enemy.w > p.x &&
          enemy.x < p.x + p.w &&
          Math.abs(p.y - enemy.h) < minDy
        ) {
          pf = p;
          minDy = Math.abs(p.y - enemy.h);
        }
      }
    }
    if (pf) {
      enemy.y = pf.y - enemy.h;
      enemy.platform = pf;
    } else {
      // 没有平台，放到底部
      enemy.y = GAME_HEIGHT - enemy.h;
      enemy.platform = null;
    }
  }
}

// 控制
const keys = {};
window.addEventListener('keydown', e => keys[e.code] = true);
window.addEventListener('keyup', e => keys[e.code] = false);

function updateMario() {
  // 左右移动
  if (keys['ArrowLeft'] || keys['KeyA']) mario.vx = -mario.speed;
  else if (keys['ArrowRight'] || keys['KeyD']) mario.vx = mario.speed;
  else mario.vx = 0;

  // 跳跃
  if ((keys['ArrowUp'] || keys['Space'] || keys['KeyW']) && mario.onGround) {
    mario.vy = -mario.jumpPower;
    mario.onGround = false;
  }

  // 重力
  mario.vy += 0.7;
  if (mario.vy > 16) mario.vy = 16;

  // 世界偏移逻辑：主角在屏幕左1/3处后，世界向左偏移
  if (mario.x - worldOffsetX > HERO_SCREEN_X && mario.vx > 0) {
    worldOffsetX += mario.vx;
  } else {
    mario.x += mario.vx;
  }

  mario.y += mario.vy;

  // 平台碰撞（只检测y方向下落时的碰撞，避免穿透）
  mario.onGround = false;
  for (const p of platforms) {
    if (
      mario.vy >= 0 &&
      mario.x + mario.width > p.x &&
      mario.x < p.x + p.w &&
      mario.y + mario.height > p.y &&
      mario.y + mario.height - mario.vy <= p.y + 4
    ) {
      mario.y = p.y - mario.height;
      mario.vy = 0;
      mario.onGround = true;
    }
  }

  // 边界
  if (mario.x < worldOffsetX) mario.x = worldOffsetX;
  if (mario.y > GAME_HEIGHT) loseLife();

  // 无敌帧倒计时
  if (mario.invincible > 0) mario.invincible--;
}

function updateEnemies() {
  for (const enemy of enemies) {
    // 只在x轴巡逻
    enemy.x += enemy.vx * enemy.dir;
    // 到达巡逻边界反向
    if (enemy.x < enemy.range[0]) {
      enemy.x = enemy.range[0];
      enemy.dir = 1;
    } else if (enemy.x + enemy.w > enemy.range[1]) {
      enemy.x = enemy.range[1] - enemy.w;
      enemy.dir = -1;
    }
    // 始终贴合平台顶部
    if (enemy.platform) {
      enemy.y = enemy.platform.y - enemy.h;
    }
  }
}

function checkMarioEnemyCollision() {
  if (mario.invincible > 0) return;
  for (const enemy of enemies) {
    if (
      mario.x + mario.width > enemy.x &&
      mario.x < enemy.x + enemy.w &&
      mario.y + mario.height > enemy.y &&
      mario.y < enemy.y + enemy.h
    ) {
      loseLife();
      mario.invincible = 60; // 1秒无敌
      break;
    }
  }
}

function loseLife() {
  mario.lives--;
  mario.x = worldOffsetX + 100;
  mario.y = 400;
  mario.vx = 0;
  mario.vy = 0;
  mario.invincible = 60;
  if (mario.lives <= 0) {
    alert('游戏结束！');
    mario.lives = 3;
    mario.score = 0;
    worldOffsetX = 0;
    mario.x = 100;
  }
}

function drawBackground() {
  // 无限背景平铺
  if (assets.bg) {
    const bgWidth = assets.bg.width;
    let startX = -((worldOffsetX) % bgWidth);
    for (let x = startX; x < GAME_WIDTH; x += bgWidth) {
      ctx.drawImage(assets.bg, x, 0, bgWidth, GAME_HEIGHT);
    }
  } else {
    ctx.fillStyle = '#cbe6c1';
    ctx.fillRect(0, 0, GAME_WIDTH, GAME_HEIGHT);
  }
}

function drawPlatforms() {
  for (const p of platforms) {
    const drawX = p.x - worldOffsetX;
    if (drawX + p.w < 0 || drawX > GAME_WIDTH) continue;
    if (assets.platform) ctx.drawImage(assets.platform, drawX, p.y, p.w, p.h);
    else {
      ctx.fillStyle = '#a3b18a';
      ctx.fillRect(drawX, p.y, p.w, p.h);
    }
  }
}

function drawEnemies() {
  for (const enemy of enemies) {
    const drawX = enemy.x - worldOffsetX;
    if (drawX + enemy.w < 0 || drawX > GAME_WIDTH) continue;
    if (assets.enemy) ctx.drawImage(assets.enemy, drawX, enemy.y, enemy.w, enemy.h);
    else {
      ctx.save();
      ctx.fillStyle = '#8e5a3d';
      ctx.strokeStyle = '#333';
      ctx.lineWidth = 2;
      ctx.beginPath();
      ctx.ellipse(drawX + enemy.w/2, enemy.y + enemy.h/2, enemy.w/2, enemy.h/2, 0, 0, Math.PI*2);
      ctx.fill();
      ctx.stroke();
      ctx.restore();
    }
  }
}

function drawMario() {
  const drawX = mario.x - worldOffsetX;
  if (assets.mario) ctx.drawImage(assets.mario, drawX, mario.y, mario.width, mario.height);
  else {
    ctx.save();
    ctx.globalAlpha = mario.invincible > 0 ? 0.5 : 1;
    ctx.fillStyle = '#f7c873';
    ctx.strokeStyle = '#333';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.ellipse(drawX + mario.width/2, mario.y + mario.height/2, 22, 24, 0, 0, Math.PI*2);
    ctx.fill();
    ctx.stroke();
    ctx.restore();
  }
}

function drawUI() {
  document.getElementById('score').textContent = '分数: ' + mario.score;
  document.getElementById('lives').textContent = '生命: ' + mario.lives;
}

// 无限平台生成
function generatePlatforms() {
  // 找到最右侧平台
  let rightMost = 0;
  for (const p of platforms) {
    if (p.x + p.w > rightMost) rightMost = p.x + p.w;
  }
  // 保证屏幕右侧有平台
  while (rightMost < worldOffsetX + GAME_WIDTH * 2) {
    // 随机高度和宽度
    const w = 80 + Math.random() * 120;
    const h = 20;
    const gap = 80 + Math.random() * 120;
    const y = 320 + Math.random() * 180;
    platforms.push({ x: rightMost + gap, y, w, h });
    rightMost = rightMost + gap + w;
  }
  // 移除左侧看不到的平台
  platforms = platforms.filter(p => p.x + p.w > worldOffsetX - 200);
}

// 无限怪物生成
function generateEnemies() {
  // 保证屏幕右侧有怪物
  let rightMost = 0;
  for (const e of enemies) {
    if (e.x > rightMost) rightMost = e.x;
  }
  while (rightMost < worldOffsetX + GAME_WIDTH * 2) {
    // 随机选择一个平台
    const candidatePlatforms = platforms.filter(p => p.x > worldOffsetX + GAME_WIDTH/2 && p.w > 60);
    if (candidatePlatforms.length === 0) break;
    const pf = candidatePlatforms[Math.floor(Math.random() * candidatePlatforms.length)];
    const ex = pf.x + 10 + Math.random() * (pf.w - 50);
    const ew = 40, eh = 48;
    const range = [pf.x, pf.x + pf.w - ew];
    enemies.push({ x: ex, w: ew, h: eh, vx: 1.5 + Math.random(), dir: Math.random() > 0.5 ? 1 : -1, range, platformIndex: platforms.indexOf(pf) });
    rightMost = ex;
  }
  // 移除左侧看不到的怪物
  enemies = enemies.filter(e => e.x + e.w > worldOffsetX - 200);
}

function gameLoop() {
  drawBackground();
  drawPlatforms();
  updateMario();
  generatePlatforms();
  generateEnemies();
  updateEnemies();
  checkMarioEnemyCollision();
  drawEnemies();
  drawMario();
  drawUI();
  requestAnimationFrame(gameLoop);
}

// 启动
loadAssets().then(() => {
  initEnemiesOnPlatform();
  gameLoop();
}); 