/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-27 17:36:46
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-05-27 17:44:57
 * @FilePath: /mobile-yw/script/process/index.js
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */

// @ts-nocheck
/**
 * 流程展示控制脚本
 */

// 流程节点配置
const processNodes = [
  { id: 1, title: '提交申请' },
  { id: 2, title: '部门审核' },
  { id: 3, title: '财务审核' },
  { id: 4, title: '总经理审批' },
  { id: 5, title: '完成' }
];

// 流程状态管理
const processState = {
  currentNodeIndex: -1,  // 当前节点索引
  history: [],           // 历史路径
  nodeLoops: {},         // 每个节点的循环次数
  totalLoops: 0,         // 总循环次数
};

// DOM 元素
const elements = {
  processNodes: null,
  processProgress: null,
  btnNext: null,
  btnBack: null,
  btnReset: null,
  currentStatus: null,
  totalLoops: null,
  pathHistory: null
};

// 初始化DOM元素引用
function initElements() {
  elements.processNodes = document.getElementById('processNodes');
  elements.processProgress = document.getElementById('processProgress');
  elements.btnNext = document.getElementById('btnNext');
  elements.btnBack = document.getElementById('btnBack');
  elements.btnReset = document.getElementById('btnReset');
  elements.currentStatus = document.getElementById('currentStatus');
  elements.totalLoops = document.getElementById('totalLoops');
  elements.pathHistory = document.getElementById('pathHistory');
}

// 初始化流程节点
function initProcessNodes() {
  const processNodesElement = document.getElementById('processNodes');
  if (!processNodesElement) return;
  
  processNodesElement.innerHTML = '';
  
  processNodes.forEach((node, index) => {
    const nodeElement = document.createElement('div');
    nodeElement.className = 'node';
    nodeElement.setAttribute('data-index', String(index));
    nodeElement.setAttribute('data-id', String(node.id));
    
    const nodePoint = document.createElement('div');
    nodePoint.className = 'node-point';
    nodePoint.textContent = String(node.id);
    
    const nodeTitle = document.createElement('div');
    nodeTitle.className = 'node-title';
    nodeTitle.textContent = node.title;
    
    nodeElement.appendChild(nodePoint);
    nodeElement.appendChild(nodeTitle);
    processNodesElement.appendChild(nodeElement);
    
    // 添加点击事件，允许直接跳转到该节点
    nodeElement.addEventListener('click', () => {
      if (processState.currentNodeIndex >= 0) {
        jumpToNode(index);
      }
    });
  });
}

// 更新流程进度条
function updateProgress() {
  const progressElement = document.getElementById('processProgress');
  if (!progressElement) return;
  
  if (processState.currentNodeIndex < 0) {
    progressElement.style.width = '0%';
    return;
  }
  
  const progressPercentage = (processState.currentNodeIndex / (processNodes.length - 1)) * 100;
  progressElement.style.width = `${progressPercentage}%`;
}

// 更新节点状态
function updateNodeStatus() {
  const processNodesElement = document.getElementById('processNodes');
  if (!processNodesElement) return;
  
  const nodeElements = processNodesElement.querySelectorAll('.node');
  
  nodeElements.forEach((node, index) => {
    // 清除之前的状态
    node.classList.remove('active', 'completed');
    
    // 移除之前的循环标记
    const oldBadge = node.querySelector('.loop-badge');
    if (oldBadge) {
      oldBadge.remove();
    }
    
    if (index < processState.currentNodeIndex) {
      node.classList.add('completed');
    } else if (index === processState.currentNodeIndex) {
      node.classList.add('active');
    }
    
    // 添加循环标记
    const nodeId = parseInt(node.getAttribute('data-id') || '0');
    if (processState.nodeLoops[nodeId] && processState.nodeLoops[nodeId] > 1) {
      const loopBadge = document.createElement('div');
      loopBadge.className = 'loop-badge';
      loopBadge.textContent = String(processState.nodeLoops[nodeId]);
      const nodePoint = node.querySelector('.node-point');
      if (nodePoint) {
        nodePoint.appendChild(loopBadge);
      }
    }
  });
}

// 更新按钮状态
function updateButtons() {
  const btnBack = document.getElementById('btnBack');
  const btnNext = document.getElementById('btnNext');
  
  if (btnBack) {
    btnBack.disabled = processState.currentNodeIndex <= 0;
  }
  
  if (btnNext) {
    btnNext.disabled = processState.currentNodeIndex >= processNodes.length - 1;
  }
}

// 更新状态信息
function updateStatusInfo() {
  const currentStatusElement = document.getElementById('currentStatus');
  const totalLoopsElement = document.getElementById('totalLoops');
  
  if (currentStatusElement) {
    if (processState.currentNodeIndex < 0) {
      currentStatusElement.textContent = '未开始';
    } else {
      const currentNode = processNodes[processState.currentNodeIndex];
      currentStatusElement.textContent = currentNode.title;
    }
  }
  
  if (totalLoopsElement) {
    totalLoopsElement.textContent = String(processState.totalLoops);
  }
}

// 更新路径历史
function updatePathHistory() {
  const pathHistoryElement = document.getElementById('pathHistory');
  if (!pathHistoryElement) return;
  
  pathHistoryElement.innerHTML = '';
  
  processState.history.forEach((nodeId, index) => {
    const node = processNodes.find(n => n.id === nodeId);
    if (!node) return;
    
    const pathItem = document.createElement('span');
    pathItem.className = 'path-item';
    
    // 检查是否是循环路径
    if (index > 0 && processState.history.indexOf(nodeId) < index) {
      pathItem.classList.add('loop-path');
    }
    
    pathItem.textContent = node.title;
    pathHistoryElement.appendChild(pathItem);
  });
}

// 移动到下一个节点
function moveNext() {
  if (processState.currentNodeIndex >= processNodes.length - 1) return;
  
  processState.currentNodeIndex++;
  const currentNode = processNodes[processState.currentNodeIndex];
  
  // 记录历史路径
  processState.history.push(currentNode.id);
  
  // 更新节点循环次数
  if (!processState.nodeLoops[currentNode.id]) {
    processState.nodeLoops[currentNode.id] = 1;
  } else {
    processState.nodeLoops[currentNode.id]++;
    
    // 如果节点被重复访问，增加总循环次数
    if (processState.nodeLoops[currentNode.id] > 1) {
      processState.totalLoops++;
    }
  }
  
  updateUI();
}

// 返回上一个节点
function moveBack() {
  if (processState.currentNodeIndex <= 0) return;
  
  processState.currentNodeIndex--;
  const currentNode = processNodes[processState.currentNodeIndex];
  
  // 记录历史路径
  processState.history.push(currentNode.id);
  
  // 更新节点循环次数
  if (!processState.nodeLoops[currentNode.id]) {
    processState.nodeLoops[currentNode.id] = 1;
  } else {
    processState.nodeLoops[currentNode.id]++;
    
    // 如果节点被重复访问，增加总循环次数
    if (processState.nodeLoops[currentNode.id] > 1) {
      processState.totalLoops++;
    }
  }
  
  updateUI();
}

// 跳转到指定节点
function jumpToNode(index) {
  if (index < 0 || index >= processNodes.length) return;
  
  // 如果是往回跳，标记为循环
  if (index < processState.currentNodeIndex) {
    processState.totalLoops++;
  }
  
  processState.currentNodeIndex = index;
  const currentNode = processNodes[index];
  
  // 记录历史路径
  processState.history.push(currentNode.id);
  
  // 更新节点循环次数
  if (!processState.nodeLoops[currentNode.id]) {
    processState.nodeLoops[currentNode.id] = 1;
  } else {
    processState.nodeLoops[currentNode.id]++;
  }
  
  updateUI();
}

// 重置流程
function resetProcess() {
  processState.currentNodeIndex = -1;
  processState.history = [];
  processState.nodeLoops = {};
  processState.totalLoops = 0;
  
  updateUI();
}

// 更新整个UI
function updateUI() {
  updateProgress();
  updateNodeStatus();
  updateButtons();
  updateStatusInfo();
  updatePathHistory();
}

// 初始化
function init() {
  initElements();
  initProcessNodes();
  updateUI();
  
  // 绑定按钮事件
  const btnNext = document.getElementById('btnNext');
  const btnBack = document.getElementById('btnBack');
  const btnReset = document.getElementById('btnReset');
  
  if (btnNext) {
    btnNext.addEventListener('click', moveNext);
  }
  
  if (btnBack) {
    btnBack.addEventListener('click', moveBack);
  }
  
  if (btnReset) {
    btnReset.addEventListener('click', resetProcess);
  }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', init);
