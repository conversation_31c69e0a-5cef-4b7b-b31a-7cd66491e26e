<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-27 17:35:43
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-05-27 17:56:21
 * @FilePath: /mobile-yw/script/process/index.html
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
-->
<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>流程展示</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      body {
        font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        background-color: #f5f5f5;
        padding: 20px;
      }
      .container {
        max-width: 800px;
        margin: 0 auto;
        background-color: #fff;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
      h1 {
        text-align: center;
        margin-bottom: 30px;
        color: #333;
      }
      .process-container {
        position: relative;
        margin: 50px 0;
      }
      .process-line {
        position: absolute;
        top: 30px;
        left: 0;
        width: 100%;
        height: 4px;
        background-color: #e0e0e0;
        z-index: 1;
      }
      .process-progress {
        position: absolute;
        top: 30px;
        left: 0;
        height: 4px;
        background-color: #4caf50;
        z-index: 2;
        transition: width 0.5s ease;
      }
      .process-nodes {
        display: flex;
        justify-content: space-between;
        position: relative;
        z-index: 3;
      }
      .node {
        display: flex;
        flex-direction: column;
        align-items: center;
        cursor: pointer;
      }
      .node-point {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background-color: #fff;
        border: 4px solid #e0e0e0;
        margin-bottom: 10px;
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
        transition: all 0.3s ease;
      }
      .node.active .node-point {
        border-color: #4caf50;
        background-color: #4caf50;
        color: white;
      }
      .node.completed .node-point {
        border-color: #4caf50;
        background-color: #4caf50;
        color: white;
      }
      .node-title {
        font-size: 14px;
        color: #666;
        text-align: center;
        max-width: 100px;
      }
      .node.active .node-title {
        color: #4caf50;
        font-weight: bold;
      }
      .loop-badge {
        position: absolute;
        top: -10px;
        right: -10px;
        background-color: #ff5722;
        color: white;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        font-size: 12px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: bold;
      }
      .controls {
        display: flex;
        justify-content: center;
        margin-top: 40px;
        gap: 10px;
      }
      .btn {
        padding: 10px 15px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        transition: background-color 0.3s;
      }
      .btn-next {
        background-color: #4caf50;
        color: white;
      }
      .btn-back {
        background-color: #ff9800;
        color: white;
      }
      .btn-reset {
        background-color: #f44336;
        color: white;
      }
      .btn:hover {
        opacity: 0.9;
      }
      .btn:disabled {
        background-color: #ccc;
        cursor: not-allowed;
      }
      .process-info {
        margin-top: 30px;
        padding: 15px;
        background-color: #f9f9f9;
        border-radius: 4px;
        border-left: 4px solid #2196f3;
      }
      .path-history {
        margin-top: 20px;
      }
      .path-item {
        display: inline-block;
        margin-right: 5px;
        margin-bottom: 5px;
      }
      .path-item:not(:last-child)::after {
        content: '→';
        margin-left: 5px;
        color: #999;
      }
      .loop-path {
        color: #ff5722;
        font-weight: bold;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>流程展示</h1>

      <div class="process-container">
        <div class="process-line"></div>
        <div class="process-progress" id="processProgress"></div>
        <div class="process-nodes" id="processNodes">
          <!-- 节点将通过JS动态生成 -->
        </div>
      </div>

      <div class="controls">
        <button class="btn btn-back" id="btnBack" disabled>返回上一步</button>
        <button class="btn btn-next" id="btnNext">下一步</button>
        <button class="btn btn-reset" id="btnReset">重置</button>
      </div>

      <div class="process-info">
        <h3>当前状态: <span id="currentStatus">未开始</span></h3>
        <p>总循环次数: <span id="totalLoops">0</span></p>
        <div class="path-history">
          <h3>路径历史:</h3>
          <div id="pathHistory"></div>
        </div>
      </div>
    </div>

    <script src="index.js"></script>
  </body>
</html>
